<template>
  <div class="wrapper">
    <div class="item-wrapper">
      <div class="item-unit">状态</div>
      <div class="item-content">
        <van-radio-group v-model="optionInfo.status" direction="horizontal">
          <van-radio :name="1">正常</van-radio>
          <van-radio :name="0">停用</van-radio>
        </van-radio-group>
      </div>
    </div>
    <div class="item-wrapper">
      <div class="item-unit">小单位</div>
      <div class="item-price">
        <van-field
            v-model="optionInfo.sPrice"
            label="批发价"
            type="number"
            :formatter="checkInputFormatterPoint4"
            @input="inputPrice('sPrice')"
        />
      </div>
      <div class="item-barcode">
        <div style="display:flex;">
          <van-field
              v-model="optionInfo.sBarcode"
              label="条码"
          />
          <van-button style="background-color:#fff;border:none;margin-left:20px;margin-left:0px;width:50px;"
                      type="info" @click="btnScanBarcode_click('s')">
            <svg width="30px" height="30px" fill="#666">
              <use xlink:href="#icon-barcodeScan"></use>
            </svg>
          </van-button>
        </div>
      </div>
    </div>
    <div class="item-wrapper">
      <div class="item-unit">大单位</div>
      <div class="item-price">
        <van-field
            v-model="optionInfo.bPrice"
            label="批发价"
            type="number"
            :formatter="checkInputFormatterPoint4"
            @input="inputPrice('bPrice')"
        />
      </div>
      <div class="item-barcode">
        <div style="display:flex;">
          <van-field
              v-model="optionInfo.bBarcode"
              label="条码"
          />
          <van-button style="background-color:#fff;border:none;margin-left:20px;margin-left:0px;width:50px;"
                      type="info" @click="btnScanBarcode_click('b')">
            <svg width="30px" height="30px" fill="#666">
              <use xlink:href="#icon-barcodeScan"></use>
            </svg>
          </van-button>
        </div>
      </div>
    </div>
    <div class="item-wrapper">
      <div class="item-unit">中单位</div>
      <div class="item-price">
        <van-field
            v-model="optionInfo.mPrice"
            label="批发价"
            type="number"
            :formatter="checkInputFormatterPoint4"
            @input="inputPrice('mPrice')"
        />
      </div>
      <div class="item-barcode">
        <div style="display:flex;">
          <van-field
              v-model="optionInfo.mBarcode"
              label="条码"
          />
          <van-button style="background-color:#fff;border:none;margin-left:20px;margin-left:0px;width:50px;"
                      type="info" @click="btnScanBarcode_click('m')">
            <svg width="30px" height="30px" fill="#666">
              <use xlink:href="#icon-barcodeScan"></use>
            </svg>
          </van-button>
        </div>
      </div>
    </div>
  </div>

</template>

<script>
import {Field, Button, RadioGroup, Radio} from 'vant'
export default {
  name: "AttributeInfoEntry",
  props: {
    optionInfo: {
      type: Object
    },
    FormData: {
      type: Object
    }
  },
  components: {
    "van-field": Field,
    "van-button": Button,
    "van-radio-group": RadioGroup,
    "van-radio": Radio
  },
  data() {
    return {

    }
  },
  mounted() {

  },
  methods: {
    inputPrice(priceType) {
      switch (priceType) {
        case 'bPrice':
          if (!this.optionInfo.bPrice || !this.FormData.bfactor) return
          this.optionInfo.sPrice = toMoney(Number(this.optionInfo.bPrice) / Number(this.FormData.bfactor))
          if (this.FormData.mfactor)
            this.optionInfo.mPrice = toMoney(Number(this.optionInfo.sPrice) * Number(this.FormData.mfactor))
          break
        case 'sPrice':
          if (!this.FormData.sfactor) return
          if (this.FormData.bfactor)
            this.optionInfo.bPrice = toMoney(Number(this.optionInfo.sPrice) * Number(this.FormData.bfactor))
          if (this.FormData.mfactor)
            this.optionInfo.mPrice = toMoney(Number(this.optionInfo.sPrice) * Number(this.FormData.mfactor))
          break
        case 'mPrice':
          if (!this.optionInfo.mPrice || !this.FormData.mfactor) return
          this.optionInfo.sPrice = toMoney(Number(this.optionInfo.mPrice) / Number(this.FormData.mfactor))
          if (this.FormData.bfactor)
            this.optionInfo.bPrice = toMoney(Number(this.optionInfo.sPrice) * Number(this.FormData.bfactor))
          break
      }
    },
    async btnScanBarcode_click(unit_type) {
      try {
        const result = await this.scanBarcodeNew({
          unit_type: `attribute_info_entry_${unit_type}`
        })

        if (!result.code) {
          return
        }

        if (unit_type === 'b')
          this.optionInfo.bBarcode = result.code
        else if (unit_type === 'm')
          this.optionInfo.mBarcode = result.code
        else if (unit_type === 's')
          this.optionInfo.sBarcode = result.code
        this.$forceUpdate()
      } catch (error) {
        console.error('扫码失败:', error)
        this.$toast('扫码失败，请重试')
      }
    },

  }
}
</script>

<style scoped lang="less">
.wrapper {
  width: 100%;
  border-box:box-sizing;
  display: flex;
  flex-direction: column;
  .item-wrapper {
    .item-unit {
      height: 30px;
      font-size: 15px;
      font-weight: 500;
      background: #dddddd;
      display: flex;
      align-items: center;
      justify-content: flex-start;
      padding: 0 10px;
    }

    .item-content {
      padding: 12PX 30PX;
      background: #fff;
    }
  }
}

</style>
