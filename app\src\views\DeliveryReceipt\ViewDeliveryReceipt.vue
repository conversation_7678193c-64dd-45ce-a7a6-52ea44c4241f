<template>
  <div class="public_query">
    <div class="public_query_title">
      <van-form>
        <div class="public_query_titleSrc">
          <div class="public_query_titleSrc_item">
            <input type="text" v-model="queryCondiValues.searchStr" placeholder="客户名/手机/编号" @input="onClientNameInput" @click="onSearchStrClick"/>
            <van-icon name="user-o" />
          </div>
          <div class="public_query_titleSrc_item">
            <YjSelectTree
            ref="selectTreeRef"
            :target="target"
            :title="title"
            :confirmColor="confirmColor"
            :rangeKey="rangeKey"
            :rootNode="rootNode"
		        :idKey="idKey"
            :sonNode="sonNode"
            :multipleCheck="multipleCheck"
            :parentSelectable="parentSelectable"
            :popupHeight="'90%'"
            @getRootNode="getRootNode"
            @handleConfirm="onRegionSelected"
          >
            <template #select-tree-content>
                <input type="text" v-model="queryCondiLabels.regionName" placeholder="选择片区" readonly />
                <van-icon name="wap-home-o" />
              </template>
            </YjSelectTree>
          </div>
        </div>
      </van-form>
<!--      <van-cell-group class="cellgroup" style="margin-top:2px;">-->
<!--        <van-field v-model="queryCondiLabels.dateTimeInfo" style="color:#ccc;" readonly label="" placeholder="日期范围" @click="showDate = true" />-->
<!--      </van-cell-group>-->
      <yj-select-calendar-cache
        :start-time-fld-name.sync="queryCondiValues.startDate"
        :end-time-fld-name.sync="queryCondiValues.endDate"
        :cache-key="'ViewDeliveryReceiptCacheKey'"
        :options-btn="[{key: '7-day', name: '近7天'},{key: '3-day', name: '近3天'},{key: '1-month', name: '近1月'},{key: '1-day', name: '今天'}]"
        @handleConfirm="handleYjSelectCalendarCacheConfirm"
      />
    </div>
    <van-pull-refresh :disabled="disabledPullRefresh" style="height: 90%" v-model="pullRefreshLoading" @refresh="onRefresh">
      <div class="sales_list_boxs" ref="sales_list_boxs" v-if="deliveryList.length > 0">
        <van-list
          v-model="loading"
          :finished="finished"
          :immediate-check="false"
          finished-text="到底了~"
          @load="onNextPage"
        >
          <ul class="sales_ul" ref="sales_ul">
            <!--隐藏顶部 下拉距离参照-->
            <div ref="deliveryScroll"></div>
            <li v-for="(item, index) in deliveryList" :key="index" :ref="'sales_li' + item.order_sheet_no">
              <van-swipe-cell style="width:100%">
                <div class="sheet_wrapper" :style="{background:item.state=='submited'?'#eee':'#fff'}">
                  <div class="sup_info">
                    <div class="sup_name">
                      {{ item.sup_name }}
                      <div v-if="item.order_source" class="order-source-wrapper">
                        {{item.order_source === 'xcx' ? '商城' : ''}}
                      </div>
                    </div>
                    <div class="sup_contact">
                      <div class="sup_tel">
                        <a v-if="!isIOS&&!isHarmony" :href="'tel:' + item.sup_tel">{{item.sup_tel}}</a>
                        <div v-else @click="call(item.sup_tel)">{{item.sup_tel}}</div>
                      </div>
                      <div class="sup_add" v-if="item.distance">
                        <div v-if="item.distance == -1">{{ item.distanceStr }}</div>
                        <div v-else @click="openNavigationAppDialog(item)">{{ item.distanceStr }}<i class="iconfont">&#xe640;</i></div>
                      </div>
                    </div>
                  </div>
                  <div class="sheet_info">
                    <div class="sheet_info_left">
                      <!-- 未签收 订单编号 点击进去和下面是一样的 都是跳转销售单-->
                      <div v-if="!queryCondiValues.isReceipted" class="sheet_no_tag" style="margin-bottom:10px;margin-top:10px;font-size:10px;">
                        <div class="sheet_no" @click.stop="onClientRowClick(item)">{{ item.order_sheet_no }}</div>
                      </div>
                      <!-- 未签收 转销售单 蓝色让人觉得可点击 -->
                      <div v-if="!queryCondiValues.isReceipted" class="sheet_no_tag" style="color: #1989fa;margin-bottom:10px;font-size:10px;">
                        <div class="sheet_no" @click.stop="onClientRowClick(item)" >{{ item.sale_sheet_no ? item.sale_sheet_no : item.sheet_type.indexOf('T')>-1 ? '转退货单' : '转销售单' }}</div>
                      </div>
                      <!-- 已签收 点击跳转销订 蓝色让人觉得可点击-->
                      <div v-if="queryCondiValues.isReceipted" class="sheet_no_tag" style="color: #1989fa;margin-bottom:10px;margin-top:10px;font-size:10px;">
                        <div class="sheet_no" @click.stop="onClientRowClick(item,true)">{{ item.order_sheet_no }}</div>
                      </div>
                      <!-- 已签收 销+单号 点击跳转销售单 -->
                      <div v-if="queryCondiValues.isReceipted" class="sheet_no_tag" style="color: #1989fa;margin-bottom:10px;font-size:10px;">
                        <div class="sheet_no" @click.stop="onClientRowClick(item)">{{ (item.sheet_type.indexOf('T')>-1?'退':'销')+' '+item.sale_sheet_no }}</div>
                      </div>
                      <div class="sheet_happentime">
                        {{ item.happen_time }}
                      </div>
                    </div>
                    <div class="sheet_tag">
                      <van-tag v-if="item.receipt_status == 'bf'" plain type="warning">部分签收</van-tag>
                      <van-tag v-if="item.receipt_status == 'js'" plain type="danger">整单拒收</van-tag>
                    </div>
                    <div class="sheet_info_right">￥{{item.total_amount}}
                      <div class="sheet_left" v-if="item.left_amount!=='0'">欠款:{{item.left_amount}}</div>

                    </div>

                  </div>
                  <div class="sheet_add">
                    {{item.sup_addr}}
                  </div>
                  <div class="seller_senders_info">
                    <div v-if="item.seller_name">业务员: {{item.seller_name}}</div>
                    <div v-if="item.senders_name">送货员: {{item.senders_name}}</div>
                  </div>
                  <div class="mark-brief" v-if="item.make_brief">
                    备注: {{item.make_brief}}
                  </div>
                </div>
                <template #right v-if="item.receipt_status == ''">
                  <div style="display: flex;justify-content: flex-start;height: 100%;">
                    <div v-if="canRetreatVan" @click="clickRetreatSheetBtn(item)" class="retreat-btn">回撤</div>
                    <div @click="clickRejectSheetBtn(item)" class="reject-btn">整单拒收</div>
                  </div>
                </template>
                <!-- <template #right v-if="item.receipt_status == ''">
                  <div @click="clickRetreatSheetBtn(item)" class="reject-btn">回撤</div>
                  <div @click="clickRejectSheetBtn(item)" class="reject-btn">整单拒收</div>

                </template> -->
              </van-swipe-cell>
            </li>
          </ul>
        </van-list>
      </div>
    </van-pull-refresh>
    <div class="sales_list_boxs_no" v-if="deliveryList.length === 0">
      <div class="whole_box_no_icon iconfont">&#xe664;</div>
      <p>没有相关订单</p>
    </div>
    <!-- <van-popup style="overflow: hidden !important" v-model="showAddress" duration="0.4" position="bottom" :style="{ height: '90%', width: '100%' }">
      <RegionSelection @onRegionSelected="onRegionSelected"></RegionSelection>
    </van-popup> -->
    <van-popup v-model="showChooseNavigationApp" position="bottom" :style="{ height: '30%' }">
      <div class="navi-select-item" @click="onNaviSelectItem(item)" v-for="(item, index) in navigationAppList" :key="index">
        {{ item.name }}
      </div>
    </van-popup>
    <van-calendar v-model="showDate" type="range" @confirm="onConfirm" title="请选择起止日期" :allow-same-day="true" :min-date="minDate" :max-date="maxDate" />
    <div class="wrapper" v-show="!showAddress">
      <div>共计:{{totalAmount?totalAmount:0}}元</div>
      <div class="content">
        共<span class="record">{{ total }}</span>条
      </div>
    </div>
  </div>
</template>
<script>
import { SwipeCell, Cell, CellGroup, Toast, PullRefresh, Tag, Form, Icon, List, Popup, Field, Calendar, Dialog } from "vant";
import {
  GetOrdersForSale, GetOrdersForSale_done,
  SaleOrderSheetReject,
  SaleOrderSheetRecover,
  SaleOrderSheetRetreat,
  TransformToGaodePositionRequest,
} from "../../api/api";
import Navi from '../service/Navi'
import RegionSelection from "../components/RegionSelection";
import Position from '../../components/Position';
import YjSelectTree from "../components/yjTree/YjSelectTree.vue";
import YjSelectCalendarCache from "../components/yjSelect/yjCalendar/YjSelectCalendarCache.vue";
export default {
  name: "ViewDeliveryReceipt",
  data() {
    return {
      pullRefreshLoading: false,
      disabledPullRefresh: false,
      isQuerying: false, // 添加查询状态标志，防止重复查询

      coords: {},
      list: [],
      loading: false,
      finished: false,
      loadingDebounceTimer: null, // 防抖定时器
      isLoadingLocked: false, // 锁定loading状态，防止van-list重新设置
      pageSize: 20,
      startRow: 0,
      getTotal: true,
      navigationAppList: [
        {
          id: 1,
          name: "高德地图",
        },
        {
          id: 2,
          name: "百度地图",
        },
      ],
      showChooseNavigationApp: false,
      deliveryList: [],
      showAddress: false,
      selectedSupcustNavigatorInfo: {},
      total: 0,
      totalAmount :0,
      /*
      dateTime : {
        startDate: '',
        endDate: '',
        dateTimeInfo: ''
      },*/
      showDate: false,
      minDate: new Date(2000, 1, 1),
      maxDate: new Date(),
      target:'region',
      rangeKey: 'name',
			idKey: 'id',
      sonNode:'subNodes',
      asPage:true,
			multipleCheck: true,
			parentSelectable: true,
			foldAll: true,
			confirmColor:'#e3a2a2',
			title: '片区选择',
      rootNode:{},
      debounceTimer: null,
    };
  },
  props: {
    queryCondiValues: Object,
    queryCondiLabels: Object,
  },
  activated() {
    // 从销售单页面返回时只更新总计
    let params = {
      ...this.queryCondiValues,
      pageSize: 1, // 设置为1以减少数据传输
      startRow: 0,
      getTotal: true, // 只获取总计信息
      isBoss: window.isBoss(),
      isAssignVanNecessary: this.isAssignVanNecessary,
      noVanOrderToSale: this.noVanOrderToSale,
      onlyTotal: true// 添加标识,后端可以据此只返回总计信息
    }
    
    var func = GetOrdersForSale
    if (params.isReceipted) {
      func = GetOrdersForSale_done
    }

    func(params).then((res) => {
      if (res.result === "OK") {
        this.totalAmount = res.totalAmount
        this.total = res.total
      }
    })
  },
  computed:{
    isHarmony(){
      return window.isHarmony
    },
    isIOS() {
      return window.isiOS
    }
  },
  async mounted() {
    window.addEventListener("scroll", this.handleScrollY, true);
    var operRegions = this.$store.state.operInfo.operRegions;
    if (operRegions) {
      operRegions = JSON.stringify(operRegions);
      //operRegions=operRegions.replace('[').replace(']')
      this.queryCondiValues.operRegions = operRegions;
    }
    this.deliveryList.length = 0;
    if (hasRight('orderFlow.retreatFromVan.see')) {
      this.canRetreatVan = true;
    }

    // 初始化定位
    this.initializePosition();
  },
  async beforeDestroy(){ 
     window.g_listDealer=null
  },
  onHide() {
    window.removeEventListener("scroll", this.handleScrollY, false);
  },
  watch: {
    loading(newVal, oldVal) {
      // 如果loading被锁定为false，阻止van-list设置为true
      if (this.isLoadingLocked && newVal === true) {
        this.$nextTick(() => {
          this.loading = false;
        });
      }
    }
  },
  components: {
    YjSelectCalendarCache,
    "van-form": Form,
    "van-icon": Icon,
    "van-list": List,
    "van-popup": Popup,
    "van-tag": Tag,
    "van-pull-refresh": PullRefresh,
    "van-swipe-cell": SwipeCell,
    "van-cell": Cell,
    "van-cell-group": CellGroup,
    "van-field": Field,
    "van-calendar": Calendar,
    [Dialog.Component.name]: Dialog.Component,
    // RegionSelection,
    YjSelectTree

  },
  methods: {
    async initializePosition() {
      // 防止重复初始化
      if (this.hasInitialized) {
        console.log("已经初始化过，跳过重复初始化");
        return;
      }

      console.log("开始初始化定位...");
      this.hasInitialized = true;

      // 页面初始化时的定位逻辑，类似客户列表页面
      var positionGot = false
      var timeoutOccured = false

      setTimeout(() => {
        if (!positionGot) {
          timeoutOccured = true
          console.log("定位超时，开始查询");
          this.newQuery();
        }
      }, 5000)

      let params = {
        message: "需要定位权限来获取客户距离",
        key: "positionToCustom",
        positionMode: "net-gps", // 使用net-gps定位方式
        updateCallback: (position) => {
          // 处理GPS更新结果
          console.log("GPS更新结果:", position);

          // 更新当前位置
          this.queryCondiValues.currentLng = position.longitude;
          this.queryCondiValues.currentLat = position.latitude;

          // 重新计算订单距离并更新UI
          var tmStart = new Date().getTime()
          var hInterval = setInterval(() => {
            if (this.deliveryList.length > 0) {
              this.updateOrderDistances()
              clearInterval(hInterval)
            }
            var tmNow = new Date().getTime()
            if (tmNow - tmStart > 2000) {
              clearInterval(hInterval)
            }
          }, 100)
        }
      }

      try {
        let res = await Position.getPosition(params);

        this.queryCondiValues.currentLng = res.longitude;
        this.queryCondiValues.currentLat = res.latitude;
        positionGot = true

        if (!timeoutOccured) {
          console.log("定位成功，开始查询");
          this.newQuery();
        }
      } catch (error) {
        console.error("初始定位错误:", error);
        positionGot = true
        if (!timeoutOccured) {
          console.log("定位失败，开始查询");
          this.newQuery();
        }
      }
    },
    removeRededRec_From_DeliveryList(rededSheet_id) {
      this.deliveryList = this.deliveryList.filter(item => item.sale_sheet_id !== rededSheet_id);
    },
    handleScrollY() {
      //思路：监听参照元素和下拉位置的距离，当距离接近参照元素时可以进行下拉，当远离参照元素时，禁止下拉。
      //因为是下拉，所以与距离为负数 当与参照距离过远则禁用下拉组件 反之上拉到一定位置时，则开启下拉组件 (zxk)
      //console.log(this.$refs.deliveryScroll == undefined);
      if (!this.$refs.deliveryScroll) {
        return;
      }
      if (this.$refs.deliveryScroll.getBoundingClientRect().top <= -50) {
        this.disabledPullRefresh = true;
      } else {
        this.disabledPullRefresh = false;
      }
    },
    async onRefresh() {
      console.log("执行下拉刷新")

      // 防止重复刷新
      if (this.isQuerying) {
        console.log("查询正在进行中，跳过下拉刷新");
        this.pullRefreshLoading = false;
        return;
      }

      try {
        let params = {
          message: "需要定位权限来获取客户距离",
          key: "positionToCustom",
          positionMode: "net-gps", // 使用net-gps定位方式
          updateCallback: (position) => {
            // 处理GPS更新结果
            console.log("GPS更新结果:", position);

            // 更新当前位置
            this.queryCondiValues.currentLng = position.longitude;
            this.queryCondiValues.currentLat = position.latitude;

            // 刷新订单列表以更新距离
            this.updateOrderDistances();
          }
        }

        // 获取初始位置（网络定位结果）
        let res = await Position.getPosition(params);

        console.log("初始位置结果:", res);

        if (res.result == "OK") {
          this.queryCondiValues.currentLng = res.longitude;
          this.queryCondiValues.currentLat = res.latitude;

          // 使用初始位置进行查询
          this.newQuery();
        } else {
          this.pullRefreshLoading = false;
        }
      } catch (error) {
        console.error("定位错误:", error);
        // 处理错误情况
        this.$toast("获取位置信息失败");
        this.pullRefreshLoading = false;
      }
    },

    newQuery() {
      // 防止重复查询
      if (this.isQuerying) {
        console.log("查询正在进行中，跳过重复请求");
        return;
      }

      this.isQuerying = true;
      this.loading = false; // 重置加载状态
      this.isLoadingLocked = false; // 重置锁定状态
      this.startRow = 0;
      this.finished = false;
      this.deliveryList = [];
      this.startRow = 0;
      this.$nextTick(() => {
        this.onNextPage();
      })
    },
    openNavigationAppDialog(item) {
      //打开弹出框并且把选中商家的导航信息存入全局变量
      this.selectedSupcustNavigatorInfo = {
        sup_addr: item.sup_addr,
        addr_lng: item.addr_lng,
        addr_lat: item.addr_lat,
      };

      this.showChooseNavigationApp = true;
    },
    onNaviSelectItem(item) {
      if (isiOS) {
        this.jumpiOSNaviUrlBySelectAppName(item.name);
      } else if(isHarmony){
        this.jumpHarmonyNaviUrlBySelectAppName(item.name);
      } else{
        this.jumpAndroidNaviUrlBySelectAppName(item.name);
      }
      //隐藏弹出框
      this.showChooseNavigationApp = false;
    },
    async getCurPosition(){
        let params = {
          message: "需要定位权限来获取客户距离",
          key: "positionToCustom",
          positionMode: "net-gps", // 使用net-gps定位方式
          updateCallback: (position) => {
            // 处理GPS更新结果
            console.log("GPS更新结果:", position);

            // 更新当前位置
            this.queryCondiValues.currentLng = position.longitude;
            this.queryCondiValues.currentLat = position.latitude;

            // 刷新订单列表以更新距离
            this.updateOrderDistances();
          }
        }
        const position = await Position.getPosition(params);
        console.log(position)
        return position
    },
    updateOrderDistances() {
      // 遍历订单列表，重新计算距离
      this.deliveryList.forEach(item => {
        if (this.checkOrderPositionIsValid(item)) {
          // 计算新的距离
          const distance = this.getDistance(
            this.queryCondiValues.currentLat,
            this.queryCondiValues.currentLng,
            item.addr_lat,
            item.addr_lng
          );

          // 更新距离
          item.distance = distance;
          item.distanceStr = this.processDistanceAndFormatUnit(parseFloat(distance));
        }
      });

      // 强制更新视图
      this.$forceUpdate();
    },
    checkOrderPositionIsValid(item) {
      return item.addr_lat !== '' && item.addr_lng !== '' &&
             item.addr_lat !== '0' && item.addr_lng !== '0' &&
             item.addr_lat && item.addr_lng;
    },
    getDistance(lat1, lng1, lat2, lng2) {
      // 如果任何一个坐标无效，返回-1
      if (!lat1 || !lng1 || !lat2 || !lng2 ||
          lat2 === '' || lng2 === '' ||
          lat2 === '0' || lng2 === '0') {
        return -1;
      }

      // 将字符串转换为数字
      lat1 = parseFloat(lat1);
      lng1 = parseFloat(lng1);
      lat2 = parseFloat(lat2);
      lng2 = parseFloat(lng2);

      // 地球半径（米）
      const EARTH_RADIUS = 6378137.0; // 单位M

      // 将经纬度转换为弧度
      const radLat1 = (lat1 * Math.PI) / 180.0;
      const radLat2 = (lat2 * Math.PI) / 180.0;
      const radLng1 = (lng1 * Math.PI) / 180.0;
      const radLng2 = (lng2 * Math.PI) / 180.0;

      // 计算差值
      const a = radLat1 - radLat2;
      const b = radLng1 - radLng2;

      // 使用球面距离公式
      let s = 2 * Math.asin(
        Math.sqrt(
          Math.pow(Math.sin(a / 2), 2) +
          Math.cos(radLat1) * Math.cos(radLat2) *
          Math.pow(Math.sin(b / 2), 2)
        )
      );

      // 计算距离
      s = s * EARTH_RADIUS;

      // 四舍五入到整数
      return Math.round(s);
    },
    async jumpHarmonyNaviUrlBySelectAppName(name) {
      //从全局变量中取选中商家的导航信息
      const navigationInfo = this.selectedSupcustNavigatorInfo;
      const { addr_lng, addr_lat, sup_addr } = navigationInfo
      const { longitude, latitude }=await this.getCurPosition()
      if (name == "百度地图") {
        const navi = new Navi("baidu", isiOS, addr_lng + "," + addr_lat, sup_addr, isHarmony)
        var ref = cordova.InAppBrowser.open(navi.getBaiduUrl(), "_system");
        ref.show();
      }
      if (name == "高德地图") {
        const navi = new Navi("gaode", isiOS,addr_lng + "," + addr_lat, sup_addr, isHarmony, longitude + "," + latitude)
        var ref = cordova.InAppBrowser.open(await navi.getGaoDeUrl(), "_system");
        ref.show();
      }
    },
    async jumpiOSNaviUrlBySelectAppName(name) {
      //从全局变量中取选中商家的导航信息
      const navigationInfo = this.selectedSupcustNavigatorInfo;
      const { addr_lng, addr_lat, sup_addr } = navigationInfo
      if (name == "百度地图") {
        const navi = new Navi("baidu", isiOS, addr_lng + "," + addr_lat, sup_addr)
        cordova.InAppBrowser.open(navi.getBaiduUrl(), "_system");
      }
      if (name == "高德地图") {
        const navi = new Navi("gaode", isiOS, addr_lng + "," + addr_lat, sup_addr)
        cordova.InAppBrowser.open(await navi.getGaoDeUrl(), "_system");

      }
    },

    async jumpAndroidNaviUrlBySelectAppName(name) {
      //从全局变量中取选中商家的导航信息
      const navigationInfo = this.selectedSupcustNavigatorInfo;
      const { addr_lng, addr_lat, sup_addr } = navigationInfo
      if (name == "百度地图") {
        const navi = new Navi("baidu", isiOS, addr_lng + "," + addr_lat, sup_addr)
        window.location.href = navi.getBaiduUrl()
      }
      if (name == "高德地图") {
        // const position = await this.baiduPoi2gaodePoi(addr_lng,addr_lat);
        // const lng = position.split(",")[0];
        // const lat = position.split(",")[1];
        const navi = new Navi("gaode", isiOS, addr_lng + "," + addr_lat, sup_addr)
        window.location.href = await navi.getGaoDeUrl()
      }
    },
    async onNextPage() {
      if (this.finished) {
        return;
      }

      // 如果loading被锁定，说明上次加载刚完成，强制解锁并重置loading
      if (this.isLoadingLocked) {
        this.isLoadingLocked = false;
        this.loading = false;
        // 等待一个tick让状态稳定
        await this.$nextTick();
      }

      // 防止重复加载
      if (this.loading) {
        return;
      }

      // 设置加载状态
      this.loading = true;

      const setting = this.$store.state.operInfo.setting
      var flowExistMove = ""
      if (setting) flowExistMove = setting.flowExistMove.toString().toLowerCase()
      var isAssignVanNecessary = flowExistMove == 'true'
      var noVanOrderToSale = window.getRightValue('delicacy.noVanOrderToSale.value').toLowerCase() == 'true'
      var ignorePayFailedSheetOnOrderToSale = window.getSettingValue('ignorePayFailedSheetOnOrderToSale').toLowerCase() == 'true'
      let params = {
        ...this.queryCondiValues,
        pageSize: this.pageSize,
        startRow: this.startRow,
        getTotal: this.getTotal,
        isBoss: window.isBoss(),
        isAssignVanNecessary: isAssignVanNecessary,
        noVanOrderToSale: noVanOrderToSale,
        ignorePayFailedSheetOnOrderToSale: ignorePayFailedSheetOnOrderToSale  
      }
      var func = GetOrdersForSale
      if (params.isReceipted) {
        func = GetOrdersForSale_done
      }

      Toast.loading({
        message: "加载中...",
        forbidClick: true,
        style: { backgroundColor: "#000" },
      })
      func(params).then((res) => {
        Toast.clear()

        if (res.result === "OK") {
          if (this.finished) {
            this.isQuerying = false; // 重置查询状态
            return;
          }

          res.data.forEach((item) => {

            if (item.last_visit_time) {
              var today = new Date(this.getDatePart(new Date()))
              if (new Date(item.last_visit_time) > today) {
                item.isVisited = true
              }
            }
            if (item.distance) {
              item.distanceStr = this.processDistanceAndFormatUnit(parseFloat(item.distance))
            }
            item.state = ''
            this.deliveryList.push(item)
          })
          this.totalAmount = res.totalAmount
          this.total = res.total
          this.startRow = Number(this.startRow) + this.pageSize

          // 使用防抖机制重置loading状态
          this.resetLoadingWithDebounce();

          if (this.deliveryList.length >= Number(res.total)) {
            this.finished = true
          }

          // 如果总数据量小于等于页面大小，说明只有一页数据，直接完成
          if(this.total <= this.pageSize){
            this.finished = true
          }
        }
        else
        {
          Toast.fail(res.msg)
          this.resetLoadingWithDebounce();
          this.finished = true;
        }

        // 查询完成，重置查询状态和下拉刷新状态
        this.isQuerying = false;
        this.pullRefreshLoading = false;
      }).catch((error) => {
        Toast.clear();
        Toast.fail("加载失败");
        this.loading = false;
        this.finished = true;
        this.isQuerying = false; // 出错时也要重置查询状态
        this.pullRefreshLoading = false; // 出错时也要重置下拉刷新状态
        console.error("查询出错:", error);
      })
    },
    isDistanceMoreOneKM(distance) {
      return distance > 1000;
    },
    processDistanceAndFormatUnit(distance) {
      let distanceStr = "";
      if (this.isDistanceMoreOneKM(distance)) {
        distance = distance / 1000;
        distanceStr = distance.toFixed(2) + " km ";
      } else {
        distanceStr = parseInt(distance) + " m ";
      }
      return distanceStr;
    },
    clickRetreatSheetBtn(item) {
      let params = {
        operKey: this.$store.state.operKey,
        sheetIDs: item.order_sheet_id,
        senders: []
      }
      console.log(item)
      Dialog.confirm({
        title: "回撤",
        message: "确认要回撤到装车前吗?",
        width:"320px"
      }).then(() => {
        SaleOrderSheetRetreat(params).then((res) => {
          if (res.result === "OK") {
            Toast.success('回撤成功')
            this.newQuery()
          } else {
            Toast.fail(res.msg)
          }
        })
      })
      return
    },
    clickRejectSheetBtn(item) {
      //console.log(item);
      let params = {
        operKey: this.$store.state.operKey,
        sheet_id: item.order_sheet_id,
      }
      SaleOrderSheetReject(params).then((res) => {
        if (res.result === "OK") {
          Toast.success('拒收成功');
          this.newQuery();
        } else {
          Toast.fail(res.msg);
        }
      });
    },
    onClientRowClick(item,isOrder = false) {
      
      //点击跳转到销售单页面并加载销售单
      window.g_curSheetInList = item;
      console.log(this.queryCondiValues.isReceipted)
      //if (this.queryCondiValues.isReceipted) {
      if (item.sale_sheet_id) {
        // 如果是T或者TD
        if (item.sheet_type.indexOf("T")>-1) {//未签收：item.sheet_type=TD，已签收：item.sheet_type=T，都跳转X/T(不跳转订单)
          if(isOrder){
            this.$router.push({
              path: "/SaleSheet",
              query: {
                sheetID: item.order_sheet_id,
                sheetType: "TD",
              },
            });
          }
          else
          {
            this.$router.push({
              path: "/SaleSheet",
              query: {
                sheetID: item.sale_sheet_id,
                sheetType: "T",
              },
            });
          }

        }
        else 
        {
          if (item.sale_sheet_id.length <= 0) {
            // Toast.success('拒收的订单没有对应的销售单');
            Dialog.confirm({
              title: "恢复拒收订单",
              message: "订单已被拒收。\n要将它恢复成未签收状态吗?",
              width:"320px"
            }).then(() => {
              let params = {
                operKey: this.$store.state.operKey,
                sheet_id: item.order_sheet_id,
              }
              SaleOrderSheetRecover(params).then((res) => {
                if (res.result === "OK") {
                  Toast.success('恢复成功');
                  this.newQuery();
                } else {
                  Toast.fail(res.msg);
                }
              });
            });
            return;
          }
          if(isOrder){
            this.$router.push({
              path: "/SaleSheet",
              query: {
                sheetID: item.order_sheet_id,
                sheetType: "XD",
                order_source: item.order_source
              },
            });
          }else{
            this.$router.push({
              path: "/SaleSheet",
              query: {
                sheetID: item.sale_sheet_id,
                sheetType: "X",
                order_source: item.order_source
              },
            });
          }
        }
      } 
      else 
      {
          window.g_listDealer ={
            list:this.deliveryList,
            fieldName:"order_sheet_id",
            fieldValue:item.order_sheet_id,
            action:"orderToSale",
          }
    
       if (item.sheet_type=="TD") {
            this.$router.push({
              path: "/SaleSheet",
              query: {
                sheetID: item.order_sheet_id,
                sheetType: "T",
                fromOrderSheet: true,
                fromDelivery: true
              },
            })
        } 
        else 
        {
          if (item.sale_sheet_id !== "") {
            this.$router.push({
              path: "/SaleSheet",
              query: {
                sheetID: item.sale_sheet_id,
                sheetType: "X",
                order_source: item.order_source
              },
            });
          } else {
            this.$router.push({
              path: "/SaleSheet",
              query: {
                sheetID: item.order_sheet_id,
                sheetType: "X",
                fromOrderSheet: true,
                fromDelivery: true,
                order_source: item.order_source
              },
            });
          }

        }
      }
    },
    getRootNode(node) {
      this.rootNode=node
    },
    call(tel) {
      var ref = window.open("tel://" + tel,"_system")
      if(isHarmony){
        ref.show()
      }
    },
    onRegionSelected(region) {
      // console.log(region)
      if(region.length>0){
        let regionID=[];
        let regionName=[];
        region.forEach((item)=>{
          regionID.push(item.id)
          regionName.push(item.name)
        })
        this.queryCondiLabels.regionName = regionName.join(',');
        this.queryCondiValues.regionID = regionID.join(',');
      }else {
        this.queryCondiLabels.regionName = "";
        this.queryCondiValues.regionID = "";
      }
      // if (region.length>0) {
      //   this.queryCondiLabels.regionName = region[0].name;
      //   this.queryCondiValues.regionID = region[0].id;
      // } else {
      //   this.queryCondiValues.regionID = "";
      //   this.queryCondiLabels.regionName = "";
      // }
      this.$refs.selectTreeRef.handleCancel()
       console.log("name input new query")
      this.newQuery();
    },
    onClientNameInput() {
      console.log("name input new query")
      // 清除之前的定时器
      if (this.debounceTimer) {
        clearTimeout(this.debounceTimer);
      }
      // 设置新的定时器
      this.debounceTimer = setTimeout(() => {
        this.newQuery();
      }, 300); // 300 毫秒的延迟
    },
    onSearchStrClick() {
      this.queryCondiValues.searchStr = ''
    },
    onConfirm(date) {
      const [start, end] = date;
      this.showDate = false;
      // this.dateTime.startDate = `${this.formatDate(start)}`;
      //this.dateTime.endDate = `${this.formatDate(end)}`;
      this.queryCondiValues.startDate = `${this.formatDate(start)}`
      this.queryCondiValues.endDate = `${this.formatDate(end)}`
      this.queryCondiLabels.dateTimeInfo = this.queryCondiValues.startDate + " 至 " + this.queryCondiValues.endDate;

      // this.dateTime.dateTimeInfo =this.dateTime.startDate + " 至 " + this.dateTime.endDate;
      this.$emit("handleDateSon", this.queryCondiValues)
    },
    handleYjSelectCalendarCacheConfirm(startTime, endTime) {
      this.$emit("handleDateSon", this.queryCondiValues)
    },

    // 防抖重置loading状态
    resetLoadingWithDebounce() {
      // 清除之前的定时器
      if (this.loadingDebounceTimer) {
        clearTimeout(this.loadingDebounceTimer);
      }

      // 设置新的定时器
      this.loadingDebounceTimer = setTimeout(() => {
        this.loading = false;
        this.isLoadingLocked = true; // 锁定loading状态

        // 持续锁定，只有在用户真正滚动到底部时才解锁
        // 不使用定时器自动解锁
      }, 100);
    }
  },
};
</script>
<style lang="less" scoped>
// height:136px
@flex_w: {
  display: flex;
  flex-wrap: wrap;
};
@flex_a_j: {
  display: flex;
  align-items: center;
  justify-content: center;
};
@flex_a_bw: {
  display: flex;
  align-items: center;
  justify-content: space-between;
};
@flex_a_end: {
  display: flex;
  align-items: center;
  justify-content: flex-end;
};
@flex_a_flex: {
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
};
.navi-select-item {
  font-size: 0.65rem;
  color: #1887f7;
  border-bottom: solid 0.025rem #ccc;
  padding-top: 0.5rem;
  padding-bottom: 0.5rem;
}
.public_query {
  background: #ffffff;
  height: 100%;
  .public_query_title {
    padding-top: 5px;
    border-bottom: 1px solid #f2f2f2;
    .public_query_titleSrc {
      padding: 0 10px;
      height: 35px;
      @flex_a_bw();
      margin-top: 5px;
      .public_query_titleSrc_item {
        width: 48%;
        height: 100%;
        border: 1px solid #cccccc;
        border-radius: 4px;
        overflow: hidden;
        position: relative;
        vertical-align: top;
        // div {
        //   height: 100%;
        //   width: calc(100% - 40px);
        //   padding: 0 30px 0 10px;
        //   border: none;
        //   font-size: 15px;
        //   line-height: 35px;
        //   color: #333333;
        //   text-align: left;
        // }
        input {
          height: 100%;
          width: calc(100% - 45px);
          padding: 0 45px 0 5px;
          border: none;
          font-size: 15px;
          line-height: 35px;
          color: #333333;
          vertical-align: top;
        }
        span {
          position: absolute;
          right: 0;
          top: 0;
          bottom: 0;
          width: 45px;
          font-size: 16px;
          color: #000;
          @flex_a_j();
          background: #4c99e7;
        }
        .van-icon {
          position: absolute;
          right: 0;
          top: 0;
          bottom: 0;
          width: 30px;
          font-size: 22px;
          @flex_a_j();
          color: #666666;
        }
      }
    }
    .public_list_title {
      height: 40px;
      @flex_a_bw();
      margin-top: 5px;
      padding: 0 5px;
      div {
        height: 40px;
        line-height: 40px;
        font-size: 15px;
        text-align: center;
        width: calc(25% - 10px);
        padding: 0 5px;
        font-weight: 500;
        color: #333333;
      }
      div:first-child {
        width: calc(20% - 10px);
        text-align: left;
      }
      div:last-child {
        width: calc(25% - 10px);
        text-align: right;
      }
    }
  }
}
.sales_list_boxs {
  height: calc(100% - 88px);
  overflow-x: hidden;
  overflow-y: auto;
  background: #f2f2f2;
  width: 100%;
}
.sales_list_boxs_no {
  height: calc(100% - 54px);
  @flex_a_flex();
  background: #f2f2f2;
  color: #999999;
  .whole_box_no_icon {
    font-size: 50px;
  }
  p {
    font-size: 15px;
  }
}
.sales_ul {
  width: 100%;
  height: auto;
  overflow: hidden;
  //padding: 0 5px;
  //background: #ffffff;
  li {
    width: 100%;
    height: auto;
    overflow: hidden;
    //padding: 0px 10px;
    border-bottom: 1px solid #f2f2f2;
    display: flex;
    flex-direction: column;
    align-items: center;
    // .sales_ul_t {
    //   overflow: hidden;
    //   @flex_a_bw();
    //   height: auto;
    //   div {
    //     font-size: 15px;
    //   }
    //   .sales_ul_tl {
    //     color: #333333;
    //     width: 30%;
    //     text-align: left;
    //   }
    //   .sales_ul_tc {
    //     color: #333333;
    //     text-align: left;
    //     width: 43%;
    //   }
    //   .sales_ul_tr {
    //     font-size: 15px;
    //     color: #1989fa;
    //     width: 27%;
    //     text-align: right;
    //   }
    // }
    // .sales_ul_b {
    //   overflow: hidden;
    //   @flex_a_bw();
    //   height: auto;
    //   margin-top: 5px;
    //   .sales_ul_bl {
    //     font-size: 13px;
    //     color: #666666;
    //     width: 30%;
    //     min-height: 15px;
    //     text-align: left;
    //   }
    //   .sales_ul_bc {
    //     font-size: 13px;
    //     color: #666666;
    //     width: 43%;
    //     text-align: left;
    //   }
    //   .sales_ul_br {
    //     font-size: 13px;
    //     width: 27%;
    //     color: #666666;
    //     min-height: 30px;
    //     @flex_a_end();
    //     i {
    //       font-size: 22px;
    //       color: #1989fa;
    //       margin-left: 10px;
    //     }
    //   }
    // }
    .retreat-btn {
      margin-left: 10px;
      display: flex;
      color: #fff;
      width: 50px;
      font-size: 19px;
      align-items: center;
      justify-content: center;
      background: rgb(25, 137, 250);
      writing-mode: vertical-rl;
    }
    .reject-btn {
      margin-left: 10px;
      display: flex;
      color: #fff;
      width: 50px;
      font-size: 19px;
      align-items: center;
      justify-content: center;
      background: #ee0a24;
      writing-mode: vertical-rl;
    }
  }
  // li:last-child {
  //   border-bottom: none;
  // }
}
.wrapper {
  position: fixed;
  left: 0px;
  bottom: 0px;
  width: 100%;
  height: 35px;
  
  font-size: 0.5em;
  color: #333;
  border-top: 1px solid #f2f2f2;
  box-shadow: 0 -2px 5px #f2f6fc;
  background-color: #fff;
  z-index: 1000;
  padding-left: 8px;
  padding-right: 8px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  .content {
    padding-right: 15px;
  }
  .record {
    padding: 0 10px;
  }
}

.sheet_wrapper {
  width: 100%;
  border-radius: 5px;
  display: flex;
  flex-direction: column;
  margin-bottom: 5px;
  padding: 6px;

  background-color: #fff;
  box-sizing: border-box;

  .sup_info {
    border-bottom: 1px solid #f2f2f2;
    margin-bottom: 2px;
    margin: 0 5px;
    padding-bottom: 4px;
    display: flex;
    .sup_name {
      flex: 3;
      display: flex;
      align-items: center;
      justify-content: flex-start;
      font-size: 18px;
      font-weight: bolder;
      text-align: left;
    }
    .order-source-wrapper {
      border: 1px solid #fde3e4;
      padding: 1px 5px;
      background-color: #fde3e4;
      border-radius: 10px;
      margin-left: 10px;
    }
    .sup_contact {
      flex: 2;
      font-size: 16px;
      display: flex;
      flex-direction: column;
      align-items: flex-end;

      .sup_tel {
        margin: 2px 0;
        a {
          color: rgb(12, 89, 190);
        }
      }
    }
  }
  .sheet_info {
    margin: 2px 5px;

    display: flex;
    .sheet_info_left {
      flex: 2;
      display: flex;
      padding: 4px 0;
      flex-direction: column;
      .sheet_no_tag {
        display: flex;
        justify-content: space-between;
        .sheet_no {
          font-size: 17px;
        }
      }
      .sheet_happentime {
        display: flex;
        color: #ccc;
      }
    }
    .sheet_tag {
      flex: 1;
      max-width: 35px;
      display: flex;
      align-items: center;
      justify-content: center;
      span {
        padding: 2px;
      }
    }
    .sheet_info_right {
      flex: 2;
      font-family: numfont;
      font-size: 26px;
      justify-content: center;
      align-items: center;
      color: #c40000;
      .sheet_left {
        font-size: 17px;
        color: rgb(150, 145, 145);
      }
    }
  }
  .sheet_add {
    border-top: 1px solid #ddd;
    padding-top: 2px;
    width: 100%;
    color: #555;
    display: flex;
    padding-left: 5px;
  }
  .seller_senders_info {
    margin: 4px 5px 0;
    display: flex;
    justify-content: space-between;
    margin-right: 20px;
    color: #000;
  }
  .mark-brief {
    margin: 6px 5px 0;
    display: flex;
    color: #999;
  }
}
</style>

