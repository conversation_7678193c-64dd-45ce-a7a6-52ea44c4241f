<template>
    <div class="container">
        <!-- <div id="baidumap"></div>  -->
        <!-- <div class="content-wrapper"> -->
            <div class="content-container">
                <div class="label">位置信息</div>
                <div class="address-text">{{ curPositionInfo.address }}</div>
            </div>
            <div class="content-container">
                <div class="label">考勤点位置</div>
                <div class="target-address">{{ groupInfo.check_addr }}</div>
            </div>
            <div class="content-container">
                <div class="label">距离</div>
                <div class="target-distance">距考勤点{{ Number(groupInfo.distance).toFixed(2) }}米</div>
            </div>
            <div class="content-container">
                <div class="label">现场照片<span v-if="photoNecessary !== 'False' && attendanceForm.photos.length == 0"
                        class="tips">(未拍照!请先拍照再签到!)</span></div>
                <div class="photos-container">
                    <img class="photo" v-for="item, index in attendanceForm.photos" :key="index" :src="item">
                    <div class="takephoto-icon iconfont" @click="takeAttendancePhotos">
                        &#xe62e;
                    </div>
                </div>
            </div>
            <div class="content-container">
                <div class="label">文字说明</div>
                <van-field v-model="attendanceForm.remarks" rows="2" autosize type="textarea" maxlength="50" placeholder="请输入"
                    show-word-limit />
            </div>
        <!-- </div> -->
        <div class="submit-container">
            <div class="submit-btn" :class="{disabled: isSigning}" @click="!isSigning ? signClick() : null">
                {{ isSigning ? '处理中..' : signBtnText }}
            </div>
        </div>
    </div>
</template>
<script>

import { Field } from 'vant'
import TakePhoto from '../service/TakePhoto.js'
import ImageUtil from '../service/Image.js'
import { GetHeartBeat } from '../../api/api.js'

export default {
    name: "AttendanceSign",
    props: {
        curAddress: "",
        curPositionInfo: {},
        groupInfo: {},
        signBtnText: "",
        isSigning: {
            type: Boolean,
            default: false
        }
    },
    components: {
        "van-field": Field
    },
    data() {
        return {
            photoNecessary: '',
            attendanceForm: {
                longitude: 0,
                latitude: 0,
                remarks: "",
                photos: []
            }
        }
    },
    async mounted() {

        // var map = new BMap.Map("baidumap");
        //  map.enableScrollWheelZoom(true);
        //  const point=new BMap.Point(this.curPositionInfo.longitude,this.curPositionInfo.latitude)
        // map.centerAndZoom(point, 15);
        // const marker=this.createVisitorMarker(point)
        // map.addOverlay(marker)
        this.attendanceForm.longitude = this.curPositionInfo.longitude
        this.attendanceForm.latitude = this.curPositionInfo.latitude
        this.attendanceForm.groupId = this.groupInfo.group_id
        this.photoNecessary = this.groupInfo.photo_necessary
        if (this.photoNecessary !== 'False')
            await this.takeAttendancePhotos()
    },
    methods: {
        splitAddress(addr) {
            let addrSegs = []
            if (!addr) {
                return ['', '']
            }
            if (addr.indexOf("(") != -1) {
                addrSegs = addr.split("(")
            }
            else {
                addrSegs.push(addr)
            }
            return addrSegs
        },

        async takeAttendancePhotos() {
            const currentTime= await this.getCurrentTimeFromServer()
            const originBase64 = await TakePhoto.takePhotos()
            const compressBase64 = await ImageUtil.compress(originBase64)
            const addrSegs = this.splitAddress(this.curPositionInfo.address)
            const textSettings = {
                waterAreaSetting: {
                    x: 0,
                    y: 176,
                    w: 0,
                    h: 176
                },
                color: "#ddd",
                font: "24px Arial",
                texts: [{
                    content: addrSegs[0],
                    marginLeft: 20,
                    marginBottom: 88
                },
                {
                    content: addrSegs[1],
                    marginLeft: 20,
                    marginBottom: 64
                },
                {
                    content: currentTime.format("yyyy-MM-dd hh:mm:ss"),
                    marginLeft: 20,
                    marginBottom: 40
                }
                ]
            }
            var waterBase64 = await ImageUtil.waterMarker(compressBase64, textSettings)
            this.attendanceForm.photos.push(waterBase64)
        },
        createVisitorMarker(point, label = "", icon = {}) {
            var marker = new BMap.Marker(point);
            marker.setLabel(label);
            return marker;
        },

        signClick() {
            this.$emit("signClick", this.attendanceForm)
        },
        async getCurrentTimeFromServer(){
            const res =await GetHeartBeat({})
            console.log(res)
            if(res && res.server_time){
                return new Date(res.server_time)
            }
            return new Date()
        },
    }
}
</script>
<style lang="less" scoped>
#baidumap {
    height: 120px !important;
}

.container {
    display: flex;
    flex-direction: column;
    padding-bottom: 90px; /* 为按钮留出空间 */
    min-height: 100vh;
    box-sizing: border-box;
}

.label {
    font-weight: 600;
    font-size: 16px;
    margin-bottom: 10px;
}
// .content-wrapper{
//     height: calc(100% - 50px);
//     overflow-y: scroll;
// }
.content-container {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    padding: 15px 20px;
    margin-bottom: 10px;
    padding-bottom: 10px;
}

.address-text {
    text-overflow: ellipsis;
    white-space: nowrap;
    width: 100%;
    overflow: hidden;

}

.target-distance {
    font-size: 16px;
    color: #333;
}

.target-address {
    font-size: 16px;
    color: #333;
}

.photos-container {
    display: flex;
    flex-direction: row;

}

.photo {
    height: 60px;
    width: 60px;
    margin-left: 10px;
    border-radius: 6px;
}

.iconfont {
    width: 40px;
    height: 40px;
    padding: 10px;
    font-size: 36px;
    color: #ccc;
    border: 1px solid #ccc;
}

.submit-container {
    position: fixed;
    bottom: 20px;
    left: 20px;
    right: 20px;
    z-index: 100;
}

.submit-btn {
    width: 100%;
    height: 50px;
    line-height: 50px;
    background: #ff4444;
    border-radius: 25px;
    text-align: center;
    font-size: 18px;
    color: white;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 4px 12px rgba(255, 68, 68, 0.3);
    border: none;
    outline: none;
}

.submit-btn:hover {
    background: #ff3333;
    transform: translateY(-2px);
    box-shadow: 0 6px 16px rgba(255, 68, 68, 0.4);
}

.submit-btn:active {
    transform: translateY(0);
    box-shadow: 0 2px 8px rgba(255, 68, 68, 0.3);
}

.submit-btn.disabled {
    background: #d0d0d0;
    color: #999;
    cursor: not-allowed;
    transform: none;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.submit-btn.disabled:hover {
    background: #d0d0d0;
    transform: none;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.tips {
    color: #c40000;
    animation: animated-border 1.5s infinite;
    border-radius: 4px;
}

@keyframes animated-border {
    0% {
        box-shadow: 0 0 0 0 #fad5ce;
    }

    100% {
        box-shadow: 0 0 0 20px rgba(218, 9, 9, 0);
    }
}

.van-popup .van-popup--bottom {
    z-index: 1998 !important;

}

.van-toast .van-toast--middle .van-toast--text {
    z-index: 99999 !important;
}</style>