import { Toast } from "vant"

/**
 * 全局扫码服务类
 * 参考 Position 定位功能的设计模式
 * 提供统一的扫码接口，支持参数化配置
 */
class BarcodeScanner {
    constructor() {
        // 插件实例
        this.scannerPlugin = null
    }

    /**
     * 静态方法：扫描条码/二维码
     * @param {Object} params 扫码参数配置
     * @param {Object} params.barcodeFormats 支持的条码格式配置
     * @param {boolean} params.barcodeFormats.QRCode 是否支持二维码，默认true
     * @param {boolean} params.barcodeFormats.Code128 是否支持Code128，默认true
     * @param {boolean} params.barcodeFormats.EAN13 是否支持EAN13，默认true
     * @param {boolean} params.beepOnSuccess 扫码成功是否蜂鸣，默认true
     * @param {boolean} params.vibrateOnSuccess 扫码成功是否震动，默认false
     * @param {number} params.detectorSize 检测区域大小，默认0.6
     * @param {boolean} params.rotateCamera 是否旋转摄像头，默认false
     * @param {string} params.unit_type 单位类型，用于业务逻辑
     * @returns {Promise} 返回扫码结果
     */
    static async scan(params = {}) {
        const scanner = new BarcodeScanner()
        return await scanner.performScan(params)
    }

    /**
     * 执行扫码操作
     * @param {Object} params 扫码参数
     * @returns {Promise} 扫码结果
     */
    async performScan(params = {}) {
        // 默认支持的格式配置
        const defaultFormats = {
            Code128: true,   // 默认支持Code128
            Code39: true,    // 默认支持Code39
            Code93: true,    // 默认支持Code93
            CodaBar: false,
            DataMatrix: false,
            EAN13: true,     // 默认支持EAN13
            EAN8: true,      // 默认支持EAN8
            ITF: false,
            QRCode: false,   // 默认不支持二维码
            UPCA: false,
            UPCE: false,
            PDF417: false,
            Aztec: false
        }

        // 合并用户配置和默认配置
        const barcodeFormats = { ...defaultFormats, ...(params.barcodeFormats || {}) }
        const unit_type = params.unit_type || ''
        const beepOnSuccess = params.beepOnSuccess !== undefined ? params.beepOnSuccess : true
        const vibrateOnSuccess = params.vibrateOnSuccess !== undefined ? params.vibrateOnSuccess : false
        const detectorSize = params.detectorSize || 0.9
        const rotateCamera = params.rotateCamera || false

        // 调试日志
        console.log('📱 BarcodeScanner配置:')
        console.log('  - unit_type:', unit_type)
        console.log('  - barcodeFormats对象:', barcodeFormats)
        console.log('  - QRCode支持:', barcodeFormats.QRCode)
        console.log('  - 用户传入的params:', JSON.stringify(params, null, 2))

        return new Promise((resolve, reject) => {
            // iOS配置
            const iosconfig = {
                barcodeFormats: barcodeFormats,
                beepOnSuccess: beepOnSuccess,
                vibrateOnSuccess: vibrateOnSuccess,
                detectorSize: detectorSize,
                rotateCamera: rotateCamera
            }

            // Android旧插件配置
            const androidconfig = {
                barcodeFormats: barcodeFormats,
                beepOnSuccess: beepOnSuccess,
                vibrateOnSuccess: vibrateOnSuccess,
                detectorSize: detectorSize,
                rotateCamera: rotateCamera,
                // 其他Android特有配置...
                preferFrontCamera: false,
                showFlipCameraButton: true,
                showTorchButton: true,
                torchOn: false,
                saveHistory: true,
                prompt: "Place a barcode inside the scan area",
                resultDisplayDuration: 1000,
                orientation: "portrait",
                disableAnimations: true,
                disableSuccessBeep: false
            }

            // MLKit插件配置
            const barcodeScannerAndroidConfig = {
                preferFrontCamera: false,
                showFlipCameraButton: true,
                showTorchButton: true,
                torchOn: false,
                saveHistory: true,
                prompt: "Place a barcode inside the scan area",
                resultDisplayDuration: 1000,
                orientation: "portrait",
                disableAnimations: true,
                disableSuccessBeep: false,
                barcodeFormats: barcodeFormats
            }

            // 检查运行环境
            if (!window.cordova) {
                // Web端调试模式
                this.simulateScan(resolve, reject)
                return
            }

            // 调试插件可用性
            console.log('🔍 插件检测:')
            console.log('  - window.isiOS:', window.isiOS)
            console.log('  - cordova.plugins:', typeof cordova.plugins)
            console.log('  - cordova.plugins.mlkit:', typeof cordova.plugins.mlkit)
            console.log('  - cordova.plugins.mlkit.barcodeScanner:', typeof cordova.plugins.mlkit?.barcodeScanner)
            console.log('  - cordova.plugins.barcodeScanner:', typeof cordova.plugins.barcodeScanner)

            // 选择插件
            const plugin = window.isiOS || typeof cordova.plugins.mlkit?.barcodeScanner == 'undefined' ?
                cordova.plugins.barcodeScanner : cordova.plugins.mlkit.barcodeScanner

            // 调试日志 - 显示插件类型
            const pluginType = window.isiOS ? 'iOS原生' : (typeof cordova.plugins.mlkit?.barcodeScanner == 'undefined' ? '旧Android插件' : 'MLKit插件')
            console.log('🔧 使用插件:', pluginType)
            console.log('🔧 选中的插件对象:', plugin)

            // iOS扫码
            if (window.isiOS) {
                console.log('🔧 调用iOS原生扫码')
                console.log('🔧 iOS配置:', iosconfig)

                plugin.scan(
                    async (result) => {
                        const res = { unit_type, code: result.text, format: result.format }
                        resolve(res)
                    },
                    async (error) => {
                        reject(error)
                    },
                    iosconfig
                )
            } else {
                // Android扫码
                const useOldPlugin = typeof cordova.plugins.mlkit?.barcodeScanner == 'undefined'
                console.log('使用旧插件:', useOldPlugin)

                if (useOldPlugin) {
                    console.log('🔧 调用旧Android插件扫码')
                    console.log('🔧 旧插件配置:', androidconfig)

                    plugin.scan(
                        async (result) => {
                            const res = { unit_type, code: result.text, format: result.format }
                            resolve(res)
                        },
                        async (error) => {
                            reject(error)
                        },
                        androidconfig
                    )
                } else {
                    // 使用MLKit插件 - 需要特殊的格式转换
                    console.log('🔧 调用MLKit插件扫码')
                    console.log('🔧 MLKit插件对象:', plugin)

                    // MLKit插件需要的配置格式
                    const mlkitConfig = {
                        barcodeFormats: barcodeFormats,  // 传递格式对象，让MLKit插件自己转换
                        beepOnSuccess: beepOnSuccess,
                        vibrateOnSuccess: vibrateOnSuccess,
                        detectorSize: detectorSize,
                        rotateCamera: rotateCamera
                    }

                    console.log('🔧 MLKit配置详情:', JSON.stringify(mlkitConfig, null, 2))

                    // 确保使用正确的MLKit插件调用方式
                    plugin.scan(
                        mlkitConfig,  // 第一个参数是配置
                        async (result) => {  // 第二个参数是成功回调
                            console.log('🎉 MLKit扫码成功:', result)
                            const res = { unit_type, code: result.text, format: result.format }
                            resolve(res)
                        },
                        async (error) => {  // 第三个参数是失败回调
                            console.error('❌ MLKit扫码失败:', error)
                            reject(error)
                        }
                    )
                }
            }
        })
    }

    /**
     * Web端调试模拟扫码
     * @param {Function} resolve Promise resolve函数
     * @param {Function} reject Promise reject函数
     */
    simulateScan(resolve, reject) {
        // 模拟扫码延迟
        setTimeout(() => {
            // 检查是否有自定义的调试扫码结果
            const debugScanResult = localStorage.getItem('debug_scan_result')
            
            let result
            if (debugScanResult) {
                try {
                    result = JSON.parse(debugScanResult)
                    console.log('🔧 使用自定义调试扫码结果:', result)
                } catch (e) {
                    console.warn('自定义调试扫码结果格式错误，使用默认结果')
                    result = this.getDefaultDebugResult()
                }
            } else {
                result = this.getDefaultDebugResult()
            }

            console.log('🌐 Web调试模式：模拟扫码结果', result)
            resolve(result)
        }, 1000) // 模拟1秒的扫码时间
    }

    /**
     * 获取默认的调试扫码结果
     * @returns {Object} 调试扫码结果
     */
    getDefaultDebugResult() {
        // 预设的一些调试扫码结果
        const debugResults = [
            { code: '6901028075831', format: 'EAN_13', type: 'barcode' }, // 商品条码
            { code: 'https://www.example.com', format: 'QR_CODE', type: 'qrcode' }, // 二维码URL
            { code: 'DEBUG123456', format: 'CODE_128', type: 'barcode' }, // Code128
            { code: '测试二维码内容', format: 'QR_CODE', type: 'qrcode' } // 中文二维码
        ]

        // 随机选择一个结果
        const randomResult = debugResults[Math.floor(Math.random() * debugResults.length)]
        
        return {
            unit_type: '',
            code: randomResult.code,
            format: randomResult.format
        }
    }

    /**
     * 设置调试扫码结果（用于Web端测试）
     * @param {string} code 扫码内容
     * @param {string} format 扫码格式
     */
    static setDebugResult(code, format = 'QR_CODE') {
        const result = { code, format }
        localStorage.setItem('debug_scan_result', JSON.stringify(result))
        console.log('🔧 设置调试扫码结果:', result)
    }

    /**
     * 清除调试扫码结果
     */
    static clearDebugResult() {
        localStorage.removeItem('debug_scan_result')
        console.log('🔧 清除调试扫码结果')
    }
}

export default BarcodeScanner
