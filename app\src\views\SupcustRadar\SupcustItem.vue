<template>
    <div class="container">
        <div class="left">
        <div class="name">{{supcustInfo.name}}</div>
        <div class="tag">
            <van-tag class="tag-item"  v-for="item,index in tagArrayFrom(supcustInfo.tag)" :key="index">
                {{item}}
            </van-tag>
        </div>
        <div class="address">{{supcustInfo.address}}</div>
                <div>
            <div v-if="!supcustInfo.linkedSupInfo">
                <van-button class="oper-btn" @click="showSelectCustomer">🔗关联</van-button>
                <van-button class="oper-btn" @click="addSupcustClick">＋添加</van-button>
            </div>
            <div v-else>客户已存在客户档案中</div>
        </div>
        </div>
        <div class="right">
        <div  @click="openNavigationAppDialog(supcustInfo)" class="distance">{{supcustInfo.distance}}<i class="iconfont">&#xe640;</i></div>

        <div class="telephone">{{supcustInfo.telephone}}</div>
        </div>
        <van-popup :style="{ height: '60%' }" position='bottom'  v-model="showSupcusts">
            <select-customer @onClientSelected="linkSupcustClick"></select-customer>
        </van-popup>
        <van-popup v-model="showChooseNavigationApp" position="bottom" :style="{ height: '30%' }">
            <div class="navi-select-item" @click="onNaviSelectItem(item)" v-for="(item, index) in navigationAppList" :key="index">
                {{ item.name }}
          </div>
        </van-popup>
    </div>    
</template>
<script>
import {Tag,Popup,Button} from 'vant'
import Navi from '../service/Navi.js'
import SelectCustomer from "../components/SelectCustomer"
export default {
    data(){
        return{
            showSupcusts:false,
            supcustInfo_:{},
            selectedCustomer:{},
            showChooseNavigationApp:false,
            navigationAppList: [
                {
                id: 1,
                name: "高德地图",
                },
                {
                id: 2,
                name: "百度地图",
                }
            ]
        }
    },
    components:{
        "van-tag":Tag,
        "van-popup":Popup,
        "van-button":Button,
        "select-customer":SelectCustomer
    },
    props:{
        supcustInfo:{}
    },
    mounted(){
        this.supcustInfo_=this.supcustInfo
    },
    methods:{
    openNavigationAppDialog({lng,lat,address}) {
      //打开弹出框并且把选中商家的导航信息存入全局变量
      this.selectedSupcustNavigatorInfo = {addr_lng:lng,addr_lat:lat,sup_addr:address}
      this.showChooseNavigationApp = true;
    },
    onNaviSelectItem(item) {
      if (isiOS) {
        this.jumpiOSNaviUrlBySelectAppName(item.name);
      } else if(isHarmony){
        this.jumpHarmonyNaviUrlBySelectAppName(item.name);
      } else{
        this.jumpAndroidNaviUrlBySelectAppName(item.name);
      }
      //隐藏弹出框
      this.showChooseNavigationApp = false;
    },
    async getCurPosition(){
        const params = {
            message: "需要定位权限来获取当前位置",
            key: "positionSupcustRadar"
        };
        const position = await Position.getPosition(params);
        console.log(position);
        return position;
    },
    async jumpHarmonyNaviUrlBySelectAppName(name) {
      //从全局变量中取选中商家的导航信息
      const navigationInfo = this.selectedSupcustNavigatorInfo;
      const { addr_lng, addr_lat, sup_addr } = navigationInfo
      const { longitude, latitude }=await this.getCurPosition()
      if (name == "百度地图") {
        const navi = new Navi("baidu", isiOS, addr_lng + "," + addr_lat, sup_addr, isHarmony)
        var ref = cordova.InAppBrowser.open(navi.getBaiduUrl(), "_system");
        ref.show();
      }
      if (name == "高德地图") {
        const navi = new Navi("gaode", isiOS,addr_lng + "," + addr_lat, sup_addr, isHarmony, longitude + "," + latitude)
        var ref = cordova.InAppBrowser.open(await navi.getGaoDeUrl(), "_system");
        ref.show();
      }
    },
   async jumpiOSNaviUrlBySelectAppName(name) {
      //从全局变量中取选中商家的导航信息
      const navigationInfo = this.selectedSupcustNavigatorInfo;
      const{addr_lng,addr_lat,sup_addr}=navigationInfo
      if (name == "百度地图") {
        const navi=new Navi("baidu",isiOS,addr_lng+","+addr_lat,sup_addr)
        cordova.InAppBrowser.open(navi.getBaiduUrl(),"_system");
      }
      if (name == "高德地图") {
          const navi=new Navi("gaode",isiOS,addr_lng+","+addr_lat,sup_addr)
          cordova.InAppBrowser.open(await navi.getGaoDeUrl(),"_system");
        
      }
    },

   async jumpAndroidNaviUrlBySelectAppName(name) {
      //从全局变量中取选中商家的导航信息
      const navigationInfo = this.selectedSupcustNavigatorInfo;
      const{addr_lng,addr_lat,sup_addr}=navigationInfo
      if (name == "百度地图") {
        const navi=new Navi("baidu",isiOS,addr_lng+","+addr_lat,sup_addr)
        window.location.href = navi.getBaiduUrl()
      }
      if (name == "高德地图") {
          // const position = await this.baiduPoi2gaodePoi(addr_lng,addr_lat);
          // const lng = position.split(",")[0];
          // const lat = position.split(",")[1];
          const navi=new Navi("gaode",isiOS,addr_lng+","+addr_lat,sup_addr)
          window.location.href =await navi.getGaoDeUrl()
      }
    },
        showSelectCustomer(){
            this.showSupcusts=true
        },
        linkSupcustClick(customer){
            this.$emit("linkSupcustClick",{
                supcustInfo:this.supcustInfo,
                linkCustomerInfo:customer
            })
            this.showSupcusts=false
        },

        addSupcustClick(){
            this.$emit("addSupcustClick",{
                supcustInfo:this.supcustInfo
            })
        },
        tagArrayFrom(tag){
            if(!tag){
                return []
            }
            return tag.split(";")
        }
    }
}
</script>
<style scoped>
.name{
    color: #337ecc;
    font-weight: 600;
    font-size: 16px;
}
.address{
    color: #337ecc;
    font-size: 12px;
}
.name-row{
    display: flex;
    flex-direction: row;
}
.container{
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
    padding: 10px;
    border-radius: 4px;
    
}
.left{
    display:  flex;
    flex-direction: column;
    width: 60%;
    align-items: flex-start;
}
.left>div{
    margin-top: 10px;
}
.navi-select-item {
  font-size: 0.65rem;
  color: #337ecc;
  border-bottom: solid 0.025rem #f0f0f0;
  padding-top: 0.5rem;
  padding-bottom: 0.5rem;
}
.right{
       display:  flex;
    flex-direction: column;
    width: 40%;
    align-items: flex-end;

}
.tag-item{
    margin-right: 4px;
    background: #337ecc;
    color: #f0f0f0;
    border-radius: 4px;
    padding: 4px;
}
.distance{
    color:#337ecc;
}
.oper-btn{
    background: #337ecc;
    color: #f0f0f0;
    border-radius: 6px;
    border: 1px #337ecc solid;
    margin-left: 6px;
}
</style>