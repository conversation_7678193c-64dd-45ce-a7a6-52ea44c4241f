<template>
  <div class="wrapper">
    <div class="content">
      <div class="title-wrapper">
        <van-nav-bar left-arrow safe-area-inset-top title="陈列协议" @click-left="goBack">
          <template #right>
            <div class="submitSalesSlip" @click="handleShowSettlementPopup">
              <svg style="margin:2px 2px 0 0 " width="30px" height="30px" stroke-width="1.3" class="black">
                <use :xlink:href="'#icon-plane'"></use>
              </svg>
            </div>
          </template>
        </van-nav-bar>
      </div>
      <div class="sheet-wrapper" id="displaySheetWrapperId">
        <div class="approved-reded">
          <div class="sheet_state approved" v-if="sheet.approve_time &&(sheet.red_flag === '' || sheet.red_flag === '0')">
            <img src="../../assets/images/approved.png" />
          </div>
          <div class="sheet_state reded" v-if="sheet.red_flag === '1'">
            <img src="../../assets/images/reded.png" />
          </div>
        </div>
        <div class="sheet-info-id" v-show="sheet.sheet_no">
          <div>{{sheet.sheet_no}}</div>
          <div>{{sheet.happen_time}}</div>
        </div>
        <div class="sheet-info">
          <!--          客户-->
          <div class="sheet-info-item" @click="onSelectClient">
            <div class="info-item-icon">
              <van-icon name="user-o" />
            </div>
            <div class="info-item-content">
              <input type="text" v-model="sheet.sup_name" placeholder="客户" readonly />
            </div>
          </div>
          <!--          费用科目-->
          <div class="sheet-info-item" @click="handleSelectFreeOut">
            <div class="info-item-icon">
              <van-icon name="paid" />
            </div>
            <div class="info-item-content">
              <input type="text" v-model="sheet.fee_sub_name" placeholder="费用科目" readonly />
            </div>
          </div>
          <!--          时间-->
          <div class="sheet-info-items">
            <div class="sheet-info-item" @click="handleSelectTime('start')">
              <div class="info-item-icon">
                <van-icon name="underway-o" />
              </div>
              <div class="info-item-content">
                <input type="text" v-model="sheet.startTimeShow" placeholder="开始月份" readonly />
              </div>
            </div>
            <div class="sheet-info-item" @click="handleSelectTime('end')">
              <div class="info-item-icon" style="padding:0 15px; font-size: 20px; color:#ddd">
                ~
              </div>
              <div class="info-item-content">
                <input type="text" v-model="sheet.endTimeShow" placeholder="结束月份" readonly />
              </div>
            </div>
          </div>
          <!--          月数-->
          <div class="sheet-info-item">
            <div class="info-item-icon-font">
              月数
            </div>
            <div class="info-item-content" @click="handleSelectMonth">
              <input type="text" v-model="showTimeConfig.month" placeholder="月数" readonly />
            </div>
          </div>
          <!--          模板-->
          <div class="sheet-info-item" @click="handleSelectDisplayTemplateShow">
            <div class="info-item-icon">
              <van-icon name="description" />
            </div>
            <div class="info-item-content">
              <input type="text" v-model="sheet.disp_template_name" placeholder="请选择陈列模板" readonly />
            </div>
          </div>
        </div>
        <ConcaveDottedCenter />
        <div class="sheet-rows">
          <div class="sheet-rows-list" ref="sheetRowsListRef">
            <van-swipe-cell v-for="item in sheet.sheetRows" :key="item.items_id + '_' + item.unit_no">
              <DisplaySheetRowsItem :sheetRow="item" :sheet="sheet" :showTimeConfig="showTimeConfig" :displayTemplateInfoOverWriteFlag="displayTemplateInfoOverWriteFlag" />
              <template #right>
                <van-button @click="handleDeleteItem(item)" square text="删除" type="danger" class="delete-button swipe-right" />
              </template>
            </van-swipe-cell>
          </div>
        </div>
        <div class="sheet-total-info">
          <div class="total-info-items">
            <div class="total-into-item">
              <div class="into-item-name"></div>
              <div class="into-item-content">共 {{ totalRows }} 行</div>
            </div>
<!--            <div class="total-into-item">-->
<!--              <div class="into-item-name"></div>-->
<!--              <div class="into-item-content">{{ showTotalUnitInfo }}</div>-->
<!--            </div>-->
          </div>
          <div class="total-into-item" v-show="Number(moneyTotal) !== 0">
            <div class="into-item-name">钱合计：</div>
            <div class="into-item-content">共 {{ moneyTotal }} 元</div>
          </div>
          <div class="total-into-item" v-show="showTotalUnitInfo">
            <div class="into-item-name">商品合计：</div>
            <div class="into-item-content">共 {{ showTotalUnitInfo }}</div>
          </div>
<!--          <div class="total-into-item" v-show="Number(sheetRowsTotalSubAmount) !== 0">-->
<!--            <div class="into-item-name">商品合计：</div>-->
<!--            <div class="into-item-content">共 {{ sheetRowsTotalSubAmount }} 元</div>-->
<!--          </div>-->
          <div class="total-into-item" v-show="sheet.make_brief !== ''">
            <div class="into-item-name">备注：</div>
            <div class="into-item-content">{{ sheet.make_brief }}</div>
          </div>
        </div>
        <div class="sheet-work-content">
          <ActionsTemplate ref="actionsTemplateRef" :work-content="sheet.sign_work_content.sign_work_content" :can-edit-template="canEditTemplate" :canvas-content="{supName: sheet.sup_name, supAddr: sheet.sup_addr}" />
          <ActionNeedReview v-if="needReview && canReview && sheet.approve_time !== '' && sheet.red_flag === ''" :review-info="sheet" @handleConfirmResult="handleConfirmResult" />
          <ActionReviewResult v-if="displayTemplateInfo && displayTemplateInfo.sign_need_review && sheet.review_time" @handleUpdateWorkContent="handleUpdateWorkContent" :review-info="{reviewer: sheet.reviewer,reviewerName: sheet.reviewerName,review_time: sheet.review_time,review_comment: sheet.review_comment,review_refused: sheet.review_refused}" />
        </div>
        <div class="current-display-list-wrapper">
          <div class="current-display-item" v-for="displayItem in currentDisplayList" :key="displayItem.sheet_id">
            <div class="current-time">
              <div>{{displayItem.start_time.substr(0,7)}}</div>
              <div>{{displayItem.end_time.substr(0,7)}}</div>
            </div>
           <div class="current-other">
             <div>{{displayItem.sheet_no}}</div>
             <div>{{displayItem.disp_template_name}}</div>
           </div>
          </div>
        </div>
      </div>
      <div class="operation-wrapper">
        <div class="operation-input">
          <input id="codes" type="text" v-model="searchStr" placeholder="名称/简拼/条码/货号" :disabled="!!sheet.approve_time" @keydown="onSearchInputKeyDown($event)" :style="{ backgroundColor: sheet.approve_time ? '#f2f2f2' : '' }" />
        </div>
        <div class="operation-btn">
          <van-button @click="handleGoSelectItemPage">
            <svg width="35px" height="35px" fill="#F56C6C">
              <use xlink:href="#icon-add"></use>
            </svg>
          </van-button>
        </div>
        <div class="operation-btn">
          <van-button type="info" @click="handleScanBarcode">
            <svg width="30px" height="30px" fill="#555">
              <use xlink:href="#icon-barcodeScan"></use>
            </svg>
          </van-button>
        </div>
      </div>
    </div>
    <!--    选择客户-->
    <van-popup v-model="clientSelectDialog" position="bottom" :lock-scroll="false" :style="{ height: '100%',position:'absolute', width: '100%', overflow: 'hidden'}">
      <!-- <div
      v-if="',CG,CT,'.indexOf(',' + sheet.sheetType + ',') < 0"
      @click="addSupcust" style="float: right; padding: 12px">
        <svg width="26px" height="26px">
          <use xlink:href="#icon-add-supcust"></use>
        </svg>
      </div> -->
      <SelectCustomer canHide='true' @handleHide='clientSelectDialog = false' ref='selectCustomer' :showAcctCusts="false" :sheetType="sheet.sheetType" @onClientSelected="handleOnClientSelected" />

    </van-popup>
    <!--    选择费用支出-->
    <van-popup v-model="feeOutListShowDialog" position="bottom">
      <van-picker :columns="feeOutList" show-toolbar title="费用支出" value-key="l" @confirm="handleOnConfirmFreeOut" />
    </van-popup>
    <!--    选择月份-->
    <van-popup v-model="showTimeConfig.monthPopup" position="bottom">
      <van-picker :columns="showTimeConfig.monthArr" show-toolbar cancel-button-text=" " title="月份" default-index="0" @confirm="handleOnConfirmMonth" />
    </van-popup>
    <!--    选择起始结束时间-->
    <van-popup v-model="showTimeConfigFlag" position="bottom" :close-on-click-overlay="false">
      <van-datetime-picker v-model="showTimeConfig.time" type="year-month" title="选择年月" :formatter="formatter" @confirm="handleTimeConfirm" @cancel="handleTimeCancel" />
    </van-popup>
    <!--    结算-->
    <van-popup v-model="settlementPopup" position="right" :style="{ width: '90%', height: '100%'}">
      <div style="height:30px;border-top:1px solid #ccc"></div>

      <div class="other_operate">
        <van-divider style="margin-top: 10px;">附件</van-divider>
        <div class="other_operate_content">
          <div class="appendixphoto-container" v-for="item, index in sheet.appendixPhotos" :key="index" style="justify-content:center;">
            <img @click="preview_photo(index)" class="photo" :src="item">
            <div v-if="sheet.approve_time == '' && !isSubmitting" class="remove-icon" @click="remove_photo(index)">
              <van-icon name="close" />
            </div>
          </div>
          <div class="iconfont" v-if="sheet.approve_time == '' && !isSubmitting" @click="showImageSelect = true">&#xe62e;
          </div>
        </div>
      </div>

      <div class="other_operate">
        <van-field v-model="sheet.make_brief" label="备注" label-width="40px" placeholder="请输入备注" :disabled="!!sheet.approve_time" />
        <div style="height:30px"></div>
        <div class="other_operate_content">
          <button class="color-ffcccc" @click="handleSave" v-if="canMake" :disabled="sheet.approve_time !== '' || isSubmitting">保存</button>
          <button class="color-ffcccc" @click="handleApprove" v-if="canApprove" :disabled="sheet.approve_time !== '' || isSubmitting">审核</button>
        </div>
        <div class="other_operate_content">
          <button style="height: 45px;border-radius:12px" :style="{ color: sheet.red_flag !== '' ? '#ff9999' : '#ee0a24',borderColor: sheet.red_flag !== '' ? '#ff9999' : '#ee0a24'}" v-if="canRed && sheet.approve_time" :disabled="sheet.red_flag !== ''" @click="handleSheetRed">终止</button>
          <button v-if="sheet.sheet_id && !sheet.approve_time && canDelete" style="height: 45px;border-radius:12px" @click="handleSheetDelete">删除</button>
          <button @click="handleSheetPrint" v-if="canPrint" style="height: 45px;border-radius:12px" :disabled="!sheet.sheet_id || isPrinting">打印</button>
        </div>
        <!--        目前不考虑条码情况，后续加上-->
        <template v-if="canPrint && false">
          <van-divider>打印条码</van-divider>
          <van-radio-group v-model="printBarcodeStyle" style="font-size: 12px; margin-left: 0; display: flex; justify-content: center;padding-top: 10px;">
            <van-radio name="noBarcode" style="margin-right: 10px">不打印</van-radio>
            <van-radio name="actualUnit" style="margin-right: 10px">实际单位</van-radio>
            <van-radio name="smallUnit" style="margin-right: 10px">小单位</van-radio>
          </van-radio-group>
          <van-checkbox v-if="printBarcodeStyle === 'actualUnit' || printBarcodeStyle === 'smallUnit' " shape="square" v-model="printBarcodePic" icon-size="20px" style="font-size: 12px; margin-left: 50px; margin-top: 10px">打印条码图</van-checkbox>
        </template>
        <!-- <van-divider style="margin: 20px 0" @click="moreOptions = !moreOptions">
          <svg v-if="moreOptions" width="26px" height="26px" fill="#d8d8d8">
            <use xlink:href="#icon-double_arrow"></use>
          </svg>

          <svg style="-webkit-transform: rotate(180deg);" v-else width="26px" height="30px" fill="#d8d8d8">
            <use xlink:href="#icon-double_arrow"></use>
          </svg>
        </van-divider> -->
        <template>
          <div class="other_operate_content">
            <van-button type="default" :disabled="isSubmitting || sheet.approve_time !== ''" @click="handleSheetEmpty">清空</van-button>
            <van-button style="border-radius:12px;" type="default" @click="handleSheetCopy">复制</van-button>
            <van-button type="default" @click="handleOut">退出</van-button>
          </div>
        </template>
      </div>
    </van-popup>
    <!--    缓存-->
    <van-popup v-model="showUnsubmitedSheets" round>
      <div class="lowItem">
        <h4>点击打开未提交单据</h4>
        <ul class="lowItem_ul">
          <li v-for="(item, index) in unsubmitedSheets" :key="index" @click="onUnsubmitedSheetSelected(item)">
            <div class="lowItem_ull">
              {{ item.sup_name }}
            </div>
            <div class="lowItem_ulr">
              {{ item.saveTime }}
            </div>
            <div style="width: 70px; line-height: 40px" class="btn-delete" @click.stop="onUnsubmitedSheetDelete(index)">
              删除
            </div>
          </li>
        </ul>
        <van-button type="default" @click="showUnsubmitedSheets = false">新建单据</van-button>
      </div>
    </van-popup>
    <!--    终止-->
    <van-dialog v-model="displayAppendBriefDialog" title="终止原因" show-cancel-button @confirm="handleConfirmAppendBrief" @cancel="handleCancelAppendBrief" width="320px">
      <van-field v-model="displayAppendBriefContent" rows="1" autosize type="textarea" placeholder="请输入终止原因" />
    </van-dialog>
    <!--    选择模板-->
    <van-popup v-model="displayTemplateShowPopup" position="bottom" :style="{ width: '100%', height: '100%'}">
      <SelectDisplayTemplate v-if="displayTemplateShowPopup" @handleSelectTemplateItem="handleSelectTemplateItem" @handleCancelTemplate="handleCancelTemplate" @handleClose="handleCloseDisplayTemplateShowPopup" />
    </van-popup>
    <!--    选择附件-->
    <van-action-sheet v-model="showImageSelect" :actions="imageSelectActions" cancel-text="取消" description="请输入附件上传方式" close-on-click-action @select="onImageWaySelected" style="height: auto;" />
   
  </div>
</template>

<script>
import {
  NavBar,
  Button,
  Icon,
  Picker,
  DatetimePicker,
  SwipeCell,
  Toast,
  Field,
  RadioGroup,
  Radio,
  Divider,
  ActionSheet,
  ImagePreview,
  Checkbox, Dialog
} from "vant"
import DisplaySheetRowsItem from "./DisplaySheetRowsItem"
import SelectCustomer from "../components/SelectCustomer"
import ConcaveDottedCenter from "../components/ConcaveDottedCenter"
import {
  DisplayAgreementLoad,
  DisplayGetFeeOutSubForKS,
  DisplaySaveAndApprove,
  DisplaySave,
  GetGoodsItemList,
  DisplayDelete,
  DisplayAppendBrief,
  DisplayAgreementSheetTerminate,
  QueryOneInfoDisplayTemplate,
  UpdateDisplaySignActionReview, UpdateDisplaySignWorkContent, GetCurrentDisplayList,SaveSingleImage
} from "../../api/api";
import Printing from "../Printing/Printing";
import SelectDisplayTemplate from "../components/selectDisplayTemplate/SelectDisplayTemplate";
import ActionsTemplate from "../components/actionsTemplate/ActionsTemplate";
import ActionReviewResult from "../components/actionsTemplate/ActionReviewResult";
import ActionNeedReview from "../components/actionsTemplate/ActionNeedReview";
import globalVars from "../../static/global-vars";
import ImageUtil from '../service/Image'
import TakePhoto from '../service/TakePhoto'
export default {
  name: "displayAgreementSheet",
  components: {
    ActionNeedReview,
    ActionReviewResult,
    ActionsTemplate,
    SelectDisplayTemplate,
    "van-action-sheet": ActionSheet,
    [ImagePreview.Component.name]: ImagePreview.Component,
    "van-nav-bar": NavBar,
    "van-icon": Icon,
    "van-button": Button,
    "van-picker": Picker,
    "van-swipe-cell": SwipeCell,
    "van-datetime-picker": DatetimePicker,
    "van-field": Field,
    ConcaveDottedCenter,
    DisplaySheetRowsItem,
    SelectCustomer,
    "van-radio-group": RadioGroup,
    "van-radio": Radio,
    "van-divider": Divider,
    "van-checkbox": Checkbox,
    [Dialog.Component.name]: Dialog.Component,
  },
  beforeRouteLeave(to, from, next) {
    if (this.sheet.supcust_id && this.sheet.sheet_no === '') {
      this.saveCurSheetToCache(this.sheet)
    }
    next();
  },
  beforeRouteEnter(to, from, next) {
    if (from.path === "/displaySelectItem") {
      next(vm => {
        setTimeout(() => {
          vm.displaySheetShoppingCar = from.query.displaySheetShoppingCar
          vm.handleGetSelectItemToSheetRows()
        }, 300)
      })
    }
    next()
  },
  data() {
    return {
      searchStr: '',
      sheet: { // 来自load
        fixinG_ARREARS: '',
        company_tel: '',
        company_address: '',
        sum_quantity_unit_conv: '',
        isSum: '',
        sumType: '',
        sheetRows: [],
        mergedSheetRows: [],
        company_setting: null,
        happenNow: false,
        isImportingSheet: false,
        sheet_type: 23,
        supcust_id: null,
        sup_name: "",
        sup_addr: "",
        fee_sub_id: null,
        fee_sub_name: "",
        money_inout_flag: 0,
        total_amount: 0,
        seller_id: "",
        seller_name: "",
        start_time: "",
        startTimeShow: "",  // 展示用
        end_time: "",
        endTimeShow: "", // 展示用
        adjust_sheet_id: "",
        adjust_sheet_no: "",
        red_sheet_no: "",
        orig_sheet_id: "",
        total_money: 0,
        total_quantity: "",
        terminate_time: "",
        terminator_oper: "",
        sheet_id: "",
        sheet_no: "",
        company_id: "",
        company_name: "",
        sheet_name: "",
        operKey: "",
        operID: "",
        sheetType: "",
        red_flag: "",
        red_sheet_id: "",
        maker_id: "",
        maker_name: "",
        make_time: "",
        happen_time: "",
        tempHappenTime: true,
        approver_id: "",
        approver_name: "",
        approve_time: "",
        make_brief: "",
        approve_brief: "",
        submit_time: "",
        print_count: "",
        isFromWeb: false,
        disp_template_id: '',
        disp_template_name: '',  // 无须提交
        sign_work_content: {
        sendMessage: false,
        sign_work_content: [],
        appendixPhotos: []
        },
        reviewer: '',
        reviewerName: '', // 无需提交
        review_time: '',
        review_comment: '',
        review_refused: '',
        sheet_attribute: '',
      },
      clientSelectDialog: false,
      feeOutList: [],
      feeOutListShowDialog: false,
      showTimeConfig: {
        startTimeShowFlag: false,
        endTimeShowFlag: false,
        time: new Date(),
        startOrEnd: 'start',
        month: 1,
        monthPopup: false,
        monthArr: [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12]
      },
      displaySheetShoppingCar: [],
      cardItem: {
        items_id: "",  //money
        items_name: "", //钱
        unit_no: "",  //元
        month1_qty: "",
        month1_given: "",
        month2_qty: "",
        month2_given: "",
        month3_qty: "",
        month3_given: "",
        month4_qty: "",
        month4_given: "",
        month5_qty: "",
        month5_given: "",
        month6_qty: "",
        month6_given: "",
        month7_qty: "",
        month7_given: "",
        month8_qty: "",
        month8_given: "",
        month9_qty: "",
        month9_given: "",
        month10_qty: "",
        month10_given: "",
        month11_qty: "",
        month11_given: "",
        month12_qty: "",
        month12_given: "",
        sub_amount: "",
        remark: "",
        otherUnitInfo: []
      },
      settlementPopup: false,
      isSubmitting: false,
      moreOptions: false,
      printBarcodeStyle: 'noBarcode',
      printBarcodePic: false,
      isPrinting: false,
      showUnsubmitedSheets: false,
      unsubmitedSheets: [],
      displayAppendBriefDialog: false,
      displayAppendBriefContent: '',
      displayTemplateShowPopup: false,
      displayTemplateInfo: {},
      displayTemplateInfoOverWriteFlag: true,
      msgId: '',
      currentDisplayList:[],
      currentDisplayTemplateList:[],
      showImageSelect:false,
      imageSelectActions: [{ name: '拍照' }, { name: '相册' }]
    }
  },
  watch: {
    'sheet.supcust_id': {
      handler: function (val, oldVal) {
        console.log(val)
        if (val !== null) {
          this.handleGetCurrentDisplayList()
        }
      },
      deep: true
    },
  },
  mounted() {
    let that = this
    this.displayTemplateInfoOverWriteFlag = false
    this.handleLoadData()
    this.handleGetFreeOutList()
    window.sayCode = function (result) {
      that.pageSayCode(result)
    }
  },
  computed: {
    showTimeConfigFlag: {
      get() {
        return this.showTimeConfig.startTimeShowFlag || this.showTimeConfig.endTimeShowFlag
      },
      set() { }
    },
    totalRows() {
      return this.sheet.sheetRows.length
    },
    moneyTotal() {
      if (this.sheet.sheetRows.length > 0) {
        return this.sheet.sheetRows[0].sub_amount
      } else {
        return 0
      }
    },
    sheetRowsTotalSubAmount() {
      let result = 0
      if (this.sheet.sheetRows.length > 1) {
        for (let i = 1; i < this.sheet.sheetRows.length; i++) {
          result += Number(this.sheet.sheetRows[i].sub_amount)
        }
      }
      return result
    },
    showTotalUnitInfo() {
      if (this.sheet.total_quantity !== '') {
        return this.sheet.total_quantity
      } else {
        let result = ''
        let bNum = 0
        let mNum = 0
        let sNum = 0
        if (this.sheet.sheetRows.length > 0) {
          this.sheet.sheetRows.forEach(item => {
            if (item.items_id !== 'money') {
              let unitInfo = item.otherUnitInfo ? item.otherUnitInfo.find(otherItem => otherItem.unit_no === item.unit_no) : null
              if (unitInfo) {
                if (unitInfo.unit_type === 'b') {
                  bNum += this.handleCalcUnitTotalInfo(item)
                } else if (unitInfo.unit_type === 'm') {
                  mNum += this.handleCalcUnitTotalInfo(item)
                } else if (unitInfo.unit_type === 's') {
                  sNum += this.handleCalcUnitTotalInfo(item)
                }
              } else {  // 防止没有的情况，但是依旧存在一定的问题
                sNum += this.handleCalcUnitTotalInfo(item)
              }

            }
          })
        }
        if (bNum !== 0) result += (bNum + '大')
        if (mNum !== 0) result += (mNum + '中')
        if (sNum !== 0) result += (sNum + '小')
        return result
      }
    },
    canRed() {
      // eslint-disable-next-line no-undef
      return hasRight("sale.sheetDisplayAgreement.red")
    },
    canMake() {
      // eslint-disable-next-line no-undef
      return hasRight("sale.sheetDisplayAgreement.make");
    },
    canApprove() {
      // eslint-disable-next-line no-undef
      return hasRight("sale.sheetDisplayAgreement.approve");
    },
    canDelete() {
      // eslint-disable-next-line no-undef
      return hasRight("sale.sheetDisplayAgreement.delete");
    },
    canPrint() {
      // eslint-disable-next-line no-undef
      return hasRight("sale.sheetDisplayAgreement.print");
    },
    canEditTemplate() {
      return this.sheet.approve_time === '' || this.sheet.review_refused === true
    },
    canReview() {
      // eslint-disable-next-line no-undef
      return hasRight("sale.sheetDisplayAgreement.review");
    },
    needReview() {
      // 有陈列协议模板 且 需要复核 且 复核时间为空
      return this.sheet.disp_template_id !== '' && this.displayTemplateInfo.sign_need_review && this.sheet.review_time === '' && this.sheet.sheet_id !== ''
    }
  },
  methods: {
    remove_photo(idx) {
      console.log({ idx })
      this.sheet.appendixPhotos.splice(idx, 1)
    },
    preview_photo(idx) {
      ImagePreview({
        images: this.sheet.appendixPhotos,
        startPosition: idx,
        closeable: true,
        // asyncClose: true
      });
    },
    onImageWaySelected(item){
      switch (item.name) {
        case '拍照':
          this.takeAppendixPhotos(Camera.PictureSourceType.CAMERA)
          break;
        case '相册':
          this.takeAppendixPhotos(Camera.PictureSourceType.PHOTOLIBRARY)
          break;
    }
    },
    async takeAppendixPhotos(sourceType) {
      const that = this;
      const originBase64 = await TakePhoto.takePhotos(sourceType)
      const compressBase64 = await ImageUtil.compress(originBase64)
      SaveSingleImage({
        operKey: this.$store.state.operKey,
        imageBase64: compressBase64
      }).then((res) => {
        if (res?.result === 'OK' && res?.data) {
          const image = res.data
          Toast.success({ message: '上传成功', duration: 500 })
          console.log('[上传图片] 成功', res)
          that.sheet.appendixPhotos.push(image)
        } else {
          const msg = res.msg ?? '图片上传失败'
          // Toast.fail(msg)
          console.warn('[上传图片] 失败', res)
          Toast.fail('[上传图片] 失败' + res)
          // that.sheet.appendixPhotos.push(compressBase64)
        }
      }).catch((err) => {
        Toast.fail('[上传图片] 失败' + err)
        console.error('[上传图片] 网络错误/通信失败' + err)
        // Toast.fail('上传失败,请检查网络连接')
        // that.sheet.appendixPhotos.push(compressBase64)
      })
    },
    goBack() {
      // eslint-disable-next-line no-undef
      myGoBack(this.$router)
    },
    // + 跳转
    handleGoSelectItemPage() {
      if (this.sheet.approve_time) return
      let errMsg = this.handleCheckBaseInfo()
      if (errMsg) {
        Toast.fail(errMsg)
        return
      }
      this.displaySheetShoppingCar = []
      let query = {
        searchStr: this.searchStr || "",
        sheet: this.sheet,
        displaySheetShoppingCar: this.displaySheetShoppingCar
      };
      this.$store.commit("currentSheet", this.sheet);
      this.$router.push({ path: "/displaySelectItem", query: query })
    },
    // 扫码，录入
    pageSayCode(result) {
      this.searchStr = result;
      this.queryScan()
    },
    onSearchInputKeyDown(e) {
      if (e.keyCode === 13) {
        this.queryScan()
      }
    },
    // 手机扫码
    async handleScanBarcode() {
      try {
        const result = await this.scanBarcodeNew({
          unit_type: 'display_agreement_sheet'
        })

        if (!result.code) {
          return
        }

        this.searchStr = result.code
        await this.queryScan()
      } catch (error) {
        console.error('扫码失败:', error)
        this.$toast('扫码失败，请重试')
      }
    },
    async queryScan() {
      this.$store.commit("currentSheet", this.sheet);
      let params = {
        searchStr: this.searchStr,
        brandID: '',
        classID: '',
        pageSize: 20,
        startRow: 0,
        status: '1',
      }
      await GetGoodsItemList(params).then(res => {
        if (res.result === "OK") {
          if (res.data.length === 0) {
            Toast('未找到对应商品')
          } else if (res.data.length === 1) {
            let item = res.data[0]
            let canAddFlag = true
            let itemId = item.item_id + ','
            let findObj = this.sheet.sheetRows.find(sheetRow => {
              let sheetRowItemsId = sheetRow.items_id + ','
              if (sheetRowItemsId.includes(itemId)) {
                return sheetRow
              }
            })
            if (findObj !== undefined) {
              canAddFlag = false
            }
            if (!canAddFlag) {
              Toast("商品已存在，无法添加")
            } else {
              this.displaySheetShoppingCar = []
              this.displaySheetShoppingCar.push(item)
              this.handleGetSelectItemToSheetRows()
              this.handleScroll()
            }
          }
          else if (res.data.length > 1) {
            this.handleGoSelectItemPage()
          }
        }
      }).catch(err => {
        console.log(err)
      })
    },
    // 扫码等添加的商品进行滚动
    handleScroll() { //sheetRowsListRef
      let that = this
      setTimeout(() => {
        // eslint-disable-next-line no-undef
        $('#displaySheetWrapperId').animate({ scrollTop: that.$refs.sheetRowsListRef.scrollHeight }, 150);
      })
    },
    //  初始化单据
    async handleLoadData() {  // 加载初始化数据
      await DisplayAgreementLoad({
        sheetID: this.$route.query.sheetID ? this.$route.query.sheetID : '',
      }).then(res => {
        if (res.result === 'OK') {
          this.sheet = res.sheet
          if (res.sheet.sign_work_content === '') {
            this.sheet.sign_work_content = {
              sendMessage: false,
              sign_work_content: []
            }
          } else {
            this.sheet.sign_work_content = {
              sendMessage: false,
              sign_work_content: JSON.parse(this.sheet.sign_work_content)
            }
          }
          this.sheet.review_refused = this.sheet.review_refused === 'True' ? true : ''
          this.handleInitSheetRows()
          const sheetID = this.sheet.sheet_id
          if (!sheetID) {
            this.showSheetsFromCache()
          }
          if (this.sheet.disp_template_id) {
            this.handleLoadTemplateContent()
          }
          this.msgId = this.$route.query.msgId ? this.$route.query.msgId : ''
          
          const appendixOrigin = JSON.parse(res.sheet.appendix_photos ? res.sheet.appendix_photos : "[]")
          const obsUrls = appendixOrigin.filter(imgurl => !ImageUtil.isBase64(imgurl)).map(imgurl => { return globalVars.obs_server_uri + "/uploads" + imgurl })
          this.sheet.appendixPhotos = []
          this.sheet.appendixPhotos = this.sheet.appendixPhotos.concat(obsUrls)
          console.log('obsUrls', obsUrls)

        }
      }).catch(err => {
        console.log(err)
      })
    },
    //  加载模板
    async handleLoadTemplateContent() {
      await QueryOneInfoDisplayTemplate({
        disp_template_id: Number(this.sheet.disp_template_id),
        operKey: this.$store.state.operKey
      }).then(res => {
        let item = res.result.item
        if (item.start_time.length === 10) {
          item.start_time += ' 00:00:00'
        }
        if (item.end_time.length === 10) {
          item.end_time += ' 00:00:00'
        }
        item.sign_actions = item.sign_actions ? JSON.parse(item.sign_actions) : []
        item.keep_actions = item.keep_actions ? JSON.parse(item.keep_actions) : []
        item.maintain_actions = item.maintain_actions ? JSON.parse(item.maintain_actions) : []
        item.fd_seller_actions = item.fd_seller_actions ? JSON.parse(item.fd_seller_actions) : []
        item.fd_sender_actions = item.fd_sender_actions ? JSON.parse(item.fd_sender_actions) : []
        item.cx_give_actions = item.cx_give_actions ? JSON.parse(item.cx_give_actions) : []
        item.give_items = JSON.parse(item.give_items)
        item.month_maintain_times = Number(item.month_maintain_times)
        item.maintain_interval_days = Number(item.maintain_interval_days)
        item.latest_sign_month_day = Number(item.latest_sign_month_day)
        item.sign_need_review = item.sign_need_review === 'True'
        item.maintain_need_review = item.maintain_need_review === 'True'
        item.keep_need_review = item.keep_need_review === 'True'
        item.fd_seller_need_review = item.fd_seller_need_review === 'True'
        item.fd_sender_need_review = item.fd_sender_need_review === 'True'
        item.cx_give_need_review = item.cx_give_need_review === 'True'
        this.displayTemplateInfo = item
        this.sheet.disp_template_name = item.disp_template_name
        this.sheet.sign_work_content.sendMessage =  item.sign_need_review === true
        this.$forceUpdate()
      }).catch(() => {
        Toast('获取单据失败')
      })
    },
    formatter(type, val) {
      if (type === 'year') {
        return `${val}年`;
      } else if (type === 'month') {
        return `${val}月`;
      }
      return val;
    },
    handleInitSheetRows() {
      if (this.sheet.approve_time === '' && this.sheet.sheetRows.length === 0) {
        this.handleInitAddMoney()
        this.sheet.startTimeShow = new Date().format("yyyy-MM")
        this.sheet.start_time = this.sheet.startTimeShow + '-01 00:00:00'
        this.sheet.endTimeShow = this.sheet.startTimeShow
        this.sheet.end_time = this.sheet.startTimeShow + '-01 00:00:00'
      }
      if (this.sheet.approve_time !== '' || this.sheet.make_time !== '') {
        this.handleCalcMonth()
      }
    },
    handleInitAddMoney() {
      this.sheet.sheetRows = []
      let cardInfo = JSON.parse(JSON.stringify(this.cardItem))
      cardInfo.items_id = "money"
      cardInfo.items_name = "钱"
      cardInfo.unit_no = "元"
      cardInfo.sub_amount = 0
      cardInfo.otherUnitInfo.push({
        unit_type: 'b',
        unit_no: '元',
        unit_factor: 1,
        wholesale_price: 1,
      })
      this.sheet.sheetRows.push(cardInfo)
    },
    handleCalcMonth() {
      let startTime = new Date(this.sheet.start_time.myReplace('-', '/')).format("yyyy-MM-dd")
      let endTime = new Date(this.sheet.end_time.myReplace('-', '/')).format("yyyy-MM-dd")
      this.sheet.startTimeShow = new Date(this.sheet.start_time.myReplace('-', '/')).format("yyyy-MM")
      this.sheet.endTimeShow = new Date(this.sheet.end_time.myReplace('-', '/')).format("yyyy-MM")
      let startTimeArr = startTime.split('-')
      let endTimeArr = endTime.split('-')
      this.showTimeConfig.month = 1 + ((parseInt(endTimeArr[0]) * 12 + parseInt(endTimeArr[1])) - (parseInt(startTimeArr[0]) * 12 + parseInt(startTimeArr[1])))
    },
    // 选择客户
    onSelectClient() {
      // if(!this.canChangeCustomer){
      //   this.$toast.fail("强制拜访,无法切换客户")
      //   return
      // }
      if (this.sheet.approve_time) return
      this.clientSelectDialog = true
    },
    handleOnClientSelected(obj) {
      this.sheet.sup_name = obj.titles
      this.sheet.sup_addr = obj.sup_addr
      this.sheet.supcust_id = obj.ids
      this.clientSelectDialog = false
    },
    // 费用支出
    async handleGetFreeOutList() {
      await DisplayGetFeeOutSubForKS().then(res => {
        if (res.result === 'OK') {
          this.feeOutList = res.feeOutList
        }
      }).catch(err => {
        console.log(err)
      })
    },
    handleSelectFreeOut() {
      if (this.sheet.approve_time) return
      this.feeOutListShowDialog = true
    },
    handleOnConfirmFreeOut(obj) {
      this.sheet.fee_sub_id = obj.v
      this.sheet.fee_sub_name = obj.l
      this.feeOutListShowDialog = false
    },
    handleGetSelectItemToSheetRows() {
      if (this.displaySheetShoppingCar.length > 0) {
        let cardInfo = JSON.parse(JSON.stringify(this.cardItem))
        this.displaySheetShoppingCar.forEach(item => {
          cardInfo.items_id === '' ? cardInfo.items_id = item.item_id : cardInfo.items_id += "," + item.item_id
          cardInfo.items_name === '' ? cardInfo.items_name = item.item_name : cardInfo.items_name += "," + item.item_name
        })
        cardInfo.otherUnitInfo = []
        if (this.displaySheetShoppingCar[0].b_unit_no !== '') {
          cardInfo.otherUnitInfo.push({
            unit_type: 'b',
            unit_no: this.displaySheetShoppingCar[0].b_unit_no,
            unit_factor: this.displaySheetShoppingCar[0].b_unit_factor,
            wholesale_price: this.displaySheetShoppingCar[0].b_wholesale_price,
          })
        }
        if (this.displaySheetShoppingCar[0].m_unit_no !== '') {
          cardInfo.otherUnitInfo.push({
            unit_type: 'm',
            unit_no: this.displaySheetShoppingCar[0].m_unit_no,
            unit_factor: this.displaySheetShoppingCar[0].m_unit_factor,
            wholesale_price: this.displaySheetShoppingCar[0].m_wholesale_price,
          })
        }
        if (this.displaySheetShoppingCar[0].s_unit_no !== '') {
          cardInfo.otherUnitInfo.push({
            unit_type: 's',
            unit_no: this.displaySheetShoppingCar[0].s_unit_no,
            unit_factor: this.displaySheetShoppingCar[0].s_unit_factor,
            wholesale_price: this.displaySheetShoppingCar[0].s_wholesale_price,
          })
        }
        cardInfo.unit_no = cardInfo.unit_no
        this.sheet.sheetRows.push(cardInfo)
      }

    },
    handleDeleteItem(item) {
      if (this.sheet.approve_time) return
      if (item.items_id === 'money') {
        Toast('钱不支持删除')
        return;
      }
      let index = this.sheet.sheetRows.findIndex(sheetRow => sheetRow.items_id === item.items_id)
      this.sheet.sheetRows.splice(index, 1)

    },
    // 处理日期的变化
    handleSelectTime(startOrEndFlag) {
      if (this.sheet.approve_time) return
      if (startOrEndFlag === 'start') {
        this.showTimeConfig.startTimeShowFlag = true
        this.showTimeConfig.startOrEnd = 'start'
        if (this.sheet.start_time) {
          this.showTimeConfig.time = new Date(this.sheet.start_time.myReplace('-', '/'))
        }
      } else if (startOrEndFlag === 'end') {
        this.showTimeConfig.endTimeShowFlag = true
        this.showTimeConfig.startOrEnd = 'end'
        if (this.sheet.end_time) {
          this.showTimeConfig.time = new Date(this.sheet.end_time.myReplace('-', '/'))
        }
      }
    },
    // 确认时间
    handleTimeConfirm(obj) {
      let data = new Date(obj)
      if (this.showTimeConfig.startOrEnd === 'start') {
        // 处理模板时间
        if (this.sheet.disp_template_id) {
          const templateStartTime = new Date(this.displayTemplateInfo.start_time.myReplace('-', '/'))
          const templateEndTime = new Date(this.displayTemplateInfo.end_time.myReplace('-', '/'))
          if (data < templateStartTime || data > templateEndTime) {
            Toast('开始时间超过模板设置')
            return
          }
        }
        this.showTimeConfig.startTimeShowFlag = false
        this.handleStartTimeChange(data)
      } else {
        // 判断结束时间
        let startTime = new Date(this.sheet.start_time.myReplace('-', '/'))
        let maxTime = new Date(this.commonAddMouth(this.sheet.start_time, 11, 1).myReplace('-', '/'))
        if (data < startTime) {
          Toast('结束时间应大于开始时间')
          return
        } else if (maxTime < data) {
          Toast('结束时间大于一年限制')
          return
        }
        if (this.sheet.disp_template_id) {
          let templateEndTime = new Date(this.displayTemplateInfo.end_time.myReplace('-', '/'))
          if (data > templateEndTime) {
            Toast('结束时间大于模板设置')
            return
          }
        }
        this.handleEndTimeChange(data)
        this.showTimeConfig.endTimeShowFlag = false
      }
      if (this.sheet.start_time !== '' && this.sheet.end_time !== '') {
        this.handleCalcMonth()
      }
    },
    // 月份
    handleSelectMonth() {
      if (this.sheet.approve_time) return
      this.showTimeConfig.monthPopup = true
    },
    // 确认月份
    handleOnConfirmMonth(obj) {
      this.showTimeConfig.month = obj
      this.showTimeConfig.monthPopup = false
      this.sheet.end_time = this.commonAddMouth(this.sheet.start_time, this.showTimeConfig.month - 1, 1)
      this.sheet.endTimeShow = this.commonAddMouth(this.sheet.start_time, this.showTimeConfig.month - 1, 0)
      if (this.sheet.disp_template_id) {
        let templateEndTime = new Date(this.displayTemplateInfo.end_time.myReplace('-', '/'))
        if (new Date(this.sheet.end_time.myReplace('-', '/')) > templateEndTime) {
          Toast('结束时间超出模板设置，自动修正')
          let templateEndTime = new Date(this.displayTemplateInfo.end_time.myReplace('-', '/'))
          this.sheet.endTimeShow = templateEndTime.format("yyyy-MM")
          this.sheet.end_time = templateEndTime.format("yyyy-MM-dd hh:mm:ss")
          this.handleCalcMonth()
        }
      }

    },
    // 在基础上添加月份
    commonAddMouth(dateStr, num, type) {
      var monthnum = 0;
      if (typeof (num) == "string") {
        monthnum = parseInt(num);
      } else {
        monthnum = num;
      }
      var date = new Date(dateStr.myReplace('-', '/'));
      //获取原日
      var day = date.getDate();
      //获取原月份
      // var month = date.getMonth();
      //设置增加月份
      date.setMonth(date.getMonth() + (monthnum * 1), 1);
      //获取增加的后的月份
      var Jmonth = date.getMonth() + 1;
      //获取增加的后的年份
      var Jyear = date.getFullYear();
      if (Jmonth === 4 || Jmonth === 6 || Jmonth === 9 || Jmonth === 11) {
        //小月
        if (day > 30) {
          day = 30;
        }
      } else if (Jmonth === 2) {
        //2月判断是否闰年
        if (((Jyear % 4) === 0) && ((Jyear % 100) !== 0) || ((Jyear % 400) === 0)) {
          if (day > 29) {
            day = 29;
          } else {
            day = 28;
          }
        }
        if (day > 28) {
          day = 28
        }
      } else {
        //大月
        if (day > 31) {
          day = 31;
        }
      }
      var tHours = this.doHandleMonth(date.getHours())
      var tMinutes = this.doHandleMonth(date.getMinutes())
      var tSeconds = this.doHandleMonth(date.getSeconds())
      Jmonth = this.doHandleMonth(Jmonth);
      day = this.doHandleMonth(day);
      if (type === 0) {
        return Jyear + "-" + Jmonth;
      }
      return Jyear + "-" + Jmonth + "-" + day + " " + tHours + ":" + tMinutes + ":" + tSeconds;
    },
    doHandleMonth(month) {
      let m = month;
      if (month.toString().length === 1) {
        m = "0" + month;
      }
      return m;
    },
    handleTimeCancel() {
      this.showTimeConfig.startTimeShowFlag = false
      this.showTimeConfig.endTimeShowFlag = false
    },
    // 结算
    handleShowSettlementPopup() {
      this.settlementPopup = true
    },
    async handleSave() {
      const paramSheet = this.handleGetSheet()
      let errMsg = this.handleCheckSaveAndApprove()
      if (errMsg) {
        Toast.fail(errMsg)
        return
      }
      this.isSubmitting = true
      await DisplaySave(paramSheet).then(res => {
        if (res.result === 'OK') {
          this.sheet.approve_time = res.approve_time ? res.approve_time : ''
          this.sheet.happen_time = res.happen_time
          this.sheet.sheet_id = res.sheet_id
          this.sheet.sheet_no = res.sheet_no
          Toast.success("保存成功")
          setTimeout(() => {
            this.removeCurSheetFromCache()
          }, 300)
          this.isSubmitting = false
        } else {
          Toast.fail(res.msg)
        }
      }).catch(() => {
        Toast.fail("保存失败")
        this.isSubmitting = false
      })
    },
    async handleApprove() {
      const paramSheet = this.handleGetSheet()
      let errMsg = this.handleCheckSaveAndApprove()
      if (errMsg) {
        Toast.fail(errMsg)
        return
      }
      this.isSubmitting = true
      await DisplaySaveAndApprove(paramSheet).then(res => {
        if (res.result === 'OK') {
          this.sheet.approve_time = res.approve_time
          this.sheet.happen_time = res.happen_time
          this.sheet.sheet_id = res.sheet_id
          this.sheet.sheet_no = res.sheet_no
          Toast.success("审核成功")
          setTimeout(() => {
            this.removeCurSheetFromCache()
          }, 300)
          this.isSubmitting = false
        } else {
          Toast.fail(res.msg)
        }
      }).catch(() => {
        Toast.fail("审核失败")
        this.isSubmitting = false
      })
    },
    handleCheckBaseInfo() {
      let errMsg = ''
      if (!this.sheet.supcust_id) {
        errMsg = ' 请选择客户'
      }
      if (!this.sheet.fee_sub_id) {
        errMsg = ' 请选择费用'
      }
      return errMsg
    },
    handleCheckSaveAndApprove() {
      let errMsg = this.handleCheckBaseInfo()
      if (errMsg) {
        return errMsg
      }
      if (this.sheet.disp_template_id) {
        errMsg = this.$refs.actionsTemplateRef.handleGetTemplateResult()
        if (errMsg) {
          return errMsg
        }
      }
      var tmpNeed = window.getSettingValue('dispTemplateNecessary')
      if (tmpNeed.toLowerCase() == 'true' && !this.sheet.disp_template_id) {
        return '必须指定模板'
      }
      errMsg = this.handleCheckSameItem()
      if (errMsg) {
        return errMsg
      }
      return errMsg
    },
    handleCheckSameItem() {
      let errMsg = '';
      const itemsIdSet = new Set();
      for (let i = 0; i < this.sheet.sheetRows.length; i++) {
        let row = this.sheet.sheetRows[i]
        if (itemsIdSet.has(row.items_id)) {
          errMsg = row.items_name + ',存在重复';
          break;
        } else {
          itemsIdSet.add(row.items_id);
        }
      }
      return errMsg;

    },
    handleGetSheet() {
      const sheetTemp = JSON.parse(JSON.stringify(this.sheet))
      sheetTemp.operKey = this.$store.state.operKey
      sheetTemp.IsFromWeb = false
      if (sheetTemp.seller_id === '') {
        sheetTemp.seller_id = this.$store.state.operInfo.oper_id
        sheetTemp.seller_name = this.$store.state.operInfo.oper_name
      }
      sheetTemp.total_money = this.moneyTotal
      sheetTemp.total_amount = this.sheetRowsTotalSubAmount
      sheetTemp.total_quantity = this.showTotalUnitInfo
      if (sheetTemp.sign_work_content && sheetTemp.sign_work_content.sign_work_content) {
        sheetTemp.signSendMessage = sheetTemp.sign_work_content.sendMessage
        sheetTemp.sign_work_content = JSON.stringify(sheetTemp.sign_work_content.sign_work_content)
      }
      
      let appendixObject = {
          photos: sheetTemp.appendixPhotos
      };
      sheetTemp.appendix_photos = JSON.stringify(appendixObject);
      return sheetTemp
    },
    // 处理单位信息
    handleCalcUnitTotalInfo(item) {
      let qty = 0
      for (let i = 0; i < 12; i++) {
        let key = 'month' + (i + 1) + '_qty'
        qty += Number(item[key])
      }
      return qty
    },
    // 终止
    handleSheetRed() {
      Dialog.confirm({
        title: '终止单据',
        message: '请确认是否终止',
        width:"320px"
      }).then(async () => {
        this.displayAppendBriefDialog = true
      }).catch(() => {
        Toast("取消终止");
      })
    },
    // 确认终止
    async handleConfirmAppendBrief() {
      if (this.displayAppendBriefContent === '') {
        Toast('请输入终止原因')
      } else {
        await DisplayAgreementSheetTerminate({
          operKey: this.$store.state.operKey,
          sheet_id: this.sheet.sheet_id
        }).then(async (res) => {
          if (res.result === "OK") {
            await DisplayAppendBrief({
              newBrief: '终止原因:' + this.displayAppendBriefContent,
              operKey: this.$store.state.operKey,
              sheetID: this.sheet.sheet_id
            }).then(res => {
              if (res.result === 'OK') {
                Toast.success("终止,即将退出该页面");
                this.displayAppendBriefDialog = false
                this.removeCurSheetFromCache()
                setTimeout(() => {
                  this.handleOut()
                }, 1000);
              } else {
                Toast('添加终止原因失败')
              }
            }).catch(() => {
              Toast('添加终止原因失败')
            })
          } else {
            Toast.fail("终止失败");
          }
        }).catch(() => {
          Toast("终止失败");
        })
      }
    },
    handleCancelAppendBrief() {
      this.displayAppendBriefDialog = false
    },
    async handleSheetDelete() {
      Dialog.confirm({
        title: '删除单据',
        message: '请确认是否删除',
        width:"320px"
      }).then(async () => {
        await DisplayDelete({
          operKey: this.$store.state.operKey,
          sheet_id: this.sheet.sheet_id
        }).then((res) => {
          if (res.result === "OK") {
            Toast.success("删除成功,即将退出该页面");
            this.removeCurSheetFromCache()
            setTimeout(() => {
              this.handleOut()
            }, 1000);
          } else {
            Toast.fail("删除失败");
          }
        }).catch(() => {
          Toast("删除失败");
        })
      }).catch(() => {
        Toast("取消删除");
      })
    },
    handleSheetPrint() {
      this.isPrinting = true
      this.handleGetSheet()
      Printing.printDisplayAgreementSheet(this.sheet, this.showTimeConfig.month, res => {
        this.isPrinting = false
        if (res.result === "OK") {
          Toast.success("打印成功");
        } else {
          Toast.fail(res.msg);
        }
      })
    },
    handleSheetEmpty() {
      Dialog.confirm({
        title: "清空单据",
        message: "请确认是否清空单据？",
        width:"320px"
      }).then(() => {
        this.searchStr = ''
        this.displaySheetShoppingCar = []
        this.sheet.sheetRows = []
        this.handleInitAddMoney()
        Toast.success("已清空");
      }).catch(() => {
        Toast("取消清空");
      });
    },
    handleSheetCopy() {
      Dialog.confirm({
        title: "复制单据",
        message: "请确认是否复制?",
        width:"320px"
      }).then(() => {
        this.sheet.sheet_id = ''
        this.sheet.sheet_no = ''
        this.sheet.approve_time = ''
        this.sheet.approver_id = ''
        this.sheet.happen_time = ''
        this.sheet.make_time = ''
        this.sheet.maker_id = ''
        this.sheet.red_flag = ''
        Toast("复制成功");
      }).catch(() => {
        Toast("取消复制");
      })
    },
    handleOut() {
      // eslint-disable-next-line no-undef
      myGoBack(this.$router);
    },
    saveCurSheetToCache(currentSheet) {
      const key = "unsubmitedSheets_CX"
      let unsubmitedSheets = this.$store.state[key];
      if (!unsubmitedSheets) unsubmitedSheets = [];
      let sheet = JSON.parse(JSON.stringify(currentSheet))
      Date.prototype.format = function (fmt) {
        var o = {
          "M+": this.getMonth() + 1, //月份
          "d+": this.getDate(), //日
          "h+": this.getHours(), //小时
          "m+": this.getMinutes(), //分
          "s+": this.getSeconds(), //秒
          "q+": Math.floor((this.getMonth() + 3) / 3), //季度
          S: this.getMilliseconds(), //毫秒
        };
        if (/(y+)/.test(fmt))
          fmt = fmt.replace(
            RegExp.$1,
            (this.getFullYear() + "").substr(4 - RegExp.$1.length)
          );
        for (var k in o)
          if (new RegExp("(" + k + ")").test(fmt))
            fmt = fmt.replace(
              RegExp.$1,
              RegExp.$1.length === 1
                ? o[k]
                : ("00" + o[k]).substr(("" + o[k]).length)
            );
        return fmt;
      };
      sheet.saveTime = new Date().format("yyyy-MM-dd h:m");
      for (var i = unsubmitedSheets.length - 1; i >= 0; i--) {
        var sht = unsubmitedSheets[i];
        if (sht.branch_name === sheet.branch_name) {
          unsubmitedSheets.splice(i, 1);
        }
      }
      unsubmitedSheets.unshift(sheet);
      if (unsubmitedSheets.length > 5)
        unsubmitedSheets.splice(5, unsubmitedSheets.length - 5);
      this.$store.commit(key, unsubmitedSheets);
    },
    showSheetsFromCache() {
      var key = "unsubmitedSheets_CX"
      if (this.$store.state[key] && this.$store.state[key].length > 0) {
        if (this.$route.query.showUnsubmitedSheetsQureyFlag !== undefined) {
          let cacheSheetsFlag = this.$route.query.showUnsubmitedSheetsQureyFlag !== 'false'
          if (!cacheSheetsFlag) { // 拜访门店进入，只显示自己的缓存
            this.unsubmitedSheets = this.$store.state[key].filter(item => item.branch_id === this.$route.query.branch_id)
          }
        } else {
          this.unsubmitedSheets = this.$store.state[key];
        }
        this.showUnsubmitedSheets = this.unsubmitedSheets.length > 0
      }
    },
    removeCurSheetFromCache() {
      var key = "unsubmitedSheets_CX";
      var unsubmitedSheets = this.$store.state[key];
      if (!unsubmitedSheets) unsubmitedSheets = [];
      var sheet = this.sheet

      Date.prototype.format = function (fmt) {
        var o = {
          "M+": this.getMonth() + 1, //月份
          "d+": this.getDate(), //日
          "h+": this.getHours(), //小时
          "m+": this.getMinutes(), //分
          "s+": this.getSeconds(), //秒
          "q+": Math.floor((this.getMonth() + 3) / 3), //季度
          S: this.getMilliseconds(), //毫秒
        };
        if (/(y+)/.test(fmt))
          fmt = fmt.replace(
            RegExp.$1,
            (this.getFullYear() + "").substr(4 - RegExp.$1.length)
          );
        for (var k in o)
          if (new RegExp("(" + k + ")").test(fmt))
            fmt = fmt.replace(
              RegExp.$1,
              RegExp.$1.length === 1
                ? o[k]
                : ("00" + o[k]).substr(("" + o[k]).length)
            );
        return fmt;
      };
      sheet.saveTime = new Date().format("yyyy-MM-dd h:m");
      for (var i = unsubmitedSheets.length - 1; i >= 0; i--) {
        var sht = unsubmitedSheets[i];
        if (sht.branch_name === sheet.branch_name) {
          unsubmitedSheets.splice(i, 1);
        }
      }
      this.$store.commit(key, unsubmitedSheets);
    },
    // 获取缓存
    onUnsubmitedSheetSelected(obj) {
      this.sheet = obj;
      this.showUnsubmitedSheets = false;
    },
    onUnsubmitedSheetDelete(index) {
      const key = "unsubmitedSheets_CX";
      const unsubmitedSheets = this.$store.state[key];
      unsubmitedSheets.splice(index, 1);
      this.$store.commit(key, this.unsubmitedSheets);
    },
    // 选择模板
    handleSelectDisplayTemplateShow() {
      if (this.sheet.approve_time) return
      this.displayTemplateShowPopup = true
    },
    handleCloseDisplayTemplateShowPopup() {
      this.displayTemplateShowPopup = false
    },
    // 处理模板
    handleSelectTemplateItem(templateItem) {
      console.log(templateItem)
      if (this.currentDisplayTemplateList.indexOf(templateItem.disp_template_id) !== -1) {
        Toast.fail("当前模板生效中，请勿重复使用")
        return
      }
      this.displayTemplateShowPopup = false
      // 判断时间并调整，如果通过则可以进行下一步，否则进行提示
      const errMessage = this.handleSelectTemplateItemToTime(templateItem)
      if (errMessage !== '') {
        Toast.fail(errMessage)
      } else {
        this.displayTemplateInfo = templateItem
        this.sheet.disp_template_id = templateItem.disp_template_id
        this.sheet.disp_template_name = templateItem.disp_template_name
        this.handleSelectTemplateItemToReviewInfo()
        this.handleSelectTemplateItemToWorkContent(templateItem)
        this.handleSelectTemplateItemToSheetRows(templateItem)
      }
    },
    handleCancelTemplate() {
      this.sheet.disp_template_id = ''
      this.sheet.disp_template_name = ''
      this.sheet.sign_work_content = {
        sendMessage: false,
        sign_work_content: []
      }
      this.handleSelectTemplateItemToReviewInfo()
      this.displayTemplateShowPopup = false
    },
    //  选中模板时间处理
    handleSelectTemplateItemToTime(templateItem) {
      let errMessage = ''
      let templateStartTime = new Date(templateItem.start_time.myReplace('-', '/'))
      let templateEndTime = new Date(templateItem.end_time.myReplace('-', '/'))
      let sheetStartTime = new Date(this.sheet.start_time.myReplace('-', '/'))
      let sheetEndTime = new Date(this.sheet.end_time.myReplace('-', '/'))
      // 开始时间处理
      // if (this.sheet.start_time === '' || sheetStartTime < templateStartTime) {
        this.sheet.startTimeShow = templateStartTime.format("yyyy-MM")
        this.sheet.start_time = templateStartTime.format("yyyy-MM-dd hh:mm:ss")
        sheetStartTime = new Date(this.sheet.start_time.myReplace('-', '/'))
        this.sheet.endTimeShow = templateEndTime.format("yyyy-MM")
        this.sheet.end_time = templateEndTime.format("yyyy-MM-dd hh:mm:ss")
        sheetEndTime = new Date(this.sheet.end_time.myReplace('-', '/'))
      // }
      // 当前月进行处理
      if (this.handleLatestSignMonthDay(templateItem)) {
        // 如果下单的时间今天是超过了 x号为当前月，那么这个开始时间得 + 1
        //  start_time 和 new Date() 为一个月，那么就需要加，
        this.sheet.start_time = this.commonAddMouth(this.sheet.start_time, 1, 1)
        this.sheet.startTimeShow = this.commonAddMouth(this.sheet.start_time, 1, 0)
        templateStartTime = new Date(templateItem.start_time.myReplace('-', '/'))
        // let sheetStartTimeMonth = sheetStartTime.getMonth() + 1
        // let todayMonth = new Date().getMonth() + 1
        // if (todayMonth === sheetStartTimeMonth) {
        //
        // }
      }
      // this.sheet.end_time = this.commonAddMouth(this.sheet.start_time, this.showTimeConfig.month - 1, 1)
      // this.sheet.endTimeShow = this.commonAddMouth(this.sheet.start_time, this.showTimeConfig.month - 1, 0)
      // sheetEndTime = new Date(this.sheet.end_time.myReplace('-', '/'))
      // // 结束时间时间处理
      // if (this.sheet.end_time === '' || sheetEndTime > templateEndTime) {
      //   this.sheet.endTimeShow = templateEndTime.format("yyyy-MM")
      //   this.sheet.end_time = templateEndTime.format("yyyy-MM-dd hh:mm:ss")
      //   sheetEndTime = new Date(this.sheet.end_time.myReplace('-', '/'))
      // }
      // 判断起止时间是否正常
      if (sheetStartTime > sheetEndTime) {
        errMessage = '开始时间超过结束时间，请确认起止时间，月份'
      } else if (sheetEndTime > templateEndTime) {
        errMessage = '结束时间超过模板设置，请确认起止时间，月份'
      }
      if (errMessage) {
        this.displayTemplateInfo = {}
        this.sheet.disp_template_id = ''
        this.sheet.disp_template_name = ''
        this.sheet.startTimeShow = new Date().format("yyyy-MM")
        this.sheet.start_time = this.sheet.startTimeShow + '-01 00:00:00'
        this.sheet.endTimeShow = this.sheet.startTimeShow
        this.sheet.end_time = this.sheet.startTimeShow + '-01 00:00:00'
        this.handleCalcMonth()
      } else {
        // 计算月份
        this.handleCalcMonth()
      }
      return errMessage
    },
    // 处理当前月还是只能开到下个月
    handleLatestSignMonthDay(templateItem) {
      const sheetStartTimeDay = new Date().getDate()
      const latestSignMonthDay = templateItem.latest_sign_month_day
      // true 需要加一个月
      return Number(latestSignMonthDay) !== 0 && Number(sheetStartTimeDay) > Number(latestSignMonthDay)
    },
    // 处理选择模板复核信息
    handleSelectTemplateItemToReviewInfo() {
      this.sheet.reviewer = ''
      this.sheet.review_time = ''
      this.sheet.review_comment = '' // 考虑之前备注情况是否保留
      this.sheet.review_refused = ''
    },
    // 处理模板规范
    handleSelectTemplateItemToWorkContent(templateItem) {
      // console.log(templateItem)sendMessage: templateItem.sign_need_review,
      this.sheet.sign_work_content = {
        sendMessage: false,
        sign_work_content: []
      }
      if (templateItem.sign_actions.length !== 0) {
        templateItem.sign_actions.forEach(signActionItem => {
          let pushObj = {
            action: signActionItem,
            work_content: ''
          }
          if (signActionItem.type === 'photo') {
            pushObj.work_content = {
              mandatory: [],  // 必选列表
              mandatoryName: [],
              optional: [],   // 可选列表
              optionalName: [],
            }
              let num = pushObj.action.items.length === 0 ? Number(pushObj.action.minNum) : pushObj.action.items.length
              for (let i = 0; i < num; i++) {
                  pushObj.work_content.mandatory[i] = ''
              }
          } else {
            pushObj.work_content = ''
          }
          this.sheet.sign_work_content.sign_work_content.push(pushObj)
        })
      }
      this.sheet.sign_work_content.sendMessage = templateItem.sign_need_review === true
    },
    handleStartTimeChange(data) {
      this.sheet.startTimeShow = data.format("yyyy-MM")
      this.sheet.start_time = data.format("yyyy-MM-dd hh:mm:ss")
      if (this.handleLatestSignMonthDay(this.displayTemplateInfo) && this.sheet.disp_template_id) {
        let sheetStartTimeMonth = new Date(this.sheet.start_time.myReplace('-', '/')).getMonth() + 1
        let todayMonth = new Date().getMonth() + 1
        if (todayMonth === sheetStartTimeMonth) {
          this.sheet.start_time = this.commonAddMouth(this.sheet.start_time, 1, 1)
          this.sheet.startTimeShow = this.commonAddMouth(this.sheet.start_time, 1, 0)
          Toast(this.displayTemplateInfo.latest_sign_month_day + '号后，只能从下个月开单')
        }
      }
      this.sheet.end_time = this.commonAddMouth(this.sheet.start_time, this.showTimeConfig.month - 1, 1)
      this.sheet.endTimeShow = this.commonAddMouth(this.sheet.start_time, this.showTimeConfig.month - 1, 0)

      if (this.sheet.disp_template_id) {
        let templateEndTime = new Date(this.displayTemplateInfo.end_time.myReplace('-', '/'))
        if (new Date(this.sheet.end_time.myReplace('-', '/')) > templateEndTime) {
          Toast('结束时间超出模板设置，自动修正')
          let templateEndTime = new Date(this.displayTemplateInfo.end_time.myReplace('-', '/'))
          this.sheet.endTimeShow = templateEndTime.format("yyyy-MM")
          this.sheet.end_time = templateEndTime.format("yyyy-MM-dd hh:mm:ss")
          this.handleCalcMonth()
        }
      }



    },
    handleEndTimeChange(data) {
      this.sheet.endTimeShow = data.format("yyyy-MM")
      this.sheet.end_time = data.format("yyyy-MM-dd hh:mm:ss")






    },
    //  选中模板后商品行处理
    handleSelectTemplateItemToSheetRows(templateItem) {
      // 判断是否需要覆盖
      this.displayTemplateInfoOverWriteFlag = true
      // 第一年
      let startYear = new Date(this.sheet.start_time.myReplace('-', '/')).getFullYear()
      let endYear = new Date(this.sheet.end_time.myReplace('-', '/')).getFullYear()
      let startMonth = new Date(this.sheet.start_time.myReplace('-', '/')).getMonth() + 1
      let endMonth = new Date(this.sheet.end_time.myReplace('-', '/')).getMonth() + 1
      // 1. 添加商品
      this.sheet.sheetRows = []
      this.$forceUpdate()
 
      setTimeout(() => {
        templateItem.give_items.forEach(giveItem => {
          let cardInfo = JSON.parse(JSON.stringify(this.cardItem))
          cardInfo.items_id = giveItem.items_id
          cardInfo.items_name = giveItem.items_name
          // cardInfo.sub_amount = Number(giveItem.sub_amount)
          cardInfo.otherUnitInfo = giveItem.otherUnitInfo
          cardInfo.unit_no = giveItem.unit_no
          console.log(cardInfo.unit_no)
          this.sheet.sheetRows.push(cardInfo)
        })
        // 加入sheetRows之后，由内部组件进行初始化操作，初始化完成之后进行赋值
        // 2. 将对应月份的值赋值, 第一个月会导致
        templateItem.give_items.forEach((giveItem, giveItemIndex) => {
          if (startYear === endYear) {  //
            let j = 0
            for (let i = startMonth; i <= endMonth; i++) {
              let templateEndkey = startYear + '_' + i
              let sheetRowKey = 'month' + (++j) + '_qty'
              this.sheet.sheetRows[giveItemIndex][sheetRowKey] = Number(giveItem[Object.keys(giveItem).find(key => key.endsWith(templateEndkey))])
            }
          } else {  // 跨年情况
            let j = 0
            for (let i = startMonth; i <= 12; i++) {
              let templateEndkey = startYear + '_' + i
              let sheetRowKey = 'month' + (++j) + '_qty'
              this.sheet.sheetRows[giveItemIndex][sheetRowKey] = Number(giveItem[Object.keys(giveItem).find(key => key.endsWith(templateEndkey))])
            }
            for (let i = 1; i <= endMonth; i++) {
              let templateEndkey = endYear + '_' + i
              let sheetRowKey = 'month' + (++j) + '_qty'
              this.sheet.sheetRows[giveItemIndex][sheetRowKey] = Number(giveItem[Object.keys(giveItem).find(key => key.endsWith(templateEndkey))])
            }
          }
        })
      }, 100)
      setTimeout(() => {
        this.displayTemplateInfoOverWriteFlag = false
      }, 500)
    },
    // 复核情况
    async handleConfirmResult(result) {
      let reviewResult = {
        operKey: this.$store.state.operKey,
        sheet_id: this.sheet.sheet_id,
        review_comment: this.sheet.review_comment,
        review_refused: result,
        msgId: this.msgId,
        receiverId: this.sheet.seller_id,
        supName: this.sheet.sup_name,
        sheetNo: this.sheet.sheet_no
      }
      await UpdateDisplaySignActionReview(reviewResult).then(res => {
        if (res.result === 'OK') {
          this.sheet.reviewer = res.operID
          this.sheet.review_time = res.currentTime
          this.sheet.review_refused = reviewResult.review_refused
          this.sheet.reviewerName = this.$store.state.operInfo.oper_name
          Toast('复核完成')
        } else {
          Toast('复核失败，请重试或联系管理员')
        }
      }).catch(() => {
        Toast('复核失败，请重试')
      })
    },
    async handleUpdateWorkContent() {
      let updateWorkContent = {
        operKey: this.$store.state.operKey,
        sheet_id: this.sheet.sheet_id,
        signWorkContent: JSON.stringify(this.sheet.sign_work_content),
        supcust_id: this.sheet.supcust_id,
        msgId: this.msgId,
        receiverId: this.sheet.seller_id,
        supName: this.sheet.sup_name,
        sheetNo: this.sheet.sheet_no
      }
      await UpdateDisplaySignWorkContent(updateWorkContent).then(res => {
        if (res.result === 'OK') {
          this.sheet.reviewer = ''
          this.sheet.review_time = ''
          this.sheet.review_comment = ''
          this.sheet.review_refused = ''
          Toast.success("更新成功")
        }
      }).catch(() => {
        Toast.fail('更新失败')
      })
    },
    async handleGetCurrentDisplayList() {
      const params = {
        operKey: this.$store.state.operKey,
        supcust_id: this.sheet.supcust_id
      }
      await GetCurrentDisplayList(params).then(res => {
        if (res.code === 0) {
          this.currentDisplayList = res.result
          this.currentDisplayTemplateList = this.currentDisplayList.filter((item, index, self) => {
            // 利用findIndex方法找到第一个与当前元素id相等的元素索引
            const i = self.findIndex(t => t.disp_template_id === item.disp_template_id);
              // 如果当前索引等于当前元素在self中的最初出现位置索引，则表示元素符合要求，不是重复元素，保留
            return i === index;
          }).map(item => {
            return item.disp_template_id
          })
          console.log(this.currentDisplayList)
          console.log(this.currentDisplayTemplateList)
        }
      }).catch(() => {
        Toast.fail('获取生效陈列单失败')
      })
    },
  },
}
</script>

<style scoped lang="less">
.appendixphoto-container {
  margin-left: -10px;
  border-radius: 50%;
  width: 60px;
  position: relative;
  margin-left: 10px;
  height: 60px;
  position: relative;

  .remove-icon {
    position: absolute;
    top: -7px;
    margin-left: 51px;
    color: #f31010;
    font-size: 16px;
    float: right;
  }
}
.photo {
  margin-right: 6px;
  border-radius: 6px;
  width: 60px;
  height: 60px;
}
.wrapper {
  width: 100%;
  height: 100%;
  box-sizing: border-box;
  background-color: #eee;
  .content {
    display: flex;
    flex-direction: column;
    box-sizing: border-box;
    width: 100%;
    height: 100%;
    .sheet-wrapper {
      box-sizing: border-box;
      flex: 1;
      overflow-y: auto;
      padding: 10px 0;
      background-color: #fff;
      .sheet-info-id {
        box-sizing: border-box;
        padding: 5px 10px;
        display: flex;
        justify-content: space-between;
      }
      .sheet-info {
        width: 100%;
        padding: 10px 20px;
        box-sizing: border-box;
        display: flex;
        flex-direction: column;
        .sheet-info-item {
          box-sizing: border-box;
          width: 100%;
          display: flex;
          align-items: center;
          margin-top: 10px;
          .info-item-icon {
            .van-icon {
              width: 30px;
              font-size: 22px;
              color: #aaa;
              background-color: #ffffff;
            }
          }
          .info-item-icon-font {
            border: 1px solid #aaa;
            color: #aaaaaa;
            padding: 0 4px;
            border-radius: 5px;
          }
          .info-item-content {
            box-sizing: border-box;
            flex: 1;
            input {
              width: 100%;
              border: none;
              font-size: 15px;
              line-height: 30px;
              color: #333333;
              vertical-align: top;
              border-bottom: 1px solid #eee;
              text-align: right;
            }
          }
        }
        .sheet-info-items {
          display: flex;
          margin-top: 10px;
          .sheet-info-item + .sheet-info-item {
            margin-top: 0;
          }
        }
      }
      .sheet-rows {
        background-color: #eee;
        .sheet-rows-title {
          box-sizing: border-box;
          width: 100%;
          height: 25px;
          display: flex;
          justify-content: space-between;
          padding: 0 20px;
          background-color: #fff;
        }
        .sheet-rows-list {
          box-sizing: border-box;
          width: 100%;
          padding: 5px;
        }
      }
      .approved-reded {
        position: absolute;
        left: 25px;
        top: 165px;
        margin-top: -40px;
        z-index: 99999;
      }
    }
    .operation-wrapper {
      background-color: #fff;
      width: 100%;
      height: 55PX;
      display: flex;
      align-items: center;
      border-top: 1PX solid #f2f2f2;
      justify-content: space-between;
      padding-right: 20PX;
      box-sizing: border-box;
      .operation-input {
        width: 190PX;
        height: 50PX;
        flex:6;
        display: flex;
        justify-content: start;
        align-items: center;
        padding-left: 10PX;
        input {
          width: 190PX;
          font-size: 14PX;
          border-style: none;
          border-bottom: 1PX solid #eee;
          background-color: #fff;
          height: 35PX;
        }
      }
      .operation-btn {
        flex: 1;
        button {
          background-color: transparent;
          border: none;
          width: 50PX;
          height: 50PX;
        }
      }
    }
    .sheet-total-info {
      box-sizing: border-box;
      padding: 10px;
      width: 100%;
      display: flex;
      flex-direction: column;
      color: #aaaaaa;
      .total-info-items {
        display: flex;
        justify-content: space-between;
      }
      .total-into-item {
        padding: 5px 0;
        display: flex;
      }
    }
  }
  .swipe-right {
    height: 100%;
  }
}
.color-ffcccc {
  height: 45px;
  border-radius: 12px;
  background-color: #ffcccc;
}
.settlement-wrapper {
  width: 100%;
  height: 100%;
  box-sizing: border-box;
  background-color: #fff;
  .settlement-content {
    width: 100%;
    height: 100%;
    box-sizing: border-box;
    overflow-y: auto;
    padding: 20px 10px;
    display: flex;
    flex-direction: column;
    .settlement-btns {
      border-top: 1px #eee solid;
      margin-top: 10px;
      width: 100%;
      padding-top: 10px;
      button {
        min-width: 90px;
        height: 45px;
        border-radius: 12px;
        margin-right: 40px;
      }
    }
  }
}
.other_operate {
  width: 100%;
  height: auto;
  .other_operate_content {
    height: 40px;
    vertical-align: top;
    margin-bottom: 15px;
    display: flex;
    align-items: center;
    justify-content: center;
    button {
      width: 100px;
      height: 100%;
      vertical-align: top;
      margin: 0 15px;
    }
  }
}
.current-display-list-wrapper {
  width: 100%;
  margin-top: 30px;
  display: flex;
  flex-direction: column;
  box-sizing: border-box;
  padding: 0 10px;
  .current-display-item {
    width: 100%;
    display: flex;
    flex-direction: column;
    color: #aaa;
    .current-time {
      display: flex;
      justify-content: space-between;
    }
    .current-other {
      display: flex;
      justify-content: space-between;
    }
  }
  .current-display-item + .current-display-item {
    border-top: 1px dotted #aaa;
    padding: 5px 0;
  }
}
.lowItem {
  width: 300px;
  height: auto;
  overflow: hidden;
  padding: 10px;
  h4 {
    height: 40px;
    font-size: 16px;
    font-weight: 500;
    color: #333333;
    display: flex;
    align-items: center;
    justify-content: center;
  }
  .lowItem_ul {
    height: auto;
    overflow: hidden;
    margin-bottom: 10px;
    li {
      height: auto;
      overflow: hidden;
      padding: 10px;
      font-size: 14px;
      display: flex;
      align-items: center;
      justify-content: space-between;
      border-bottom: 1px solid #f2f2f2;
    }
    li:last-child {
      border-bottom: none;
    }
  }
}
</style>



