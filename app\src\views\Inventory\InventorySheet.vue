<template>
  <div class="wrapper">
    <div class="wrapper-head">
      <van-nav-bar
        left-arrow
        safe-area-inset-top
        title="盘点单"
        @click-left="goback"
      >
        <template #right>
          <div class="submitSalesSlip" @click="handleSubmitClick">
            <svg width="30px" height="30px" style="margin:2px 2px 0 0 " stroke-width="1.3" class="black">
              <use :xlink:href="'#icon-plane'"></use>
            </svg>
          </div>
        </template>
      </van-nav-bar>
    </div>
    <div class="content" style="padding: 10px 10px 60px 10px;">
      <div class="content-query" style="padding: 10px 15px 3px 15px;">
        <div class="content-query-item">
          <van-icon name="wap-home-o" @click="onSelectBranch" />
          <input style="padding: 0 10px 4px 0px;" type="text" v-model="sheet.branch_name" placeholder="仓库" readonly @click="onSelectBranch" />
        </div>
      </div>
      <div class="public_query_title" v-if="sheet.sheet_no" style="margin-bottom: 1px">
        <div class="public_query_title_t" style="padding: 10px 10px 0;">
          <span>{{ sheet.sheet_no }}</span>
          <span>{{ sheet.approve_time }}</span>
        </div>
      </div>
      <ConcaveDottedCenter />
      <div class="content-sheet-rows">
        <InventorySheetRows ref="InventorySheetRows" :sheet="sheet" @handleItemDelete="handleItemDelete"
          @handleSheetRowsSort="handleSheetRowsSort" @handleItemEdit="handleItemEdit" />
        <InventorySheetRowsTotalInformation ref="InventorySheetRowsTotalInformation" :sheet="sheet" />
      </div>
    </div>
    <div class="footer">
      <input class="footer_input" id="codes" type="text" style="padding-left: 3px;" v-model="searchStr" @keydown="onSearchInputKeyDown($event)" placeholder="名称/简拼/条码/货号"
        :disabled="!!sheet.approve_time" :style="{ backgroundColor: sheet.approve_time ? '#f2f2f2' : '' }" />
      <van-button class="footer_iconBt" :disabled="sheet.approve_time !== ''" @click="btnClassView_click">
        <svg width="35px" height="35px" fill="#F56C6C">
          <use xlink:href="#icon-add"></use>
        </svg>
      </van-button>
      <van-button
        class="footer_iconBt"
        type="info"
        @click="btnScanBarcode_click"
      >
        <svg width="30px" height="30px" fill="#555">
          <use xlink:href="#icon-barcodeScan"></use>
        </svg>
      </van-button>
    </div>
    <van-popup v-model="bPopupBranchPicker" round position="bottom">
      <van-picker show-toolbar title="选择仓库" :columns="branchList" value-key="branch_name"
        @cancel="bPopupBranchPicker = false" @confirm="onConfirmBranch" />
    </van-popup>
    <van-popup v-model="showUnsubmitedSheets"  round>
      <div class="lowItem" style="width: 320px;">
        <h4>点击打开未提交单据</h4>
        <ul class="lowItem_ul">
          <li v-for="(item, index) in unsubmitedSheets" :key="index">
            <div class="lowItem_ull" @click="onUnsubmitedSheetSelected(item)">
              {{ item.branch_name }}
            </div>
            <div class="lowItem_ulr" @click="onUnsubmitedSheetSelected(item)">
              {{ item.saveTime }}
            </div>
            <div  class="btn-delete" @click="onUnsubmitedSheetDelete(index)">
              删除
            </div>
          </li>
        </ul>
        <van-button type="default" @click="showUnsubmitedSheets = false"
          >新建单据
        </van-button>
      </div>
    </van-popup>
    <van-popup v-model="popupEditSheetRows" :close-on-click-overlay="false" position="bottom"
      :style="{ height: '440px' }">
      <InventoryEditSheetRows ref="editSheetRows" @closeEdit="closeEdit" :sheet="sheet"
        :editSheetRowsInfo="editSheetRowsInfo" />
    </van-popup>
    <van-popup v-model="popupAddInventorySheetRow" :lazy-render="true" position="bottom"
      style="height:100vh;overflow:hidden">
      <InventoryMultiSelect v-if="popupAddInventorySheetRow" ref="multiSelect" :sheet="sheet" @closePage="closePage"
        @closeSingleChoicePage="closeSingleChoicePage">
      </InventoryMultiSelect>
    </van-popup>
    <van-popup v-model="showSubmitPopup" duration="0.4" :style="{ height: '100%', width: '80%' }" class="van_popup" position="right">
     <div class="other_operate">  <div style="height:30px;border-top:1px solid #ccc"></div>
        <van-field v-model="sheet.make_brief" label="备注" style="white-space: nowrap;" label-width="40px" placeholder="请输入备注"
          :disabled="sheet.approve_time ? true : false" />
        <div style="height:30px"></div>
        <div class="other_operate_content">
          <button v-if="canMake" :disabled="sheet.approve_time !== ''" @click="handleSheetSave"
            style="height: 45px;border-radius:12px;background-color: #ffcccc;">保存</button>
          <button v-if="canApprove" :disabled="sheet.approve_time !== ''" @click="handleSheetApprove"
            style="height: 45px;border-radius:12px;background-color: #ffcccc;">审核</button>
        </div>
        <div class="other_operate_content">
          <button style="height: 45px;border-radius:12px" :style="{
            color: sheet.red_flag !== '' ? '#ff9999' : '#ee0a24',
            borderColor: sheet.red_flag !== '' ? '#ff9999' : '#ee0a24',
          }" v-if="canRed && sheet.approve_time" :disabled="sheet.red_flag !== ''"
            @click="handleSheetRed">红冲</button>
          <button v-if="sheet.sheet_id && !sheet.approve_time && canDelete" style="height: 45px;border-radius:12px"
            @click="handleSheetDelete">删除</button>
          <button @click="btnGotoPrintView_click()" v-if="canPrint" style="height: 45px;border-radius:12px"
            :disabled="(sheetStatusForPrint === 'saved' && !sheet.sheet_id) || (sheetStatusForPrint === 'approved' && !sheet.approve_time) || isPrinting">打印</button>
        </div>
        <template v-if="canPrint">
          <van-divider>打印条码</van-divider>
          <van-radio-group v-model="printBarcodeStyle"
            style="font-size: 12px; margin-left: 0; display: flex; justify-content: center;padding-top: 10px;">
            <van-radio name="noBarcode" style="margin-right: 10px">不打印</van-radio>
            <van-radio name="actualUnit" style="margin-right: 10px">实际单位</van-radio>
            <van-radio name="smallUnit" style="margin-right: 10px">小单位</van-radio>
          </van-radio-group>
          <van-checkbox v-if="printBarcodeStyle === 'actualUnit' || printBarcodeStyle === 'smallUnit'" shape="square"
            v-model="printBarcodePic" icon-size="20px"
            class="print-barcode"
            style="margin-left: 50px; margin-top: 10px">打印条码图</van-checkbox>
        </template>
        <van-divider style="margin: 20px 0" @click="moreOptions = !moreOptions">
          <svg v-if="moreOptions" width="26px" height="26px" fill="#d8d8d8">
            <use xlink:href="#icon-double_arrow"></use>
          </svg>

          <svg
            style="-webkit-transform: rotate(180deg);"
            v-else
            width="26px"
            height="30px"
            fill="#d8d8d8"
          >
            <use xlink:href="#icon-double_arrow"></use>
          </svg>
        </van-divider>
        <template v-if="moreOptions">
          <div class="other_operate_content">
            <van-button type="default" :disabled="IsSubmiting || sheet.approve_time !== ''" style="border-radius:12px;"
              @click="onEmpty">清空</van-button>
            <van-button style="border-radius:12px;" type="default" @click="btnCopySheet_click">复制</van-button>
            <van-button type="default" style="border-radius:12px;" @click="btnOut">退出</van-button>
          </div>
        </template>

        <!-- 打印详情界面 -->
      <transition name="hide-show">
        <div v-if="isInPrintView" class="sales_more">
          <div v-if="this.useTmp" class="print-template-wrapper" style="margin: 0px 0.55rem;">
            <div style="margin-top: 20px;margin-bottom: 10px;margin-left: 10px; text-align: left;">打印模板</div>
            <div class="radio-tmp-position">
              <!-- <div v-for="(item,i) in tmplist" :key="i" style="display: inline-block; margin: 5px 0; text-align: left; width: 50%;"> -->
              <div v-for="(item, i) in tmplist" :key="i" class="radio-tmp-style">
                <!-- //appearance: none; -->
                <input type="radio" name="template" :id="i" :value="i" v-model="selectedTmpId" style="position: relative; margin-left: 10px;" class="radio-tmp-type" />
                <label :for="i" class="radio-tmp-name">
                  {{ item.name }}
                </label>
              </div>
            </div>
          </div>
          <div v-else style="height:170px;" />
          <!-- 搞个选择打印机 -->
          <div class="select-printer">
            <van-cell-group inset style="width:100%">
              <van-cell is-link title="打印机" title-style="flex: inherit;" :value="defaultPrinter.name" @click="showPrinterSelectionPage" />
            </van-cell-group>
          </div>
          <div v-if="!this.useTmp && arrCompanyNamesToPrint && arrCompanyNamesToPrint.length" style="margin: 0px 0.55rem;">
            <div class="print-company-title" style="margin-top:10px;margin-bottom: 10px;margin-left: 10px; text-align: left;">公司名称</div>
            <div class="radio-tmp-position setHeight">
              <!-- <div v-for="(item,i) in tmplist" :key="i" style="display: inline-block; margin: 5px 0; text-align: left; width: 50%;"> -->
              <div v-for="(name, i) in arrCompanyNamesToPrint" :key="i" class="radio-tmp-style">
                <!-- //appearance: none; -->
                <input type="radio" name="companyName" :id="i" :value="name" v-model="companyNameToPrint" style="position: relative; margin-left: 10px;" class="radio-tmp-type" />
                <label :for="i" class="radio-tmp-name">
                  {{ name }}
                </label>
              </div>
            </div>
          </div>
          <div style="margin-top: 10px;">
            <van-checkbox-group v-model="printBarcodeStyleForSale" style="display: flex;  margin: 20px;  margin-left: 1px; padding-top: 0px; ">
              <div style="margin-left: 8px;margin-right:20px; line-height: 30px;">条码</div>
              <van-checkbox name="actualUnit" style="margin-right: 10px;">实际单位</van-checkbox>
              <van-checkbox name="smallUnit" style="margin-right: 10px">小单位</van-checkbox>
            </van-checkbox-group>
            <van-checkbox shape="square" v-model="printBarcodePic" style="margin-left: 56px;">打印条码图</van-checkbox>
            <div class="print-count" v-if="defaultPrinter.type !== 'cloud'" style="margin-bottom: 15px;">
              <button style="font-size: 20px; background: white;color:#777; min-width: 40px;" @click="printCount = printCount < 5 ? printCount + 1 : 5">+</button>
              <div style="display: inline-block; margin-right: 5px; color: #8d8d8d;">{{ printCount }}次</div>
              <button style="font-size: 20px; background: white;color:#777; min-width: 40px;" @click="printCount = printCount > 1 ? printCount - 1 : 1">-</button>
            </div>
          </div>
          <div style="margin-top: 25px;">
            <van-button class="print-btns" style="flex:1; margin-right:5%;" type="default" @click="isInPrintView = false">返回</van-button>
            <van-button class="print-btns" style="flex:1;margin-left:5%;  background-color: #ffcccc;" type="default" @click="btnPrint_click" :disabled="isPrinting">确认打印</van-button>
          </div>
        </div>
      </transition>
      </div>
    </van-popup>
    <van-popup v-model="PopupPrintersSelect" position="bottom">
      <van-picker title="选择打印机" show-toolbar value-key="name" :columns="printers" :default-index="defaultPrinterIndex" @confirm="confirmPrinterSelectionChange" @cancel="hidePrinterSelectionPage" />
    </van-popup>
  </div>
</template>

<script>
import {
  Cell,
  CellGroup,
  CheckboxGroup,
  NavBar,
  Field,
  Icon,
  Picker,
  Toast,
  Button,
  Dialog,
  RadioGroup,
  Radio,
  Divider,
  Checkbox,
} from "vant";
import {
  GetBranchList,
  AppGetTemplate,
  AppGetSheetToPrint,
  AppGetSheetToPrint_Post,
  AppCloudPrint_sheetTmp,
  AppSheetInventoryLoad,
  AppSheetInventorySubmit,
  AppSheetInventoryRed,
  AppSheetInventoryDelete,
  AppSheetInventorySave,
  AppSheetInventoryGetItemList,
  AppSheetInventoryGetStockQtyList,
} from "../../api/api";
import InventorySheetRows from "./InventorySheetRows";
import ConcaveDottedCenter from "../components/ConcaveDottedCenter";
import InventoryMixin from "./InventoryMixin";
import mixins from "../SaleSheet/sheetMixin/mixin";
import InventorySheetRowsTotalInformation from "./InventorySheetRowsTotalInformation";
import InventoryEditSheetRows from "./InventoryEditSheetRows";
import Printing from "../Printing/Printing";
import InventoryMultiSelect from "./InventoryMultiSelect";

export default {
  name: "InventorySheet",
  mixins: [InventoryMixin, mixins],
  components: {
    "van-cell": Cell,
    "van-cell-group": CellGroup,
    "van-checkbox-group": CheckboxGroup,
    InventoryMultiSelect,
    InventoryEditSheetRows,
    InventorySheetRowsTotalInformation,
    InventorySheetRows,
    "van-nav-bar": NavBar,
    "van-icon": Icon,
    "van-button": Button,
    "van-picker": Picker,
    ConcaveDottedCenter,
    "van-radio-group": RadioGroup,
    "van-radio": Radio,
    "van-divider": Divider,
    "van-checkbox": Checkbox,
    "van-field": Field,
  },
  data() {
    return {
      selectedTmpId: 0, // 选择模板自动赋值
      companyNameToPrint: '',
      printCount: 1,
      printBarcodeStyleForSale: [],
      arrCompanyNamesToPrint: [],
      printers: [],
      PopupPrintersSelect: false,
      tmplist: [],
      defaultPrinterIndex: 0,
      defaultPrinter: {},
      isPrinting: false,
      useTmp: false,
      isInPrintView: false,  // false审核界面 true打印详情
      sheet: {
        sheetType: "PD",
        sheet_no: "",
        sheet_id: "",
        approve_time: "",
        make_time: "",
        sheetRows: [],
        make_brief: "",
      },
      branchList: [],
      bPopupBranchPicker: false,
      IsSubmiting: false,
      searchStr: "",
      showUnsubmitedSheets: false,
      unsubmitedSheets: [],
      editSheetRowsInfo: [],
      popupEditSheetRows: false,
      showSubmitPopup: false,
      printBarcodeStyle: "noBarcode",
      moreOptions: false,
      printBarcodePic: false,
      isPrinting: false,
      popupAddInventorySheetRow: false,
    };
  },
  mounted() {
    this.loadSheet();
    this.handleRegisterSayCode();
    this.loadBranches();

    this.companyNameToPrint = this.$store.state.companyNameToPrint || ''
    var companyNames = window.getSettingValue('companyNamesToPrint')
    if (companyNames) {
      this.arrCompanyNamesToPrint = companyNames.split('\n')
    }
    //this.changOrderPayWay();
    this.printBarcodeStyleForSale = this.$store.state.printBarcodeStyleForSale
    if (!this.printBarcodeStyleForSale) {
      if (this.$store.state.printBarcodeStyle == 'noBarcode' || !this.$store.state.printBarcodeStyle) this.printBarcodeStyleForSale = [];
      else this.printBarcodeStyleForSale = [this.$store.state.printBarcodeStyle];
    }

    // 获取当前公司的打印机列表
    let printers = window.getCompanyStoreValue('c_printers')
    // 兼容旧版本
    if (!printers) {
      printers = []
      const printer = {
        id: this.$store.state.printerID ? this.$store.state.printerID : this.$store.state.printerID,
        name: this.$store.state.printer_name || '蓝牙打印机',
        type: this.$store.state.printerType || 'bluetooth',
        kind: this.$store.state.printer_kind || 'tiny',
        paperSize: this.$store.state.paperSize || '58',
        bluetoothID: this.$store.state.printerID,
        bluetoothType: 'classic',
        bluetoothDataStyle: this.$store.state.bluetoothSendDataStyle,
        useTemplate: this.$store.state.useTemplateToPrint === true || this.$store.state.printStyle == 'template',

        brand: this.$store.state.printer_brand || '',
        cloudID: this.$store.state.device_id || '',
        cloudCode: this.$store.state.check_code || '',

        isDefault: true
      }
      printers.push(printer)
    }
    this.printers = printers

    for (let _i = 0; _i < printers.length; _i++) {
      const _printer = printers[_i]
      if (_printer.isDefault) {
        this.defaultPrinter = _printer;
        this.defaultPrinterIndex = _i;
        break;
      }
    }
  },
  activated() {
    this.$refs.InventorySheetRows.handleUpdate();
    this.$refs.InventorySheetRowsTotalInformation.handleUpdate();
  },
  beforeRouteLeave(to, from, next) {
    // if (to.name !== 'InventorySelectItems' && this.sheet.branch_name && this.sheet.sheet_no === '') {
    //  this.saveCurSheetToCache(this.sheet)
    // }
    next();
  },
  watch: {
    "sheet.sheetRows": {
      handler: function () {
        this.handleSelectedSheetRows('PD')
        if (this.sheet.branch_name && this.sheet.sheet_id === '') {
          this.saveCurSheetToCache(this.sheet)
        }
      },
      deep: true,
    },
    popupEditSheetRows: {
      handler: function(newVal) {
        if (newVal === false) {
          this.$refs.InventorySheetRows.handleUpdate();
          this.$refs.InventorySheetRowsTotalInformation.handleUpdate();
        }
      },
      deep: true,
    },
  },
  computed: {
    canEdit() {
      if (this.sheet.approve_time) return false;
      if (!this.canApprove && this.sheet.sheet_id) return false;
      return true;
    },
    canRed() {
      return hasRight("stock.sheetInvent.red");
    },
    canMake() {
      return hasRight("stock.sheetInvent.make");
    },
    canDelete() {
      return hasRight("stock.sheetInvent.delete");
    },
    canPrint() {
      return hasRight("stock.sheetInvent.print");
    },
    canApprove() {
      return hasRight("stock.sheetInvent.approve");
    },
    allowPrintBeforeApprove() {
      return hasRight("delicacy.allowPrintBeforeApprove.value");
    },
    allowPrintBeforeSave() {
      return hasRight("delicacy.allowPrintBeforeSave.value");
    },
    sheetStatusForPrint() {
      return window.getRightValue("delicacy.moveSheetStatusForPrint.value");
    },
  },
  methods: {
    goback() {
      // eslint-disable-next-line no-undef
      myGoBack(this.$router);
    },
    async loadSheet() {
      const sheetID = this.$route.query.sheetID || "";
      if (sheetID) {
        this.showUnsubmitedSheets = false;
      }
      let params = {
        sheetID: sheetID,
      };
      await AppSheetInventoryLoad(params)
        .then((res) => {
          if (res.result !== "OK") {
            // this.sheet.sheetType=sheetType
            // Toast.fail("请求失败")
            return;
          }
          if (!res.sheet) {
            Toast.fail("无表单数据");
            return;
          }
          this.sheet = res.sheet;
          this.handleSheetRowsForLoad();
          this.$store.commit("attrOptions", res.attrOptions);
          const sheetID = this.sheet.sheet_id;
          if (!sheetID) {
            this.showSheetsFromCache();
          }
          // 增加无产期初始化
          this.sheet.sheetRows.forEach((r) => {
          if (r.batch_level && !r.produce_date) {
              r.produce_date = '无产期'
            }
          })
        })
        .catch((err) => {
          console.log("???");
          Toast(err);
        });
    },
    onSelectBranch() {
      if (this.sheet.approve_time) {
        return;
      }
      this.bPopupBranchPicker = true;
    },
    // 根据权限获取仓库列表
    loadBranches() {
      this.showCustomer = false;
      let params = {};
      GetBranchList(params).then((res) => {
        this.branchList = [];
        if (res.result === "OK") {
          for (let i = 0; i < res.data.length; i++) {
            let branch = res.data[i];
            let branchValid = window.hasBranchSheetRight(
              branch.branch_id,
              this.sheet.sheetType
            );
            // if (branchValid) {
            //   this.branchList.push(branch);
            // }
            if (branchValid) {
              let branchPosition = JSON.parse(branch.branch_position)
              let newBranchPosition = []
              branchPosition.forEach(e=>{
                if(e.branch_position !=="0"){
                  newBranchPosition.push(e)
                }
              })
              this.branchList.push({
                branch_id:branch.branch_id,
                branch_name:branch.branch_name,
                branch_position:newBranchPosition,
                branch_type:branch.branch_type
              })
            }
          }
          if(this.sheet.branch_id){
            this.branchList.some(b=>{
              if(b.branch_id.toString() == this.sheet.branch_id.toString()){
                this.$store.commit("setCurBranchPositionList",b.branch_position)
                return true
              }
            })
          }
          // this.$store.commit("setBranchList",this.branchList)
          return this.branchList;
        }
      });
    },
    // 确认仓库
    onConfirmBranch(value) {
      this.sheet.branch_id = value.branch_id;
      this.sheet.branch_name = value.branch_name;
      this.branchList.some(b=>{
        if(b.branch_id == value.branch_id){
          this.$store.commit("setCurBranchPositionList", b.branch_position)
          return true
        }
      })
      this.bPopupBranchPicker = false;
      // var sheetBranches=this.$store.state.sheetBranches;//[sheetType]
      // sheetBranches[this.sheet.sheetType]=
      //     {
      //       branch_id: this.sheet.branch_id,
      //       branch_name: this.sheet.branch_name
      //     }
      // this.$store.commit("sheetBranches", sheetBranches)
    },
    handleRegisterSayCode() {
      window.sayCode = (result) => {
        this.pageSayCode(result);
      };
    },
    pageSayCode(result) {
      this.searchStr = result;
      this.queryScan();
    },
    onSearchInputKeyDown(e) {
      if (e.keyCode === 13) {
        this.queryScan();
      }
    },
    queryScan() {
      if (!this.sheet.branch_id) {
        Toast.fail("请选择仓库"); return
      }
      console.log(this.searchStr)
      this.$store.commit("currentSheet", this.sheet);
      let params = {
        searchStr: this.searchStr,
        showStockOnly: false,
        classID: "",
        branchID: this.sheet.branch_id,
        pageSize: 20,
        startRow: 0,
        brandIDs: "",
      };
      AppSheetInventoryGetItemList(params).then((res) => {
        if (res.result === "OK") {
          if (res.data.length === 0) {
            Toast("未找到对应商品");
            return;
          } else if (res.data.length === 1) {
            let item = res.data[0];
            item.item_images = this.handleImage(item.item_images);
            console.log(item);
            this.$store.commit("distinctStockFlag", false);
            this.$store.commit("shoppingCarFinish", false);
            let itemObj = JSON.parse(JSON.stringify(item));
            itemObj.isSelectFlag = false;
            itemObj.singleChoice = true;
            itemObj.distinctStockFlag = false;
            if (item.b_unit_no) itemObj.b_unit_qty = ""; // 录入数量
            if (item.m_unit_no) itemObj.m_unit_qty = ""; // 录入数量
            itemObj.s_unit_qty = ""; // 录入数量
            itemObj.remark = ""; // 录入数量
            if (itemObj.mum_attributes) {
              if (!itemObj.mum_attributes.forEach)
                itemObj.mum_attributes = JSON.parse(itemObj.mum_attributes);
              if (itemObj.mum_attributes.find((attr) => attr.distinctStock)) {
                this.$store.commit("distinctStockFlag", true);
                itemObj.distinctStockFlag = true;
              }
            }
            this.popupAddInventorySheetRow = true;
            this.$store.commit("multiSelectOpenFlag", true);
            this.$store.commit("attrShowFlag", false);
            setTimeout(() => {
              this.$refs.multiSelect.loadData([itemObj]);
            }, 310);
          } else if (res.data.length > 1) {
            this.btnClassView_click();
          }
        }
      });
    },

    async btnScanBarcode_click() {
      try {
        const result = await this.scanBarcodeNew({
          unit_type: 'inventory'
        })

        if (!result.code) {
          return
        }

        this.searchStr = result.code
        this.btnClassView_click()
      } catch (error) {
        console.error('扫码失败:', error)
        this.$toast('扫码失败，请重试')
      }
    },
    btnClassView_click() {
      if (!this.sheet.branch_id) {
        Toast.fail("请选择仓库");
      } else {
        let query = {
          searchStr: this.searchStr || "",
          sheet: this.sheet,
        };
        this.$store.commit("currentSheet", this.sheet);
        this.$router.push({ path: "/InventorySelectItems", query: query });
        this.searchStr = "";
      }
    },
    handleItemDelete(item, index) {
      this.sheet.sheetRows.splice(index, 1);
    },
    handelGetSheet() {
      //let tempTemp = {...this.sheet}
      this.sheet.IsFromWeb = false;
      // tempTemp.SheetRows = this.sheet.sheetRows
      this.sheet.cost_price_type = "3";
      if (this.$store.state.operInfo.setting)
        this.sheet.cost_price_type = this.$store.state.operInfo.setting.costPriceType;

      if (!this.sheet.seller_id)
        this.sheet.seller_id = this.$store.state.operInfo.oper_id;

      // this.sheet.seller_name = this.$store.state.operInfo.oper_name
      this.sheet.maker_name =
        this.sheet.maker_name === ""
          ? this.$store.state.operInfo.oper_name
          : this.sheet.maker_name;
      this.sheet.operKey = this.$store.state.operKey;
      this.sheet.sheetType = "PD";
      // this.sheet.sheet_id = this.sheet.sheet_id
      this.sheet.sheet_id_red_me = "";
      // this.sheet.sheet_no = ''
      this.sheet.sheet_type = "SHEET_INVENT_INPUT";
      this.sheet.buy_amount = 0;
      this.sheet.cost_amount_avg = 0;
      this.sheet.wholesale_amount = 0;
      this.sheet.sheetRows.forEach((item) => {
        if (item.difference_qty === undefined) {
          item.difference_qty = item.real_quantity - item.stock_qty;
        }
        if (item.sys_quantity === "") item.sys_quantity = 0;
        if (item.real_quantity === "") item.real_quantity = 0;
        this.sheet.buy_amount +=
          Number(item.difference_qty) * Number(item.buy_price);
        this.sheet.cost_amount_avg +=
          Number(item.difference_qty) * Number(item.cost_price_avg);
        this.sheet.wholesale_amount +=
          Number(item.difference_qty) * Number(item.wholesale_price);
      });
      this.sheet.buy_amount = toMoney(this.sheet.buy_amount);
      this.sheet.cost_amount_avg = toMoney(this.sheet.cost_amount_avg);
      this.sheet.wholesale_amount = toMoney(this.sheet.wholesale_amount);
    },
    handleItemEdit(item) {
      if (!this.canEdit) return;
      this.editSheetRowsInfo = [item];
      this.popupEditSheetRows = true;
      setTimeout(() => {
        this.$refs.editSheetRows.loadData();
      }, 350);
    },
    closeEdit() {
      this.$refs.InventorySheetRows.handleUpdate();
      this.$refs.InventorySheetRowsTotalInformation.handleUpdate();
      this.popupEditSheetRows = false;
      this.$refs.editSheetRows.clearState();
    },
    handleSubmitClick() {
      if (!this.sheet && this.sheet.sheetRows.length <= 0) {
        Toast.fail("请添加商品");
        return;
      }
      this.showSubmitPopup = true;
    },
    async handleSheetSave() {
      // 保存单据
      for (let i = 0; i < this.sheet.sheetRows.length; i++) {
          let r = this.sheet.sheetRows[i]
          if (r.batch_level && !r.produce_date) {
            Toast.fail(`请输入第${i + 1}商品【${r.item_name}】的生产日期，或选择“无产期”`)
            return
          }
        }
      if (this.sheet.branch_id === "") {
        Toast.fail("请选择仓库");
        return;
      }
      let message = await this.handleRefreshGetStockQtyList();
      console.log("message", message);
      if (message !== "") {
        this.handelGetSheet();
        Toast.fail(message);
        this.$refs.InventorySheetRows.handleUpdate();
        this.$refs.InventorySheetRowsTotalInformation.handleUpdate();
        return;
      }
      this.handelGetSheet();
      if (this.sheet.sheetRows.length === 0) {
        Toast.fail("请选择商品");
      } else {
        this.IsSubmiting = true;
        this.sheet.inventory_type = "0";//设置一下盘点单的类型（0：部分盘点，1：整仓盘点），不然电脑端审核的时候会提示再选一遍盘点类型
        //2024-07-18修改的，此时手机端只支持部分盘点
        console.log(this.sheet);
        AppSheetInventorySave(this.sheet)
          .then((res) => {
            if (res.result === "OK") {
              this.sheet.sheet_no = res.sheet_no;
              this.sheet.sheet_id = res.sheet_id;
              this.sheet.make_time = res.currentTime;
              setTimeout(() => {
                this.removeCurSheetFromCache();
              }, 300);
              Toast.success("保存成功");
            } else {
              Toast.fail("保存失败");
            }
          })
          .catch((err) => {
            console.log(err);
          });
      }
    },
    async handleSheetApprove() {
      for (let i = 0; i < this.sheet.sheetRows.length; i++) {
          let r = this.sheet.sheetRows[i]
          if (r.batch_level && !r.produce_date) {
            Toast.fail(`请输入第${i + 1}商品【${r.item_name}】的生产日期，或选择“无产期”`)
            return
          }
        }
      Dialog.confirm({
        title: "审核单据",
        message: "请确认是否审核?",
        width:"320px"
      }).then(async () => {
        let message = await this.handleRefreshGetStockQtyList();
        if (message) {
          this.handelGetSheet();
          Toast.fail(message);
          this.$refs.InventorySheetRows.handleUpdate();
          this.$refs.InventorySheetRowsTotalInformation.handleUpdate();
          return;
        }
        this.handelGetSheet();
        if (!this.sheet.approver_name) {
          this.sheet.approver_name = this.$store.state.operInfo.oper_name;
        }
        this.IsSubmiting = true;
        console.log(this.sheet);
        AppSheetInventorySubmit(this.sheet).then((res) => {
          this.IsSubmiting = false;
          if (res.result === "OK") {
            this.sheet.sheet_no = res.sheet_no;
            this.sheet.sheet_id = res.sheet_id;
            this.sheet.make_time = res.currentTime;
            this.sheet.approve_time = res.approve_time;
            if (window.g_curSheetInList) {
              // window.g_curSheetInList.approve_time=res.approve_time
              window.g_curSheetInList.state = "approved";
            }
            this.removeCurSheetFromCache();
            Toast.success("审核成功");
          } else {
            Toast.fail(res.msg);
          }
        });
      });
    },
    handleSheetRed() {
      Dialog.confirm({
        title: "红冲单据",
        message: "请确认是否红冲",
        width:"320px"
      }).then(() => {
        let params = {
          operKey: this.$store.state.operKey,
          sheetID: this.sheet.sheet_id,
        };
        AppSheetInventoryRed(params).then((res) => {
          if (res.result === "OK") {
            Toast.success("红冲成功,即将退出该页面");
            this.removeCurSheetFromCache();
            setTimeout(() => {
              this.btnOut();
            }, 1000);
          } else {
            Toast.fail("红冲失败:" + res.msg);
          }
        });
      });
    },
    handleSheetDelete() {
      Dialog.confirm({
        title: "删除单据",
        message: "请确认是否删除",
        width:"320px"
      }).then(() => {
        // var delFunc;
        // delFunc = SheetMoveDelete;
        let params = {
          operKey: this.$store.state.operKey,
          sheet_id: this.sheet.sheet_id,
        };
        AppSheetInventoryDelete(params).then((res) => {
          if (res.result === "OK") {
            Toast.success("删除成功,即将退出该页面");
            this.removeCurSheetFromCache();
            setTimeout(() => {
              this.btnOut();
            }, 1000);
          } else {
            Toast.fail("删除失败:" + res.msg);
          }
        });
      });
    },
    showPrinterSelectionPage() {
      this.PopupPrintersSelect = true
    },
    hidePrinterSelectionPage() {
      this.PopupPrintersSelect = false
    },
    confirmPrinterSelectionChange(selectedPrinter) {
      this.useTmp = selectedPrinter.useTemplate
      this.printers.forEach((prt) => {
        if (prt.id === selectedPrinter.id) {
          prt.isDefault = true
        } else {
          prt.isDefault = false
        }
      })
      window.setCompanyStoreValue('c_printers', this.printers)
      // Toast.success('修改成功!')
      this.defaultPrinter = selectedPrinter
      for (let _i = 0; _i < this.printers.length; _i++) {
        const _printer = this.printers[_i]
        if (_printer.isDefault) {
          this.defaultPrinterIndex = _i;
          break;
        }
      }

      this.PopupPrintersSelect = false

      const printer_kind = selectedPrinter.kind
      if (this.useTmp) {
        if (this.templatesLoaded) {
          this.confirmTemplates(printer_kind)
        } else {
          this.loadPrintTemplates((data) => {
            this.confirmTemplates(printer_kind)
          })
        }
      }
    },
    /** 将模板加载到this.tmplist中,并执行验证 */
    confirmTemplates(printer_kind) {
      this.tmplist = printer_kind == 'tiny' ? this.printTemplatesTiny : this.printTemplates
      if (this.tmplist.length == 0) {
        var err = printer_kind == 'tiny' ? '没有可用的小票模板' : '没有可用的打印模板'
        Toast.fail(err); return
      }
    },
    loadPrintTemplates(successCb) {
      this.tmplist = []
      this.printTemplates = []
      this.printTemplatesTiny = []
      var params = {
        sheetType: this.sheet.sheet_type,
        clientID: this.sheet.supcust_id
      }
      AppGetTemplate(params).then(data => {
        var templateList = data.templateList
        for (let i = 0; i < templateList.length; i++) {
          const template = templateList[i]
          var inserttmp = {
            name: template.template_name,
            i: i,
            tmp: template
          }
          this.printTemplates.push(inserttmp)
          try {
            let tmp_tocheck = JSON.parse(template.template_content)
            if (tmp_tocheck.width <= 110) {
              this.printTemplatesTiny.push(inserttmp)
            }
          } catch {
            console.error('在解析模板的宽度时发生错误,inserttmp:', inserttmp)
          }
        }
        this.templatesLoaded = true
        if (successCb) {
          successCb(data)
        }
      })
    },
    // 跳转打印详情 判断是否使用模板与打印+渲染模板radio
    btnGotoPrintView_click() {
      this.isInPrintView = true
      // 获取默认打印机
      const defaultPrinter = window.getDefaultPrinter()
      // 判断是否为云打印
      var printer_kind = defaultPrinter.kind
      if (!printer_kind) {
        if (defaultPrinter.type == "cloud")
          printer_kind = '24pin'
        else
          printer_kind = 'tiny'
      }
      this.useTmp = defaultPrinter.useTemplate
      //获取模板并展示
      if (this.useTmp) {
        if (this.templatesLoaded) {
          this.confirmTemplates(printer_kind)
        } else {
          this.loadPrintTemplates((data) => {
            this.confirmTemplates(printer_kind)
          })
        }
      }
      this.isPrinting = false
    },
    btnPrint_click(){
      console.log("点击了确认打印的按钮")
      if(this.sheet.sheet_no && this.sheetHasChange && !this.sheet.approve_time){
        this.doSave(()=>{
          //this.handleSheetPrint()
          this.confirmPrint()
        })     
      }
      else{ 
        //this.handleSheetPrint()
        this.confirmPrint()
      }
    },
    // 最终打印方法
    async confirmPrint() {
      
      if (!this.useTmp) {
        this.$store.commit('companyNameToPrint', this.companyNameToPrint)
        console.log("confirmPrint：这里是不带模板打印的方法调用")
        this.print_noTemplate(this.printCount)
      } else {
        if (!this.tmplist || this.tmplist.length == 0) {
          Toast.fail("没有可用的打印模板")
        } else {
          var tmp
          try {
            tmp = this.tmplist[this.selectedTmpId].tmp
          }
          catch (e) {
            Toast.fail('打印错误' + e.message)
            return
          }
          console.log("选择的模板是：" + tmp.template_name)
          this.btnPrint_ByTemplate(tmp, this.printCount)
        }
      }
       setTimeout(()=>{
          this.sheetHasChange = false
      },100) 
    },
    //打印  不按照模板
    async print_noTemplate(printCount) {
      console.log("打印不按照模板")
      this.isPrinting = true
      var that = this
      var sheet_id = this.sheet.sheet_id
      var sheet = this.sheet
      console.log("打印的单据是：", sheet)
      const defaultPrinter = window.getDefaultPrinter()
      const printerType = defaultPrinter.type
      console.log("打印机类型是：", printerType)
      // 判断是否为云打印
      var printer_kind = defaultPrinter.kind
      if (!printer_kind) {
        if (printerType == "cloud")
          printer_kind = '24pin'
        else
          printer_kind = 'tiny'
      }
      console.log("打印种类是：",printer_kind)
      // 不使用模板打印(小票打印)的场合，直接发送打印请求
      if (printerType == "cloud") { // 云打印机 不使用次数
        console.log("进入云打印分支")
        var that = this
        var imageBase64 = "";
        if (that.$store.state.company_cachet)
          imageBase64 = "data:image/png;base64," + that.$store.state.company_cachet;
        sheet = JSON.parse(JSON.stringify(sheet))
        var errMsg = that.checkAttrQtyValid()
        if (errMsg) {
          Toast.fail(errMsg)
          return
        }

        let b64Esc_tinyTicket = await Printing._sheetToB64Esc(sheet, that.printBarcodeStyleForSale, that.printBarcodePic)

        var params = {
          operKey: this.$store.state.operKey,
          tmp: { width: '80', height: '100' }, // 后端接收directPrint的ESC指令时只会读取模板宽高 所以随意指定一个值
          sheet: sheet,
          printer_brand: defaultPrinter.brand,
          device_id: defaultPrinter.cloudID,
          check_code: defaultPrinter.cloudCode,
          cus_orderid: this.sheet.sheet_no,
          copies: "1",  // 云打印暂不启用
          //directPrint:true,
          escCommand: b64Esc_tinyTicket,
        }
        AppCloudPrint_escCmd(params).then(res => {
          console.log(res);
          this.showCloudPrintResult(res)
        })
        this.isPrinting = false
      }
      else { // 蓝牙打印机 启用多次打印
        console.log("进入蓝牙打印分支")
        const showTotalSheetInfo = this.$refs.InventorySheetRowsTotalInformation.showTotalSheetInfo;
        console.log("showTotalSheetInfo", showTotalSheetInfo)
        Printing.printInventorySheet(this.sheet,showTotalSheetInfo, this.printBarcodeStyle,this.printBarcodePic,null,(res) => {
            this.isPrinting = false;
            if (res.result === "OK") {
              Toast.success("打印成功");
            } else {
              Toast.fail(res.msg);
            }
          });
      }
    },
    // 按照模板打印
    btnPrint_ByTemplate(tmp,printCount) {
      console.log("打印按模板打印")
      if (this.sheet.sheet_id == "") {
        Toast.fail("请先保存单据");
        return;
      }
      var sTmp = tmp.template_content
      tmp = JSON.parse(tmp.template_content);
      var printTemplate = []

      if (sTmp.indexOf('"prepay_balance"')) printTemplate.push({ name: "prepay_balance" })
      if (sTmp.indexOf('"arrears_balance"')) printTemplate.push({ name: "arrears_balance" })
      if (sTmp.indexOf('"print_count"')) printTemplate.push({ name: "print_count" })

      var smallUnitBarcode = this.printBarcodeStyle === 'smallUnit'

      //get sheet
      var params = {
        sheetType: this.sheet.sheetType,
        //operKey: this.$store.state.operKey,
        sheet_id: this.sheet.sheet_id,
        smallUnitBarcode: smallUnitBarcode,
        printTemplate: JSON.stringify(printTemplate)
      }
      console.log("打印模板数据：", params)
      // 使用POST方法避免printTemplate参数过长导致的问题
      AppGetSheetToPrint_Post(params).then(data => {
        console.log("打印模板数据dddddddddd：", data)
        if (data.result != 'OK') {
          Toast.fail(data.msg)
        }
        else {
          var sheet = data.sheet

          // start print
          const defaultPrinter = window.getDefaultPrinter()
          if (defaultPrinter.type === "cloud") {
            var params = {
              operKey: this.$store.state.operKey,
              tmp: tmp,
              sheet: sheet,
              printer_brand: defaultPrinter.brand,
              device_id: defaultPrinter.cloudID,
              check_code: defaultPrinter.cloudCode,
              cus_orderid: this.sheet.sheet_no,
              copies: "1"
            }
            AppCloudPrint_sheetTmp(params).then(res => {
              console.log(res);
              if (res.result == "OK") {
                Toast.success("打印成功,请等待");
              }
              else if (res.return_msg == "打印成功" || (!res.printer_state && !res.return_msg)) {//兼容写法，适应老版本，要去掉
                Toast.success("打印成功,请等待");
              }
              else {
                if (res.msg) {
                  Toast.fail(res.msg);
                }
                else {
                  var result = res.printer_state ? res.printer_state : res.return_msg
                  Toast.fail(result);
                }

              }
            })
          }
          else { // 24bit print
            var params = {
              operKey: this.$store.state.operKey,
              tmp: tmp,
              sheet: sheet
            }
            var blocks = []
            AppSheetToEsc(params).then(res => {
              Printing.initPrinter()
              var imageBytes = this.base64ToUint8Array(res)
              blocks.push({ imageBytes: imageBytes })
              Printing.printSheetOrInfo(blocks, function c(e) {
                console.log(e)
                if (e.result == 'Error') { Toast.fail(e.msg) }
                else { Toast.success(e.msg) }
              })
            })
          }
        }
      })
    },
    handleSheetPrint() {
      console.log("盘点单原处理方法")
      this.isPrinting = true;
      const showTotalSheetInfo = this.$refs.InventorySheetRowsTotalInformation.showTotalSheetInfo;
      this.handelGetSheet();
      const defaultPrinter = window.getDefaultPrinter();
      if (defaultPrinter.type === "cloud") {
        Toast("云打印功能持续开发中，敬请期待，请选择蓝牙打印");
      } else {
        Printing.printInventorySheet(
          this.sheet,
          showTotalSheetInfo,
          this.printBarcodeStyle,
          this.printBarcodePic,
          null,
          (res) => {
            this.isPrinting = false;
            if (res.result === "OK") {
              Toast.success("打印成功");
            } else {
              Toast.fail(res.msg);
            }
          }
        );
      }
    },
    btnOut() {
      myGoBack(this.$router);
    },
    btnCopySheet_click() {
      logUserAction({
        tm: new Date().format("yyyy-MM-dd h:m"),
        act: "copyclick",
        sn: this.sheet.sheet_no,
      });
      Dialog.confirm({
        title: "复制单据",
        message: "请确认是否复制?",
        width:"320px"
      }).then(async () => {
        this.sheet.sheet_id = "";
        this.sheet.sheet_no = "";

        this.sheet.approve_time = "";
        this.sheet.approver_id = "";
        this.sheet.happen_time = "";
        this.sheet.make_time = "";
        this.sheet.maker_id = "";
        this.sheet.red_flag = "";
        this.sheet.red_sheet_id = "";
        this.sheet.print_count = "";
        this.sheet.submit_time = "";
        Toast("复制成功");
        let message = await this.handleRefreshGetStockQtyList();
        if (message !== "") {
          this.handelGetSheet();
          this.$refs.InventorySheetRows.handleUpdate();
          this.$refs.InventorySheetRowsTotalInformation.handleUpdate();
          Toast("已成功更新当前库存");
          return;
        }
        logUserAction({
          tm: new Date().format("yyyy-MM-dd h:m"),
          act: "copyed",
          sn: this.sheet.sheet_type,
        });
      });
    },
    onEmpty() {
      Dialog.confirm({
        title: "清空单据",
        message: "请确认是否清空",
        width:"320px"
      }).then(() => {
        this.sheet.sheet_no = "";
        this.sheet.sheet_id = "";
        this.sheet.approve_time = "";
        this.sheet.make_time = "";
        this.sheet.sheetRows = [];
        this.sheet.buy_amount = "";
        this.sheet.cost_amount_avg = "";
        this.sheet.wholesale_amount = "";
      });
    },
    closePage(flag) {
      this.popupAddInventorySheetRow = false;
      this.$store.commit("multiSelectOpenFlag", false);
      this.$store.commit("shoppingCarFinish", false);
      this.$store.commit("shoppingCarObj", {
        sheetType: this.moveType,
        clearFlag: true,
      });
    },
    closeSingleChoicePage() {
      this.$store.commit("multiSelectOpenFlag", false);
      this.$store.commit("shoppingCarFinish", false);
      this.popupAddInventorySheetRow = false;
      this.$forceUpdate();
    },
    handleSheetRowsSort() {
      // let tempSheetRow = JSON.parse(JSON.stringify(this.sheet.sheetRows))
      this.sheet.sheetRows = this.sheet.sheetRows.sort((a, b) =>
        a.item_name.localeCompare(b.item_name, "zh")
      ); //a~z 排序
      // this.sheet.sheetRows = []
      //this.sheet.sheetRows = tempSheetRow
      this.$forceUpdate();
    },
    handleSheetRowsForLoad() {
      if (this.sheet.sheetRows.length > 0) {
        // 处理打开单据的时候,盘盈盘亏信息
        this.sheet.sheetRows.forEach((sheetRow) => {
          sheetRow.real_quantity =
            (sheetRow.b_unit_qty || 0) * (sheetRow.b_unit_factor || 0) +
            (sheetRow.m_unit_qty || 0) * (sheetRow.m_unit_factor || 0) +
            (sheetRow.s_unit_qty || 0) * 1;
          const difference_qty = sheetRow.real_quantity - sheetRow.stock_qty;
          sheetRow.difference_qty = difference_qty;
          let cost_price = sheetRow.buy_price;
          let costPriceType = sheetRow.cost_price_type;
          if (costPriceType === "2") {
            cost_price = sheetRow.cost_price_avg;
          }
          if (difference_qty > 0) {
            let profit_wholesale_amount = toMoney(
              difference_qty * sheetRow.wholesale_price
            );
            let profit_cost_amount = toMoney(difference_qty * cost_price);
            let profit_buy_amount = toMoney(
              difference_qty * sheetRow.buy_price
            );
            sheetRow.profit_wholesale_amount = profit_wholesale_amount;
            sheetRow.profit_cost_amount = profit_cost_amount;
            sheetRow.profit_buy_amount = profit_buy_amount;
            sheetRow.loss_qty = "";
            sheetRow.loss_wholesale_amount = "";
            sheetRow.loss_cost_amount = "";
            sheetRow.loss_buy_amount = "";
            sheetRow.b_loss_qty = 0;
            sheetRow.m_loss_qty = 0;
            sheetRow.s_loss_qty = 0;
            const res = this.getQtyUnit(
              difference_qty,
              sheetRow.b_unit_no,
              sheetRow.b_unit_factor,
              sheetRow.m_unit_no,
              sheetRow.m_unit_factor,
              sheetRow.s_unit_no
            );
            sheetRow.b_profit_qty = res.b_qty;
            sheetRow.m_profit_qty = res.m_qty;
            sheetRow.s_profit_qty = res.s_qty;
            sheetRow.profit_qty = res.qtyUnit;
          } else if (difference_qty < 0) {
            let loss_wholesale_amount = toMoney(
              difference_qty * sheetRow.wholesale_price
            );
            let loss_cost_amount = toMoney(difference_qty * cost_price);
            let loss_buy_amount = toMoney(difference_qty * sheetRow.buy_price);
            sheetRow.loss_wholesale_amount = loss_wholesale_amount;
            sheetRow.loss_cost_amount = loss_cost_amount;
            sheetRow.loss_buy_amount = loss_buy_amount;
            sheetRow.profit_qty = "";
            sheetRow.profit_wholesale_amount = "";
            sheetRow.profit_cost_amount = "";
            sheetRow.profit_buy_amount = "";
            sheetRow.b_profit_qty = 0;
            sheetRow.m_profit_qty = 0;
            sheetRow.s_profit_qty = 0;
            var res = this.getQtyUnit(
              difference_qty,
              sheetRow.b_unit_no,
              sheetRow.b_unit_factor,
              sheetRow.m_unit_no,
              sheetRow.m_unit_factor,
              sheetRow.s_unit_no
            );
            sheetRow.b_loss_qty = res.b_qty;
            sheetRow.m_loss_qty = res.m_qty;
            sheetRow.s_loss_qty = res.s_qty;
            sheetRow.loss_qty = res.qtyUnit;
          } else if (difference_qty === 0) {
            sheetRow.profit_qty = "";
            sheetRow.profit_wholesale_amount = "";
            sheetRow.profit_cost_amount = "";
            sheetRow.profit_buy_amount = "";
            sheetRow.loss_qty = "";
            sheetRow.loss_wholesale_amount = "";
            sheetRow.loss_cost_amount = "";
            sheetRow.loss_buy_amount = "";
            sheetRow.b_profit_qty = 0;
            sheetRow.m_profit_qty = 0;
            sheetRow.s_profit_qty = 0;
            sheetRow.b_loss_qty = 0;
            sheetRow.m_loss_qty = 0;
            sheetRow.s_loss_qty = 0;
          }
        });
      }
    },
    async handleRefreshGetStockQtyList() {
      let items_id = [];
      let message = "";
      this.sheet.sheetRows.forEach((sheetRow) => {
        items_id.push(sheetRow.item_id);
      });
      let params = {
        branch_id: this.sheet.branch_id,
        items_id: items_id.join(","),
        stockOnly: false,
        operKey: this.$store.state.operKey,
      };
      /*await AppSheetInventoryGetStockQtyList(params, message).then(res => {
         if (res.result === "OK") {
           res.records.forEach(queryRow => {
             for (let i = 0; i < this.sheet.sheetRows.length; i++) {
               let sheetRow = this.sheet.sheetRows[i]
               if (queryRow.item_id === sheetRow.item_id && (sheetRow.stock_qty || 0) !== (queryRow.stock_qty||0)) {
                 this.sheet.sheetRows[i].current_qty = queryRow.current_qty;
                 this.sheet.sheetRows[i].stock_qty = queryRow.stock_qty;
                 this.sheet.sheetRows[i].sys_quantity= queryRow.stock_qty;
                 this.updateQueryItem(this.sheet.sheetRows[i])
                 message += this.sheet.sheetRows[i].item_name + ";"
               }
             }
           })
           console.log(message)
         }
       })
       .catch(err => {
         console.log(err);
         message=err
       })*/
      var res = await AppSheetInventoryGetStockQtyList(params, message);
      //计算盈亏
      
      if (res.result === "OK") {
        console.log(this.sheet.sheetRows);
        console.log(res.records);
        for (let j = 0; j < res.records.length; j++) {
          let queryRow = res.records[j];
          // let batch_level = queryRow.batch_level
          let produceDate = queryRow.produce_date?  queryRow.produce_date.slice(0, 10):""
          for (let i = 0; i < this.sheet.sheetRows.length; i++) {
            let sheetRow = this.sheet.sheetRows[i];
            if (
              queryRow.item_id === sheetRow.item_id &&
              sheetRow.produce_date === produceDate &&
                sheetRow.batch_no === queryRow.batch_no &&
                sheetRow.branch_position === queryRow.branch_position
            ) {
              if (
                (sheetRow.stock_qty.toString() || 0) !==
                (queryRow.stock_qty.toString() || 0)
              ) {
                this.sheet.sheetRows[i].current_qty = queryRow.current_qty;
                this.sheet.sheetRows[i].stock_qty = queryRow.stock_qty;
                this.sheet.sheetRows[i].sys_quantity = queryRow.stock_qty;
                this.updateQueryItem(this.sheet.sheetRows[i]);
                message += this.sheet.sheetRows[i].item_name + ";";
              }
            }
          }
          // if (batch_level !== "") {
          //   let produceDate = queryRow.produce_date === "" ? "" : queryRow.produce_date.slice(0, 10)
          //   for (let i = 0; i < this.sheet.sheetRows.length; i++) {
          //     let sheetRow = this.sheet.sheetRows[i]
          //     if (queryRow.item_id === sheetRow.item_id && (sheetRow.produce_date === produceDate && sheetRow.batch_no === queryRow.batch_no)) {
          //       console.log(queryRow)
          //       console.log(sheetRow)
          //       if ((sheetRow.stock_qty.toString() || 0) !== (queryRow.stock_qty.toString() || 0)) {
          //         this.sheet.sheetRows[i].current_qty = queryRow.current_qty;
          //         this.sheet.sheetRows[i].stock_qty = queryRow.stock_qty;
          //         this.sheet.sheetRows[i].sys_quantity = queryRow.stock_qty;
          //         this.updateQueryItem(this.sheet.sheetRows[i])
          //         message += this.sheet.sheetRows[i].item_name + ";"
          //       }
          //     }
          //   }
          // } else {
          //   for (let i = 0; i < this.sheet.sheetRows.length; i++) {
          //     let sheetRow = this.sheet.sheetRows[i]
          //     if (queryRow.item_id === sheetRow.item_id && (sheetRow.stock_qty || 0) !== (queryRow.stock_qty || 0)) {
          //       this.sheet.sheetRows[i].current_qty = queryRow.current_qty;
          //       this.sheet.sheetRows[i].stock_qty = queryRow.stock_qty;
          //       this.sheet.sheetRows[i].sys_quantity = queryRow.stock_qty;
          //       this.updateQueryItem(this.sheet.sheetRows[i])
          //       message += this.sheet.sheetRows[i].item_name + ";"
          //     }
          //   }
          // }
        }
      } else {
        return res.msg;
      }

      return message === "" ? "" : (message += "等库存已变化，请重新检查提交");
    },
    updateQueryItem(sheetRow) {
      let costPriceType = "";
      const setting = this.$store.state.operInfo.setting;
      if (setting && setting.costPriceType) {
        costPriceType = setting.costPriceType;
      }
      const difference_qty = sheetRow.real_quantity - sheetRow.stock_qty;
      sheetRow.difference_qty = difference_qty;
      sheetRow.sys_quantity = sheetRow.stock_qty;
      sheetRow.stockQty = sheetRow.stock_qty;
      let cost_price = sheetRow.buy_price;
      if (costPriceType === "2") {
        cost_price = sheetRow.cost_price_avg;
      }
      if (difference_qty > 0) {
        let profit_wholesale_amount = toMoney(
          difference_qty * sheetRow.wholesale_price
        );
        let profit_cost_amount = toMoney(difference_qty * cost_price);
        let profit_buy_amount = toMoney(difference_qty * sheetRow.buy_price);
        sheetRow.profit_wholesale_amount = profit_wholesale_amount;
        sheetRow.profit_cost_amount = profit_cost_amount;
        sheetRow.profit_buy_amount = profit_buy_amount;
        sheetRow.loss_qty = "";
        sheetRow.loss_wholesale_amount = "";
        sheetRow.loss_cost_amount = "";
        sheetRow.loss_buy_amount = "";
        sheetRow.b_loss_qty = 0;
        sheetRow.m_loss_qty = 0;
        sheetRow.s_loss_qty = 0;
        const res = getQtyUnit(
          difference_qty,
          sheetRow.b_unit_no,
          sheetRow.b_unit_factor,
          sheetRow.m_unit_no,
          sheetRow.m_unit_factor,
          sheetRow.s_unit_no
        );
        sheetRow.b_profit_qty = res.b_qty;
        sheetRow.m_profit_qty = res.m_qty;
        sheetRow.s_profit_qty = res.s_qty;
        sheetRow.profit_qty = res.qtyUnit;
        sheetRow.add_qty = res.qtyUnit;
      } else if (difference_qty < 0) {
        let loss_wholesale_amount = toMoney(
          difference_qty * sheetRow.wholesale_price
        );
        let loss_cost_amount = toMoney(difference_qty * cost_price);
        let loss_buy_amount = toMoney(difference_qty * sheetRow.buy_price);
        sheetRow.loss_wholesale_amount = loss_wholesale_amount;
        sheetRow.loss_cost_amount = loss_cost_amount;
        sheetRow.loss_buy_amount = loss_buy_amount;
        sheetRow.profit_qty = "";
        sheetRow.profit_wholesale_amount = "";
        sheetRow.profit_cost_amount = "";
        sheetRow.profit_buy_amount = "";
        sheetRow.b_profit_qty = 0;
        sheetRow.m_profit_qty = 0;
        sheetRow.s_profit_qty = 0;
        var res = getQtyUnit(
          difference_qty,
          sheetRow.b_unit_no,
          sheetRow.b_unit_factor,
          sheetRow.m_unit_no,
          sheetRow.m_unit_factor,
          sheetRow.s_unit_no
        );
        sheetRow.b_loss_qty = res.b_qty;
        sheetRow.m_loss_qty = res.m_qty;
        sheetRow.s_loss_qty = res.s_qty;
        sheetRow.loss_qty = res.qtyUnit;
        sheetRow.add_qty = res.qtyUnit;
      } else if (difference_qty === 0) {
        sheetRow.profit_qty = "";
        sheetRow.profit_wholesale_amount = "";
        sheetRow.profit_cost_amount = "";
        sheetRow.profit_buy_amount = "";
        sheetRow.loss_qty = "";
        sheetRow.loss_wholesale_amount = "";
        sheetRow.loss_cost_amount = "";
        sheetRow.loss_buy_amount = "";
        sheetRow.b_profit_qty = 0;
        sheetRow.m_profit_qty = 0;
        sheetRow.s_profit_qty = 0;
        sheetRow.b_loss_qty = 0;
        sheetRow.m_loss_qty = 0;
        sheetRow.s_loss_qty = 0;
        sheetRow.add_qty = "";
      }
      function getQtyUnit(
        qty,
        b_unit_no,
        b_unit_factor,
        m_unit_no,
        m_unit_factor,
        s_unit_no
      ) {
        let b_qty = 0,
          m_qty = 0,
          s_qty = 0;
        let leftQty = qty;
        let unitsQty = "";
        let absLeftQty = Math.abs(leftQty);
        let flag = leftQty < 0 ? -1 : 1;
        if (b_unit_factor) {
          b_qty = parseInt(absLeftQty / b_unit_factor);
          absLeftQty = absLeftQty % b_unit_factor;
          if (b_qty < 0.001) {
            b_qty = 0;
          }
          if (b_qty > 0) {
            b_qty *= flag;
            unitsQty += toMoney(b_qty) + b_unit_no;
          }
        }
        if (m_unit_factor) {
          m_qty = parseInt(absLeftQty / m_unit_factor);
          absLeftQty = absLeftQty % m_unit_factor;
          if (m_qty < 0.001) {
            m_qty = 0;
          }
          if (m_qty > 0) {
            m_qty *= flag;
            unitsQty += toMoney(m_qty) + m_unit_no;
          }
        }
        s_qty = absLeftQty;
        if (s_qty < 0.001) {
          s_qty = 0;
        }

        if (s_qty > 0) {
          s_qty *= flag;
          unitsQty += toMoney(s_qty) + s_unit_no;
        }
        return {
          qtyUnit: unitsQty,
          b_qty: b_qty,
          m_qty: m_qty,
          s_qty: s_qty,
        };
      }
    },
  },
};
</script>

<style lang="less" scoped>
.sales_more {
  background-color: #fff;
  position: absolute;
  top: 0px;
  width: 100%;
  height: 100%;
  left: 0px;

  .print-template-wrapper {
    height: 250px;
  }

  .select-printer {
    display: flex;
    width: 100%;
    min-height: 45px;

    /deep/.van-cell-group.van-cell-group--inset.van-hairline--top-bottom {
      margin: 0px;
    }
  }

  /deep/.van-checkbox-group {
    font-size: 16px;
  }

  .print-count {
    display: flex;
    justify-content: flex-end;
    align-items: center;
  }

  .print-btns {
    height: 45px;
    border-radius: 12px;
    width: calc(45% - 5px);
  }

  .other_payway {
    // margin: 15px 0;
    .payway-minHeight {
      min-height: 40px;
    }

    .payway {
      height: 30px;

      font-size: 15px;
      @flex_a_j();
      margin: 5px;

      .van-col {
        height: inherit;
        @flex_a_j();
        color: #000000;
      }

      input {
        width: 90px;
        height: 100%;
        border: none;
        outline: none;
        border-bottom: 0.5px solid #eee;
        text-align: center;
      }

      .payway_add {
        font-size: 20px;
        color: #ccc;
        margin-left: 10px;
        margin-top: 10px;
      }

      .arrow {
        line-height: 17px;
      }
    }

    .payway::after {
      position: absolute;
      content: "";
      border-bottom: 1px solid #444;
    }
  }

  /deep/.van-field {
    height: calc(100% - 46px);
    color: #000000;
  }

  /deep/.van-radio-group {
    margin-top: 5px;
  }

  /deep/.van-radio {
    margin: 0 0 10px 0;
  }

  /deep/.van-divider {
    margin: 10px 0 3px;
    color: #ddd;
  }

  h4 {
    font-size: 16px;
    text-align: left;
    height: 35px;
    line-height: 35px;
    padding-left: 10px;
    color: #333333;
    font-weight: normal;
  }
}
.radio-tmp-position {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  height: 210px;
  overflow-y: auto;
}
.setHeight {
  height: 60px;
}
.radio-tmp-style {
  display: inline-block;
  position: relative;
  left: 0;
  margin: 5px 0;
  width: 50%;
  text-align: left;
}
// 模板radio样式
.radio-tmp-type {
  width: 20px;
  height: 20px;
  appearance: none;
  position: relative;
}

.radio-tmp-type:before {
  content: "";
  width: 20px;
  height: 20px;
  border: 1px solid #8d8d8d;
  display: inline-block;
  border-radius: 50%;
  vertical-align: middle;
}

.radio-tmp-type:checked:before {
  content: "";
  width: 20px;
  height: 20px;
  border: 1px solid #8d8d8d;
  background: #8d8d8d;
  display: inline-block;
  border-radius: 50%;
  vertical-align: middle;
}

.radio-tmp-type:checked:after {
  content: "";
  width: 10px;
  height: 5px;
  border: 0.065rem solid white;
  border-top: transparent;
  border-right: transparent;
  text-align: center;
  display: block;
  position: absolute;
  top: 6px;
  left: 5px;
  vertical-align: middle;
  transform: rotate(-45deg);
}
.myslide-right-enter-active,
.myslide-right-leave-active,
.myslide-left-enter-active,
.myslide-left-leave-active {
  transition: all 0.4s ease;
}
.hide-show-div-enter-active {
  transition: opacity 0.5s;
}

.hide-show-div-enter {
  opacity: 0;
}

.hide-show-div-leave-active {
  transition: opacity 0.5s;
}

.hide-show-div-leave-to {
  opacity: 0;
}

@flex_w: {
  display: flex;
  flex-wrap: wrap;
}

;

@flex_a_j: {
  display: flex;
  align-items: center;
  justify-content: center;
}

;

@flex_a_bw: {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

;

.wrapper {
  width: 100%;
  height: 100%;
  box-sizing: border-box;
  background-color: #fff;
  display: flex;
  flex-direction: column;

  .content {
    width: 100%;
    box-sizing: border-box;
    background-color: #fff;
    overflow-y: auto;
    display: flex;
    flex-direction: column;
    // padding: 10px 10px 60px 10px;

    .content-query {
      box-sizing: border-box;
      width: 100%;
      display: flex;
      flex-direction: column;
      margin-bottom: 10px;
      // padding: 10px 15px 3px 15px;

      .content-query-item {
        height: 25px;
        line-height: 25px;
        display: flex;

        input {
          height: 100%;
          width: 100%;
          // padding: 0 10px 4px 0px;
          border: none;
          font-size: 15px;
          line-height: 35px;
          color: #333333;
          vertical-align: top;
          border-bottom: 1px solid #eee;
          text-align: right;
        }

        .van-icon {
          width: 30px;
          text-align: center;
          font-size: 22px;
          display: flex;
          align-items: center;
          justify-content: center;
          color: #aaa;
          background-color: #ffffff;
        }
      }
    }

    .public_query_title {
      background: #ffffff;
    }

    .public_query_title_t {
      height: 25px;
      line-height: 25px;
      font-size: 15px;
      color: #000000;
      // padding: 10px 10px 0;
      @flex_a_bw();
    }
  }

  .lowItem {
    width: 300px;
    height: auto;
    overflow: hidden;
    padding: 10px;

    h4 {
      height: 40px;
      font-size: 16px;
      font-weight: 500;
      color: #333333;
      @flex_a_j();
    }

    .lowItem_ul {
      height: auto;
      overflow: hidden;
      margin-bottom: 10px;

      li {
        height: auto;
        overflow: hidden;
        padding: 10px;
        font-size: 14px;
        @flex_a_bw();
        border-bottom: 1px solid #f2f2f2;
        .btn-delete{
          max-width: 70px; 
          line-height: 40px;
        }
      }

      li:last-child {
        border-bottom: none;
      }
    }
  }

  .footer {
    width: 100%;
    height: 50PX;
    position: absolute;
    bottom: 0;
    box-shadow: 0 1PX 5PX rgba(100, 100, 100, 0.2);
    background-color: #fff;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 10PX;
    box-sizing: border-box;

    .footer_input {
      // display: flex;
      // align-items: center;
      // justify-content: center;
      // width: 190px;
      // height: 50px;
      width: 190PX;
      font-size: 14PX;
      border-style: none;
      border-bottom: 1PX solid #eee;
      // background-color: #eee;
      height: 35PX;
      // padding-left: 3px;
    }

    .footer_iconBt {
      display: flex;
      // align-items: center;
      justify-content: center;
      background-color: transparent;
      border: none;
      // margin-left: 20px;
      align-items: end;
      text-align: right;
      width: 50PX;
      height: 50PX;
    }
  }

  .van_popup {
    height: 100%;
    overflow: hidden;
  }
  .print-barcode{
    font-size: 12px;
  }
  .other_operate {
    width: 100%;
    height: auto;

    .other_operate_content {
      height: 40px;
      vertical-align: top;
      margin-bottom: 15px;
      @flex_a_j();

      button {
        width: 100px;
        height: 100%;
        vertical-align: top;
        margin: 0 15px;
        white-space: nowrap;
      }
    }
  }
}</style>
