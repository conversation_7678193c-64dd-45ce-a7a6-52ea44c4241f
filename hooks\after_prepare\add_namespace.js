#!/usr/bin/env node

/**
 * Cordova Hook: 添加 Android namespace 配置
 * 
 * 这个 hook 会在每次 cordova prepare 之后自动执行，
 * 确保 Android build.gradle 文件中包含 namespace 配置
 */

var fs = require('fs');
var path = require('path');

module.exports = function(context) {
    // 获取平台路径
    var platformDir = path.join(context.opts.projectRoot, 'platforms', 'android', 'app');
    
    // build.gradle 文件路径
    var buildGradlePath = path.join(platformDir, 'build.gradle');

    // 要添加的 namespace 配置
    var namespaceConfig = "    namespace 'com.yingjiang.app'";

    // 检查 build.gradle 文件是否存在
    if (!fs.existsSync(buildGradlePath)) {
        console.log('❌ [Namespace Hook] build.gradle 文件不存在: ' + buildGradlePath);
        return;
    }

    // 读取 build.gradle 文件内容
    var buildGradleContent = fs.readFileSync(buildGradlePath, 'utf8');

    // 检查是否已经包含 namespace 配置
    if (buildGradleContent.includes('namespace')) {
        console.log('✅ [Namespace Hook] namespace 配置已存在，无需添加');
        return;
    }

    // 查找 android 块的位置
    var androidBlockRegex = /android\s*{/;
    var match = buildGradleContent.match(androidBlockRegex);
    
    if (!match) {
        console.log('❌ [Namespace Hook] 未找到 android 块');
        return;
    }

    // 在 android 块的第一行后插入 namespace 配置
    var insertPosition = match.index + match[0].length;
    var newContent = buildGradleContent.slice(0, insertPosition) + '\n' + namespaceConfig + buildGradleContent.slice(insertPosition);

    // 写回文件
    fs.writeFileSync(buildGradlePath, newContent, 'utf8');
    
    console.log('✅ [Namespace Hook] 成功添加 namespace 配置: com.yingjiang.app');
};