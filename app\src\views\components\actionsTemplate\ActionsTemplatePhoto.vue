<template>
  <div class="actions-template-item-wrapper">
    <div class="photo-title-tip">
      <div class="photo-title-tip-left">
        <div class="photo-title"> {{ workContent.action.name }} </div>
        <div class="photo-tip">
          {{ workContent.action.tip }}
        </div>
      </div>
      <div class="photo-title-tip-right">
        <van-popover
            v-model="showPopover"
            trigger="click"
            placement="left">
          <div class="action-wrapper">
            <div class="action-item" @click="showPopover = false" v-for="(item, index) in tipsActions" :key="'action_' + index">
              {{ item.text }}
            </div>
          </div>
          <template #reference>
            <van-icon name="info-o" />
          </template>
        </van-popover>
      </div>
    </div>
    <div class="photo-content">
      <div v-for="(item, index) in workContent.action.items" :key="'photo-item-' + item.id" class="photo-content-item">
        <div class="photo-item-img">
          <div v-if="workContent.work_content.mandatory[index] === ''" class="photo-add" @click="handlePhotoClick(true, index)">
            <van-icon name="plus" />
          </div>
          <div v-else class="photo-img">
            <img :src="workContent.work_content.mandatory[index]" alt="" @click="handleShowPreView(workContent.work_content.mandatory, index)"/>
            <div class="photo-img-close" v-if="canEditTemplate" @click="handlePhotoListSplice(workContent.work_content.mandatory, index, true)">
              <van-icon name="close" />
            </div>
          </div>
        </div>
        <div class="photo-item-title">
          {{ item.title }}
        </div>
      </div>
      <template v-if="calcPhotoOptionalNum > 0">
        <div v-for="(item, index) in workContent.work_content.optional" :key="'photo-optional-' + item" class="photo-content-item">
          <div class="photo-item-img">
            <div class="photo-img">
              <img :src="item" alt="" @click="handleShowPreView(workContent.work_content.optional, index)"/>
              <div class="photo-img-close"  v-if="canEditTemplate" @click="handlePhotoListSplice(workContent.work_content.optional, index, false)">
                <van-icon name="close" />
              </div>
            </div>
          </div>
        </div>
      </template>
      <template v-if="calcOptionalNum - calcPhotoOptionalNum > 0">
        <div class="photo-content-item photo-opt">
          <div class="photo-item-img">
            <div class="photo-add photo-add-opt"  @click="handlePhotoClick(false, -1)">
              <van-icon name="plus" />
            </div>
          </div>
          <div class="photo-item-title">
            可选
          </div>
        </div>
      </template>
    </div>
  </div>
</template>

<script>
import {Icon, ImagePreview, Popover, Toast} from "vant";
import globalVars from "../../../static/global-vars";
import { SaveSingleImage } from "../../../api/api";
export default {
  name: "ActionsTemplatePhoto",
  props: {
    workContent: {
      type: Object,
      default: () => {}
    },
    canEditTemplate: {
      type: Boolean,
      default: true
    },
    canvasContent: {
      type: Object,
      default: () => {}
    },
    TakePhotoCallBack: {
      type: Function,
      default() {
        return null
      }
    }
  },
  components: {
    "van-icon": Icon,
    "van-popover": Popover
  },
  mounted() {
    this.handleTipsActions()
    this.handleWorkContent()
  },
  watch: {
    'workContent.action': {
      handler() {
        this.handleTipsActions()
      },
      deep: true
    },
  },
  computed: {
    calcOptionalNum() {
      return this.workContent.action.maxNum - this.workContent.action.minNum
    },
    calcPhotoOptionalNum() {
      const tempArr = this.workContent.work_content.optional.filter(item => {
        return item !== ""
      })
      return tempArr.length
    }
  },
  data() {
    return {
      showPopover: false,
      tipsActions: []
    }
  },
  methods: {
    // 图片提示
    handleTipsActions() {
      this.tipsActions = []
      if(this.workContent.action.items.length < this.workContent.action.minNum) {
        const length = this.workContent.action.minNum - this.workContent.action.items.length
        for (let i = 0 ; i < length ; i ++) {
          this.workContent.action.items.push({
            "id": this.workContent.action.items.length > 0 ? this.workContent.action.items[this.workContent.action.items.length - 1] .id + 1 : 1,
            "tip": "暂无要求",
            "title": "必选"
          })
          // this.workContent.work_content.mandatory.push('')
        }
      }
      this.workContent.action.items.forEach(item => {
        this.tipsActions.push({text : item.title + "： " +item.tip})
      })
    },
    // 初始化之前拍摄缓存数据
    handleWorkContent() { // 加载base64 或者是 OBS
      let keyArr = ['mandatory', 'optional']
      for (let j = 0 ; j < keyArr.length; j ++) {
        let key = keyArr[j]
        for (let i = 0; i < this.workContent.work_content[key].length; i++) {
          if (this.workContent.work_content[key][i]) {
            let imgSrc = this.workContent.work_content[key][i]
            if (imgSrc.indexOf('data:') === 0 || imgSrc.indexOf(globalVars.obs_server_uri) === 0) {
              this.$set(this.workContent.work_content[key], i,imgSrc)
            } else {
              this.$set(this.workContent.work_content[key], i, globalVars.obs_server_uri + '/' + imgSrc)
            }
          } else {
            this.$set(this.workContent.work_content[key], i, '')
          }
        }
      }
    },
    // 拍照
    // mandatoryOrOptionalFlag true 必选， false可选
    handlePhotoClick(mandatoryOrOptionalFlag, index) {
      // console.warn('operkey', this.$store.state.operKey)
      // console.warn('It is NEW')
      if (!this.canEditTemplate) return
      let faceImageUrl = ""
      this.onTakePhotoAndCallBackImageUrl(async (res) => {
        faceImageUrl = res;
        const waterMakerCompressImage = await this.compressImageWithWaterMark(faceImageUrl);
        SaveSingleImage({ operKey: this.$store.state.operKey, imageBase64: waterMakerCompressImage }).then((res) => {
          if (res?.result === 'OK' && res?.data) {
            const image = res.data
            Toast.success({ message: '上传成功', duration: 500 })
            console.log('上传成功', res)
            resolveImage(this, image)
          } else {
            // const msg = res.msg ?? '图片上传失败'
            // Toast.fail(msg)
            console.warn('上传失败', res)
            resolveImage(this, waterMakerCompressImage)
          }
        }).catch((err) => {
          console.error('上传失败(通信错误)', err)
          resolveImage(this, waterMakerCompressImage)
        })
      });
      function resolveImage(_this, image) {
        if(mandatoryOrOptionalFlag) {
          _this.$set(_this.workContent.work_content.mandatory, index, image)
          // this.$set(this.workContent.work_content.mandatoryName, index, 'actionsTemplateMandatory/' + this.nanoid() + '.' + this.workContent.work_content.mandatory[index].split(';')[0].split('/')[1])
        } else {
          _this.workContent.work_content.optional.push(image)
          // this.workContent.work_content.optionalName.push('actionsTemplateOptional/' + this.nanoid() + '.' + this.workContent.work_content.optional[this.workContent.work_content.optional.length - 1].split(';')[0].split('/')[1])
        }
        _this.$forceUpdate()
        _this.TakePhotoCallBack()
      }
    },
    //时间，公司名称，地址
    //公共拍照函数
    //param list
    onTakePhotoAndCallBackImageUrl(callback) {
      let CameraOptions = {
        // eslint-disable-next-line no-undef
        destinationType: Camera.DestinationType.DATA_URL, //返回FILE_URI类型
        // eslint-disable-next-line no-undef
        sourceType: Camera.PictureSourceType.CAMERA, //返回FILE_URI类型
      };
      navigator.camera.getPicture(cameraSuccess, cameraError, CameraOptions);
      // eslint-disable-next-line no-inner-declarations
      function cameraSuccess(data) {
        // //原图
        if(data.indexOf("base64") == -1){
            data = "data:image/jpeg;base64," + data;
        }
        callback(data);
      }
      // eslint-disable-next-line no-inner-declarations
      function cameraError() {}
    },
    async compressImageWithWaterMark(origenBaseImg) {
      const operName = this.$store.state.operInfo.oper_name;
      let that_ = this;
      const compressImage = await this.compressImage(origenBaseImg, 720);
      return new Promise((resolve, reject) => {
        var newImage = new Image();
        newImage.src = compressImage;
        newImage.setAttribute("crossOrigin", "Anonymous"); //url为外域时需要
        var imgWidth, imgHeight;
        newImage.onload = function () {
          imgWidth = this.width;
          imgHeight = this.height;
          var waterCanvas = document.createElement("canvas");
          var ctx = waterCanvas.getContext("2d");
          //Canvas实现水印
          waterCanvas.width = imgWidth;
          waterCanvas.height = imgHeight;
          ctx.clearRect(0, 0, waterCanvas.width, waterCanvas.height);
          ctx.drawImage(this, 0, 0, waterCanvas.width, waterCanvas.height);
          ctx.fillStyle = "rgba(0, 0, 0, 0.5)";
          ctx.fillRect(0, waterCanvas.height - 176, waterCanvas.width, 176);
          ctx.fillStyle = "#ddd";
          ctx.font = "20px Arial";
          ctx.textBaseline = "middle";
          if (that_.canvasContent) {
            if (that_.canvasContent.supName) {
              ctx.fillText(that_.canvasContent.supName, 20, waterCanvas.height - 152);
              if (typeof that_.canvasContent.supAddr != 'undefined') {
                if (that_.canvasContent.supAddr.indexOf("(") !== -1) {
                  const addrArr = that_.canvasContent.supAddr.split("(");
                  ctx.fillText(addrArr[0], 20, waterCanvas.height - 120);
                  ctx.fillText(addrArr[1], 20, waterCanvas.height - 88);
                } else {
                  ctx.fillText(that_.canvasContent.supAddr, 20, waterCanvas.height - 120);
                }
              }
            }
          }
          ctx.fillText("业务员: " + operName + " " + that_.getCurrentTime(), 20, waterCanvas.height - 56);
          var newBaseImage = waterCanvas.toDataURL("image/jpeg"); //压缩语句
          resolve(newBaseImage);
        };
      });
    },
    async compressImage(origenBaseImg, w) {
      var newImage = new Image();
      var quality = 0.5; //压缩系数0-1之间
      return new Promise((resolve, reject) => {
        newImage.src = origenBaseImg;
        newImage.setAttribute("crossOrigin", "Anonymous"); //url为外域时需要
        var imgWidth, imgHeight;
        newImage.onload = function () {
          imgWidth = this.width;
          imgHeight = this.height;
          var canvas = document.createElement("canvas");
          var ctx = canvas.getContext("2d");
          if (Math.max(imgWidth, imgHeight) > w) {
            if (imgWidth > imgHeight) {
              canvas.width = w;
              canvas.height = (w * imgHeight) / imgWidth;
            } else {
              canvas.height = w;
              canvas.width = (w * imgWidth) / imgHeight;
            }
          } else {
            canvas.width = imgWidth;
            canvas.height = imgHeight;
            quality = 0.8;
          }
          ctx.clearRect(0, 0, canvas.width, canvas.height);
          ctx.drawImage(this, 0, 0, canvas.width, canvas.height);
          var newBaseImage = canvas.toDataURL("image/jpeg", quality); //压缩语句
          // 如想确保图片压缩到自己想要的尺寸,如要求在50-150kb之间，请加以下语句，quality初始值根据情况自定
          while (newBaseImage.length / 1024 > 150) {
            quality -= 0.001;
            newBaseImage = canvas.toDataURL("image/jpeg", quality);
          }
          resolve(newBaseImage);
        };
      });
    },
    getCurrentTime() {
      let myDate = new Date();
      let moths = (myDate.getMonth() + 101 + "").substring(1);
      let getHours = myDate.getHours();
      let getMinutes = (myDate.getMinutes() + 100 + "").substring(1);
      return myDate.getFullYear() +
          "-" +
          moths +
          "-" +
          myDate.getDate() +
          " " +
          getHours +
          ":" +
          getMinutes +
          ":" +
          myDate.getSeconds();
    },
    handleShowPreView(list,index) {
      const showList = list.filter(item => {
        return item !== ''
      })
      if (showList.length > 0) {
        ImagePreview({
          images: showList,
          startPosition: index,
          closeable: true,
        });
      }

    },
    handlePhotoListSplice(list,index,mandatoryOrOptionalFlag) {
      if (mandatoryOrOptionalFlag) {
        this.$set(list, index, "")
        // this.$set(this.workContent.work_content.mandatoryName, index, "")
      } else {
        list.splice(index,1)
        // this.workContent.work_content.optionalName.splice(index,1)
      }
    },
    handleCheckResult() {
      let messageError = "";
      console.log('handleCheckResult', this.workContent.work_content.mandatory)
      for (let i = 0; i < this.workContent.work_content.mandatory.length; i++) {
        if (this.workContent.work_content.mandatory[i] === '') {
            console.log('ActionsTemplatePhoto.vue,this.photoAction.items.length', this.workContent)
            if (this.workContent.action.items.length > 0) {
                if (i < this.workContent.action.items.length) { // 兼容没有提示的情况
                    messageError = this.workContent.action.name + ':' + this.workContent.action.items[i].title + "未拍照";
                } else {
                    messageError = this.workContent.action.name + "未拍照";
                }
            } else {
                messageError = this.workContent.action.name + "未拍照";
            }

          return messageError
        }
      }
      return messageError
    },
    nanoid (t=21) {
      return crypto.getRandomValues(new Uint8Array(t)).reduce(((t,e)=>t+=(e&=63)<36?e.toString(36):e<62?(e-26).toString(36).toUpperCase():e>62?"-":"_"),"")
    },
    // handleLoadStore(i) {
    //   this.$store.state.unSubmitVisitTemplate[i].actionResult.mandatory.forEach((value,index) => {
    //     this.$set(this.workContent.work_content.mandatory, index, value)
    //   })
    //   this.$store.state.unSubmitVisitTemplate[i].actionResult.mandatoryName.forEach((value,index) => {
    //     this.$set(this.workContent.work_content.mandatoryName, index, value)
    //   })
    //   this.$store.state.unSubmitVisitTemplate[i].actionResult.optional.forEach((value,index) => {
    //     this.$set(this.workContent.work_content.optional, index, value)
    //   })
    //   this.$store.state.unSubmitVisitTemplate[i].actionResult.optionalName.forEach((value,index) => {
    //     this.$set(this.workContent.work_content.optionalName, index, value)
    //   })
    // }

    // async getCurPositionToSupcustDistance(sup_lat, sup_lng) {
    //   var res = null;
    //   if (this.lastPosition && this.lastPosition.result ===  "OK") {
    //     var now = new Date();
    //     var passSeconds = now.getTime() - this.lastPosition.time.getTime();
    //     if (passSeconds < 30000) res = this.lastPosition;
    //   }
    //   if (!res) res = await this.getCurPosition();
    //   if (res.result !== "OK") {
    //     this.$toast(res.msg);
    //     return null;
    //   }
    //   return this.getDistance(
    //       res.latitude,
    //       res.longitude,
    //       sup_lat,
    //       sup_lng
    //   );
    // },
  }
}
</script>

<style scoped lang="less">
.action-wrapper {
  min-width: 200px;
  display: flex;
  flex-direction: column;
  border-radius: 5px;
  padding: 0 10px;
  .action-item {
    display: flex;
    align-items: center;
    height: 35px;
    box-sizing: border-box;
    margin: 2px 5px;
  }
  .action-item +.action-item {
    border-top: 1px solid #eee;
  }
}
.actions-template-item-wrapper {
  display: flex;
  flex-direction: column;
  box-sizing: border-box;
  padding: 10px ;
  .photo-title-tip {
    display: flex;
    .photo-title-tip-left {
      flex: 1;
      display: flex;
      justify-content: flex-start;
      align-items: center;
      .photo-title {
        font-size: 18px;
        font-weight: bolder;
      }
      .photo-tip {
        flex: 1;
        height: 100%;
        display: flex;
        justify-content: flex-start;
        align-items: center;
        margin-left: 10px;
        color: #aaaaaa;
        font-size: 14px;
        text-align: left;
      }
    }
    .photo-title-tip-right {
      font-size: 16px;
      color: #aaaaaa;
    }

  }
  .photo-content {
    margin-top: 10px;
    display: flex;
    overflow-x: auto;
    .photo-content-item {
      display: flex;
      flex-direction: column;
      align-items: center;
      width: 65px;
      margin: 0 10px;
      .photo-item-img {
        width: 50px;
        height: 50px;
        .photo-add{
          width: 100%;
          height: 100%;
          display: flex;
          justify-content: center;
          align-items: center;
          border: 1px dotted #ddd;
          border-radius: 10px;
        }
        .photo-img{
          width: 100%;
          height: 100%;
          display: flex;
          justify-content: center;
          align-items: center;
          border: 1px dotted #ddd;
          border-radius: 10px;
          position: relative;
          img {
            width: 100%;
            height: 100%;
          }
          .photo-img-close {
            position: absolute;
            right: -8px;
            top: -2px;
            color: #ee0a24;
            font-size: 20px;
            color: #ee0a24;
          }
        }
      }
      .photo-item-title {
        margin-top: 10px;
        color: #aaa
      }
    }
    .photo-opt {
      color: #aaaaaa;
      .photo-add-opt {
        border: none !important;
      }
      .photo-item-title {
        font-size: 14px;
      }
    }
    .photo-content-item  + .photo-content-item {
      margin-left: 15px;
    }

  }
}
</style>
