#!/usr/bin/env node

/**
 * 在 prepare 后自动复制 Gradle 额外配置文件到平台目录
 * 可以处理多个 Gradle 配置文件的复制
 */

const fs = require('fs');
const path = require('path');

module.exports = function(context) {
    const projectRoot = context.opts.projectRoot;
    const platformRoot = path.join(projectRoot, 'platforms', 'android');
    
    // 定义需要复制的文件列表
    const filesToCopy = [
        {
            source: path.join(projectRoot, 'build-extras.gradle'),
            target: path.join(platformRoot, 'app', 'build-extras.gradle')
        }
        // 可以在这里添加更多需要复制的文件
        // {
        //     source: path.join(projectRoot, 'other-file.gradle'),
        //     target: path.join(platformRoot, 'app', 'other-file.gradle')
        // }
    ];

    console.log('🔧 [Copy Gradle Extras] Starting file copy operations...');

    filesToCopy.forEach(file => {
        if (fs.existsSync(file.source)) {
            try {
                // 确保目标目录存在
                const targetDir = path.dirname(file.target);
                if (!fs.existsSync(targetDir)) {
                    fs.mkdirSync(targetDir, { recursive: true });
                }

                // 复制文件
                fs.copyFileSync(file.source, file.target);
                console.log(`✅ [Copy Gradle Extras] Successfully copied ${path.basename(file.source)} to platform directory`);
            } catch (error) {
                console.error(`❌ [Copy Gradle Extras] Error copying ${path.basename(file.source)}:`, error.message);
            }
        } else {
            console.log(`⚠️ [Copy Gradle Extras] Source file ${path.basename(file.source)} not found, skipping`);
        }
    });
};