<template>
  <div style="overflow-y:auto;height: 100%;">
    <div class="wrapper-page" v-if="hasReject">
      <ul class="sales_ul">
        <li class="item_title">拒收</li>

        <li v-for="(item,index) in sheetDetail" v-if="item.back_type=='reject'" :key="index+'_reject'" ref="'orders_li'+item.order_sheet_no">
          <van-swipe-cell style="width:100%">
            <div class="sheet_wrapper" :style="{background:item.state=='submited'?'#eee':'#fff'}">
              <div class="sup_info" v-if="index===0">
                <div class="sup_name">{{ item.sup_name }}
                  <div v-if="item.order_source" class="order-source-wrapper">
                    {{item.order_source === 'xcx' ? '商城' : ''}}
                  </div>
                </div>
                <div class="sheet_no_tag" style="color: #1989fa; text-decoration: none">
                  <div class="sheet_no" @click.stop="onClientRowClick(item)">{{ item.sale_order_sheet_no }}</div>
                </div>
              </div>
              <div class="sup_info" v-if="index>0&&!(item.back_type ===sheetDetail[index-1].back_type&&item.sale_order_sheet_no === sheetDetail[index-1].sale_order_sheet_no)">
                <div class="sup_name">{{ item.sup_name }}
                  <div v-if="item.order_source" class="order-source-wrapper">
                    {{item.order_source === 'xcx' ? '商城' : ''}}
                  </div>
                </div>
                <div class="sheet_no_tag" style="color: #1989fa; text-decoration: none">
                  <div class="sheet_no" @click.stop="onClientRowClick(item)">{{ item.sale_order_sheet_no }}</div>
                </div>
              </div>
              <div class="sheet_info">
                <div class="sheet_info_left">
                  <div class="sheet_no_tag">
                    <div class="sheet_no">{{item.item_name }}</div>
                  </div>
                  <div class="sheet_happentime" style="margin-top:10px"  v-if="vanStock[item.item_id+fromBranch+item.branch_position+item.batch_id]&&vanStock[item.item_id+fromBranch+item.branch_position+item.batch_id].stock_qty_unit">
                    车辆库存:{{ vanStock[item.item_id+fromBranch+item.branch_position+item.batch_id].stock_qty_unit }}
                  </div>
                  <div class="sheet_happentime" style="margin-top:5px"  v-if="item.produce_date">
                      生产日期:{{ item.produce_date }}
                    </div>
                    <div class="sheet_happentime" style="margin-top:5px"  v-if="item.batch_no">
                      批次:{{ item.batch_no }}
                    </div>
                    <!-- <div class="sheet_happentime" style="margin-top:5px"  v-if="item.real_price">
                      ￥{{ item.need_move_qty*item.real_price }}({{item.real_price}}/{{item.unit_no}})
                    </div> -->
                </div>
                <div class="quantity" style="margin-top:5px">
                  <div v-if="item.status" class="order-source-wrapper">
                    <van-tag type="warning" v-if="item.status=='bf'">部分回库</van-tag>
                    <van-tag type="danger" v-if="item.status=='over'">数量超出</van-tag>
                    <!-- <span class="order-source-wrapper" v-if="item.status=='bf'">部分回库</span> -->
                    <!-- <span class="order-source-wrapper" v-if="item.status=='qb'">全部回库</span> -->
                    <!-- <span class="order-source-wrapper" v-if="item.status=='over'">超出数量</span> -->
                  </div>
                  <div style="color: #ccc;margin-top:10px">回库:<span style="color: #1989fa; text-decoration: none" @click.stop="onItemRowClick(item,index,'reject','move')">{{item.move_qty+item.back_unit_no}}</span></div>
                  <div style="color: #ccc;margin-top:5px"> 留车:<span style="color: #1989fa; text-decoration: none" @click.stop="onItemRowClick(item,index,'reject','sale')">{{item.sale_qty+item.sale_unit_no}}</span></div>
                  <div style="color: #ccc;margin-top:5px">拒收:{{item.need_move_qty+item.unit_no}}</div>
                  
                </div>
              </div>
              <div class="sheet_happentime" style="color: #ccc;" v-if="item.real_price">拒收金额:￥{{ item.need_move_qty*item.real_price }}({{item.real_price}}/{{item.unit_no}})</div>
            </div>
          </van-swipe-cell>
        </li>
      </ul>
    </div>
    <div class="wrapper-page" v-if="hasReturn">
      <ul class="sales_ul">
        <li class="item_title">退货</li>
        <li v-for="(item,index) in sheetDetail" v-if="item.back_type=='return'" :key="index+'_returned'" ref="'orders_li'+item.order_sheet_no">
          <van-swipe-cell style="width:100%">
            <div class="sheet_wrapper" :style="{background:item.state=='submited'?'#eee':'#fff'}">
              <div class="sup_info" v-if="index===0">
                <div class="sup_name">{{ item.sup_name }}
                  <div v-if="item.order_source" class="order-source-wrapper">
                    {{item.order_source === 'xcx' ? '商城' : ''}}
                  </div>
                </div>
                <div class="sheet_no_tag" style="color: #1989fa; text-decoration: none">
                  <div class="sheet_no" @click.stop="onClientRowClick(item)">{{ item.sale_sheet_no }}</div>
                </div>
              </div>
              <div class="sup_info" v-if="index>0&&!(item.back_type ===sheetDetail[index-1].back_type&&item.sale_order_sheet_no === sheetDetail[index-1].sale_order_sheet_no)">
                <div class="sup_name">{{ item.sup_name }}
                  <div v-if="item.order_source" class="order-source-wrapper">
                    {{item.order_source === 'xcx' ? '商城' : ''}}
                  </div>
                </div>
                <div class="sheet_no_tag" style="color: #1989fa; text-decoration: none">
                  <div class="sheet_no" @click.stop="onClientRowClick(item)">{{ item.sale_sheet_no }}</div>
                </div>
              </div>
              <div class="sheet_info">
                <div class="sheet_info_left">
                    <div class="sheet_no_tag">
                      <div class="sheet_no">{{item.item_name.replace('(退)','') }}</div>
                    </div>
                    <div class="sheet_happentime" style="margin-top:10px"  v-if="vanStock[item.item_id+fromBranch+item.branch_position+item.batch_id]&&vanStock[item.item_id+fromBranch+item.branch_position+item.batch_id].stock_qty_unit">
                      车辆库存:{{ vanStock[item.item_id+fromBranch+item.branch_position+item.batch_id].stock_qty_unit }}
                    </div>
                    <div class="sheet_happentime" style="margin-top:5px"  v-if="item.produce_date">
                      生产日期:{{ item.produce_date }}
                    </div>
                    <div class="sheet_happentime" style="margin-top:5px"  v-if="item.batch_no">
                      批次:{{ item.batch_no }}
                    </div>
                    <!-- <div class="sheet_happentime" style="margin-top:5px"  v-if="item.real_price">
                      ￥{{ item.need_move_qty*item.real_price }}({{item.real_price}}/{{item.unit_no}})
                    </div> -->
                  </div>
                  <div class="quantity" style="margin-top:30px">
                    <div style="color: ">回库:<span style="color: #1989fa; text-decoration: none" @click.stop="onItemRowClick(item,index,'back','move')">{{item.move_qty+item.back_unit_no}}</span></div>
                    <div style="color: #ccc;margin-top:5px"> 留车:<span style="color: #1989fa; text-decoration: none" @click.stop="onItemRowClick(item,index,'back','sale')">{{item.sale_qty+item.sale_unit_no}}</span></div>
                    <div style="color: #ccc;margin-top:5px">退货:{{item.need_move_qty+item.unit_no}}</div>
                  </div>
                <!-- <div class="sheet_row" v-for="(row,rowIndex) in item.sheetRows" :key="rowIndex+'_reRow'" ref="'reject_row'+row.item_id">
                  <div class="sheet_info_left">
                    <div class="sheet_no_tag">
                      <div class="sheet_no">{{row.item_name.replace('(退)','') }}</div>
                    </div>
                    <div class="sheet_happentime" style="margin-top:10px"  v-if="vanStock[row.item_id]&&vanStock[row.item_id].stock_qty_unit">
                      车辆库存:{{ vanStock[row.item_id].stock_qty_unit }}
                    </div>
                  </div>
                  <div class="quantity">
                    <div style="color: #ccc;">回库:<span style="color: #1989fa; text-decoration: none" @click.stop="onItemRowClick(row,rowIndex,index,'back','move')">{{row.backQty+row.back_unit_no}}</span></div>
                    <div style="color: #ccc;"> 留车:<span style="color: #1989fa; text-decoration: none" @click.stop="onItemRowClick(row,rowIndex,index,'back','sale')">{{row.sale_qty+row.sale_unit_no}}</span></div>
                    <div style="color: #ccc;">退货:{{row.quantity+row.unit_no}}</div>
                  </div>
                </div> -->
              </div>
              <div class="sheet_happentime" style="color: #ccc;" v-if="item.real_price">退货金额:￥{{ item.sub_amount }}({{toMoney(item.real_price)}}/{{item.unit_no}})</div>
            </div>
          </van-swipe-cell>
        </li>
      </ul>
    </div>
    <div style="height:200px;color:#ccc;">到底了</div>
    <div class="wrapper">
      <div class="content" v-if="totalQTY">总计:
        <span class="record">{{ totalQTY }}</span>
      </div>
      <van-button square type="default" @click="saveBackBranch" v-if="canMake && !approve_time" class="button_assign" :disabled="isSaveing">保存</van-button>
      <van-button square type="default" @click="approveBackBranch" v-if="canApprove && !approve_time" class="button_assign" :disabled="isApproveing">审核</van-button>
      <van-button square type="default" @click="deleteBackBranch" v-if="canDelete && !approve_time && back_branch_sheet_no" class="button_assign" :disabled="isApproveing||isSaveing">删除</van-button>
      <van-button square type="default" @click="cancelBackBranch" v-if="canRed && approve_time && !red_flag" class="button_assign" :disabled="!isApproveing">红冲</van-button>
      <van-button square type="default" @click="printBackBranh" class="button_assign" :disabled="(sheetStatusForPrint == 'saved' && !back_branch_sheet_no) || (sheetStatusForPrint == 'approved' && !approve_time) || isPrinting">打印</van-button>
    </div>
    <van-popup v-model="popupEditSheetRowPannel" position="bottom" :style="{ height: '75%' }">
      <div class="class_add_goods_m">

        <div class="class_add_goods">
          <EditBackBranchItem :editingRow="editingItem" @onRowEditDone="onRowEditDone" @onRowEditExit="onRowEditExit" />
        </div>
      </div>
    </van-popup>
  </div>
</template>
<script>
import { SwipeCell, Cell, CellGroup, Button, Toast, PullRefresh, Tag, Form, Icon, List, Popup, Field, Calendar, Dialog, Checkbox, CheckboxGroup } from "vant";
import EditBackBranchItem from "./BackBranchRowEdit";
export default {
  data() {
    return {
      total: 0,
      totalQTY: '',
      backSheetType: '',
      qtyChangeType: '',
      popupEditSheetRowPannel: false,
      editingItem: {},
      EditSheetRowIndex: "",
      EditSheetIndex: ""
    }
  },
  mounted() {
    setTimeout(() => {
      this.updateTotalQuantity()
    }, 2000);

  },
  components: {
    "van-form": Form,
    "van-icon": Icon,
    "van-list": List,
    "van-popup": Popup,
    "van-tag": Tag,
    "van-pull-refresh": PullRefresh,
    "van-swipe-cell": SwipeCell,
    "van-cell": Cell,
    "van-cell-group": CellGroup,
    "van-button": Button,
    "van-field": Field,
    "van-calendar": Calendar,
    "van-checkbox": Checkbox,
    "van-checkbox-group": CheckboxGroup,
    EditBackBranchItem: EditBackBranchItem,
  },
  activated() {

  },
  props: {
    sheetDetail: Array,
    isSaveing: Boolean,
    isApproveing: Boolean,
    isPrinting: Boolean,
    approve_time: String,
    back_branch_sheet_no:String,
    red_flag: String,
    canMake: Boolean,
    canRed: Boolean,
    canApprove: Boolean,
    canDelete: Boolean,
    vanStock:Object,
    hasReject:Boolean,
    hasReturn:Boolean,
    itemsUnits:Object,
    fromBranch:String,
    hasSplitRows:Boolean,
    parentFunction:Function,
    underStockItems:Array,
  },
  computed: {
    allowPrintBeforeApprove() {
      return hasRight("delicacy.allowPrintBeforeApprove.value");
    },
    allowPrintBeforeSave() {
      return hasRight("delicacy.allowPrintBeforeSave.value");
    },
    sheetStatusForPrint() {
      return window.getRightValue('delicacy.moveSheetStatusForPrint.value');
    },
  },
  methods: {
    saveBackBranch() {
      this.$emit('backBranch', 'save')
    },
    approveBackBranch() {
      this.$emit('backBranch', 'approve')
    },
    deleteBackBranch() {
      this.$emit('deleteBackBranchSheet')
    },
    cancelBackBranch() {
      this.$emit('cancelBackBranch')
    },
    printBackBranh() {
      this.$emit('printBackBranch')
    },
    onClientRowClick(item) {
      window.g_curSheetInList = item;
      var sheet_id = ""
      var sheet_type=""
      if(item.back_type ==="reject"){
        sheet_id = item.sale_order_sheet_id
        sheet_type = "XD"
        if(item.sale_order_sheet_no.includes("TD"))sheet_type = "TD"
        
      }else if(item.back_type ==="return"){
        sheet_id = item.sale_sheet_id
        sheet_type ="X"
        if(item.sale_sheet_no.includes("T"))sheet_type = "T"
      }
      this.$router.push({ path: '/SaleSheet', query: { sheetID: sheet_id, sheetType: sheet_type } })
    },
    backBranchSon() {
      this.$emit('backBranch')

    },
    getItemUnitConv(qty,m_unit_factor ,b_unit_factor,s_unit_no ,m_unit_no ,b_unit_no){
      var s=''
      var q =0 
      if(b_unit_factor){
        q= parseInt(qty / b_unit_factor)
        if (q >= 1) {
            s += q + b_unit_no
        }
        qty = qty % b_unit_factor
      }
      
      if (Math.abs(qty) < 0.0001) qty = 0

      if (qty>0 && m_unit_factor) {
          q = parseInt(qty / m_unit_factor)
          if (q >= 1) s += q + m_unit_no
          qty = qty % m_unit_factor
          if (Math.abs(qty) < 0.0001) qty = 0
      }
      if (qty > 0) s += qty + s_unit_no
      return s
    },
    onItemRowClick(obj,  index, type, qtyChangeType) {
      
      var isSplited = this.parentFunction()
      if(isSplited){
        return Toast.fail('商品汇总处已进行拆分操作,无法修改数量')
      }
      var readonly = false;
      this.backSheetType = type
      this.qtyChangeType = qtyChangeType
      if (this.approve_time) readonly = true
      this.popupEditSheetRowPannel = true;
      //this.EditSheetRowIndex = rowindex;
      this.EditSheetIndex = index
      var underStockItem={}
      var itemsUnits = this.itemsUnits[obj.item_id]
      if(this.underStockItems.length>0){
        this.underStockItems.forEach(item=>{
          if(item.item_id == obj.item_id && item.branch_position == obj.branch_position && item.batch_id == obj.batch_id){
            underStockItem = item
          }
        })
        var m_unit_factor = 0,b_unit_factor =0;
        var s_unit_no = "",m_unit_no = "",b_unit_no=""
        itemsUnits.forEach(unit=>{
          if(unit.unit_type =="s"){
            underStockItem.unit_no = unit.unit_no
            s_unit_no = unit.unit_no
          }else if(unit.unit_type = "m"){
            m_unit_factor = unit.unit_factor
            m_unit_no = unit.unit_no
          }else{
            b_unit_factor = unit.unit_factor
            b_unit_no = unit.unit_no
          }
        })
        underStockItem.stock_qty= this.vanStock[underStockItem.item_id+underStockItem.branch_id+underStockItem.branch_position+underStockItem.batch_id].stock_qty
        underStockItem.stock_qty_unit = this.vanStock[underStockItem.item_id+underStockItem.branch_id+underStockItem.branch_position+underStockItem.batch_id].stock_qty_unit
        underStockItem.left_qty = Number(underStockItem.need_move_qty)-Number(this.vanStock[underStockItem.item_id+underStockItem.branch_id+underStockItem.branch_position+underStockItem.batch_id].stock_qty)
        underStockItem.need_move_qty_conv = this.getItemUnitConv(Number(underStockItem.need_move_qty),m_unit_factor ,b_unit_factor,s_unit_no ,m_unit_no ,b_unit_no)
        underStockItem.left_qty_conv = this.getItemUnitConv(Number(underStockItem.left_qty),m_unit_factor ,b_unit_factor,s_unit_no ,m_unit_no ,b_unit_no)
      
      }
      
      
      let objs = {
        datas: obj,
        readonly: readonly,
        qtyChangeType: qtyChangeType,
        itemUnits:itemsUnits,
        fromBranch:this.fromBranch,
        underStockItem:underStockItem,
      }
      this.editingItem = objs;
    },
    onRowEditDone(sheetRow) {
      
      var allowRepeatedBackBranch = window.getSettingValue('allowRepeatedBackBranch').toLowerCase()=="true"
      var needMoveQty = Number(sheetRow.need_move_qty)*Number(sheetRow.unit_factor)
      var moveQty = Number(sheetRow.move_qty)*Number(sheetRow.back_unit_factor)
      var saleQty = Number(sheetRow.sale_qty)*Number(sheetRow.sale_unit_factor)
      if(!allowRepeatedBackBranch){
        if(this.qtyChangeType=="move"){
          var tpNum = needMoveQty - moveQty
          sheetRow.sale_qty = tpNum/Number(sheetRow.sale_unit_factor)
        }else if(this.qtyChangeType=="sale"){
          var tpNum = needMoveQty - saleQty
          sheetRow.move_qty = tpNum/Number(sheetRow.back_unit_factor)
        }
      }else{
        var leftQty = needMoveQty- moveQty- saleQty
        if(leftQty>0){
          sheetRow.status = 'bf'
        }else if(leftQty==0){
          sheetRow.status='qb'
        }else{
          sheetRow.status='over'
        }

      }
      this.sheetDetail[this.EditSheetIndex] = sheetRow
      this.$emit('qtyChange')
      this.updateTotalQuantity()
      this.onRowEditExit()
    },
    updateTotalQuantity() {
      var totalQuantity = '', m_qty = 0, b_qty = 0, s_qty = 0, count = 0
      this.sheetDetail.forEach(row=>{
        count+=1
        var units = this.itemsUnits[row.item_id]
        units.forEach(u=>{
          if(row.back_unit_no===u.unit_no &&u.unit_type=="b") b_qty +=Number(row.move_qty)
          if(row.back_unit_no===u.unit_no &&u.unit_type=="m") m_qty +=Number(row.move_qty)
          if(row.back_unit_no===u.unit_no &&u.unit_type=="s") s_qty +=Number(row.move_qty)
        })

      })
      if (b_qty && b_qty !== 0) totalQuantity += b_qty + '大'
      if (m_qty && m_qty !== 0) totalQuantity += m_qty + '中'
      if (s_qty && s_qty !== 0) totalQuantity += s_qty + '小'
      this.totalQTY = totalQuantity
      this.total = count
    },
    onRowEditExit() {
      this.backSheetType = ''
      this.EditSheetRowIndex = ''
      this.EditSheetIndex = ''
      this.qtyChangeType = ''
      this.popupEditSheetRowPannel = false
    }

  },
}
</script>
<style lang="less" scoped>
@flex_w: {
  display: flex;
  flex-wrap: wrap;
};
@flex_a_j: {
  display: flex;
  align-items: center;
  justify-content: center;
};
@flex_a_bw: {
  display: flex;
  align-items: center;
  justify-content: space-between;
};
@flex_a_end: {
  display: flex;
  align-items: center;
  justify-content: flex-end;
};
@flex_a_flex: {
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
};
.wrapper-page {
  border: 1px solid #eee;
  border-radius: 10px;
  margin-top: 20px;
  margin-left: 4px;
  margin-right: 4px;
  font-size: 16px;
}
.item_title {
  flex: 3;
  display: flex;
  align-items: center;
  justify-content: flex-start;
  font-size: 16px;
  line-height: 30px;
  font-weight: bolder;
  text-align: center;
  color: #000;
  justify-content: space-around;
  font-weight: bolder;
}
.quantity {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 40%;
}
.backQty {
  display: flex;
  align-content: center;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  margin-right: 10px;
}
.navi-select-item {
  font-size: 0.65rem;
  color: #1887f7;
  border-bottom: solid 0.025rem #ccc;
  padding-top: 0.5rem;
  padding-bottom: 0.5rem;
}
.public_query {
  background: #ffffff;
  height: 100%;
  .public_query_title {
    padding-top: 5px;
    border-bottom: 1px solid #f2f2f2;
    .public_query_titleSrc {
      padding: 0 10px;
      height: 35px;
      @flex_a_bw();
      margin-top: 5px;
      .public_query_titleSrc_item {
        width: 48%;
        height: 100%;
        border: 1px solid #cccccc;
        border-radius: 4px;
        overflow: hidden;
        position: relative;
        vertical-align: top;
        input {
          height: 100%;
          width: calc(100% - 45px);
          padding: 0 45px 0 5px;
          border: none;
          font-size: 15px;
          line-height: 35px;
          color: #333333;
          vertical-align: top;
        }
        span {
          position: absolute;
          right: 0;
          top: 0;
          bottom: 0;
          width: 45px;
          font-size: 16px;
          color: #000;
          @flex_a_j();
          background: #4c99e7;
        }
        .van-icon {
          position: absolute;
          right: 0;
          top: 0;
          bottom: 0;
          width: 30px;
          font-size: 22px;
          @flex_a_j();
          color: #666666;
        }
      }
    }
    .public_list_title {
      height: 40px;
      @flex_a_bw();
      margin-top: 5px;
      padding: 0 5px;
      div {
        height: 40px;
        line-height: 40px;
        font-size: 15px;
        text-align: center;
        width: calc(25% - 10px);
        padding: 0 5px;
        font-weight: 500;
        color: #333333;
      }
      div:first-child {
        width: calc(20% - 10px);
        text-align: left;
      }
      div:last-child {
        width: calc(25% - 10px);
        text-align: right;
      }
    }
  }
}
.sales_list_boxs {
  height: calc(100% - 88px);
  overflow-x: hidden;
  overflow-y: auto;
  background: #f2f2f2;
  width: 100%;
}
.sales_list_boxs_no {
  height: calc(100% - 54px);
  @flex_a_flex();
  background: #f2f2f2;
  color: #999999;
  .whole_box_no_icon {
    font-size: 50px;
  }
  p {
    font-size: 15px;
  }
}
.sales_ul {
  width: 100%;
  height: auto;
  overflow-y: auto;
  li {
    width: 100%;
    height: auto;
    overflow: hidden;
    border-bottom: 1px solid #f2f2f2;
    display: flex;
    flex-direction: column;
    align-items: center;
    .reject-btn {
      margin-left: 10px;
      display: flex;
      color: #fff;
      height: 100%;
      width: 100px;
      font-size: 18px;
      align-items: center;
      justify-content: center;
      font-size: 12px;
      background: #ee0a24;
    }
  }
}
.wrapper {
  position: fixed;
  left: 0px;
  bottom: 0px;
  width: 100%;
  height: 50px;
  font-size: 0.5em;
  color: #333;
  border-top: 1px solid #f2f2f2;
  box-shadow: 0 -2px 5px #f2f6fc;
  background-color: #fff;
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: space-around;
  .content {
    padding-left: 15px;
  }
  .button_assign {
    padding-right: 15px;
    height: 35px;
    border-radius: 11px;
    margin-right: 5px;
    color: #333;
    background: #ffcccc;
  }
  .record {
    padding: 0 10px;
  }
}

.sheet_wrapper {
  width: 100%;
  border-radius: 5px;
  display: flex;
  flex-direction: column;
  margin-bottom: 5px;
  padding: 6px;

  background-color: #fff;
  box-sizing: border-box;

  .sup_info {
    border-bottom: 1px solid #f2f2f2;
    margin-bottom: 2px;
    margin: 0 5px;
    padding-bottom: 4px;
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    .sup_name {
      flex: 3;
      display: flex;
      align-items: center;
      justify-content: flex-start;
      text-align: left;
    }
    .order-source-wrapper {
      border: 1px solid #fde3e4;
      padding: 1px 5px;
      background-color: #fde3e4;
      border-radius: 10px;
      margin-left: 10px;
    }
    .sup_contact {
      flex: 2;
      font-size: 16px;
      display: flex;
      flex-direction: column;
      align-items: flex-end;

      .sup_tel {
        margin: 2px 0;
        a {
          color: rgb(12, 89, 190);
        }
      }
    }
  }
  .sheet_info {
    margin: 2px 5px;
    display: flex;
    flex-direction: row;
    .sheet_row {
      display: flex;
      flex-direction: row;
      align-items: center;
      justify-content: center;
      align-content: center;
    }
    .sheet_info_left {
      flex: 2;
      display: flex;
      padding: 4px 0;
      flex-direction: column;
      .sheet_no_tag {
        display: flex;
        justify-content: space-between;
        .sheet_no {
        }
      }
      .sheet_happentime {
        display: flex;
        color: #ccc;
      }
    }
    .sheet_tag {
      flex: 1;
      max-width: 35px;
      display: flex;
      align-items: center;
      justify-content: center;
      span {
        padding: 2px;
      }
    }
    .sheet_info_right {
      flex: 2;
      display: flex;
      font-family: numfont;
      justify-content: center;
      align-items: center;
      color: #c40000;
    }
    .sheet_checked {
      display: flex;
      justify-content: center;
      align-items: center;
    }
  }
  .sheet_add {
    border-top: 1px solid #ddd;
    padding-top: 2px;
    width: 100%;
    color: #555;
    display: flex;
    padding-left: 5px;
  }
  .seller_senders_info {
    margin: 4px 5px 0;
    display: flex;
    justify-content: space-between;
    margin-right: 20px;
    color: #000;
  }
  .mark-brief {
    margin: 6px 5px 0;
    display: flex;
    color: #999;
  }
}
</style>
