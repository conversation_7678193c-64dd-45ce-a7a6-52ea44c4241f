import BluetoothPermissionHelper from './BluetoothPermissionHelper'

class BlePrinter{
    constructor(){
        this.permissionHelper = new BluetoothPermissionHelper()
    }
    async open_bluetooth() {
        const promise= new Promise((resolve,reject)=>{
                  ble.enable(
                      //开启成功
                    ()=> {resolve({ ret:1 })},
                    //开启失败
                    ()=> {reject({ ret:0})}
                  )
        })
        return promise
      }
    async list(){
        const promise=new Promise((resolve,reject)=>{
            ble.list(devices=>{
                resolve(devices)
            },(error)=>{
                // 使用权限助手处理错误
                if (this.permissionHelper.isPermissionError(error)) {
                    console.log("BLE列表获取权限错误，返回空数组")
                    resolve([]) // 权限错误时返回空数组而不是拒绝
                } else {
                    reject(error)
                }
            })
        })
        return promise
    }
    }
export default BlePrinter
