<!DOCTYPE html>
<html lang="en" style="background-color: #ffffff;">

<head>
  <!-- 立即设置白色背景，防止任何闪烁 -->
  <style>
    html, body {
      background-color: #000 !important;
      margin: 0 !important;
      padding: 0 !important;
    }
    #immediate-black-screen {
      position: fixed !important;
      top: 0 !important;
      left: 0 !important;
      right: 0 !important;
      bottom: 0 !important;
      width: 100vw !important;
      height: 100vh !important;
      background-color: #000000 !important;
      z-index: 999999 !important;
      display: block !important;
    }
  </style>

  <meta charset="utf-8">
  <meta http-equiv="X-UA-Compatible" content="IE=edge">
  <meta name="viewport" content="width=device-width,initial-scale=1.0">
  <meta name="viewport"
    content="width=device-width, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0, viewport-fit=cover" />
    <meta name="viewport" content="width=device-width,maximum-scale=1.0,initial-scale=1,user-scalable=no">
  <!-- 开启 safe-area-inset-bottom 属性 -->
  <!--<van-number-keyboard safe-area-inset-bottom />
  <link rel="icon" href=" BASE_URL favicon.ico">-->
  <!-- <link
  rel="stylesheet"
  href="https://cdnjs.cloudflare.com/ajax/libs/animate.css/4.1.1/animate.min.css"
/> -->
  <!-- JsBarcode已移除，现在使用按需加载方式 -->
  <script type="text/javascript" src="cordova.js"></script>
  <!--<title><%= htmlWebpackPlugin.options.title %></title>  -->
    <script type="text/javascript" src="js/index.js"></script>
    <!--zxk modify 2021/3/16-->
    <!-- <script type="text/javascript" src="http://api.map.baidu.com/api?v=2.0&ak=p9QBVLhfTeBAUnP43RDzEm1acmYPn8Bu"></script>
    <script type="text/javascript" src="http://api.map.baidu.com/api?type=webgl&v=1.0&ak=p9QBVLhfTeBAUnP43RDzEm1acmYPn8Bu"></script>
    <script type="text/javascript" src="http://bj.bcebos.com/v1/mapopen/github/BMapGLLib/Lushu/src/Lushu.min.js"></script>   -->
    <!--<title><%= htmlWebpackPlugin.options.title %></title>  -->
    <!-- <script src="static/common.js"></script> -->
    <!-- <script src="https://mapv.baidu.com/build/mapv.js"></script>
    <script src="https://code.bdstatic.com/npm/mapvgl@1.0.0-beta.159/dist/mapvgl.min.js"></script>
    <script src="https://code.bdstatic.com/npm/mapvgl@1.0.0-beta.159/dist/mapvgl.threelayers.min.js"></script> -->
    <!-- <link href="//mapopen.bj.bcebos.com/github/BMapGLLib/RichMarker/src/RichMarker.min.js" rel="stylesheet">
  <script src="//mapopen.bj.bcebos.com/github/BMapGLLib/RichMarker/src/RichMarker.min.js"></script>    <script type="text/javascript" src="js/yj_markercluster.js"></script> -->
    <!-- <script type="text/javascript" src="js/yj_markerclusterGL.js"></script> -->

    <script type="text/javascript">
      scanPlug.initialize();
    </script>

    <style>
      html, body {
        background-color: #ffffff !important;
        margin: 0;
        padding: 0;
        width: 100%;
        height: 100%;
        overflow: hidden;
      }

      #app {
        width: 100%;
        height: 100vh;
        background-color: #ffffff;
        position: relative;
      }

      html[data-theme='dark'] .app-loading {
        background-color: #2c344a;
      }

      html[data-theme='dark'] .app-loading .app-loading-title {
        color: rgb(255 255 255 / 85%);
      }
 

      .app-loading .app-loading-wrap {
        position: absolute;
        top: 50%;
        left: 50%;
        display: flex;
        transform: translate3d(-50%, -50%, 0);
        justify-content: center;
        align-items: center;
        flex-direction: column;
      }

      .app-loading .dots {
        display: flex;
        padding: 98px;
        justify-content: center;
        align-items: center;
      }

      .app-loading .app-loading-title {
        display: flex;
        margin-top: 30px;
        font-size: 30px;
        color: rgb(0 0 0 / 85%);
        justify-content: center;
        align-items: center;
      }

      .app-loading .app-loading-logo {
        display: block;
        width: 90px;
        margin: 0 auto;
        margin-bottom: 20px;
      }

      .dot {
        position: relative;
        display: inline-block;
        width: 48px;
        height: 48px;
        margin-top: 30px;
        font-size: 32px;
        transform: rotate(45deg);
        box-sizing: border-box;
        animation: antRotate 1.2s infinite linear;
      }

      .dot i {
        position: absolute;
        display: block;
        width: 20px;
        height: 20px;
        background-color: #cc0000;
        border-radius: 100%;
        opacity: 80%;
        transform: scale(0.75);
        animation: antSpinMove 1s infinite linear alternate;
        transform-origin: 50% 50%;
      }

      .dot i:nth-child(1) {
        top: 0;
        left: 0;
      }

      .dot i:nth-child(2) {
        top: 0;
        right: 0;
        animation-delay: 0.4s;
      }

      .dot i:nth-child(3) {
        bottom: 0;
        right: 0;
        animation-delay: 0.8s;
      }

      .dot i:nth-child(4) {
        bottom: 0;
        left: 0;
        animation-delay: 1.2s;
      }

      @keyframes antRotate {
        to {
          transform: rotate(405deg);
        }
      }

      @keyframes antSpinMove {
        to {
          opacity: 100%;
        }
      }
    </style>
</head>
<body style="background-color: #000; margin: 0; padding: 0;">
  <!-- 立即显示的黑色屏幕，与splash保持一致 
  <div id="immediate-black-screen" style="position: fixed; top: 0; left: 0; right: 0; bottom: 0; width: 100vw; height: 100vh; background-color: #000000; z-index: 99990; display: block;"></div>
-->
  <noscript>
    <strong>We're sorry but <%= htmlWebpackPlugin.options.title %> doesn't work properly without JavaScript enabled.
      Please enable it to continue.001</strong>
  </noscript>
  <!-- Vue应用挂载点 -->
  <div id="app"></div>

  <!-- 加载界面，黑色背景渐变到白色 -->
  <div id="app-loading" style="position: fixed; top: 0; left: 0; right: 0; bottom: 0; z-index: 99999; display: none; width: 100vw; height: 100vh; justify-content: center; align-items: center; flex-direction: column; margin: 0; padding: 0; transform: translateZ(0);">
    <!-- 白色背景层 -->
    <div id="loading-background" style="position: absolute; top: 0; left: 0; right: 0; bottom: 0; background-color: #ffffff; opacity: 0; transition: opacity 3s ease-out;"></div>

    <!-- 内容层 -->
    <div style="position: relative; z-index: 1; display: flex; flex-direction: column; justify-content: center; align-items: center;">
      <img src="./logo.svg" style="display: block; width: 90px; margin: 0 auto 20px auto;" alt="Logo"/>
      <div style="display: flex; padding: 98px; justify-content: center; align-items: center;">
        <span class="dot dot-spin"><i></i><i></i><i></i><i></i></span>
      </div>
      <div style="display: flex; margin-top: 30px; font-size: 30px; color: #000000; justify-content: center; align-items: center;">请稍候...</div>
    </div>
  </div>



  <!-- built files will be auto injected -->
</body>

</html>
