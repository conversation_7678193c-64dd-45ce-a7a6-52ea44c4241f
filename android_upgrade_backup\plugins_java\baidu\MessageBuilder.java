package com.lai.geolocation.baidu;

import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

import com.baidu.location.BDLocation;
import com.lai.geolocation.w3.Coordinates;
import com.lai.geolocation.w3.Position;

public class MessageBuilder {

  private BDLocation location;
  private JSONArray message;

  MessageBuilder(BDLocation location) {
    this.location = location;
    this.message = new JSONArray();
  }

  public JSONArray build() {
    if (message.length() == 0) {
      Position result = new Position()
        .setTimestamp(System.currentTimeMillis())
        .setCoords(new Coordinates()
           .setDescribe(location.getLocationDescribe())
          .setLatitude(location.getLatitude())
          .setLongitude(location.getLongitude())
          .setAccuracy(location.getRadius())
          .setHeading(location.getDirection())
          .setSpeed(location.getSpeed())
          .setAltitude(location.getAltitude())
        );

      JSONObject extra = new JSONObject();
      try {
        extra.put("type", location.getLocType());
        extra.put("addr", location.getAddrStr());
        extra.put("gpsAccuracyStatus", location.getGpsAccuracyStatus());
        // 添加位置提供者信息
        extra.put("provider", location.getLocType() == BDLocation.TypeGpsLocation ? "gps" : "network");
      } catch (JSONException e) {
        e.printStackTrace();
      }

      message.put(result.toJSON());
      message.put(extra);
    }

    return message;
  }

  // 添加额外属性的辅助方法
  public MessageBuilder addProperty(String key, Object value) {
    try {
      // 确保message已经初始化
      if (message.length() == 0) {
        build();
      }
      
      // 获取extra对象
      JSONObject extra;
      if (message.length() > 1) {
        extra = message.getJSONObject(1);
      } else {
        extra = new JSONObject();
        message.put(extra);
      }
      
      // 添加属性
      extra.put(key, value);
    } catch (JSONException e) {
      e.printStackTrace();
    }
    return this;
  }
}
