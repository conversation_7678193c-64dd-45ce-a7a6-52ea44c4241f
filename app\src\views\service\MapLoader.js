import { BAIDU_API_URLS } from '../../config/baiduConfig.js';

// export default async function AppUseBaiduMap () {
//     //   var script = document.createElement("script");
// 		// script.type = "text/javascript";
// 		// script.src = "http://api.map.baidu.com/api?v=2.0&ak=SLrOIdVUUI5fbvsZTMqTFshsG993NBAG&callback=init";
// 		// document.body.appendChild(script);
// 		// setTimeout(init,400)//此处进行手动调用
// //         var loadUrls = ["http://api.map.baidu.com/api?v=2.0&ak=p9QBVLhfTeBAUnP43RDzEm1acmYPn8Bu",
// //         "http://api.map.baidu.com/api?type=webgl&v=1.0&ak=p9QBVLhfTeBAUnP43RDzEm1acmYPn8Bu",
// //       "http://bj.bcebos.com/v1/mapopen/github/BMapGLLib/Lushu/src/Lushu.min.js",
// //     "https://mapv.baidu.com/build/mapv.js",
// //     "https://code.bdstatic.com/npm/mapvgl@1.0.0-beta.159/dist/mapvgl.min.js",
// //   "https://code.bdstatic.com/npm/mapvgl@1.0.0-beta.159/dist/mapvgl.threelayers.min.js"]
// loadUrls.map((url)=>{
// var script = document.createElement("script");
// script.type = "text/javascript";
// script.src = url
// document.body.appendChild(script);
// console.log(document.body)
// setTimeout(()=>{
//     window.BMap = BMap
//     window.BMapGL =BMapGL
// },400)//此处进行手动调用
// })
//   }
  

  export function UseCommonMap() {
    return new Promise(function(resolve, reject) {
        if (!window.BMap) {
            const script = document.createElement('script');
            script.type = 'text/javascript';
            script.src = `${BAIDU_API_URLS.MAP_SCRIPT_V2}&callback=commonMapInit`;
            script.onerror = reject;
            document.head.appendChild(script);
            console.error('window.BMap', window.BMap);
            window.commonMapInit = function () {
                console.log("百度地图脚本初始化成功...");
                resolve(BMap);
            };
          }else{
            resolve(window.BMap);

          }
    })
  }
  export function UseGLMap() {
    return new Promise((resolve, reject)=> {
        console.log(window.BMapGL)

            const script = document.createElement('script');
            script.type = 'text/javascript';
            script.src = `${BAIDU_API_URLS.MAP_SCRIPT_WEBGL}&callback=glMapInit`;
            script.onerror = reject;
            document.head.appendChild(script);
            console.log('window.BMap', window.BMapGL);
            window.glMapInit = function () {
                console.log("百度地图脚本初始化成功...");
                resolve(BMapGL);
            };


    })
  }
  export function UseLushu() {
    return new Promise(function(resolve, reject) {
        if (!window.BMapGLLib) {
            const script = document.createElement('script');
            script.type = 'text/javascript';
            script.src = `http://bj.bcebos.com/v1/mapopen/github/BMapGLLib/Lushu/src/Lushu.min.js`;
            script.onerror = reject;
            document.head.appendChild(script);
            console.error('window.BMap', window.BMapGLLib);
                resolve(window.BMapGLLib);

          }else{
            resolve(window.BMapGLLib);
          }
    })
  }

    export function UseMapV() {
        return new Promise(function(resolve, reject) {
                const script = document.createElement('script');
                script.type = 'text/javascript';
                script.src = `https://mapv.baidu.com/build/mapv.js`;
                script.onerror = reject;
                document.head.appendChild(script);
                console.log(window.mapvgl)
                resolve(window.mapvgl)
        })
    }
    export function UseMapvGL() {
        return new Promise(function(resolve, reject) {
            if (!window.mapvgl) {
                const script = document.createElement('script');
                script.type = 'text/javascript';
                script.src = `https://code.bdstatic.com/npm/mapvgl@1.0.0-beta.159/dist/mapvgl.min.js`;
                script.onerror = reject;
                document.head.appendChild(script);
                resolve(window.mapvgl)
              }else{
                resolve(window.mapvgl)
              }
        })
    }
        export function UseMapvGLThreeLayers() {
            return new Promise(function(resolve, reject) {
                if (!window.lushu) {
                    const script = document.createElement('script');
                    script.type = 'text/javascript';
                    script.src = `https://code.bdstatic.com/npm/mapvgl@1.0.0-beta.159/dist/mapvgl.threelayers.min.js`;
                    script.onerror = reject;
                    document.head.appendChild(script);
                    resolve(window.mapvgl)
                  }
            })
    }
