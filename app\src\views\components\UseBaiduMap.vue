<template>

</template>
<script>
import { BAIDU_API_URLS } from '../../config/baiduConfig.js';

export default {

    mounted(){
        const useScriptUrls = [
            BAIDU_API_URLS.MAP_SCRIPT_V2,
            BAIDU_API_URLS.MAP_SCRIPT_WEBGL,
            'http://bj.bcebos.com/v1/mapopen/github/BMapGLLib/Lushu/src/Lushu.min.js',
            'https://mapv.baidu.com/build/mapv.js',
            'https://code.bdstatic.com/npm/mapvgl@1.0.0-beta.159/dist/mapvgl.min.js',
            'https://code.bdstatic.com/npm/mapvgl@1.0.0-beta.159/dist/mapvgl.threelayers.min.js'
        ]
        useScriptUrls.map(url=>{
            const oScript = document.createElement('script');
            oScript.type = 'text/javascript';
            oScript.src = url;
            document.body.appendChild(oScript);
        })

    }
}
</script>
<style scoped>

</style>