package org.apache.cordova;

import android.util.Log;
import org.json.JSONException;
import org.json.JSONObject;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Locale;
import org.json.JSONArray;

/**
 * 全局WebView事件管理器
 * 管理WebView加载过程中的所有事件和错误
 * 只有在出现错误时才上报完整的事件历史
 */
public class CordovaWebViewErrorManager {
    private static final String TAG = "CordovaWebViewErrorManager";    
    private static CordovaWebViewErrorManager instance;

    private final List<WebViewEvent> eventHistory = new ArrayList<>(); 
    private final SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss.SSS", Locale.getDefault());                             
    private boolean hasError = false; // 是否出现过错误的标记    



    /**
     * WebView事件信息类（包括错误和普通事件）
     */
    public static class WebViewEvent {
        private final boolean isError;
        private final int errorCode;
        private final String description;
        private final String url;
        private final long timestamp;
        private final long duration; // 耗时(毫秒)

        public WebViewEvent(boolean isError, int errorCode, String description, String url, long timestamp, long duration) {
            this.isError = isError;
            this.errorCode = errorCode;
            this.description = description;
            this.url = url;
            this.timestamp = timestamp;
            this.duration = duration;
        }

        // Getters
        public boolean isError() { return isError; }
        public int getErrorCode() { return errorCode; }
        public String getDescription() { return description; }
        public String getUrl() { return url; }
        public long getTimestamp() { return timestamp; }
        public long getDuration() { return duration; }

        /**
         * 转换为JSON对象
         */
        public JSONObject toJson() {
            try {
                JSONObject json = new JSONObject();
                json.put("type", isError ? "ERROR" : "EVENT");
                json.put("isError", isError);
                json.put("errorCode", errorCode);
                json.put("description", description);
                json.put("url", url);
                json.put("timestamp", timestamp);
                json.put("duration", duration);
                json.put("time", new SimpleDateFormat("yyyy-MM-dd HH:mm:ss.SSS", Locale.getDefault()).format(new Date(timestamp)));
                return json;
            } catch (JSONException e) {
                return new JSONObject();
            }
        }
    }

    // 单例模式
    public static synchronized CordovaWebViewErrorManager getInstance() {
        if (instance == null) {
            instance = new CordovaWebViewErrorManager();
        }
        return instance;
    }

    /**
     * 添加事件记录（包括错误和普通事件）
     * @param isError 是否为错误事件
     * @param msg 事件消息描述
     */
    public synchronized void addEvent(boolean isError, String msg) {
        addEvent(isError, 0, msg, "", 0);
    }

    /**
     * 添加事件记录（包括错误和普通事件）
     * @param isError 是否为错误事件
     * @param errorCode 错误码
     * @param description 事件描述
     * @param url 相关URL
     * @param duration 耗时(毫秒)
     */
    public synchronized void addEvent(boolean isError, int errorCode, String description, String url, long duration) {
        WebViewEvent event = new WebViewEvent(isError, errorCode, description, url, System.currentTimeMillis(), duration);
        eventHistory.add(event);

        // 限制历史记录数量，避免内存溢出
        if (eventHistory.size() > 100) {
            eventHistory.remove(0);
        }

        // 如果是错误事件，标记出现过错误
        if (isError) {
            hasError = true;
        }

        String eventType = isError ? "错误" : "事件";
        Log.d(TAG, String.format("[%s] %s: %s (URL: %s, Duration: %dms)",
            dateFormat.format(new Date(event.getTimestamp())),
            eventType,
            description,
            url,
            duration));
    }

    /**
     * 获取事件统计信息
     */
    public synchronized JSONObject getEventStats() {
        try {
            JSONObject stats = new JSONObject();

            int errorCount = 0;
            int eventCount = 0;

            for (WebViewEvent event : eventHistory) {
                if (event.isError()) {
                    errorCount++;
                } else {
                    eventCount++;
                }
            }

            stats.put("totalEvents", eventHistory.size());
            stats.put("hasError", hasError);
            stats.put("errorCount", errorCount);
            stats.put("eventCount", eventCount);

            return stats;
        } catch (JSONException e) {
            return new JSONObject();
        }
    }

    // Getters and setters
    public synchronized List<WebViewEvent> getEventHistory() {
        return new ArrayList<>(eventHistory);
    }

    /**
     * 检查是否出现过错误（用于决定是否需要上报）
     * @return true表示出现过错误，需要上报；false表示没有错误，不需要上报
     */
    public synchronized boolean hasError() {
        return hasError;
    }

    /**
     * 重置错误状态（在上报完成后调用）
     * 调用此方法后，即使之前出现过错误，也会重置状态为无错误
     */
    public synchronized void resetErrorState() {
        hasError = false;
    }

    /**
     * 清除所有事件历史并重置错误状态
     */
    public synchronized void clearEventHistory() {
        eventHistory.clear();
        hasError = false;
    }
}