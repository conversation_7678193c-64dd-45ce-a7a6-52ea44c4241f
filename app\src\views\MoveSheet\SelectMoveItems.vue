<template>
  <div class="pages">
    <!-- <van-nav-bar title="选择商品" left-arrow @click-left=" myGoBack($router)"/>
    <div>
      <van-search v-model="searchStr" left-icon placeholder="请输入搜索关键词" @input="onSrcItem">
        <template #right-icon>

          <i class="iconfont">&#xe63c;</i>
        </template>
      </van-search>
    </div> -->

    <div class="serchWrapper">
      <div class="go_back" @click="myGoBack($router)">
        <van-icon name="arrow-left" />
      </div>
      <div class="search_content">
        <van-search  id="txtSearch" v-model="searchStr" left-icon placeholder="名称/条码/首拼" @input="onSrcItem" @click="handleSrcClear">
          <template #right-icon>
              <!-- <van-icon name="search" /> -->
            <i class="iconfont" style="font-size:12px;">&#xe63c;</i>
          </template>
        </van-search>
      </div>
      <div class="search_layout">
        <van-button class="footer_iconBt" @click="btnScanBarcode_click" style="border: none;background-color: transparent">
          <svg width="30px" height="30px" fill="#666">
            <use xlink:href="#icon-barcodeScan"></use>
          </svg>
        </van-button>
      </div>
      <div class="search_layout">
        <div class="iconfont icon_right" @click="orderByOptions = !orderByOptions">
          &#xe690;
        </div>
      </div>
    </div>
    <div class="class_box">
      <div class="class_box_l">
        <SelectItemsClasses :sheet="sheet"/>
      </div>
      <div class="class_box_r">
        <SelectMoveItemsItems
          :canSeeFromStock='canSeeFromStock'
          :canSeeToStock='canSeeToStock'
          ref="moveItems"
          @onItemsSelect_OK="onItemsSelect_OK"
          @handleNewItemClik="handleNewItemClik"
          @handleEditItem="handleEditItem"
        />
      </div>
    </div>
    <!-- <div class="public_footer">
      <van-button type="info"  @click="btnSelectItemOK_click" v-if="Number(submitBtn)>0" style="width:100px;" size="normal">确定({{submitBtn}})</van-button>
      <van-button type="info" disabled  v-else size="normal" style="width:100px;">确定</van-button>
    </div> -->

    <div class="class_footers">
      <div class="class_footer_left" @click="onStock" :style="{'color':queryCondition.showStockOnly ? '#000':'#aaa'}">
        <div class="footer_left_top" >
          <div>只看有库存</div>
          <div>
            <van-icon v-if="queryCondition.showStockOnly" name="checked" color="#000" size="18" />
            <div v-else style="width:15px;height:15px;border-radius:50%;border:1px solid #aaa"></div>
          </div>
        </div>
        <div class="footer_left_bottom">

        </div>
      </div>
      <div class="class_footer_center"> </div>
      <div class="class_footer_right">
        <van-badge
          v-if="(productLists.length ? productLists.length : 0) !== 0"
          :content="productLists.length ? productLists.length : 0">
             <button class="selectOkBtn"  @click="btnSelectItemOK_click">选好了</button>
        </van-badge>
        <button v-else class="selectOkBtn" @click="closePage">选好了</button>
      </div>
    </div>
    <!-- <van-popup v-model="popupAddMoveSheetRow" duration="0.4"  :lazy-render="false"  position="bottom" style="height:90%;overflow:hidden">
     <div style="height:100%;">
      <h5 class="custom_h5">
        添加调拨商品
        <van-icon
          name="cross"
          class="icon_h5"
          @click="popupAddMoveSheetRow = false"
        />
      </h5>
      <div style="height:100%;" class="class_add_goods">
        <AddMoveSheetRow ref="dlgEditRow" @onAddRow_OK="onAddRow_OK"/>
      </div>
      </div>

    </van-popup> -->


    <!-- 多选组件 -->
    <van-popup
      v-model="popupAddMoveSheetRow"
      :lazy-render="true"
      position="bottom"
      style="height:100vh;overflow:hidden">
      <MultiSelect
        v-if="popupAddMoveSheetRow"
        ref="multiSelect"
        :sheet="sheet"
        :moveType="moveType"
        :canSeeFromStock='canSeeFromStock'
        :canSeeToStock='canSeeToStock'
        @closePage="closePage"
        @closeSingleChoicePage="closeSingleChoicePage"
        @onAddRow_OK="onAddRow_OK"
      >
      </MultiSelect>
    </van-popup>

    <van-popup
      v-model="popupEditSheetRows"
      position="bottom"
      :style="{ height: '415px' }">
      <EditSheetRows
        ref="editSheetRows"
        @closeEdit="closeEdit"
        :sheet = "currentSheet"
        :editSheetRowsInfo="editSheetRowsInfo"
        :fromBranchPositionSource="fromBranchPositionSource"
        :toBranchPositionSource="toBranchPositionSource"
      />
    </van-popup>
    <van-popup class="van_popup" duration="0.4" v-model="orderByOptions" position="right" safe-area-inset-bottom
      :style="{ width: '85%', height: '100%' }">
      <div class="choose_wrapper">
         <div class="choose_text">排序方式</div>
           <van-dropdown-menu>
             <van-dropdown-item v-model="sort_type" :options="sortOption" />
           </van-dropdown-menu>
      </div>
      <div class="btnWrapper">
        <button class="selectOkBtn" @click="saveOrderByOption">保存配置</button>
      </div>
    </van-popup>
  </div>
</template>

<script>
import { NavBar,Search,Icon,Button,Popup,Badge, DropdownMenu, DropdownItem} from "vant"
import SelectItemsClasses from "../SaleSheet/SelectItems_Classes"
import SelectMoveItemsItems from "./SelectMoveItems_Items"
import EditSheetRows from "./EditSheetRows";
import MultiSelect from "./MultiSelect"
export default {
  name:'SelectMoveItems',
  data(){
    return{
      popupAddMoveSheetRow:false,
      productId: {},
            //查看出仓权限
      canSeeFromStock:true,
      //查看出仓权限
      canSeeToStock:true,
      productIdActive:'',
      fromBranchID:'',
      toBranchID:'',
      productLists:[],
      submitBtn:0,
      searchStr:'',
      classID:'',
      selectedItems:[],
      sheet: {},
      queryCondition: {showStockOnly:false},
      moveType:'',
      popupEditSheetRows: false,
      editSheetRowsInfo:[],
      inputTimer: '',
      orderByOptions: false,
      sort_type:"default",
      sortOption: [
        { text: '默认', value: 'default' },
        { text: '商品名称', value: 'item_name' },
        { text: '序号', value: 'order_index' }
      ],
      fromBranchPositionSource: [],
      toBranchPositionSource:[]
    }
  },
  provide() {
    return {
      queryCondition : this.queryCondition
    }
  },
  computed:{
    currentSheet() {
      return this.$store.state.currentSheet
    }
  },
  components: {
    "van-nav-bar": NavBar,
    "van-search":Search,
    "van-icon":Icon,
    "van-button":Button,
    "van-popup":Popup,
    "van-badge":Badge,
    "van-dropdown-menu":DropdownMenu,
    "van-dropdown-item":DropdownItem,
    SelectItemsClasses,
    SelectMoveItemsItems,
    MultiSelect,
    EditSheetRows
  },
  activated(){
    this.queryCondition.showStockOnly = this.$store.state.showStockOnly
  },
  watch:{
    '$store.state.classId':function(){ //监听vuex中userName变化而改变header里面的值
      this.classID = this.$store.state.classId;
      let condi = {
        fromBranchID:this.fromBranchID,
        toBranchID:this.toBranchID,
        classID: this.classID,
        searchStr:this.searchStr,
        showStockOnly: this.queryCondition.showStockOnly
      }
      this.startNewQuery(condi)
    },
    'queryCondition.showStockOnly': {
      handler: function (val, oldVal) {
        this.$store.state.showStockOnly = val
      },
       deep: true
    },
  },
  mounted() {
    this.sheet = this.$route.query.sheet
    this.moveType = this.$route.query.moveType
    this.searchStr = this.$route.query.searchStr?this.$route.query.searchStr:''
    this.fromBranchID = this.$route.query.fromBranchID
    this.toBranchID = this.$route.query.toBranchID
    this.fromBranchPositionSource = this.$route.query.fromBranchPositionSource
    this.toBranchPositionSource = this.$route.query.toBranchPositionSource
    this.canSeeFromStock=window.hasBranchOperRight(this.fromBranchID,'query_stock')
    this.canSeeToStock=window.hasBranchOperRight(this.toBranchID,'query_stock')
    this.queryCondition.showStockOnly=this.$store.state.showStockOnly//必须这样赋值才能通过provide将值传给子组件,queryCondition=new {}这样不行
    this.queryCondition.branchID= this.fromBranchID
    this.queryCondition.fromBranchID= this.fromBranchID
    this.queryCondition.toBranchID= this.toBranchID
    this.queryCondition.classID=''
    this.queryCondition.searchStr=this.searchStr
    this.sort_type = this.$store.state.queryItemSortType ?? "default"
    this.queryCondition.sortType = this.sort_type

     /*
    this.queryCondition={
      showStockOnly:this.$store.state.showStockOnly,
      branchID:this.fromBranchID,//为了给selectItemClass中只显示库存时查询库存，所以加了这变量
      fromBranchID:this.fromBranchID,
      toBranchID:this.toBranchID,
      classID: '',
      searchStr:this.searchStr,
    }*/

    this.startNewQuery(this.queryCondition)
  },
  beforeRouteLeave(to, from, next) {
    to.query.selectedItems = this.selectedItems
    console.log(this.selectedItems);
    next()
  },
  methods:{
    saveOrderByOption() {

      this.orderByOptions = false
      this.$store.commit("queryItemSortType",this.sort_type)
      this.startNewQuery(this.queryCondition)
    },
    startNewQuery(condi){
        condi.sortType = this.sort_type
        this.$refs.moveItems.startNewQuery(condi)
      // setTimeout(() => {

      // },300)
    },
    onStock(){
      this.queryCondition.showStockOnly = !this.queryCondition.showStockOnly
      let condi = {
        fromBranchID:this.fromBranchID,
        toBranchID:this.toBranchID,
        searchStr:this.searchStr,
        showStockOnly: this.queryCondition.showStockOnly
      }
      this.startNewQuery(condi)
    },
    onSrcItem(value){
      this.searchStr = value;
      if (this.inputTimer) clearTimeout(this.inputTimer)
      this.inputTimer = 0
      this.inputTimer = setTimeout(() => {
        this.inputTimer = 0
        let condi = {
          fromBranchID:this.fromBranchID,
          toBranchID:this.toBranchID,
          searchStr:this.searchStr,
          classID:this.classID,
          showStockOnly: this.queryCondition.showStockOnly
        }
        this.startNewQuery(condi)
      }, 300)


    },
    handleSrcClear() {
      if( this.searchStr !== '') {
        this.onSrcItem('')
      }
    },
    onloadSumBtn(){
      //console.log(this.$store.state.itemList)
      this.submitBtn = this.$store.state.itemList.length
    },
    onItemsSelect_OK(obj){
      this.productLists = obj
      this.$store.commit("shoppingCarObj",{
          updateFlag: true,
          sheetType: this.moveType,
          val: this.productLists
        })
      //
      //this.onBtnSunmber()
    },
    onBtnSunmber(){
      this.submitBtn = this.productLists.length
    },
    // 弹窗新增
    btnSelectItemOK_click(){
      this.popupAddMoveSheetRow = true
      this.$store.commit("multiSelectOpenFlag",this.popupAddMoveSheetRow)
      this.$store.commit("attrShowFlag",false)
      setTimeout(() => {
        this.$refs.multiSelect.loadData(this.productLists)
      }, 360);

    },
    onAddRow_OK(){
       this.$emit("onAddRow_OK")
    },
    // 单选
    handleNewItemClik(item) {
      this.$store.commit('distinctStockFlag',false)
      this.$store.commit("shoppingCarFinish",false)
      let itemObj = JSON.parse(JSON.stringify(item))
      itemObj.isSelectFlag = false
      itemObj.singleChoice = true
      itemObj.distinctStockFlag = false
      if(itemObj.mum_attributes) {
        if(!itemObj.mum_attributes.forEach) itemObj.mum_attributes=JSON.parse(itemObj.mum_attributes)
        if(itemObj.mum_attributes.find(attr=>attr.distinctStock)) {
          this.$store.commit('distinctStockFlag',true)
          itemObj.distinctStockFlag = true
        }
      }
      this.popupAddMoveSheetRow = true
      this.$store.commit("multiSelectOpenFlag",true)
      this.$store.commit("attrShowFlag",false)
      setTimeout(() => {
        this.$refs.multiSelect.loadData([itemObj])
      }, 310);

      // if(!this.attrShowFlag) {
      //   this.$store.commit('activeSelectItem',activeSelectItem)
      // }
      // this.$refs.dlgEditRow.loadData(activeSelectItem)

    },
    // onSheetRowAdd_OK(item) {
    //   let temp = this.$store.state.currentSheet
    //   row_UnitsProps2Array(item)
    //   this.$store.commit("currentSheet",temp)
    // },
    closePage(flag) {
      this.popupAddMoveSheetRow = false
      this.$store.commit("multiSelectOpenFlag",false)
      this.$store.commit("shoppingCarFinish",false)
      this.$store.commit('shoppingCarObj',{
        sheetType:this.moveType,
        clearFlag: true
      })
      if(flag !== 'noBack') {
        myGoBack(this.$router)
      }

    },
    closeSingleChoicePage() {
      this.$store.commit("multiSelectOpenFlag",false)
      this.$store.commit("shoppingCarFinish",false)
      this.popupAddMoveSheetRow = false
      this.$forceUpdate()
    },
    handleEditItem(editSheetRowsInfo) {
      this.editSheetRowsInfo = editSheetRowsInfo.sheetRows
      this.popupEditSheetRows = true
      setTimeout(() => {
        this.$refs.editSheetRows.loadData()
      }, 350);
    },
    closeEdit() {
      this.$refs.editSheetRows.clearState()
      this.popupEditSheetRows = false
    },

    async btnScanBarcode_click() {
      try {
        const result = await this.scanBarcodeNew({
          unit_type: 'select_move_items'
        })

        if (!result.code) {
          return
        }

        this.onSrcItem(result.code)
      } catch (error) {
        console.error('扫码失败:', error)
        this.$toast('扫码失败，请重试')
      }
    },
  }
}
</script>

<style lang="less" scoped>

@flex_acent_jbw:{
  display: flex;
  align-items: center;
  justify-content: space-between;
};
@flex_a_j: {
  display: flex;
  align-items: center;
  justify-content: center;
};
@flex_acent_jend:{
  display: flex;
  align-items: center;
  justify-content: flex-end;
};
.iconfont{
  color: #2760BA;
}
.serchWrapper{
  // background-color: #eee;
  background-image: linear-gradient(to bottom, #fff 0%, #eee 100%);
  height: 46PX;
  //border-bottom: 1px solid #f0f0f0;
  display: flex;
  align-items: center;
  .go_back {
    flex: 1;
  }
  .search_content{
    flex: 3;
  }
  .search_layout{
    flex:1;
  }
}
.van-search{
  background: transparent;
  padding: 0;
  .van-search__content{
    background: transparent;
  }
  /deep/.van-field__body{
    border-bottom: 1PX solid #e6e6e6 !important;
    width: 80%;
    margin: auto;
    font-size: 15PX;
  }
  /deep/#txtSearch{
    height: 20PX;
  }
  /deep/.van-field__right-icon{
    height: 24PX;
    line-height: 24PX;
  }
}
.iconfont{
  color: #bbb;
}
.class_box{
  height: calc(100% - 100PX);
  @flex_acent_jbw();
  .class_box_l{
    width: 32%;
    height: 100%;
    background: #eee;

    overflow-x: hidden;
    overflow-y: auto;

  }
  .class_box_l::-webkit-scrollbar{
    display:none;
  }
  .class_box_r{
    width: 75%;
    height: 100%;
  }
}
.class_footers{
  padding-top: 5PX;
  height: 40PX;
  border-top: 2PX solid #f2f2f2;
  background: #ffffff;
  display:flex;
  z-index: 1;
  .class_footer_left{
    width: 100%;
    flex: 1;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    .footer_left_top{
      width: 100%;
      display: flex;
      justify-content: center;
      :nth-child(1) {
        font-size: 16PX;
      }
      :nth-child(2) {
        padding: 3PX 5PX;
        display: flex;
        align-items: center;
        justify-content: center;

      }
    }
    .footer_left_bottom{
      font-size: 15PX;
      width: 100%;
      display: flex;
      justify-content: center;
      align-items: center;
    }
  }
  .class_footer_center{
    flex: 1;
    display: flex;
    justify-content: center;
    align-items: center;
    color:#aaa;
    font-size: 15PX;
  }
  .class_footer_right{
    flex: 1;
    display: flex;
    justify-content: center;
    align-items: center;
    .selectOkBtn{
      background-color: #FDE3E4;
      height:36PX;
      width:80PX;
      margin-bottom:4PX;
      border-radius:10PX;
      font-size: 16PX;
     // font-weight: bolder;
      color:#000;
      padding:0;
    }
  }
}
.custom_h5 {
  height: 46PX;
  line-height: 46PX;
  font-size: 16PX;
  color: #333333;
  background: #f2f2f2;
  color: #333333;
  position: relative;
  .icon_h5 {
    position: absolute;
    height: 46PX;
    width: 46PX;
    right: 0;
    top: 0;
    display: block;
    @flex_a_j();
    font-size: 20PX;
  }
}
.class_add_goods{
  height:calc(100% - 46px);
  overflow: auto;
  background: #f2f2f2;

}
body{
  height: 100%;
  overflow: hidden;
}
.van_popup {
  display: flex;
  flex-direction: column;
  width: 100%;

  .btnWrapper {
    width: 100%;
    box-sizing: border-box;
    padding: 0 20px;
    display: flex;
    position: fixed;
    bottom: 50px;
    justify-content: flex-end;

    .selectOkBtn {
      background-color: #fdd3d4;
      height: 36px;
      width: 80px;
      border-radius: 10px;
      // font-weight: bolder;
      color: #000;
      padding: 0;
    }
  }
}
</style>
