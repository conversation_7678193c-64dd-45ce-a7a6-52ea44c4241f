<template>
  <div class="sheet-wrapper">
    <div class="sheet-container">
      <div class="sheet-nar-bar" v-if="isPage">
        <van-nav-bar title="查门店库存上报单" left-arrow @click-left="myGoBack($router)" :placeholder="true">
          <template #right>
            <van-icon name="apps-o" size="18" @click="handleOriginShowFlag"/>
          </template>
        </van-nav-bar>
      </div>
      <div class="sheet-content" ref="sheetContentRef" id="StoreStockSheetsViewSheetContentId">
        <van-popup
          v-model="queryInfoWrapperSimpleShowFlag"
          position="top"
          :overlay="false"
          :style="{ width: '100vw','margin-top': isPage ? '46px' : '0px', left: popupLeft + 'px' }">
          <div class="sheet-query-popup">
            <StoreStockSheetsViewParams
              :isMountedQuery="true"
              :component-role="'simple'"
              :queryParams.sync="queryParams"
              @handleFinishSelect="handleFinishSelect"
              @handleClearItem="handleClearItem"
              @handleOriginShowFlag="handleOriginShowFlag"
              @handleClearAll="handleClearAll"/>
          </div>
          <div class="search_content" >
            <van-search id="txtSearch" v-model="querySearchStr" left-icon placeholder="单号"
              @input="onSearchStrChange" @click="onSearchStrClick">
              <template #right-icon>
                <i class="iconfont">&#xe63c;</i>
              </template>
            </van-search>
          </div>
          <ConcaveDottedCenter />
        </van-popup>
        <div class="sheet-query-wrapper" >
          <!--占位高度使用-->
          <StoreStockSheetsViewParams
            :component-role="'simple'"
            :isMountedQuery="false"
            :queryParams.sync="queryParams"
            @handleClearItem="handleClearItem"
            @handleFinishSelect="handleFinishSelect"
            @handleOriginShowFlag="handleOriginShowFlag"
            @handleClearAll="handleClearAll"/>
          <van-search id="txtSearch" :style="{ opacity: 0, pointerEvents: 'none' }" v-model="querySearchStr" left-icon placeholder="单号" 
            @input="onSearchStrChange" @click="onSearchStrClick">
          <template #right-icon>
              <i class="iconfont">&#xe63c;</i>
            </template>
          </van-search>
          <ConcaveDottedCenter />
        </div>
<!--        <van-sticky :offset-top="46" >-->
<!--          <div class="sheet-list-tip">-->
<!--            <div class="list-tip-item">客户</div>-->
<!--            <div class="list-tip-item">单号</div>-->
<!--            <div class="list-tip-item">入库</div>-->
<!--            <div class="list-tip-item">时间</div>-->
<!--            <div class="list-tip-item">额度</div>-->
<!--          </div>-->
<!--        </van-sticky>-->
        <div class="sheet-list-content">
          <van-pull-refresh v-model="refreshing" @refresh="onRefresh">
            <van-list
              v-model="loading"
              :finished="finished"
              finished-text="没有更多了"
              :immediate-check="false"
              @load="onLoad"
            >
              <ul class="list-item-wrapper">
                <li class="list-item-content" v-for="(item,index) in list" :key="index" @click="onSheetClick(item)">
                  <div class="sheet-main-info">
                    <div class="main-info-right gray-color">
                      {{ item.sheet_no }}
                    </div>
                    <div class="base-info-right">
                      <span v-if="item.state === 'approved'" style="color:#07c160">已审核</span>
                      <span v-if="item.state === 'unapproved'" style="color:#ff976a">未审核</span>
                      <span v-if="item.state === 'red'" style="color:#ee0a24">红冲</span>
                      <span v-if="item.state === 'reded'" style="color:#999999">被红冲</span>
                    </div>
                  </div>
                  <div class="sheet-main-info">
                    <div class="main-info-left"></div>
                    <div class="main-info-right"></div>
                  </div>
                  <div class="sheet-main-info">
                    <div class="main-info-left gray-color">{{ getShortTime(item[timeType])}} {{ item.oper_name }}</div>
                    <div class="main-info-right red-color">

                    </div>
                  </div>
                </li>
              </ul>
            </van-list>
          </van-pull-refresh>
        </div>
      </div>
      <div class="sheet-footer">
        <div class="sheet-list-basic">
          <div class="list-total-record">共 {{ total }} 条</div>
          <div class="list-total-amount"></div>
        </div>
        <div class="sheet-list-detail" ></div>
      </div>
    </div>
    <van-popup
      key="StoreStockSheetsView"
      v-model="SaleOrderSheetsPopupShowFlag"
      position="bottom"
      get-container="body"
      @close="handlePopupClose"
      :style="{ width: '100%', height : '90%'}">
        <StoreStockSheetsViewParams
          :isMountedQuery="false"
          :component-role="'original'"
          :queryParams.sync="queryParams"
          @handleClose="handleClose"
          @handleFinishSelect="handleFinishSelect"
          @handleClearAll="handleClearAll"
        />
    </van-popup>
  </div>
</template>

<script>
import {NavBar, Search, Icon, PullRefresh, List, Cell, Sticky, Popup, Toast, Row, Col} from "vant";
import StoreStockSheetsViewParams from "./StoreStockSheetsViewParams.vue";
import {GetAllStoreStockSheets} from "../../../api/api";
import ConcaveDottedCenter from "../../components/ConcaveDottedCenter.vue";
export default {
  name: "StoreStockSheetsView",
  components: {
    StoreStockSheetsViewParams,
    ConcaveDottedCenter,
    "van-search": Search,
    "van-nav-bar": NavBar,
    "van-icon": Icon,
    "van-pull-refresh": PullRefresh,
    "van-list": List,
    "van-cell": Cell,
    "van-sticky": Sticky,
    "van-popup": Popup,
    "van-row": Row,
    "van-col": Col,
  },
  props: {
    isPage: {
      type: Boolean,
      default: true
    },
    popupLeft: {
      type: Number,
      default: 0
    },
    allQueryParams: {
      type: Object,
      default: () => {}
    }
  },
  computed: {
    operRegions() {
      let operRegions = this.$store.state.operInfo.operRegions;
      if (operRegions) {
        return JSON.stringify(operRegions)
      }
      return ''
    },
    timeType() {
      return this.queryParams.timeTypeInfo.map(item => {return item.id}).join(",")
    }
  },
  data() {
    return {
      list: [],
      loading: false,
      finished: false,
      refreshing: false,
      viewAllFlag: false,
      firstQueryData:true,
      querySearchStr:'',
      queryParams: {
        sheetType: 'SS',
        pageSize: 50,
        startRow: 0,
        getTotal: true,
        regionsID: '',
        startDate: '',
        endDate: '',
        sellerInfo: [],
        customerInfo: [],
        approveStatus: [{ title: '所有', key: 'all' }],
        showRed: false,
        timeTypeInfo: this.$store.state.timeTypeInfo[0]?this.$store.state.timeTypeInfo:[{ title: '交易时间', id: 'happen_time' }]
        // timeTypeInfo: [{ title: '交易时间', id: 'happen_time' }]
      },
      queryInfoWrapperTop: 0, // 滚动相关
      queryInfoScroll: 0, // 滚动相关
      queryInfoWrapperSimpleShowFlag: true, // 筛选弹窗快捷
      SaleOrderSheetsPopupShowFlag: false,  // 全部筛选弹窗
      total: 0
    };
  },
  mounted() {
    this.$refs.sheetContentRef.addEventListener('scroll', this.handleScroll)
    if (this.$route.query.viewAllFlag) {
      this.viewAllFlag = this.$route.query.viewAllFlag
    }
  },
  activated() {
    this.$nextTick(() => {
      setTimeout(() => {
        $("#StoreStockSheetsViewSheetContentId").animate({ scrollTop: this.queryInfoScroll }, 0);
      }, 0)
    })

  },
  methods: {
    formatDate(date) {
      return `${date.getFullYear()}-${date.getMonth() + 1 < 10 ? '0' : '' }${date.getMonth() + 1}-${date.getDate() < 10 ? '0' : '' }${date.getDate()}`;
    },
    handleScroll() {
      this.queryInfoWrapperTop = this.$refs.sheetContentRef.scrollTop
      let scrollTemp = this.queryInfoWrapperTop - this.queryInfoScroll
      this.queryInfoScroll = this.queryInfoWrapperTop
      if (scrollTemp < 0) {
        this.queryInfoWrapperSimpleShowFlag = true
        return
      }
      if (this.queryInfoWrapperTop >= 50) {
        this.queryInfoWrapperSimpleShowFlag = false
      }
    },
    handleOriginShowFlag() {
      this.SaleOrderSheetsPopupShowFlag = true
    },
    handleClearItem(flag) {
      if (flag === 'sellerInfo') {
        this.queryParams.sellerInfo = []
      } else if (flag === 'customerInfo') {
        this.queryParams.customerInfo = []
      } else if (flag === 'approveStatus') {
        this.queryParams.approveStatus = [{ title: '所有', key: 'all' }]
      } else if (flag === 'showRed') {
        this.queryParams.showRed = false
      } else if (flag === 'timeTypeInfo') {
        this.queryParams.timeTypeInfo = [{ title: '交易时间', id: 'happen_time' }]
      }
      this.handleFinishSelect()
    },
    handleClose() {
      this.SaleOrderSheetsPopupShowFlag = false
    },
    handleClearAll() {
      this.queryParams.sellerInfo = []
      this.queryParams.customerInfo = []
      this.queryParams.approveStatus = [{ title: '所有', key: 'all' }]
      this.queryParams.timeTypeInfo = [{ title: '交易时间', id: 'happen_time' }]
      this.queryParams.showRed = false
      this.handleFinishSelect()
    },
    handleFinishSelect() {
      this.handleClose()
      this.onRefresh()
    },
    async onLoad() {
     
      let params = {
        sheetType: this.queryParams.sheetType,
        pageSize: this.queryParams.pageSize,
        startRow: this.queryParams.startRow,
        getTotal: this.queryParams.getTotal,
        departID: '',
        regionsID: this.queryParams.regionsID,
        startDate: this.queryParams.startDate,
        endDate: this.queryParams.endDate,
        showRed: this.queryParams.showRed,
        operID: this.queryParams.sellerInfo.map(item => {return item.id}).join(","),
        supcustID: this.queryParams.customerInfo.map(item => { return item.ids }).join(","),
        approveStatus: this.queryParams.approveStatus.map(item => {return item.key}).join(","),
        timeType: this.timeType,
        searchStr: this.querySearchStr,
      };
      var viewRange = window.getRightValue('delicacy.sheetViewRange.value')
      if( viewRange === 'self'){
        params.operID = this.$store.state.operInfo.oper_id;
      }else if(viewRange === "department"){
        params.departID = this.$store.state.operInfo.depart_id;
      } else {
        params.departID = ''
      }
      await GetAllStoreStockSheets(params).then((res) => {
        this.refreshing = false;
        if (res.result !== "OK") {
          Toast.fail(res.message);
          return
        }
        this.total = res.total;
        res.data.map((item) => {
          this.list.push(item);
        });
        this.loading = false;
        this.queryParams.startRow = Number(this.queryParams.startRow) + this.queryParams.pageSize;
        if (this.list.length >= Number(res.total)) {
          this.finished = true;
        }
      }).catch(() => {
        Toast.fail('获取列表失败')
      })
    },
    onRefresh() {
      console.log("_____onRefresh",this.firstQueryData)
      if (this.firstQueryData && this.allQueryParams) {
        Object.keys(this.allQueryParams).forEach((key) => {
          this.queryParams[key] = this.allQueryParams[key]
        })
        this.firstQueryData = false
      }
      // 清空列表数据
      this.finished = false;
      // 重新加载数据
      // 将 loading 设置为 true，表示处于加载状态
      this.loading = true;
      this.queryParams.startRow = 0;
      this.list = [];
      this.onLoad();
    },
    onSearchStrChange(value) {
      console.log(value); 
      var reg = new RegExp("'", "g");
      value = value.replace(reg, ""); 
      this.querySearchStr = value; 

      if (this.inputTimer) clearTimeout(this.inputTimer);
      this.inputTimer = 0;

      this.inputTimer = setTimeout(() => {
        this.inputTimer = 0;
        this.onRefresh();
      }, 500);
    },
    onSearchStrClick() {
      this.querySearchStr = ''; 
    },
    handlePopupClose() {
      this.handleFinishSelect()
    },
    onSheetClick(sheet) {
      window.g_curSheetInList = sheet;
      window.g_curSheetList = this.list;
      const routerObj = { path: "/StoreStockSheet", query: { sheetID: sheet.sheet_id, sheetType: this.queryParams.sheetType }}
      if (this.isPage) {
        this.$router.push(routerObj);
      } else {
        this.$emit("handleRoutePush", routerObj)
      }
    },
    getQuantityText(qty) {
      if (qty.indexOf("退") === -1) qty = qty.replace("销:", "")
      return qty
    },
  }
}
</script>
<style scoped lang="less">
.sheet-wrapper {
  width: 100vw;
  height: 100%;
  box-sizing: border-box;
  background-color: #fff;
  .sheet-container {
    width: 100%;
    height: 100%;
    box-sizing: border-box;
    display: flex;
    flex-direction: column;
    .sheet-nar-bar {
      width: 100%;
      height: 46px;
    }
    .sheet-content {
      flex: 1;
      display: flex;
      flex-direction: column;
      box-sizing: border-box;
      overflow-y: auto;
      .sheet-query-popup {

      }
      .sheet-query-wrapper {
      }
      .van-search .van-search__content{
        background-color: transparent !important;
        border: none; 
        border-bottom: 1px solid #eeeeee;
      }
      .sheet-list-tip {
        padding: 0 10px;
        display: flex;
        width: 100%;
        height: 25px;
        background-color: #fff;
        box-sizing: border-box;
        justify-content: space-between;
        .list-tip-item {
          font-size: 14px;
          height: 25px;
        }
      }
      .sheet-list-content {
        flex: 1;
        padding: 0 10px;
        .list-item-wrapper {
          height: auto;
          overflow: hidden;
          background: #ffffff;
          .gray-color {
            color: #AAAAAA;
          }
          .red-color {
            color: #e10505;
          }
          .list-item-content {
            width: 100%;
            box-sizing: border-box;
            display: flex;
            flex-direction: column;
            padding: 5px 0;
            .sheet-main-info {
              display: flex;
              justify-content: space-between;
            }
            .sheet-main-info + .sheet-main-info {
              padding: 2px 0;
            }
          }


          li {
            height: auto;
            overflow: hidden;
            border-bottom: 1px solid #eeeeee;
            padding-top: 6px;
          }
          li:last-child {
            border-bottom: none;
          }
        }
      }

    }
    .sheet-footer {
      width: 100%;
      min-height: 50px;
      box-sizing: border-box;
      border-top: 1px solid #ddd;
      display: flex;
      flex-direction: column;
      padding: 5px 10px;
      .sheet-list-basic {
        padding: 5px;
        font-size: 18px;
        width: 100%;
        display: flex;
        justify-content: space-between;
        .list-total-record {
          flex: 1;
          font-size: 18px;
          font-weight: bold;
          text-align: left;
        }
        .list-total-amount {
          flex: 1;
          font-size: 18px;
          font-weight: bold;
          text-align: right;
        }
      }
      .sheet-list-detail {
        width: 100%;
        display: flex;
        text-align: right;
        justify-content: flex-end;
        flex-wrap: wrap;
        align-items: center;
        div {
          height: 22px;
          line-height: 22px;
          padding: 0 2px;
        }

      }
    }
  }
}

</style>
