<?xml version='1.0' encoding='utf-8'?>
<widget id="com.yingjiang.app" version="3.38" xmlns="http://www.w3.org/ns/widgets" xmlns:android="http://schemas.android.com/apk/res/android" xmlns:cdv="http://cordova.apache.org/ns/1.0">
    <feature name="MiPushPlugin">
        <param name="android-package" value="com.ct.cordova.mipush.MiPushPlugin" />
    </feature>
    <feature name="HotCodePush">
        <param name="android-package" value="com.nordnetab.chcp.main.HotCodePushPlugin" />
        <param name="onload" value="true" />
    </feature>
    <feature name="Camera">
        <param name="android-package" value="org.apache.cordova.camera.CameraLauncher" />
    </feature>
    <feature name="File">
        <param name="android-package" value="org.apache.cordova.file.FileUtils" />
        <param name="onload" value="true" />
    </feature>
    <allow-navigation href="cdvfile:*" />
    <feature name="Whitelist">
        <param name="android-package" value="org.apache.cordova.whitelist.WhitelistPlugin" />
        <param name="onload" value="true" />
    </feature>
    <feature name="Device">
        <param name="android-package" value="org.apache.cordova.device.Device" />
    </feature>
    <feature name="SplashScreen">
        <param name="android-package" value="org.apache.cordova.splashscreen.SplashScreen" />
        <param name="onload" value="true" />
    </feature>
    <feature name="StatusBar">
        <param name="android-package" value="org.apache.cordova.statusbar.StatusBar" />
        <param name="onload" value="true" />
    </feature>
    <feature name="CordovaHttpPlugin">
        <param name="android-package" value="com.silkimen.cordovahttp.CordovaHttpPlugin" />
    </feature>
    <feature name="InAppBrowser">
        <param name="android-package" value="org.apache.cordova.inappbrowser.InAppBrowser" />
    </feature>
    <feature name="broadcaster">
        <param name="android-package" value="org.bsc.cordova.CDVBroadcaster" />
    </feature>
    <feature name="Canvas2ImagePlugin">
        <param name="android-package" value="com.rodrigograca.canvas2image.Canvas2ImagePlugin" />
    </feature>
    <feature name="BarcodeScanner">
        <param name="android-package" value="com.phonegap.plugins.barcodescanner.BarcodeScanner" />
    </feature>
    <feature name="cordova-plugin-mlkit-barcode-scanner">
        <param name="android-package" value="com.mobisys.cordova.plugins.mlkit.barcode.scanner.MLKitBarcodeScanner" />
    </feature>
    <feature name="CordovaMqTTPlugin">
        <param name="android-package" value="com.arcoirislabs.plugin.mqtt.CordovaMqTTPlugin" />
    </feature>
    <feature name="Wechat">
        <param name="android-package" value="xu.li.cordova.wechat.Wechat" />
    </feature>
    <feature name="scanplugin">
        <param name="android-package" value="com.olc.scan.scanplugin" />
    </feature>
    <feature name="BaiduGeolocation">
        <param name="android-package" value="com.lai.geolocation.baidu.GeolocationPlugin" />
    </feature>
    <feature name="BluetoothSerial">
        <param name="android-package" value="com.megster.cordova.BluetoothSerial" />
    </feature>
    <feature name="BLE">
        <param name="android-package" value="com.megster.cordova.ble.central.BLECentralPlugin" />
    </feature>
    <name>营匠</name>
    <description>
        懂行的ERP
    </description>
    <author email="<EMAIL>" href="http://cordova.io">
        Apache Cordova Team
    </author>
    <content src="index.html" />
    <access origin="*" />
    <allow-navigation href="*" />
    <allow-intent href="http://*/*" />
    <allow-intent href="https://*/*" />
    <allow-intent href="tel:*" />
    <allow-intent href="sms:*" />
    <allow-intent href="androidamap://*/*" />
    <allow-intent href="iosamap://*/*" />
    <allow-intent href="bdapp://*/*" />
    <allow-intent href="baidumap://*/*" />
    <allow-intent href="mailto:*" />
    <allow-intent href="geo:*" />
    <chcp>
        <auto-download enabled="false" />
        <auto-install enabled="true" />
        <native-interface version="5" />
        <config-file url="https://www.yingjiang168.com/download/YingJiangApp/www/chcp.json" />
    </chcp>
    <hook src="hooks/after_prepare/sync_version.js" type="after_prepare" />
    <hook src="hooks/after_prepare/fix_gradle_wrapper.js" type="after_prepare" />
    <hook src="hooks/after_prepare/copy_gradle_extras.js" type="after_prepare" />
    <hook src="hooks/after_prepare/fix_bd_geolocation.js" type="after_prepare" />
    <hook src="hooks/after_prepare/add_okhttp_dependency.js" type="after_prepare" />
    <hook src="hooks/after_prepare/add_namespace.js" type="after_prepare" />
    <allow-intent href="market:*" />
    <resource-file src="configs/android/network_security_config.xml" target="app/src/main/res/xml/network_security_config.xml" />
    <source-file src="configs/android/YingJiangApplication.java" target-dir="app/src/main/java/com/yingjiang/app" />
    <source-file src="configs/android/CustomExceptionHandler.java" target-dir="app/src/main/java/com/exception" />
    <edit-config file="app/src/main/AndroidManifest.xml" mode="merge" target="/manifest/application">
        <application android:name="com.yingjiang.app.YingJiangApplication" android:networkSecurityConfig="@xml/network_security_config" android:usesCleartextTraffic="true" />
    </edit-config>
    <edit-config file="app/src/main/AndroidManifest.xml" mode="merge" target="/manifest">
        <uses-permission android:name="android.permission.POST_NOTIFICATIONS" />
    </edit-config>
    <preference name="loglevel" value="DEBUG" />
    <preference name="loadUrlTimeoutValue" value="70000" />
    <preference name="StatusBarOverlaysWebView" value="false" />
    <preference name="accessBackgroundLocation" value="false" />
    <preference name="webView" value="org.jeremyup.cordova.x5engine.X5WebViewEngine" />
    <preference name="WECHATAPPID" value="wx65acc367d9bcfbb3" />
    <preference name="orientation" value="portrait" />
    <preference name="StatusBarBackgroundColor" value="#ffffff" />
    <preference name="StatusBarStyle" value="darkcontent" />
    <preference name="AndroidWebViewTarget" value="auto" />
    <preference name="AndroidHardwareAcceleration" value="true" />
    <preference name="AndroidLaunchMode" value="singleTop" />
    <preference name="AndroidPersistentFileLocation" value="Compatibility" />
    <preference name="AndroidExtraFilesystems" value="files,files-external,documents,sdcard,cache,cache-external,assets,root" />
    <preference name="DisallowOverscroll" value="true" />
    <preference name="BackgroundColor" value="0xff000000" />
    <preference name="HideKeyboardFormAccessoryBar" value="true" />
    <preference name="KeyboardDisplayRequiresUserAction" value="false" />
    <preference name="AndroidTargetSdkVersion" value="34" />
    <preference name="AndroidPrivacyMode" value="safe" />
    <preference name="EnableX5WebView" value="true" />
    <preference name="X5SafeMode" value="true" />
    <preference name="SplashScreenDelay" value="2000" />
    <preference name="AutoHideSplashScreen" value="true" />
    <preference name="FadeSplashScreen" value="false" />
    <preference name="SplashScreenBackgroundColor" value="#000000" />
    <preference name="ShowSplashScreenSpinner" value="false" />
    <preference name="android-minSdkVersion" value="22" />
    <preference name="android-targetSdkVersion" value="34" />
    <preference name="android-compileSdkVersion" value="34" />
    <preference name="MixedContentMode" value="always" />
    <preference name="AndroidInsecureFileModeEnabled" value="true" />
</widget>
