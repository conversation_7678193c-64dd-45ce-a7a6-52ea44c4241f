# Cordova Hooks

这个目录包含了项目的 Cordova hooks，用于在构建过程中执行自定义脚本。

## 版本同步 Hook

### 文件位置
- `hooks/after_prepare/sync_version.js`

### 功能说明
这个 hook 会在每次执行 `cordova prepare android` 后自动运行，确保 AndroidManifest.xml 中的版本信息与 config.xml 保持一致。

### 主要功能
1. **自动版本同步**：从 config.xml 读取版本号，自动更新到 AndroidManifest.xml
2. **智能 versionCode 计算**：支持复杂版本号格式（如 3.36.1）
3. **增量更新**：只在版本发生变化时才更新文件
4. **详细日志**：提供清晰的执行日志，便于调试

### versionCode 计算规则
默认计算规则：`major * 10000 + minor * 100 + patch`

示例：
- `3.36` → `33600`
- `3.36.1` → `33601`
- `4.0.0` → `40000`

### 使用方法

1. **自动执行**（推荐）
   ```bash
   cordova prepare android
   # 或
   cordova build android
   ```
   Hook 会自动执行，无需额外操作。

2. **手动执行**（调试用）
   ```bash
   node hooks/after_prepare/sync_version.js
   ```

### 配置

Hook 已在 `config.xml` 中配置：
```xml
<hook type="after_prepare" src="hooks/after_prepare/sync_version.js" />
```

### 自定义 versionCode 计算

如果需要修改 versionCode 的计算规则，可以编辑 `sync_version.js` 文件中的 `calculateVersionCode` 函数：

```javascript
function calculateVersionCode(version) {
    // 自定义计算逻辑
    return Math.floor(parseFloat(version) * 1000);
}
```

### 日志输出示例

```
🔄 [版本同步] 开始同步版本信息到 AndroidManifest.xml...
📋 [版本同步] 从 config.xml 读取到版本: 3.36
✅ [版本同步] 成功更新 AndroidManifest.xml:
   versionName: 3.35 → 3.36
   versionCode: 33500 → 33600
```

### 故障排除

1. **Hook 没有执行**
   - 确认 `config.xml` 中已添加 hook 配置
   - 检查文件权限（Linux/Mac 需要执行权限）

2. **版本没有更新**
   - 检查 config.xml 中的版本格式是否正确
   - 查看控制台日志输出

3. **权限问题**（Linux/Mac）
   ```bash
   chmod +x hooks/after_prepare/sync_version.js
   ```

### 扩展功能

可以根据需要扩展此 hook：
- 支持 iOS 版本同步
- 支持更复杂的版本号格式
- 集成到 CI/CD 流程中
- 添加版本号验证逻辑

## 其他 Hooks

您可以在相应的目录中添加更多 hooks：
- `before_prepare/` - 准备前执行
- `after_build/` - 构建后执行
- `before_run/` - 运行前执行
- 等等...

更多信息请参考 [Cordova Hooks 官方文档](https://cordova.apache.org/docs/en/latest/guide/appdev/hooks/)。
