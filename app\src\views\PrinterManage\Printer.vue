<template>
  <div class="pages">
    <!-- 导航栏 -->
    <van-nav-bar left-arrow :title="pageTitle" @click-left="goBack" safe-area-inset-top>
      <template #right>
        <div @click="SavePrinter">保存</div>
      </template>
    </van-nav-bar>

    <!-- 主要内容区 -->
    <div class="page-content">
      <!-- 打印机配置区 -->
      <div>
        <!-- 通用组件(有一部分通用组件因排序需要位于下方) -->
        <div class="printer-option-block">
          <div class="printer-option-label">连接方式</div>
          <van-dropdown-menu style="width:60%;">
            <van-dropdown-item
              v-model="printer.type"
              :options="printerTypeOptions"
              @change="onConnectionTypeChanged"
            />
          </van-dropdown-menu>
        </div>
        <div class="printer-option-block">
          <div class="printer-option-label">打印机分类</div>
          <van-dropdown-menu style="width:60%;">
            <van-dropdown-item
              v-model="printer.kind" 
              :options="printerKindOptions" 
              @change="onPrinterKindChanged" 
            />
          </van-dropdown-menu>
        </div>
        <div class="printer-option-block" v-if="!printer.kind.match('label')">
          <div class="printer-option-label">使用模板</div>
          <div style="height:45px;margin:12px 20px 0 0;">
            <van-switch v-model="printer.useTemplate" size="20" />
          </div>
        </div>

        <!-- 蓝牙打印相关组件 -->
        <div class="printer-option-block" v-if="printer.type == 'bluetooth'">
          <div class="printer-option-label">打印机名称</div>
          <input 
            type="text" 
            placeholder="绑定打印机后会自动命名" 
            v-model="printer.name" 
          />
        </div>
        <div class="printer-option-block" v-if="!printer.useTemplate">
          <div class="printer-option-label">纸宽</div>
          <van-dropdown-menu style="width:60%;">
            <van-dropdown-item 
              v-model="printer.paperSize" 
              :options="paperSizeOptions" 
            />
          </van-dropdown-menu>
        </div>
        <div class="printer-option-block" v-if="printer.type == 'bluetooth'">
          <div class="printer-option-label">数据发送方式</div>
          <van-dropdown-menu style="width:60%;">
            <van-dropdown-item 
              v-model="printer.bluetoothDataStyle" 
              :options="sendDataOptions" 
            />
          </van-dropdown-menu>
        </div>
        <div class="printer-option-block" v-if="printer.type == 'bluetooth' && !printer.useTemplate">
          <div class="printer-option-label">编码格式</div>
          <van-dropdown-menu style="width:60%;">
            <van-dropdown-item 
              v-model="printer.encoding" 
              :options="encodingOptions" 
              @change="onEncodingSelectionChanged" 
            />
          </van-dropdown-menu>
        </div>
        <van-notice-bar
          v-if="printer.type == 'bluetooth' && !printer.useTemplate && printer.encoding !== 'gb18030'"
          left-icon="warn-o"
          mode="closeable"
          text="注意: 大多数打印机的编码都是GB18030,编码设置错误将导致乱码。请在技术人员指导下设置编码格式!"
        />

        <div class="printer-option-block" v-if="printer.type == 'bluetooth' && printer.useTemplate">
          <div class="printer-option-label">指令格式</div>
          <van-dropdown-menu style="width:60%;">
            <van-dropdown-item 
              v-model="printer.commandFormat" 
              :options="commandFormatOptions" 
              @change="onCommandFormatSelectionChanged" 
            />
          </van-dropdown-menu>
        </div>
        <van-notice-bar
          v-if="printer.type == 'bluetooth' && printer.useTemplate && printer.commandFormat !== 'esc'"
          left-icon="warn-o"
          mode="closeable"
          text="注意: 大多数针式打印机的编码都是ESC/P,编码设置错误将导致乱码。请在技术人员指导下设置编码格式!"
        />

        <!-- 云打印相关组件 -->
        <div class="printer-option-block" v-if="printer.type == 'cloud'">
          <div class="printer-option-label">选择打印机</div>
          <van-dropdown-menu style="width:60%;">
            <van-dropdown-item 
              v-model="cloudPrinterSelect" 
              :options="cloudPrinterOptions" 
              @change="onCloudPrinterSelectionChanged" 
            />
          </van-dropdown-menu>
        </div>
        <div class="printer-option-block" v-if="printer.type == 'cloud'">
          <div class="printer-option-label">云打印品牌</div>
          <van-dropdown-menu style="width:60%;">
            <van-dropdown-item 
              v-model="printer.brand" 
              :options="printerBrandOptions" 
              @change="onBrandChanged"
            />
          </van-dropdown-menu>
        </div>
        <div class="printer-option-block" v-if="printer.type == 'cloud'">
          <div class="printer-option-label">打印机名称</div>
          <input 
            type="text"
            placeholder="建议：品牌+型号"
            v-model="printer.name"  
          />
        </div>
        <div class="printer-option-block" v-if="printer.type == 'cloud'">
          <div class="printer-option-label">制造编号</div>
          <input 
            type="text"
            placeholder="见打印机背面" 
            v-model="printer.cloudID" 
          />
        </div>
        <div class="printer-option-block" v-if="printer.type == 'cloud'">
          <div class="printer-option-label">校验码</div>
          <input 
            type="text"
            placeholder="见打印机背面" 
            v-model="printer.cloudCode"
          />
        </div>
      </div>
      <!-- 蓝牙打印机列表 -->
      <ul
        v-if="printer.type == 'bluetooth'" 
        id="pl" 
        class="printers-list" 
        style="height:100px;"
      >
        <li
          v-for="(dev, index) in deviceList" :key="index"
          :style="{ backgroundColor: checkIsUsed(dev) ? '#faa' : '' }" 
          @click="onBlueToothSelect(dev)"
        >
          <div>
            {{ dev.name }}
            <div>
              <div v-if="checkIsUsed(dev)" class="printer-btn-paired">
                选中
              </div>
              <div v-if="dev.pairState === 'unpaired'" class="printer-btn-unpaired">
                未配对
              </div>
            </div>
          </div>
          <div> {{ dev.id }} </div>
        </li>
        <li style="color:#bbb;">
          {{ isSearching ? '搜索中...' : deviceList.length > 0 ? '找齐啦~' : '没有打印机哦~' }}
        </li>
      </ul>
      <!-- 底部搜索/扫描按钮 -->
      <div id="bb1" class="bottom-block" v-if="printer.type == 'bluetooth'" @click="btnSearchBluetooth_click">
        <van-button style="background:#444;" type="info" ref="button">
          {{ isSearching ? '停止搜索' : '搜索蓝牙' }}
        </van-button>
      </div>
      <div id="bb2" class="bottom-block" v-if="printer.type == 'cloud'" @click="scanToGetCloudPrinterInfo">
        <van-button style="background:#444;" type="info" ref="button">
          扫描编号
        </van-button>
      </div>

    </div>

    <!-- 其他组件 -->
    <div class="loading-msg" v-if="loadingMsg">
      <van-loading size="24px" color="#fff">{{ loadingMsg }}...</van-loading>
    </div>
  </div>
</template>

<script>
import Printing from '../Printing/Printing'
import encoding from "../../static/encoding.js"
import toast from 'vant/lib/toast'
import {
  ImportCloudPrinters,
  SaveCloudPrinter
} from "../../api/api";
import {
  NavBar, Switch, Button, Toast, Loading, DropdownMenu, DropdownItem, Dialog, NoticeBar
} from 'vant'

export default {
  name: "Printer",
  data() {
    return {
      action: 'add',
      pageTitle: '添加打印机',
      printer: {
        id: '',
        name: '蓝牙打印机',
        trueName: undefined, // 蓝牙打印机原名。
        type: 'bluetooth',
        kind: 'tiny',
        paperSize: '80',
        bluetoothType: 'classic',//or ble
        bluetoothID: '',
        bluetoothDataStyle: '',
        useTemplate: false,
        encoding: 'gb18030',
        commandFormat: 'esc',

        brand: '',
        cloudID: '',
        cloudCode: '',

        isDefault: false
      },
      deviceList: [], // { name:'2',id:'2',pairState:'',type:''}
      cloudPrinters: [],

      printerTypeOptions: [
        { value: 'bluetooth', text: '蓝牙打印机' }, 
        { value: 'cloud', text: '云打印机' }
      ],
      printerKindOptions: [
        { value: 'tiny', text: '小票打印机' }, 
        { value: '24pin', text: '票据打印机' },
        // { value: 'label-tspl', text: '标签打印机' } // * 后续支持其他指令的标签打印机时再在text上做区分
      ],
      paperSizeOptions: [
        { value: '58', text: '58mm' },
        { value: '76', text: '76mm' },
        { value: '80', text: '80mm' },
        { value: '110', text: '110mm' }
      ],
      sendDataOptions: [
        { value: 'auto', text: '自动选择' },
        { value: 'pieces', text: '分块发送' },
        { value: 'whole', text: '整块发送' }
      ],
      encodingOptions: [
        { value: 'gb18030', text: 'GB18030(默认)' },
        { value: 'utf-8', text: 'UTF-8' }
      ],
      commandFormatOptions: [
        { value: 'esc', text: 'ESC/P(默认)' },
        { value: 'cpcl', text: 'CPCL' }
      ],
      printerBrandOptions: [
        { value: 'sw', text: '营匠' },
        { value: 'ym', text: '映美' }
      ],
      cloudPrinterOptions: [
        { icon: '', value: '-1', text: '读取中...' }
      ],
      
      openBluetooth: false,
      isAddingCloudPrinter: false,
      isSearching: false,

      loadingMsg: '',
      cloudPrinterSelect: '',
      printer_origin_json: ''
    }
  },
  components: {
    "van-nav-bar": NavBar,
    "van-switch": Switch,
    "van-button": Button,
    "van-loading": Loading,
    "van-dropdown-menu": DropdownMenu,
    "van-dropdown-item": DropdownItem,
    "van-notice-bar": NoticeBar
  },
  computed: {
  },
  mounted() {
    // * 读取路由信息(编辑模式)
    this.action = this.$route?.params?.data?.action ?? 'add'
    if (this.action === 'edit') {
      this.pageTitle = '编辑打印机'
      if (this.$route?.params?.data?.printer) {
        this.printer = this.$route.params.data.printer
        if (this.printer.id && !this.printer.id.match('bluetooth')) {
          this.cloudPrinterSelect = this.printer.id
        }
      } else {
        Toast.fail('未读取到打印机信息,请退出重试')
        console.error('Query行为是edit,但没有打印机信息。请检查: \n' +
          'this.$route?.params?.data:' + this.$route?.params?.data)
      }
    } else {
      this.pageTitle = '添加打印机'
    }

    // * 初始化打印机信息
    this.printer_origin_json = JSON.stringify(this.printer)
    this.printer = JSON.parse(this.printer_origin_json) // 切断联系。
    this.initCloudPrinters()

    // * 在下方处理一些后续添加的字段(要注意兼容之前的设置)
    console.log('this.printer:', this.printer)
    if (!this.printer.bluetoothDataStyle) {
      this.printer.bluetoothDataStyle = 'auto'
    }
    if (!this.printer.encoding) {
      this.printer.encoding = 'gb18030'
    }
    if (!this.printer.commandFormat) {
      this.printer.commandFormat = 'esc'
    }

    // * 动态调整打印机列表高度
    const plHeightUpdater = setInterval(() => {
      const pl = document.getElementById('pl')
      const bb1 = document.getElementById('bb1')
      const bb2 = document.getElementById('bb2')
      let bottomPostion = bb1?.offsetTop ? bb1.offsetTop : bb2?.offsetTop
      const topPostion = pl?.offsetTop
      if (bottomPostion && topPostion) {
        pl.style.height = (bottomPostion - topPostion) + 'px'
      }
    }, 100);
    this.$once('hook:beforeDestroy', () => {
      clearInterval(plHeightUpdater);
    }) // 页面销毁时清除定时器

    // * 搜索蓝牙功能在PC调试时会报错
    this.openBluetooth = true
    if (this.printer.type === 'bluetooth') {
      this.startSearchBluetooth()
    }
  },
  destroyed() {
    if (bluetoothSerial && bluetoothSerial.stopDiscoverUnpaired)
      bluetoothSerial.stopDiscoverUnpaired()
  },
  methods: {
    /** 最终路由返回方法 */
    routeBack(savePrinter = false) {
      const params = {
        savePrinter: savePrinter,
        action: this.action,
        printer: this.printer,
        originPrinter: JSON.parse(this.printer_origin_json)
      }
      this.myGoBack(this.$router, params)
    },
    /** 左上箭头手点返回 */
    goBack() {
      const printer_curr_json = JSON.stringify(this.printer)
      if (printer_curr_json !== this.printer_origin_json) {
        Dialog.confirm({
          title: '注意!',
          message: '要保存吗?',
          confirmButtonText: '保存',
          cancelButtonText: '不保存',
          width: "320px"
        }).then(() => {
          this.SavePrinter()
        }).catch(() => {
          this.routeBack(false)
        });
      } else {
        this.routeBack(false)
      }
    },
    /** 整合的保存方法 */
    SavePrinter() {
      // 1. 定义保存并返回的callback函数,以便Promise中调用
      const successCb = (msg) => {
        this.loadingMsg = ''
        if (msg) {
          Toast.success(msg)
        }
        this.routeBack(true)
      }
      const errorCb = (msg) => {
        this.loadingMsg = ''
        if (msg) {
          Toast.fail(msg)
        }
      }

      // 2. 开始保存进程
      this.loadingMsg = '正在保存'
      if (this.printer.type == 'cloud') {
        // ! 保存云打印机
        const isAdd = this.cloudPrinterSelect === 'add'
        let printerId = isAdd ? '' : this.printer.id
        const name = this.printer.name
        const deviceId = this.printer.cloudID
        const checkCode = this.printer.cloudCode
        const brand = this.printer.brand

        // 执行校验
        if (printerId?.match(':')) {
          // * 这一情况在从蓝牙打印机切过来后,没有选择打印机就填写信息保存时触发。
          printerId = ''
          this.printer.id = ''
        }
        if (!name?.length) {
          errorCb("请填写打印机名称"); return
        }
        if (!deviceId?.length) {
          errorCb("请填写制造编号"); return
        }
        if (!checkCode?.length) {
          errorCb("请填写校验码"); return
        }
        if (!brand?.length) {
          errorCb("请选择云打印品牌"); return
        }

        // 实行保存
        const params = {
          operKey: this.$store.state.operKey,
          isNewRecord: isAdd,
          printer_brand: brand,
          printer_brand_name: brand == 'ym' ? "映美云打印机" : "营匠云打印机",
          printer_id: printerId,
          printer_name: name,
          device_id: deviceId,
          check_code: checkCode,
          status: '1',
          cls_status_name: '启用'
        }
        console.log("开始保存进程。请求参数:", params)
        SaveCloudPrinter(params).then(res => {
          console.log('服务端返回保存结果:', res)
          if (res.result === 'OK') {
            this.printer.id = res?.record?.printer_id ?? 'unknown'
            successCb('保存成功')
          } else {
            errorCb(res.msg)
          }
        }).catch((e) => {
          console.error('保存操作失败:', e)
          errorCb('网络连接失败')
        })
      } else {
        // ! 保存蓝牙打印机
        const name = this.printer.name
        const id = this.printer.bluetoothID

        // 执行校验
        if (!name || !id) {
          console.error(`没有name(${name})或id(${id})。可能是还没有选择打印机。`)
          // errorCb('尚未选择打印机')
          // return
        }

        // 实行保存
        successCb('保存成功')
      }
    },
    onConnectionTypeChanged() {
      this.printer.kind = this.printer.type === 'bluetooth' ? 'tiny' : '24pin'
      this.onPrinterKindChanged()
    },
    onEncodingSelectionChanged() {
      console.log('Printer Encoding changed to', this.printer.encoding)
      // if (this.printer.encoding !== 'gb18030') {
      //   toast('注意:大多数打印机的编码均为GB18030,编码不正确则打印会出现乱码', 2000)
      // }
    },
    onCommandFormatSelectionChanged() {
      console.log('Printer Command Format changed to', this.printer.commandFormat)
    },
    onPrinterKindChanged() {
      this.printer.useTemplate = this.printer.kind === '24pin'
    },
    onCloudPrinterSelectionChanged() {
      if (this.cloudPrinterSelect == 'add') {
        this.isAddingCloudPrinter = true
        this.printer.id = ''
        this.printer.name = ''
        this.printer.trueName = undefined
        this.printer.cloudID = ''
        this.printer.cloudCode = ''

        this.SetPrinterNameByBrand()
      } else {
        this.isAddingCloudPrinter = false
        this.cloudPrinters.forEach(dev => {
          if (dev.printer_id == this.cloudPrinterSelect) {
            this.importPrinter(dev)
            return
          }
        });
      }
    },
    onBrandChanged() {
      if (this.cloudPrinterSelect == 'add') {
        this.SetPrinterNameByBrand()
      }
    },
    async scanToGetCloudPrinterInfo() {
      if (isiOS) { toast('请扫描云盒或云打印机\n铭牌上的二维码', 3000) } // iOS不能用扫码接口的描述功能

      try {
        // 云打印机扫码配置 - 明确启用二维码
        const scanConfig = {
          unit_type: 'cloud_printer',
          barcodeFormats: {
            QRCode: true,      // 启用二维码
            Code128: false,    // 禁用其他格式，专注二维码
            Code39: false,
            Code93: false,
            EAN13: false,
            EAN8: false
          },
          beepOnSuccess: true,
          vibrateOnSuccess: false
        }

        console.log('云打印机扫码配置:', scanConfig)
        const scanResult = await this.scanBarcodeNew(scanConfig)

        if (!scanResult.code) {
          return
        }

        var content = scanResult.code;
      // var content = '22150164ALH-5F27'
      if (content) {
        var sp, brand, dc, cc;
        if (content.toUpperCase().match('#')) { // 暴力判断,可能很危险
          sp = "#"; brand = 'sw'
        } else if (content.match('-')) {
          sp = '-'; brand = 'ym'
        } else {
          Toast.fail('此类型尚不支持\n' + content)
          return
        }

        var cs = content.split(sp);
        if (cs.length !== 2) {
          Toast.fail('此类型尚不支持\n' + content)
          return
        } else {
          dc = cs[0]; cc = cs[1]
          this.printer.brand = brand;
          this.isAddingCloudPrinter = false
          var alExist = false
          this.cloudPrinters.forEach(element => {
            if (element.device_id === dc) {
              this.cloudPrinterSelect = element.printer_id
              this.importPrinter(element) // 通过代码改option值似乎不会触发onchange
              Toast.success('扫描成功,请检查后保存')
              alExist = true
              return;
            }
          });
          if (alExist) { return; }
          this.cloudPrinterSelect = 'add'
          this.isAddingCloudPrinter = true
          //this.device_id = dc
          //this.check_code = cc
          this.printer.cloudID = dc
          this.printer.cloudCode = cc

          this.SetPrinterNameByBrand()
          Toast.success('扫描成功,请检查后保存')
        }
      }
      } catch (error) {
        console.error('扫码失败:', error)
        this.$toast('扫码失败，请重试')
      }
    },


    SetPrinterNameByBrand() {
      // create printer_name by printer_brand
      var brandName = this.printer.brand == 'ym' ? '映美' : '营匠'
      var number = 1
      this.cloudPrinters.forEach(element => {
        var name = element.printer_name
        var tempName = brandName + number + '号'
        if (name == tempName) {
          number++
        }
      })
      var finalName = brandName + number + '号'
      this.printer.name = finalName
    },
    initCloudPrinters() {
      console.log('initCloudPrinters...')
      this.cloudPrinterOptions = []
      this.cloudPrinters = []

      this.cloudPrinterOptions.push({ icon: '', value: 'add', text: '✚ (新增)' })
      this.cloudPrinterSelect = 'add'
      var params = {}
      ImportCloudPrinters(params).then(res => {
        if (res.printerList == undefined && this.printer.type == 'cloud') {
          toast({
            duration: 4000,
            message: '没有加载到云打印机。请考虑检查本机联网状态后重试、手动添加打印机或是联系管理人员。',
          });
        }

        res.printerList.forEach(element => {
          console.log('printer loaded:', element)
          this.cloudPrinterOptions.push({ icon: '', value: element.printer_id, text: element.printer_name })
          this.cloudPrinters.push(element)
          if (element.printer_id == this.printer.id) {
            this.cloudPrinterSelect = this.printer.id
          }
        })
      }).catch((e) => {
        if (this.printer.type == 'cloud') {
          Toast.fail('加载云打印机列表失败')
        }
        console.error('ImportCloudPrinters网络通信失败:', e)
      })
    },
    importPrinter(prt) {
      this.printer.id = prt.printer_id
      this.printer.brand = prt.printer_brand
      this.printer.name = prt.printer_name
      this.printer.cloudID = prt.device_id
      this.printer.cloudCode = prt.check_code
    },
    checkIsUsed(item) {
      //缓存id和当前搜索id相同并且模式一样
      //return this.$store.state.printerID === item.id && item.type === this.$store.state.bluetoothType
      return this.printer.bluetoothID === item.id// && item.type === this.printer.bluetoothType
    },
    btnSearchBluetooth_click() {
      if (this.isSearching)
        this.stopSearchBluetooth()
      else
        this.startSearchBluetooth()
    },
    startSearchBluetooth() {
      this.isSearching = true
      bluetoothSerial?.list((data) => {
        this.deviceList = data.filter(device => device.name)
        this.deviceList.map(e => {
          e.type = 'classic'
        })
        console.log('list sccess:', JSON.stringify(data))
        if (window.isiOS) this.isSearching = false
      }, (error) => {
        console.error('获取蓝牙设备列表失败:', error)
        // 检查是否是权限相关错误
        if (error && (error.toString().includes('permission') || error.toString().includes('Permission'))) {
          Toast.fail('需要蓝牙权限才能搜索设备，请在权限申请对话框中允许')
        } else {
          Toast.fail('获取列表失败，请检查蓝牙权限或稍后重试')
        }
        this.isSearching = false
      })

      if (window.isiOS) {

      }
      else {
        bluetoothSerial?.setDeviceDiscoveredListener((device) => {
          this.addDeviceToDeviceList(device, 'unpaired', "classic")
        }, (e) => { console.log(e) })

        bluetoothSerial?.discoverUnpaired((device) => {
          
        })
        setTimeout(() => {
          this.stopSearchBluetooth()
        }, 120000)
      }
    },
    stopSearchBluetooth() {
      this.isSearching = false
      bluetoothSerial?.clearDeviceDiscoveredListener()
      if (bluetoothSerial?.stopDiscoverUnpaired)
        bluetoothSerial.stopDiscoverUnpaired()
    },
    addDeviceToDeviceList(device, pairState, type) {
      if (!device.name) {
        return
      }
      if (this.deviceList.some(_device => (_device.id === device.id && _device.type === type))) {
        return
      }
      device.type = type
      device.pairState = pairState
      this.deviceList.push(device)
      this.$forceUpdate()
    },
    onBlueToothSelect(device) {
      console.log("device", device)
      if (!device) {
        Toast.fail('设备信息损坏')
      } else if (device.type === 'ble') {
        Toast.fail('暂不支持BLE打印机')
        // this.connectBlePrinter(device)
      }
      else {
        this.connectClassicPrinter(device)
      }
    },
    connectClassicPrinter(device) {
      // eslint-disable-next-line no-undef
      var that = this
      that.loadingMsg = '正在连接'
      bluetoothSerial.connect(device.id, connectClassicSuccess, connectClassicFailure)

      function connectClassicSuccess() {
        that.loadingMsg = ''
        that.printer.id = 'bluetooth_' + device.id
        that.printer.name = device.name
        that.printer.trueName = device.name
        that.printer.bluetoothID = device.id
        that.$store.state.bluetoothType = 'classic'
        Toast.success("连接成功")
        var helloInfo = "连接成功!\r\n\r\n\r\n\r\n"
        var encode = that.printer.encoding
        if (!encode) encode = 'gb18030'
        var hellobytes = new encoding.MyTextEncoder(encode, { NONSTANDARD_allowLegacyEncoding: true }).encode(helloInfo)
        var arr = Array.from(hellobytes)
        if (device.name.indexOf('Jolimark LQ') == 0) {
          var arrInitYingMei = [0x1b, 0x1d, 0x1e, 0x05, 0x01, 0x1b, 0x1d, 0x1f, 0x1b, 0x1d, 0x1e, 0x0, 0x11, 0x00, 0x1b, 0x1d, 0x1f, 0x1b, 0x1d, 0x1e, 0x05, 0x02, 0x1b, 0x1d, 0x1f]
          arr = arr.concat(arrInitYingMei)
        }

        var bt = Uint8Array.from(arr)

        Printing.writeData(bt, () => {
          setTimeout(() => {
            bluetoothSerial.disconnect()
          }, 1000)
        }, () => {
          bluetoothSerial.disconnect()
        })

        this.SavePrinter()
      }

      function connectClassicFailure() {
        that.loadingMsg = ''
        Toast.success("连接失败，请检查设备是否启动")
      }
    },
  }
}
</script>

<style lang="less" scoped>
/deep/.van-button--normal {
  font-size: 16px;
}
/deep/.van-notice-bar {
  padding-left: 10px;
}
/deep/.van-notice-bar__left-icon, .van-notice-bar__right-icon {
  font-size: 18px;
}
/deep/.van-switch .van-switch__node {
  width: 1em; 
  height:1em;
}
/deep/.van-switch--on .van-switch__node {
  -webkit-transform: translateX(1em) !important;
  -ms-transform: translateX(1em) !important;
  transform: translateX(1em) !important;
}

li:active {
  color: #000;
}
h5:active, span:active {
  background-color: transparent;
}

.page-content {
  display: flex;
  flex-direction: column;
}
.printer-option-block {
  display:flex;
  flex-direction:row;
  justify-content:space-between;
  border-bottom: #f4f4f4 1px solid;  
  margin-left:10px;
  margin-right:10px;
  height: 45px;

  .printer-option-label {
    color: #000;
    display: flex;
    width: 200px;
    text-align: left;
    align-items: center;
    margin-left: 5px; 
  }
  
  input {
    border: none;
    text-align: right;
    margin-right: 15px;
  }
}
.printers-list {
  padding-top: 0px;
  display: flex;
  flex-direction: column;
  overflow-x: hidden;
  overflow-y: scroll;
  padding: 5px;
  padding-bottom: 70px;
  background: #fff;

  .printer-btn-paired {
    border-radius:8px;
    color:#fff;
    width:60px;
    text-align:center;
  }
  .printer-btn-unpaired {
    border:0px #f4f4f4 solid;
    background:#fff; 
    width:60px;
    line-height:25px;
    color:#ff9999;
    text-align:center;
    display:block;
  }

  li {
    overflow: hidden;
    padding: 5px;
    flex-shrink: 0;
    border-bottom: 1px solid #f2f2f2;

    h5 {
      font-size: 14px;
      font-weight: normal;
      text-align: left;
    }

    div {
      font-size: 13px;
      display: flex;
      flex-direction: row;
      justify-content: space-between;
      margin-top: 8px;
    }
  }

  li:last-child {
    border-bottom: none;
  }

  li:hover {
    background: #f2f2f2;
    color: #000;
  }
}
.bottom-block {
  width: 100%;
  height: 50px;
  position: absolute;
  bottom: 0px;
  background-color: #444;

  button {
    width: 100%;
    height: 100%;
  }
}
.loading-msg {
  border-radius:20px; 
  width:200px;
  height:80px;
  background:#555;
  color:#fff; 
  position:fixed;
  top:calc(50vh - 40px);
  left:calc(50vw - 100px);
  z-index:99999999;
  display:flex;
  justify-content:center;
  align-items:center;
}

</style>
