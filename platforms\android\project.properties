# Licensed to the Apache Software Foundation (ASF) under one
# or more contributor license agreements.  See the NOTICE file
# distributed with this work for additional information
# regarding copyright ownership.  The ASF licenses this file
# to you under the Apache License, Version 2.0 (the
# "License"); you may not use this file except in compliance
# with the License.  You may obtain a copy of the License at
#
# http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing,
# software distributed under the License is distributed on an
# "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
#  KIND, either express or implied.  See the License for the
# specific language governing permissions and limitations
# under the License.


# This file was originally created by the Android Tools, but is now
# used by cordova-android to manage the state of the various third party
# libraries used in your application

# This is the Library Module that contains the Cordova Library, this is not
# required when using an AAR

# This is the application project.  This is only required for Android Studio Gradle projects

# Project target.
target=android-34
android.library.reference.1=CordovaLib
android.library.reference.2=app
cordova.gradle.include.1=cordova-hot-code-push-plugin/app-chcp.gradle
cordova.system.library.1=androidx.core:core:1.6.+
cordova.system.library.2=androidx.webkit:webkit:1.4.0
cordova.system.library.3=androidx.annotation:annotation:*
cordova.system.library.4=androidx.localbroadcastmanager:localbroadcastmanager:1.1.0
cordova.gradle.include.2=phonegap-plugin-barcodescanner/app-barcodescanner.gradle
cordova.system.library.5=androidx.legacy:legacy-support-v4:1.0.0
cordova.system.library.6=androidx.legacy:legacy-support-v4:1.0.0
cordova.system.library.7=com.google.android.material:material:1.2.0
cordova.system.library.8=com.google.mlkit:barcode-scanning:17.3.0
cordova.system.library.10=androidx.camera:camera-core:1.3.1
cordova.system.library.11=androidx.camera:camera-camera2:1.3.1
cordova.system.library.12=androidx.camera:camera-lifecycle:1.3.1
cordova.system.library.13=androidx.camera:camera-view:1.3.1
cordova.system.library.14=androidx.constraintlayout:constraintlayout:2.0.4
cordova.gradle.include.3=cordova-plugin-mlkit-barcode-scanner/app-build-extras.gradle
cordova.system.library.15=com.tencent.tbs:tbssdk:44286
cordova.system.library.16=org.eclipse.paho:org.eclipse.paho.client.mqttv3:1.1.0
cordova.gradle.include.4=cordova-plugin-wechat/app-android-build.gradle