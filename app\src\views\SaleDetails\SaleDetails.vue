<template>
  <!-- 未完成事项 -->
  <!-- 1.商品如何选取 -->
  <!-- 2.所有的selectinfo的选取可以通过新的selectOne组件来优化？可以降低引入组件数量，提高效率 -->
  <div ref="pages" class="pages">
    <van-nav-bar title="销售明细" left-arrow @click-left=" myGoBack()" safe-area-inset-top>
      <template #right>
        <div class="iconfont icon_right" @click="selectShow = !selectShow">
          &#xe690;
        </div>
      </template>
    </van-nav-bar>
    <van-popup class="van_popup" duration="0.4" v-model="selectShow" position="right" safe-area-inset-bottom
      :style="{ width: '85%', height: '100%' }">
      <!-- 侧边栏上方空出一部分 -->
      <div style="height:29px;border-top:1px solid #ccc"></div>
      <van-cell-group class="cellgroup">
        <!-- 日期 -->
        <van-field :value="selectInfo.startDate + '~' + selectInfo.endDate" readonly label="日期选择" placeholder="请选择"
          @click-input="showDate = true">
        </van-field>
        <!-- 业务员 -->
        <van-field v-model="selectInfo.operName" :disabled='disabledShowSeller' readonly label="业务员" placeholder="请选择"
          @click-input="!disabledShowSeller && (showSeller = true)">
        </van-field>
        <!-- 客户 -->
        <van-field v-model="selectInfo.supcustName" readonly label="客户" placeholder="请选择"
          @click-input="showCustomer = true">
        </van-field>
        <!-- 客户渠道 -->
        <van-field border:1px solid #ccc v-model="selectInfo.groupName" readonly label="客户渠道" placeholder="请选择"
          @click-input="showGroup = true">
        </van-field>
        <!--  供应商-->
        <van-field border:1px solid #ccc v-model="selectInfo.supplierName" readonly label="供应商" placeholder="请选择"
          @click-input="showSupplier = true">
        </van-field>
        <!-- 仓库 -->
        <van-field border:1px solid #ccc v-model="selectInfo.branchName" readonly label="仓库" placeholder="请选择"
          @click-input="showBranch = true">
        </van-field>
        <YjSelectTree ref="selectTreeRef" :target="'region'" @getRootNode='() => { return rootNode }'
          :getContainer='() => { return $refs.pages }' :title="title" :confirmColor="confirmColor" :rangeKey="rangeKey"
          :rootNode="rootNode" :idKey="idKey" :sonNode="sonNode" :parentSelectable="parentSelectable"
          :popupHeight="'90%'" @handleConfirm="onRegionSelected">
          <template #select-tree-content>
            <van-field border:1px solid #ccc v-model="selectInfo.regionName" readonly label="片区" placeholder="请选择"
              @click-input="showRegion = true" style="border-bottom:2px solid #eee">
            </van-field>
          </template>
        </YjSelectTree>
        <!-- 部门 -->
        <div v-if="departTreeShowFlag1">
          <YjSelectTree style="height: auto;" ref="selectDepart" :getContainer="'.pages'" :target="'department'"
            :title="'选择部门'" :confirmColor="confirmColor" :rangeKey="rangeKey" :rootNode="rootNode" :idKey="idKey"
            :sonNode="sonNode" :parentSelectable="parentSelectable" :popupHeight="'85%'" @getRootNode='() => { return rootNode }'
            @handleConfirm="handleDepartmentSelect">
            <template #select-tree-content>
              <van-field v-model="selectInfo.departName" readonly label="部门" placeholder="未选择" />
            </template>
          </YjSelectTree>
        </div>
        <!-- 送货员 -->
        <van-field border:1px solid #ccc v-model="selectInfo.senderName" readonly label="送货员" placeholder="请选择"
          @click-input="showSender = true">
        </van-field>
        <!-- 品牌 -->
        <van-field border:1px solid #ccc v-model="selectInfo.brandName" readonly label="品牌" placeholder="请选择"
          @click-input="showBrand = true">
        </van-field>
        <!--商品类别 -->
        <van-field border:1px solid #ccc v-model="selectInfo.itemClassName" readonly label="商品类别" placeholder="请选择"
          @click-input="showItemClass = true">
        </van-field>
        <!-- 商品 暂时去掉readonly-->
        <van-field border:1px solid #ccc v-model="selectInfo.itemName" label="商品名称" placeholder="请选择"
          @click-input="showItem = true">
        </van-field>
        <!--整单备注-->
        <van-field border:1px solid #ccc v-model="selectInfo.makeBrief" label="整单备注" placeholder="请输入"
          @click-input="showMakeBrief = true">
        </van-field>
        <!--行备注-->
        <van-field border:1px solid #ccc v-model="selectInfo.remark" label="行备注" placeholder="请选择"
          @click-input="showRemark = true">
        </van-field>
      </van-cell-group>
      <van-checkbox v-model="queryBySalesOrderChecked" @change="onQueryBySalesOrderChecked" icon-size="20px"
        style="font-size: 12px;padding:0.4rem">按销售订单查询</van-checkbox>
      <!-- 清除选择按钮 -->
      <div class="footer_button">
        <van-button style="height: 45px;border-radius:12px;background-color:#fff;border:1px solid #ccc" plain
          type="default" @click="cancelSelect">清空选择
        </van-button>
        <!-- 提交选择按钮 -->
        <van-button style="height: 45px;border-radius:12px;background-color:#fff;border:1px solid #ccc" plain
          type="info" @click="startSelect">确认选择
        </van-button>
      </div>
    </van-popup>
    <!-- 日期栏 -->
    <van-calendar v-model="showDate" type="range" @confirm="onConfirm" title="请选择起止日期"
      :default-date="[new Date(selectInfo.startDate), new Date(selectInfo.endDate)]" :allow-same-day="true"
      :min-date="minDate" :max-date="maxDate" />
    <!-- 操作员栏 -->
    <van-popup v-model="showSeller" position="bottom" close-on-click-overlay close-on-popstate safe-area-inset-bottom
      duration="0.4" :style="{ width: '100%', height: '90%' }">
      <SelectSeller @selectSellers="selectSeller" />
    </van-popup>
    <!-- 客户栏 -->
    <van-popup v-model="showCustomer" position="bottom" close-on-click-overlay close-on-popstate safe-area-inset-bottom
      duration="0.4" :style="{ width: '100%', height: '90%' }">
      <SelectCustomer @onClientSelected="selectCustomer" />
    </van-popup>
    <!-- 供应商栏 -->
    <van-popup v-model="showSupplier" position="bottom" close-on-click-overlay close-on-popstate safe-area-inset-bottom
      duration="0.4" :style="{ width: '100%', height: '90%' }">
      <SelectSupplier @onClientSelected="selectSupplier" />
    </van-popup>
    <!-- 品牌栏 -->
    <van-popup v-model="showBrand" position="bottom" close-on-click-overlay close-on-popstate safe-area-inset-bottom
      duration="0.4" :style="{ width: '100%', height: '90%' }">
      <SelectBrand @selectBrand="selectBrand" />
    </van-popup>
    <!-- 商品类别栏 -->
    <van-popup v-model="showItemClass" position="bottom" close-on-click-overlay close-on-popstate safe-area-inset-bottom
      duration="0.4" :style="{ width: '100%', height: '90%' }">
      <ItemClass :allowMultiSelect="false" @itemClassSelect="selectItemClass"></ItemClass>
    </van-popup>
    <!-- 选择商品栏 暂时弃用-->
    <van-popup v-model="test" position="bottom" close-on-click-overlay close-on-popstate safe-area-inset-bottom
      duration="0.4" :style="{ width: '100%', height: '90%' }">
      <ItemArchives />
    </van-popup>
    <!-- 送货员栏 -->
    <van-popup v-model="showSender" position="bottom" close-on-click-overlay close-on-popstate safe-area-inset-bottom
      duration="0.4" :style="{ width: '100%', height: '90%' }">
      <SelectSender @selectSenders="selectSender"></SelectSender>
      </van-popup>
      <!-- 仓库栏 -->
      <van-popup v-model="showBranch" position="bottom" close-on-click-overlay close-on-popstate safe-area-inset-bottom duration="0.4" :style="{ width: '100%', height: '90%' }">
        <SelectBranchWithPermission :sheetType="'X'" @selectBranchsWithPermission="selectBranchsWithPermission" />
      </van-popup>
      <!-- 行备注 -->
      <van-popup v-model="showRemark" position="bottom" close-on-click-overlay close-on-popstate safe-area-inset-bottom duration="0.4" :style="{ width: '100%', height: '90%' }">
        <SelectBrief @selectBrief="selectBrief"></SelectBrief>
      </van-popup>

    <!-- 下面开始显示数据 包裹在一个pull里面，一次显示30条数据-->
    <van-pull-refresh class="viewDocument_box_m" v-model="isRefresh" success-text="刷新成功" @refresh="startSelect">
      <!-- vanlist不能立刻更新 -->
      <van-list v-model="loading" :finished="finished" finished-text="到底了~" :immediate-check="false"
        @load="submitSelect">
        <div class="viewDocumentBox">
          <!-- 显示数据：单据编号，单据类型，交易时间，客户，商品名称，数量，单价，金额，商品备注 -->
          <table style="padding-bottom:40px;" class="my_table">
            <thead>
              <tr class="fixed_row">
                <th>序号</th>
                <th>单据编号</th>
                <th>单据类型</th>
                <th>交易时间</th>
                <th>客户</th>
                <th>商品</th>
                <th>数量</th>
                <th>单价</th>
                <th>金额</th>
                <th>备注</th>
              </tr>
            </thead>
            <tbody>
              <tr v-for="(item, index) in resultData" :key="index" @click="selectRow(index)"
                :class="{ 'selected': selectedRow === index }">
                <td class="table-cell">{{ index + 1 }}</td>
                <td class="table-cell">{{ item.sheet_no }}</td>
                <td class="table-cell">{{ item.sheet_type }}</td>
                <td class="table-cell">{{ item.happen_time }}</td>
                <td class="table-cell">{{ item.sup_name }}</td>
                <td class="table-cell">{{ item.item_name }}</td>
                <td class="table-cell">{{ item.quantity }}</td>
                <td class="table-cell">{{ item.real_price }}</td>
                <td class="table-cell">{{ item.sum_amount }}</td>
                <td class="table-cell">{{ item.remark }}</td>
              </tr>
            </tbody>
          </table>
        </div>
      </van-list>
    </van-pull-refresh>
    <div style="position:fixed;bottom:0px;background:#fff;height:20px;padding:10px;width:100%">合计:{{ sumResult.quantity }}
      总额￥{{ sumResult.sum_amount }}</div>
  </div>




</template>
<script>
import { NavBar, List, Icon, Calendar, Popup, Button, Field, PullRefresh, Cell, Toast, CellGroup, Checkbox, CheckboxGroup } from "vant"
import { GetSaleDetails } from "../../api/api"
import RegionSelection from "../components/RegionSelection";
import SelectSeller from "../components/SelectSeller";
import SelectCustomer from "../components/SelectCustomer";
import SelectSupplier from "../components/SelectSupplier";
import SelectBrand from "../components/SelectBrand.vue"
import ItemClass from '../components/ItemClass.vue';
import ItemArchives from "../components/ItemArchives.vue";
import SelectSender from "../components/SelectSender.vue"
import SelectBrief from "../components/SelectBrief.vue"
import SelectOne from "../components/SelectOne.vue"
import YjSelectTree from '../components/yjTree/YjSelectTree.vue'
import SelectBranchWithPermission from "../components/SelectBranchWithPermission"
import SelectGroup from "../components/SelectGroup";
export default {
  name: "SaleDetails",
  data() {
    return {
      queryBySalesOrderChecked: false, // 是否按销售订单查询 
      // show*属性用来控制弹出栏是否显示
      // selectinfo中包含了向后端请求的参数
      selectedRow: -1,
      test: false,
      finished: false,
      loading: false,
      selectShow: false,
      showDate: false,
      showSeller: false,
      showCustomer: false,
      disabledShowSeller: false,
      sheetType: "x",
      showSupplier: false,
      showBrand: false,
      showRegion: false,
      showGroup: false,
      showDepart: false,
      showItemClass: false,
      showSender: false,
      showItem: false,
      showBranch: false,
      // 是否在刷新
      isRefresh: false,
      // 显示数据时一次加载30条，可修改
      //片区树
      showInput: false,
      showMakeBrief: false,
      showRemark: false,
      showFlag: true,
      sumResult: {},
      // 指定 Object 中 key 的值作为选择器显示的内容
      // 比如:{ id:1, name: '树1' }, { id: 2, name: '树2' }
      // name就是要显示到界面上的key
      rangeKey: 'name',
      // 指定 Object 中 key 的值作为单条数据的唯一id
      // 跟上面的类似,一个道理，只不过这个是标识的唯一id键名
      idKey: 'id',
      sonNode: 'subNodes',
      asPage: true,
      // 是否多选(默认单选)
      multipleCheck: false,
      // 是否可以选父级(默认不可选)
      parentSelectable: true,
      // 折叠时关闭所有已经打开的子集 再次打开时需要一级一级打开
      // 默认不关闭已打开的子集
      foldAll: false,
      confirmColor: '#e3a2a2',
      // 标题
      title: '片区选择',
      rootNode: {},
      NumShowOneTime: 30,
      startRow: 0,
      endRow: 29,
      rowCount: 0,
      minDate: new Date(2000, 1, 1),
      maxDate: new Date(),
      selectInfo: {
        startDate: this.formatDate(new Date(), "YYYY-mm-dd") + " 00:00:00",
        endDate: this.formatDate(new Date(), "YYYY-mm-dd") + " 23:59:59",
        dateTime: this.formatDate(new Date()) + "至" + this.formatDate(new Date()),
        operName: "",
        operID: "",
        supcustID: "",
        supcustName: "",
        regionName: "",
        regionID: "",
        brandName: "",
        brandId: "",
        itemClassName: "",
        itemClassID: "",
        senderName: "",
        senderID: "",
        itemName: "",
        itemID: "",
        branchName: "",
        branchID: "",
        makeBrief: "",
        remark: "",
        departName: "",
        departID: "",
        groupName: "",
        groupID: "",
      },
      resultData: [],
    }
  },
  computed: {
    departTreeShowFlag1() {
      let flag = this.viewRange == 'self'
      return !flag
    }
  },

  methods: {
    handleDepartmentSelect(value) {
      if (value.length > 0) {
        this.selectInfo.departID = value[0].id
        this.selectInfo.departName = value[0].name
      }
      this.$refs.selectDepart.handleCancel()
    },
    onQueryBySalesOrderChecked(){
      if(this.queryBySalesOrderChecked){
        this.sheetType = "xd"
      }else{
        this.sheetType = "x"
      }
    },
    myGoBack() {
      window.sessionStorage.setItem('startDate', this.$route.query.startDate);
      window.sessionStorage.setItem('endDate', this.$route.query.endDate);
      this.$router.go(-1)
    },
    showdata() {
      console.log("data: .............")
      console.log(this.resultData)
      console.log(this.startRow)
      console.log(this.endRow)
      console.log(this.finished)
      console.log("-------------------------")
    },
    onConfirm(date) {
      const [start, end] = date;
      this.showDate = false;
      this.selectInfo.startDate = `${this.formatDate(start)} 00:00:00`;
      this.selectInfo.endDate = `${this.formatDate(end)} 23:59:59`;
      this.selectInfo.dateTime = this.selectInfo.startDate + "至" + this.selectInfo.endDate;
    },
    // select*用来处理弹出组件中的事件，即修改selectinfo
    selectSeller(value) {
      console.log("selectSeller接收到的数据:", value);
      this.showSeller = value.isSellerShow;
      this.selectInfo.operID = value.oper_id;
      this.selectInfo.operName = value.oper_name;
      console.log("设置后的selectInfo:", {
        operID: this.selectInfo.operID,
        operName: this.selectInfo.operName
      });
    },
    selectCustomer(value) {
      this.showCustomer = value.isShow;
      this.selectInfo.supcustID = value.ids;
      this.selectInfo.supcustName = value.titles;
    },
    selectSupplier(value) {
      this.showSupplier = value.isShow;
      this.selectInfo.supplierID = value.ids;
      this.selectInfo.supplierName = value.titles;
    },
    selectBrand(value) {
      this.showBrand = value.isBrandShow;
      this.selectInfo.brandName = value.brand_name
      this.selectInfo.brandId = value.brand_id
    },
    selectItemClass(value) {
      // 后台接口是单选，此处先改为单选，后续需要在放开， 组件配置项可改为单选。 工单：2010
      // this.selectInfo.itemClassID =JSON.stringify(value.path)
      // this.selectInfo.itemClassName = value.itemObjs.map(obj=>{return obj.titles}).join(",")
      this.selectInfo.itemClassID = value.path
      this.selectInfo.itemClassName = value.itemObjs.titles
      this.showItemClass = value.isShow
    },
    selectSender(value) {
      this.showSender = value.isSenderShow;
      this.selectInfo.senderID = value.oper_id;
      this.selectInfo.senderName = value.oper_name;
    },
    selectDepartment(value) {
      this.showDepart = value.isDepartShow;
      this.selectInfo.departName = value.depart_name
      this.selectInfo.departID = value.depart_id
    },
    selectGroup(value) {
      this.showGroup = value.isGroupShow;
      this.selectInfo.groupName = value.group_name
      this.selectInfo.groupID = value.group_id
    },
    selectBranchsWithPermission(value) {
      this.showBranch = value.isBrandShow
      this.selectInfo.branchID = value.branch_id
      this.selectInfo.branchName = value.branch_name
    },
    cancelSelect() {
      for (let key in this.selectInfo) {
        if (this.selectInfo.hasOwnProperty(key) && key !== 'startDate' && key !== 'endDate') {
          this.selectInfo[key] = "";  // 清空选择，但不清空日期字段
        }
      }
      
      // 设置日期为今天
      const today = new Date();
      const formattedDate = this.formatDate(today, "YYYY-mm-dd");
      this.selectInfo.startDate = `${formattedDate} 00:00:00`;
      this.selectInfo.endDate = `${formattedDate} 23:59:59`;
      this.selectInfo.dateTime = `${this.selectInfo.startDate} 至 ${this.selectInfo.endDate}`;

      // 清空数据
      this.resultData = [];
      this.startRow = 0;
      this.endRow = 29;
      this.finished = false;
    },
    onRegionSelected(value) {
      this.showRegion = false
      this.selectInfo.regionName = value[0].name
      this.selectInfo.regionID = value[0].id
      this.$refs.selectTreeRef.handleCancel()
    },
    selectBrief(value) {
      this.showRemark = value.isBriefShow;
      this.selectInfo.remark = value.oper_name;
    },
    startSelect() {
      // 将selectinfo等数据归零
      this.startRow = 0
      this.endRow = this.NumShowOneTime - 1
      this.resultData = []
      this.finished = false
      this.submitSelect()
      this.isRefresh = false
    },
    submitSelect() {
      // 用于请求数据，注意异步通信，且row的start end更新必须在这个函数里面防止不同步
      this.selectShow = false
      let params = {
        GetRowsCount: true,
        status:'approved',
        branch_id: this.selectInfo.branchID,
        branch_name: this.selectInfo.branchName,
        class_name: this.selectInfo.itemClassName,
        class_path: this.selectInfo.itemClassID,
        endDay: this.selectInfo.endDate,
        endRow: this.endRow,
        gridID: "gridItems",
        other_region: this.selectInfo.regionID,
        item_id: this.selectInfo.itemID ?? "",
        item_name: this.selectInfo.itemName,
        sheetType: this.sheetType,
        sortColumn: "",
        sortDirection: "",
        seller_id: this.selectInfo.operID,
        startDay: this.selectInfo.startDate,
        startRow: this.startRow,
        brand_id: this.selectInfo.brandId,
        brand_name: this.selectInfo.branchName,
        sup_name: this.selectInfo.supcustName,
        supcust_id: this.selectInfo.supcustID,
        senders_id: this.selectInfo.senderID,
        senders_name: this.selectInfo.senderName,
        make_brief: this.selectInfo.makeBrief,
        remark: this.selectInfo.remark,
        remark_name: this.selectInfo.remark,
        group_id: this.selectInfo.groupID,
        group_name: this.selectInfo.groupName,
        depart_id: this.selectInfo.departID,
        depart_name: this.selectInfo.departName,
      }
      if (this.selectInfo.itemClassID) {
        let other_class_arr = this.selectInfo.itemClassID.split('/')
        params.other_class = other_class_arr[other_class_arr.length - 2]
      }
      console.log("API调用参数:", params);
      console.log("业务员参数检查:", {
        operID: this.selectInfo.operID,
        operName: this.selectInfo.operName,
        seller_id_in_params: params.seller_id
      });

      var that = this
      //   GetSaleDetails(params).then(res=>{
      //     if(res.result === 'OK'){
      //       if(!that.finished)
      //     {
      //       for(let key in res.rows) {
      //       that.resultData.push(res.rows[key])
      //     }

      //     that.sumResult = res.sumResult
      //     that.loading = false
      //     that.startRow = that.startRow + that.NumShowOneTime
      //     that.endRow = that.endRow + that.NumShowOneTime
      //     if(that.resultData.length >= Number(res.rowsCount)){
      //       that.finished = true
      //     }
      //   }
      //   if ( !res || res.result != 'OK'){
      //       Toast.fail('刷新失败')
      //     }
      //   }
      //   })

      // },
      GetSaleDetails(params).then(res => {
        if (res.result === 'OK') {
          if (!this.finished) {
            for (let key in res.rows) {
              let index = parseInt(key, 10);  // 将key转换为整数
              if (index >= this.startRow && index <= this.endRow) {
                this.resultData.push(res.rows[key])
              }
            }

            this.sumResult = res.sumResult
            this.loading = false
            this.startRow = this.startRow + this.NumShowOneTime
            this.endRow = this.endRow + this.NumShowOneTime
            if (this.resultData.length >= Number(res.rowsCount)) {
              this.finished = true
            }
          }
          if (!res || res.result != 'OK') {
            Toast.fail('刷新失败')
          }
        }
      })

    },
    selectRow(index) {
      if (this.selectedRow == index) {
        let sheetid = this.resultData[index].sheet_id
        let sheetType = this.resultData[index].sheet_type
        if(!sheetid || !sheetType) {
          Toast.fail('无法打开单据，请保证单据属性完整')
          return
        }
        this.$router.push({ path: '/SaleSheet', query: { sheetID: sheetid, sheetType: sheetType } })
      } else {
        this.selectedRow = index;
      }
    }
  },
  mounted() {
    this.viewRange = window.getRightValue("delicacy.sheetViewRange.value");
    if (this.viewRange == "self") {
      console.log(this.$store.state.operInfo);
      this.selectInfo.operID = this.$store.state.operInfo.oper_id;
      this.selectInfo.operName = this.$store.state.operInfo.oper_name;
      this.disabledShowSeller = true
    }
    if (this.$route.query.startDate != undefined) this.selectInfo.startDate = this.$route.query.startDate;
    if (this.$route.query.endDate != undefined) this.selectInfo.endDate = this.$route.query.endDate;
    if (this.$route.query.supcustID != undefined) {
      this.selectInfo.supcustID = this.$route.query.supcustID;
      this.selectInfo.supcustName = this.$route.query.supcustName;
    }
    if (this.$route.query.itemID != undefined) {
      this.selectInfo.itemID = this.$route.query.itemID;
    }
    if (this.$route.query.itemName != undefined) {
      this.selectInfo.itemName = this.$route.query.itemName;
    }
    if (this.$route.query.operID != undefined) {
      this.selectInfo.operID = this.$route.query.operID;
      this.selectInfo.operName = this.$route.query.operName;
    }
    if (this.$route.query.makeBrief != undefined) {
      this.selectInfo.makeBrief = this.$route.query.makeBrief;
    }
    if (this.$route.query.remark != undefined) {
      this.selectInfo.remark = this.$route.query.remark;
    }
    if( this.$route.query.sheetType != undefined ){
      this.sheetType = this.$route.query.sheetType;
    }
    if( this.$route.query.queryBySalesOrderChecked != undefined ){
      this.queryBySalesOrderChecked = this.$route.query.queryBySalesOrderChecked =='false'?false:true;//router会把变量转成字符串，需要转回布尔值
    }
    if( this.$route.query.brandName != undefined ){
      this.selectInfo.brandName = this.$route.query.brandName;
    }
    if( this.$route.query.brandID != undefined ){
      this.selectInfo.brandId = this.$route.query.brandID;
    }
    if( this.$route.query.regionName != undefined ){
      this.selectInfo.regionName = this.$route.query.regionName;
    }
    if( this.$route.query.regionID != undefined ){
      this.selectInfo.regionID = this.$route.query.regionID;
    }
    if( this.$route.query.itemClassName != undefined ){
      this.selectInfo.itemClassName = this.$route.query.itemClassName;
    }
    if(this.$route.query.itemClassID != undefined){
      this.selectInfo.itemClassID = this.$route.query.itemClassID;
    }
    if(this.$route.query.remark != undefined){
      this.selectInfo.remark = this.$route.query.remark;
    }
    this.startSelect();
  },
  components: {
    "van-nav-bar": NavBar,
    "van-list": List,
    "van-icon": Icon,
    "van-calendar": Calendar,
    "van-popup": Popup,
    "region-selection": RegionSelection,
    "van-button": Button,
    "van-field": Field,
    "van-cell-group": CellGroup,
    "van-checkbox": Checkbox,
    "van-checkbox-group": CheckboxGroup,
    SelectSeller,
    SelectCustomer,
    SelectSupplier,
    SelectGroup,
    SelectBrand,
    ItemClass,
    ItemArchives,
    SelectSender,
    SelectOne,
    SelectBranchWithPermission,
    SelectBrief,
    "van-pull-refresh": PullRefresh,
    "van-cell": Cell,
    YjSelectTree

  }
}
</script>
<style lang="less" scoped>
.footer_button {
  // width: 100%;
  height: 45px;
  margin-top: 10px;
  vertical-align: top;
  display: flex;
  justify-content: space-between;
  margin: 10PX 5PX 0 5PX;

  button {
    // width: 100px;
    width: calc(47% - 5PX);
    height: inherit;
    // margin: 0 20px;
    vertical-align: top;
  }
}

table {
  table-layout: fixed;
  border-collapse: separate;
  border-spacing: 0;
  border: 0;
  white-space: nowrap;
}

td,
th {
  padding: 10px;
  box-sizing: border-box;
  border-right: 1px solid #eee;
  border-bottom: 1px solid #eee;
  overflow-x: auto;
  white-space: nowrap;

}

thead tr th {
  padding: 8px;
  position: sticky;
  top: 0;
  background: #eee;
  white-space: nowrap;
}

th:first-child,
td:first-child {
  position: sticky;
  left: 0;
  background: #eee;
}

th:first-child {
  z-index: 1;
  /*左上角单元格z-index，切记要设置，不然表格纵向横向滚动时会被该单元格右方或者下方的单元格遮挡*/
  background: #eee;
}

.viewDocument_box_m {
  height: 100%;
  overflow-y: scroll;
  overflow-x: auto;
}

.my_table .selected {
  background-color: #4f9256;
  /* 点击行的背景颜色 */
  border: 1px solid #000;
  /* 点击行的边框样式 */
  padding-bottom: 40px;
}

.cellgroup {
  .van-field {
    border-bottom: 2px solid #eee;
  }
}
</style>