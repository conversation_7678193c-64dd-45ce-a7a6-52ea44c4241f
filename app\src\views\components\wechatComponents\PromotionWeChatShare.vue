<template>
  <div class="we-chat-share-wrapper" v-if="showShare">
    <van-share-sheet v-model="showShare" :title="title" :options="options" @select="shareSheetSelect" />
  </div>
</template>

<script>
import { ShareSheet, Toast } from "vant";
import globalVars from "../../../static/global-vars";
export default {
  name:"PromotionWeChatShare",
  components: {
    "van-share-sheet": ShareSheet,
  },
  props:{
    title:{
      type:String,
      default:"分享给好友"
    }
  },
  data(){
    return {
      showShare:false,
      promotionType:"",
      sharePromotionItem:{},
      sharePromotionImg:"",
      options:[
        { name: '微信', icon: 'wechat' },
        { name: '图片', icon: 'poster' }
      ],
      weChatConfigInfo: {
        appId: "",
        jsapiTicket: ""
      },
      signInfo: {},
    }
  },
  computed:{
    wechatConf() {
      return globalVars.wechatConf;
    }
  },
  methods:{
    changeShowShare(type,item,img){
      console.log("接收分享",type,item,img);
      this.promotionType=type;
      this.sharePromotionItem=item;
      this.sharePromotionImg=img;
      this.showShare = true;
    },
    shareSheetSelect(option){
      let that = this
      if(option.name==="微信"||option.name==="朋友圈"){
        console.log("PromotionWeChatShare: 开始检测微信安装状态...");
        window.Wechat.isInstalled(function (installed) {
          console.log("PromotionWeChatShare: 微信安装检测结果:", installed);
          if (installed) {
            console.log("PromotionWeChatShare: 微信已安装，开始分享...");
            that.wechatshare()
          } else {
            console.log("PromotionWeChatShare: 微信未安装");
            Toast("您还没有安装微信，请先安装微信。");
          }
        }, function (reason) {
          console.error("PromotionWeChatShare: 检测安装微信失败:", reason);
          Toast("检测安装微信失败: " + reason);
        });
      }
      if(option.name === '图片'){
        this.$emit("shareSheetSelected",{
          type:"sharePicture"
        })
      }
      if(option.name === '微信'){
        this.$emit("shareSheetSelected",{
          type:"shareWechatWebpage"
        })
      }
      this.showShare = false;
    },
    wechatshare(){
      let imgUrl = this.wechatConf.imgUrl
      let title = "", webpageUrl = baseUrl;
      let baseUrl = this.wechatConf.baseUrl
      let companyName = this.$store.state.operInfo?.setting?.companyName || this.$store.state.account.companyName
      switch(this.promotionType){
        case "combine":
          title=this.sharePromotionItem.title?this.sharePromotionItem.title:`${this.sharePromotionItem.sources[0].items_name.split(',')[0]}组合`;
          break;
        case "seckill":
          title=this.sharePromotionItem.item_name;
          break;
        case "fullgift":
          title=`赠送${this.sharePromotionItem.gifts[0].items_name.split(',')[0]}等`;
          break;
        case "cashprize":
          title=`${this.sharePromotionItem.proofs[0].items_name.split(',')[0]}换购组`;
          break;
      }
      window.Wechat.share({
        message: {
          title: title,
          description: `来自【${companyName}】`,
          thumb: imgUrl,
          media: {
            type: window.Wechat.Type.MINI,
            webpageUrl: webpageUrl,
            userName:"gh_75b73eab9761",
            path:"/pages/login/login",
            hdImageData:this.sharePromotionImg?this.sharePromotionImg:imgUrl
          }
        },
        scene: window.Wechat.Scene.SESSION
      }, function () {
        Toast.success("分享成功")
      }, function (reason) {
        Toast.fail("分享失败: " + reason)
      });
    }
  }
}
</script>

<style lang="less" scoped>
/deep/ .van-share-sheet {
  min-height: 210px;
}
.we-chat-share-wrapper {
  height: 100%;
}
</style>