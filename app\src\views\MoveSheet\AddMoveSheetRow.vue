<template>
  <div>
    <div class="add_goods_box" v-cloak>
      <!-- 商品操作区域 -->
      <div class="item_name_option">
        <div class="bReturn">
        </div>
        <div class="more_option">
          <div class="more_option_1" id="more_option_1">
          </div>
          <div class="more_option_2" id="more_option_2">
          </div>
          <div class="more_option_3" id="more_option_3">
          </div>
        </div>
      </div>
      <div class="close_icon_font_wrapper">
        <div class="close_icon_font" @click="closePage">
          <van-icon name="arrow-left" style="margin-left: 5px" />
          <div class="font_wrapper"></div>
        </div>
      </div>
      <!-- 商品输入内容区域 -->
      <div class="add_box" v-cloak >
        <div v-if="distinctFlagDom" key="distinct_stock_wrapper" >
          <div class="distinct_stock_wrapper">
            <div class="distinct_msg_wrapper">
              <div>该商品包含区分库存的属性，请输入{{ showBtnAttrName }}数量</div>
            </div>
            <div class="distinct_btn_wrapper">
              <div class="distinct_btn" @click="handleAttr">输入{{ showBtnAttrName }}数量</div>
            </div>
          </div>
        </div>
        <div v-else-if="shoppingCarFinishFlag"  key="finish_stock_wrapper">
          <div class="distinct_stock_wrapper" style="height: 100px;">
            <div class="distinct_btn_wrapper">
              <div class="distinct_btn" style="background-color:#ffcccc;color:#000" @click="closePage">完 成</div>
            </div>
          </div>
        </div>
        <div v-else>
          <div ref="cursor" id="virtualCursor" class="virtualCursor" style="color:transparent;">
          </div>
          <div class="add_box_body">
            <van-row gutter="10" :style="{ 'height': '35px' }" v-if="showFromBranchPosition || showToBranchPosition">
              <van-col span="5" style="font-size:14px;" v-if="showFromBranchPosition">出仓库位:</van-col>
              <van-col span="7" class="flex" v-if="showFromBranchPosition">
                <van-field class="inputBranchPosition" v-model="itemRow.from_branch_position_name" right-icon="close"
                  placeholder="出仓库位" @click-input="fromBranchPositionShow = true" :readonly="true"
                  @click-right-icon="clearFromBranchPosition" />
                <!-- <input type="text" class="input_style" readonly v-model="itemRow.from_branch_position_name" placeholder="出仓库位"
                  id="inputFromBranchPosition" @click="fromBranchPositionShow = true" /> -->
              </van-col>
              <van-col span="5" style="font-size:14px;" v-if="showToBranchPosition">入仓库位:</van-col>
              <van-col span="7" class="flex" v-if="showToBranchPosition">
                <van-field class="inputBranchPosition" v-model="itemRow.to_branch_position_name" right-icon="close"
                  placeholder="入仓库位" :readonly="true" @click-input="toBranchPositionShow = true"
                  @click-right-icon="clearToBranchPosition" />
                <!-- <input type="text" class="input_style" readonly v-model="itemRow.to_branch_position_name" placeholder="入仓库位"
                  id="inputToBranchPosition" @click="toBranchPositionShow = true" /> -->
              </van-col>
            </van-row>
            <van-row gutter="10" :style="{ 'height': '35px' }" v-if="itemRow.batch_level">
              <van-col span="5" style="font-size:14px;">生产日期:</van-col>
              <van-col span="7" class="flex">
                <input type="text" class="input_style" readonly v-model="itemRow.produce_date" :placeholder="'产期'"
                  id="inputProduceDate" @click="handleInputClick($event,'', 'produce_date')" /></van-col>
              <van-col span="2" v-if="itemRow.batch_level !== ''"><van-icon size="24px" name="ellipsis"
                  @click="produceDateShow = true" /></van-col>
              <van-col span="1"></van-col>
              <van-col span="3" style="font-size:14px;" v-if="itemRow.batch_level === '2'">批次:</van-col>
              <van-col span="6" class="flex" v-if="itemRow.batch_level === '2'">
                <input type="text" class="input_style" v-model="itemRow.batch_no" :placeholder="'批次'" id="inputBatchNo"
                  @click="handleInputClick($event,'', 'batch_no')" />
              </van-col>
            </van-row>
            <van-row gutter="10" v-if="itemRow.bunit" :style="{ 'height': '35px' }">
              <van-col span="4" class="flex">
                <input type="text" class="input_style" readonly v-model="itemRow.bunitSum" :placeholder="'数量'"
                  id="inputQuantityBig" @click="handleInputClick($event,'', 'bunitSum')">
              </van-col>
              <van-col span="3">
                <span class="input_style">{{ itemRow.bunit }}</span>
              </van-col>
              <van-col span="6" class="flex">
                <div @touchstart="touchstartUnit(itemRow, itemRow.bunit)" class="test-long-wrapper"
                  v-longpress="handeleLongpress">
                  <van-popover v-model="itemRow.unitPrice[itemRow.bunit].showPopoverFlag">
                    <div class="popover-wrapper">
                      <div v-for="(action) in itemRow.unitPrice[this.selectUnitNo]?.actions"
                        @click="onSelectPopover(action, itemRow.bfactor)" :key="action.text" class="popover-item">
                        <div class="popover-content-left">{{ action.text }}</div>
                        <div class="popover-content-right">
                          <template v-if="action.price && Number(action.price) !== 0">{{ action.price }} / {{
                            itemRow.bunit }}
                          </template>
                          <template v-else>暂无</template>
                        </div>
                      </div>
                    </div>
                    <template #reference>

                      <!-- <input type="text" v-if="isContractSeller" class="input_style" readonly :disabled='!canChangePrice'
                        v-model="itemRow.b_contract_price" id="inputContractPriceSmall" :placeholder="'承包价'"
                        @click="handleInputClick($event, itemRow.bfactor, 'b_contract_price')"> -->
                      <input type="text" class="input_style" readonly  v-model="itemRow.b_wholesale_price"
                        id="inputContractPriceSmall" :placeholder="priceLabel"
                        @click="handleInputClick($event,itemRow.bfactor, 'b_wholesale_price')">

                    </template>
                  </van-popover>
                </div>
              </van-col>
              <!-- <van-col v-if="isContractSeller" span="4" class="flex">
                <input type="text" class="input_style" readonly
                  :value="toMoney(Number(itemRow.b_contract_price) * Number(itemRow.bunitSum))" :placeholder="'合计 '">
              </van-col> -->
              <van-col span="4" class="flex">
                <input type="text" class="input_style" readonly
                  :value="toMoney(Number(itemRow.b_wholesale_price) * Number(itemRow.bunitSum))" :placeholder="'合计 '">
              </van-col>
              <van-col span="4">
                <input type="text" class="input_style" v-model="itemRow.bremark" :placeholder="'备注'"
                  @change="onInput('bremark', $event)" @focus="handleRemarkFocus" @blur="handleRemarkBlur">

              </van-col>
            </van-row>
            <van-row gutter="10" v-if="itemRow.munit" :style="{ 'height': '35px' }">
              <van-col span="4" class="flex">
                <input type="text" class="input_style" readonly v-model="itemRow.munitSum" :placeholder="'数量'"
                  id="inputQuantityMid" @click="handleInputClick($event,'', 'munitSum')">
              </van-col>
              <van-col span="3"><span class="input_style">{{ itemRow.munit }}</span></van-col>
              <van-col span="6" class="flex">
                <div @touchstart="touchstartUnit(itemRow, itemRow.munit)" class="test-long-wrapper"
                  v-longpress="handeleLongpress">
                  <van-popover v-model="itemRow.unitPrice[itemRow.munit].showPopoverFlag">
                    <div class="popover-wrapper">
                      <div v-for="(action, showPopoverIndex) in itemRow.unitPrice[this.selectUnitNo]?.actions"
                        @click="onSelectPopover(action, itemRow.mfactor)" :key="action.text" class="popover-item">
                        <div class="popover-content-left">{{ action.text }}</div>
                        <div class="popover-content-right">
                          <template v-if="action.price && Number(action.price) !== 0">{{ action.price }} / {{
                            itemRow.munit }}
                          </template>
                          <template v-else>暂无</template>
                        </div>
                      </div>
                    </div>
                    <template #reference>

                      <!-- <input type="text" v-if="isContractSeller" class="input_style" readonly :disabled='!canChangePrice'
                        v-model="itemRow.m_contract_price" id="inputContractPriceMin" :placeholder="'承包价'"
                        @click="handleInputClick($event, itemRow.mfactor, 'm_contract_price')"> -->
                      <input type="text" class="input_style" readonly v-model="itemRow.m_wholesale_price"
                        id="inputContractPriceMin" :placeholder="priceLabel"
                        @click="handleInputClick($event, itemRow.mfactor, 'm_wholesale_price')">

                    </template>
                  </van-popover>
                </div>
              </van-col>
              <!-- <van-col v-if="isContractSeller" span="4" class="flex">
                <input type="text" class="input_style" readonly
                  :value="toMoney(Number(itemRow.m_contract_price) * Number(itemRow.munitSum))" :placeholder="'合计 '">
              </van-col> -->
              <van-col span="4" class="flex">
                <input type="text" class="input_style" readonly
                  :value="toMoney(Number(itemRow.m_wholesale_price) * Number(itemRow.munitSum))" :placeholder="'合计 '">
              </van-col>
              <van-col span="4">
                <input type="text" class="input_style" v-model="itemRow.mremark" :placeholder="'备注'"
                  @change="onInput('mremark', $event)" @focus="handleRemarkFocus" @blur="handleRemarkBlur">
              </van-col>
            </van-row>
            <van-row gutter="10" v-if="itemRow.sunit" :style="{ 'height': '35px' }">
              <van-col span="4" class="flex">
                <input type="text" class="input_style" readonly v-model="itemRow.sunitSum" id="inputQuantitySmall"
                  :placeholder="'数量'" @click="handleInputClick($event,'', 'sunitSum')">
              </van-col>
              <van-col span="3"><span class="input_style">{{ itemRow.sunit }}</span></van-col>
              <van-col span="6" class="flex">
                <div @touchstart="touchstartUnit(itemRow, itemRow.sunit)" class="test-long-wrapper"
                  v-longpress="handeleLongpress">
                  <van-popover v-model="itemRow.unitPrice[itemRow.sunit].showPopoverFlag">
                    <div class="popover-wrapper">
                      <div v-for="(action) in itemRow.unitPrice[this.selectUnitNo]?.actions"
                        @click="onSelectPopover(action, itemRow.sfactor)" :key="action.text" class="popover-item">
                        <div class="popover-content-left">{{ action.text }}</div>
                        <div class="popover-content-right">
                          <template v-if="action.price && Number(action.price) !== 0">{{ action.price }} / {{
                            itemRow.sunit }}
                          </template>
                          <template v-else>暂无</template>
                        </div>
                      </div>
                    </div>
                    <template #reference>

                      <!-- <input type="text" v-if="isContractSeller" class="input_style" readonly :disabled='!canChangePrice'
                        v-model="itemRow.s_contract_price" id="inputContractPriceSmall" :placeholder="'承包价'"
                        @click="handleInputClick($event, itemRow.sfactor, 's_contract_price')"> -->
                      <input type="text" class="input_style" readonly v-model="itemRow.s_wholesale_price"
                        id="inputContractPriceSmall" :placeholder="priceLabel"
                        @click="handleInputClick($event, itemRow.sfactor, 's_wholesale_price')">

                    </template>
                  </van-popover>
                </div>
              </van-col>
              <!-- <van-col v-if="isContractSeller" span="4" class="flex">
                <input type="text" class="input_style" readonly
                  :value="toMoney(Number(itemRow.s_contract_price) * Number(itemRow.sunitSum))" :placeholder="'合计 '">
              </van-col> -->
              <van-col span="4" class="flex">
                <input type="text" class="input_style" readonly
                  :value="toMoney(Number(itemRow.s_wholesale_price) * Number(itemRow.sunitSum))" :placeholder="'合计 '">
              </van-col>
              <van-col span="4">
                <input type="text" class="input_style" v-model="itemRow.sremark" :placeholder="'备注'"
                  @change="onInput('sremark', $event)" @focus="handleRemarkFocus" @blur="handleRemarkBlur">
              </van-col>
            </van-row>
          </div>
        </div>

      </div>
      <div class="sotck-info" :style="{ 'height': '50px' }">
        <div class="product_delist" v-show="!distinctFlagDom && !shoppingCarFinishFlag">
          <div class="product_delist_wrapper" v-show="canSeeFromStock" key="canSeeFromStock">
            <div class="stockInfo-wrapper"
              v-if="Number(itemRow.fb_qty) !== 0 || Number(itemRow.fm_qty) !== 0 || Number(itemRow.fs_qty) !== 0">
              <div class="stockInfo-title">出仓</div>
              <div class="stockInfo-title">可用:</div>
              <div class="stockInfo-content-wrapper">
                <div class="stockInfo-content">
                  <div class="stockInfo-content-num"> {{unitStockQty.from_usable_stock_qty_unit}}</div>
                  <div class="stockInfo-content-unit"></div>
                </div>
              </div>
            </div>
            <div class="stockInfo-wrapper"
              v-if="Number(itemRow.fb_qty) !== 0 || Number(itemRow.fm_qty) !== 0 || Number(itemRow.fs_qty) !== 0">
              <div class="stockInfo-title">出仓</div>
              <div class="stockInfo-title">实际:</div>
              <div class="stockInfo-content-wrapper">
                <div class="stockInfo-content">
                  <div class="stockInfo-content-num"> {{unitStockQty.from_stock_qty_unit}}</div>
                  <div class="stockInfo-content-unit"></div>
                </div>
              </div>
            </div>
          </div>
          <div v-if="canSeeToStock" class="product_delist_wrapper" key="'canSeeToStock">
            <div class="stockInfo-wrapper"
              v-if="Number(itemRow.tb_qty) !== 0 || Number(itemRow.tm_qty) !== 0 || Number(itemRow.ts_qty) !== 0">
              <div class="stockInfo-title">入仓</div>
              <div class="stockInfo-title">可用:</div>
              <div class="stockInfo-content-wrapper">
                <div class="stockInfo-content">
                  <div class="stockInfo-content-num"> {{unitStockQty.to_usable_stock_qty_unit}}</div>
                  <div class="stockInfo-content-unit"></div>
                </div>
                <!-- <div class="stockInfo-content" v-if="Number(itemRow.tb_qty) !== 0">
                  <div class="stockInfo-content-num">{{ itemRow.tb_qty }}</div>
                  <div class="stockInfo-content-unit">{{ itemRow.bunit }}</div>
                </div>
                <div class="stockInfo-content" v-if="Number(itemRow.tm_qty) !== 0">
                  <div class="stockInfo-content-num">{{ itemRow.tm_qty }}</div>
                  <div class="stockInfo-content-unit">{{ itemRow.munit }}</div>
                </div>
                <div class="stockInfo-content" v-if="Number(itemRow.ts_qty) !== 0">
                  <div class="stockInfo-content-num">{{ itemRow.ts_qty }}</div>
                  <div class="stockInfo-content-unit">{{ itemRow.sunit }}</div>
                </div> -->
              </div>
            </div>
            <div class="stockInfo-wrapper"
              v-if="Number(itemRow.tb_qty) !== 0 || Number(itemRow.tm_qty) !== 0 || Number(itemRow.ts_qty) !== 0">
              <div class="stockInfo-title">入仓</div>
              <div class="stockInfo-title">实际:</div>
              <div class="stockInfo-content-wrapper">
                <div class="stockInfo-content">
                  <div class="stockInfo-content-num"> {{unitStockQty.to_stock_qty_unit}}</div>
                  <div class="stockInfo-content-unit"></div>
                </div>
              </div>
            </div>
            <!-- <h6>
              入仓:<span>{{ itemRow.tb_qty }}{{ itemRow.bunit }}</span>
              <span>{{ itemRow.tm_qty }}{{ itemRow.munit }}</span>
              <span>{{ itemRow.ts_qty }}{{ itemRow.sunit }}</span>
            </h6> -->
          </div>
        </div>
        <div class="product_unit" v-show="!distinctFlagDom && !shoppingCarFinishFlag">
          <div style="font-size: 14px"
            v-if="(itemRow.bunit && itemRow.munit) || (itemRow.bunit && itemRow.sunit) || (itemRow.munit && itemRow.sunit)">
            <span style="font-size: 14px" v-if="itemRow.bunit">{{ (itemRow.sfactor ? itemRow.sfactor :
              itemRow.s_unit_factor) }}{{ itemRow.bunit }}=</span>
            <span style="font-size: 14px" v-if="itemRow.munit">{{ (itemRow.bfactor ? itemRow.bfactor :
              itemRow.b_unit_factor) / (itemRow.mfactor ? itemRow.mfactor : itemRow.m_unit_factor) }}{{ itemRow.munit
  }}=</span>
            <span style="font-size: 14px" v-if="itemRow.sunit && (itemRow.bunit || itemRow.munit)">{{ (itemRow.bfactor ?
              itemRow.bfactor : itemRow.b_unit_factor) }}{{ itemRow.sunit }}</span>
          </div>
        </div>
      </div>

    </div>
    <!-- 自定义键盘 -->
    <div class="numPad" id="numPad" v-show="showNumPadFlag"
      :style="{ 'background-color': ((!distinctFlagDom && !shoppingCarFinishFlag) ? '#ddd' : '#fff') }">
      <div style="width: 100%;height: 100%" v-show="!distinctFlagDom && !shoppingCarFinishFlag">
        <table class="numPad_table" cellspacing="0px" cellpadding="0px">
          <tr>
            <td>
              <div class="numbtn1" @touchend="onNumTouchEnd($event)" @touchstart="onClickNum('1', $event, 0.6)">1</div>
            </td>
            <td>
              <div class="numbtn1" @touchend="onNumTouchEnd($event)" @touchstart="onClickNum('2', $event, 0.6)">2</div>
            </td>
            <td>
              <div class="numbtn1" @touchend="onNumTouchEnd($event)" @touchstart="onClickNum('3', $event, 0.6)">3</div>
            </td>
            <td style="border-right: 0;">
              <div class="numbtn1" @touchend="onNumTouchEnd($event)" @touchstart="onDelNum($event)"
                style="border-right: 0;">
                <svg width="30px" height="30px" fill="#666">
                  <use xlink:href="#icon-backspace"></use>
                </svg>
              </div>
            </td>
          </tr>
          <tr>
            <td>
              <div class="numbtn1" @touchend="onNumTouchEnd($event)" @touchstart="onClickNum('4', $event, 0.6)">4</div>
            </td>
            <td>
              <div class="numbtn1" @touchend="onNumTouchEnd($event)" @touchstart="onClickNum('5', $event, 0.6)">5</div>
            </td>
            <td>
              <div class="numbtn1" @touchend="onNumTouchEnd($event)" @touchstart="onClickNum('6', $event, 0.6)">6</div>
            </td>

            <td rowspan="3" style="border-right: 0;">
              <div style="border-right: 0;" class="numbtn1 save_btn" @touchstart="btnOK_clicked($event)"
                @touchend="onbtnOKClickedEnd($event)">确认</div>
            </td>
          </tr>
          <tr>
            <td>
              <div class="numbtn1" @touchend="onNumTouchEnd($event)" @touchstart="onClickNum('7', $event, 0.6)">7</div>
            </td>
            <td>
              <div class="numbtn1" @touchend="onNumTouchEnd($event)" @touchstart="onClickNum('8', $event, 0.6)">8</div>
            </td>
            <td>
              <div class="numbtn1" @touchend="onNumTouchEnd($event)" @touchstart="onClickNum('9', $event, 0.6)">9</div>
            </td>
          </tr>
          <tr>
            <td colspan="2">
              <div class="numbtn1" @touchend="onNumTouchEnd($event)" @touchstart="onClickNum('0', $event, 0.6)">0</div>
            </td>
            <td>
              <div class="numbtn1" @touchend="onNumTouchEnd($event)" @touchstart="onClickNum('.', $event, 0.6)">.</div>
            </td>
            <!-- <td colspan="1">
              <div class="numbtn1" @touchend="onNumTouchEnd($event)" @touchstart="onClickNum('-', $event, 0.6)">-</div>
            </td>
            <td>
              <div class="numbtn1" @touchend="onNumTouchEnd($event)" @touchstart="onClickNum('0', $event, 0.6)">0</div>
            </td>
            <td>
              <div class="numbtn1" @touchend="onNumTouchEnd($event)" @touchstart="onClickNum('.', $event, 0.6)">.</div>
            </td> -->
          </tr>
        </table>
      </div>
    </div>
    <!-- 自定义键盘 -->
    <van-popup v-model="produceDateShow" position="bottom" :style="{ width: '100%', height: '50%' }">
      <ProduceDate :ItemInfo="itemRow" :batchStock="batchStockListForShow" @setDateAndBatchNo="setDateAndBatchNo">
      </ProduceDate>
    </van-popup>
    <van-popup v-model="fromBranchPositionShow" position="bottom" :style="{ width: '100%', height: '50%' }">
      <BranchInfoCascader :branchPositionSource="fromBranchPositionSource" :title="'请选择出仓库位'"
        @handleCloseBranchInfo="handleCloseBranchInfo" @setItemRowBranchInfo="setItemRowBranchInfo">
      </BranchInfoCascader>
    </van-popup>
    <van-popup v-model="toBranchPositionShow" position="bottom" :style="{ width: '100%', height: '50%' }">
      <BranchInfoCascader :branchPositionSource="toBranchPositionSource" :title="'请选择入仓库位'"
        @handleCloseBranchInfo="handleCloseBranchInfo" @setItemRowBranchInfo="setItemRowBranchInfo">
      </BranchInfoCascader>
    </van-popup>
  </div>
</template>

<script>
import { CreateItemsForAttrRows, GetBatchStockForMove, GetBranchPosition, GetBranchPositionForMove } from "../../api/api";
import { Field, Col, Row, Button, Toast, Popup, Dialog, Divider, Icon, Popover } from 'vant'
import Mixin from './MoveSheetMixin/mixin.js'
import ProduceDate from '../components/produceDate/ProduceDate.vue'
import BranchInfoCascader from '../components/BranchInfoCascader/BranchInfoCascader.vue'
export default {
  name: "AddMoveSheetRow",
  mixins: [Mixin],
  data() {
    return {
      itemRow: {},
      itemRowCopy: {},// 通过对itemRow拷贝，记录信息，供还原使用
      editingItemRowIndex: null,
      curObject: {},
      curFactor: '',
      curProp: '',
      curInput: null,
      audio_num: [], // iOS下audio_num设为数组已降低声音播放延迟
      audio_del: {},
      audio_comfirm: {},
      invokeComputedVar: '',
      smallUnitStock: '',
      produceDateShow: false,
      loop: 0,
      // batchStock: [],
      fromBranchPositionShow: false,
      toBranchPositionShow: false,
      showFromBranchPosition: false,
      showToBranchPosition: false,
      showNumPad: true, // 控制自定义键盘显示
      fromBranchPositionSource: [],
      toBranchPositionSource: [],
      curFromBranchId: "",
      curToBranchId: "",
      fromBatchStock: [],
      toBatchStock: [],
      batchStockListForShow: [],
      fromBranchList: [],
      toBranchList: [],
      selectUnitNo: '',
      from_usable_stock_qty_unit:'',
      to_usable_stock_qty_unit:'',
      from_stock_qty_unit:'',
      to_stock_qty_unit:''
    };
  },
  components: {
    "van-field": Field,
    "van-row": Row,
    "van-col": Col,
    "van-button": Button,
    "van-popup": Popup,
    "van-divider": Divider,
    "van-icon": Icon,
    "van-popover": Popover,
    ProduceDate,
    BranchInfoCascader,
  },
  props: {
    sheet: {
      type: Object
    },
    moveType: {
      type: String
    },
    //查看出仓权限
    canSeeFromStock: {
      type: Boolean
    },
    //查看出仓权限
    canSeeToStock: {
      type: Boolean
    },
  },
  watch: {
    // 'itemRow.bunitSum': {
    //   handler: function (val, oldVal) {
    //     console.log(val)
    //     let changeVal = val == undefined ? 0 : val
    //     this.itemRow.fb_qty = toMoney(Number(this.itemRow.fb_qty) - Number(changeVal))
    //     this.itemRow.tb_qty = toMoney(Number(this.itemRow.tb_qty) + Number(changeVal))
    //   },
    //   deep: true
    // },

  },
  computed: {
    showNoProduceDate() {
      return this.$store.state.operInfo.setting && this.$store.state.operInfo.setting.showNoProduceDate && this.$store.state.operInfo.setting.showNoProduceDate.toLowerCase() === "true" ? true : false
    },
    canChangePrice() {
      var changeContractPrice = window.getRightValue('delicacy.changeContractPrice.value')
      return !changeContractPrice || changeContractPrice == 'true'
    },
    priceLabel() {
      if(this.isContractSeller){
        return "承包价"
      }else{
        return "批发价"
      }
    },
    isShowNegativeStock() {
      return this.$store.state.operInfo.setting && this.$store.state.operInfo.setting.showNegativeStock && this.$store.state.operInfo.setting.showNegativeStock == "True" ? true : false
    },
    attrShowFlag() {
      return this.$store.state.attrShowFlag
    },
    shoppingCarFinishFlag() {
      return this.$store.state.shoppingCarFinish
    },
    noItemIdSheetRows() {
      return this.$store.state.noItemIdSheetRows
    },
    distinctFlagDom() {
      return this.itemRow.distinctStockFlag && !this.attrShowFlag && !this.shoppingCarFinishFlag
    },
    showNumPadFlag() {
      return Boolean(this.showNumPad)
    },
    showBtnAttrName() {
      let result = ""
      if (this.itemRow.mum_attributes) {
        result = this.itemRow.mum_attributes.map(obj => { return obj.attrName }).join("/");
      }
      return result
    },
    fromBranchAllowNegativeStock() {
      var allowNegativeStock = window.getRightValue('delicacy.allowNegativeStock.value')
      if (allowNegativeStock == 'false') return false
      if (this.$store.state.branches) {
        let branch = this.$store.state.branches.find(item => item.branch_id == this.sheet.from_branch_id)
        if (branch) return (branch.allow_negative_stock || '').toLowerCase() != 'false'
      }
      return true
    },
    appSheetQtyMerge() {
      var s = getSettingValue('appSheetQtyMerge')
      return s.toLowerCase() !== "false"
    },
    isContractSeller() {

      var v = window.getRightValue('delicacy.isContractSeller.value')
      if (v == "true") return true
      return false
    },
    unitStockQty(){
      let from_branch_position = this.itemRow.from_branch_position?this.itemRow.from_branch_position:"0"
      let to_branch_position = this.itemRow.to_branch_position?this.itemRow.to_branch_position:"0"
      let produce_date = this.itemRow.produce_date === ""? "无产期" : this.itemRow.produce_date
      let batch_no = this.itemRow.batch_no?this.itemRow.batch_no:""
      let fromStockQty = this.fromBatchStock.filter(bs=>(bs.branch_position==from_branch_position&&bs.produce_date==produce_date&&bs.batch_no==batch_no))
      let toStockQty = this.toBatchStock.filter(bs=>(bs.branch_position==to_branch_position&&bs.produce_date==produce_date&&bs.batch_no==batch_no))
      this.itemRow.from_stock_qty = fromStockQty.length?fromStockQty[0].usable_stock_qty:0
      this.itemRow.to_stock_qty = toStockQty.length?toStockQty[0].usable_stock_qty:0
      this.itemRow.fromStock = fromStockQty.length?fromStockQty[0].usable_stock_qty:0
      this.itemRow.toStock = toStockQty.length?toStockQty[0].usable_stock_qty:0
      this.from_usable_stock_qty_unit = this.getUnitQtyFromSmallUnitQty(this.itemRow,true)
      this.to_usable_stock_qty_unit = this.getUnitQtyFromSmallUnitQty(this.itemRow,false)
      this.itemRow.fromStock = fromStockQty.length?fromStockQty[0].stock_qty:0
      this.itemRow.toStock = toStockQty.length?toStockQty[0].stock_qty:0
      this.itemRow.from_stock_qty = fromStockQty.length?fromStockQty[0].stock_qty:0
      this.itemRow.to_stock_qty = toStockQty.length?toStockQty[0].stock_qty:0
      this.from_stock_qty_unit = this.getUnitQtyFromSmallUnitQty(this.itemRow,true)
      this.to_stock_qty_unit = this.getUnitQtyFromSmallUnitQty(this.itemRow,false)
      return {from_usable_stock_qty_unit:this.from_usable_stock_qty_unit,to_usable_stock_qty_unit:this.to_usable_stock_qty_unit,from_stock_qty_unit:this.from_stock_qty_unit ,to_stock_qty_unit:this.to_stock_qty_unit }
    },
  },
  mounted() {
    this.initAudio()
    this.loadBranchSetting()
  },
  methods: {
    initAudio() {
      if (window.isiOS && window.Media) {
        var that = this
        that.audio_num = []
        var audio_size = 3;
        for (let i = 0; i < audio_size; ++i) {
          that.audio_num.push(new Media(`num${i}.wav`))
        }
        that.audio_num.audio_size = audio_size
        that.audio_num.audio_index = 0
        that.audio_num.play = function () {
          console.log('i am played:-)' + this.audio_index)
          this[this.audio_index].play()
          this.audio_index = (this.audio_index + 1) % this.audio_size
        }
        that.audio_del = new Media(`del.mp3`)
        that.audio_comfirm = new Media(`OK.mp3`)
      } else {
        this.audio_num = new Howl({
          src: ['num-3.wav'],
          preload: true
        })
        this.audio_del = new Howl({
          src: ['del.mp3'],
          preload: true
        })
        this.audio_comfirm = new Howl({
          src: ['OK.mp3'],
          preload: true
        })
      }
    },
    loadBranchSetting() {
      this.fromBranchPositionSource = this.$store.state.curFromBranchPositionList ?? []
      this.toBranchPositionSource = this.$store.state.curToBranchPositionList ?? []
      this.fromBranchList = this.$store.state.fromBranchList ?? []
      this.toBranchList = this.$store.state.toBranchList ?? []
      this.showFromBranchPosition = this.fromBranchPositionSource.length ? true : false
      this.showToBranchPosition = this.toBranchPositionSource.length ? true : false
    },
    loadData(item) {

      console.log(item)
      this.curObject = {}
      this.curProp = ''
      this.curInput = null
      item.produce_date = item.produce_date ? item.produce_date.slice(0, 10) : "";
      item.batch_no = item.batch_no ? item.batch_no : "";
      item.batch_id = item.produce_date + item.batch_no;
      if (item.bunit) item.bremark ? '' : item.bremark = ""
      if (item.munit) item.mremark ? '' : item.mremark = ""
      if (item.sunit) item.sremark ? '' : item.sremark = ""
      item?.bunitSum == undefined ? item.bunitSum = '' : ''
      item?.munitSum == undefined ? item.munitSum = '' : ''
      item?.sunitSum == undefined ? item.sunitSum = '' : ''
      item.produce_date = item.produce_date ? item.produce_date : ""
      item.batch_no = item.batch_no ? item.batch_no : ""
      item.batch_id = item.batch_id ? item.batch_id : "0"
      item = Object.assign({}, item, {
        from_branch_position: item.from_branch_position ? item.from_branch_position : "0",
        to_branch_position: item.to_branch_position ? item.to_branch_position : "0",
        from_branch_position_name: item.from_branch_position_name ? item.from_branch_position_name : "",
        to_branch_position_name: item.to_branch_position_name ? item.to_branch_position_name : ""
      })
      item?.sunitSum == undefined ? item.sunitSum = '' : ''
      item.remarkAll = ""
      this.itemRow = item
      if (this.itemRow.batch_level?.toString() == "0") {
        this.itemRow.batch_level = '';
      }
      if (!this.itemRow.bunitSum && !this.itemRow.munitSum && !this.itemRow.sunitSum) {
        this.setDefaultPosition()
      }
      this.itemRowCopy = JSON.parse(JSON.stringify((this.itemRow)))
      this.itemRow.bunitSum ? this.handleStockChange('bunitSum') : ''
      this.itemRow.munitSum ? this.handleStockChange('munitSum') : ''
      this.itemRow.sunitSum ? this.handleStockChange('sunitSum') : ''
      let that = this

      setTimeout(() => {
        if (that.itemRow.distinctStockFlag && !that.attrShowFlag) {
        }
        else {
          var defaultUnit = getSettingValue('moveSheetDefaultUnit')

          var $inputQtyB = $('#inputQuantityBig')
          var $inputQtyM = $('#inputQuantityMid')
          var $inputQtyS = $('#inputQuantitySmall')
          if ($inputQtyB.length == 0 || defaultUnit == 's') {
            that.handleInputClick({ target: $inputQtyS[0] },'', "sunitSum")
          }
          else if ($inputQtyM.length == 1 && defaultUnit == 'm') {
            that.handleInputClick({ target: $inputQtyM[0] },'', "munitSum")
          } else {
            that.handleInputClick({ target: $inputQtyB[0] },'', "bunitSum")
          }
          that.$forceUpdate()
        }

      }, 1);
      this.$forceUpdate()
    },
    formatDate() {
      if (this.curObject !== 'produce_date') {
        let dt = this.itemRow["produce_date"]
        if (this.itemRow["produce_date"] && this.itemRow["produce_date"].length == 6) {
          if (this.itemRow.batch_level == "2" && !this.itemRow.batch_no) {
            this.itemRow.batch_no = dt
          }
          dt = '20' + dt.substr(0, 2) + '-' + dt.substr(2, 2) + '-' + dt.substr(4, 2)
        }
        this.itemRow["produce_date"] = dt
      }
    },
    handleInputClick(event, curFactor, key) {
      var value = event?.target?.value == undefined ? '' : event.target.value
      if (this.itemRow[key] == undefined) {
        this.itemRow[key] = ''
      }
      this.curObject = key
      this.curFactor = curFactor
      this.curInput = event.target
      event.target.selectionStart = 0
      event.target.selectionEnd = value.length
      this.formatDate()
      if (value || key.includes("remark") || key == "batch_no") {
        this.$refs.cursor.style.display = "none"
      } else {
        this.$refs.cursor.style.display = "block"
      }
      
      this.curInput.before(this.$refs.cursor)
      this.$refs.cursor.style.left = isiOS ? '10px' : '5px'
      this.$forceUpdate()
    },
    handeleLongpress() {
      this.itemRow.unitPrice[this.selectUnitNo].showPopoverFlag = true
      console.log(this.itemRow.unitPrice[this.selectUnitNo].actions)
      this.$forceUpdate()
    },
    touchstartUnit(unit, unit_no) {
      this.selectUnitNo = unit_no
      console.log(unit, unit_no);
    },
    updateUnitPrice(sprice, unitType) {
      if (this.itemRow.bfactor && this.itemRow.bfactor != '' && unitType != 'b') this.itemRow.b_wholesale_price = checkInputFormatterPoint4(Number(sprice) * Number(this.itemRow.bfactor))
      if (this.itemRow.mfactor && this.itemRow.mfactor != '' && unitType != 'm') this.itemRow.m_wholesale_price = checkInputFormatterPoint4(Number(sprice) * Number(this.itemRow.mfactor))
      if(unitType != 's') this.itemRow.s_wholesale_price = sprice
      this.$forceUpdate()
    },
    // 点击价格方案更新价格
    onSelectPopover(action, factor) {
      this.itemRow.unitPrice[this.selectUnitNo].showPopoverFlag = false
      const sPrice = Number(Number(action.price) / Number(factor))
      this.updateUnitPrice(sPrice)
    },

    onInput(obj, e) {

      if (obj === "bremark") {
        this.itemRow.bremark = e.target.value
      } else if (obj === "mremark") {
        this.itemRow.mremark = e.target.value
      } else if (obj === "sremark") {
        this.itemRow.sremark = e.target.value
      }
      this.handleInputClick(e,'', obj)
    },
    handleRemarkFocus() {
      this.showNumPad = false
    },
    handleRemarkBlur() {
      this.showNumPad = true
    },
    async btnOK_clicked(e) {
      let err = ""
      this.curObject = ''
      this.formatDate()
      var reg = /^(\d{4})-(\d{2})-(\d{2})$/;
      let produceDate = ''
      if (this.itemRow.produce_date !== '无产期') {
        produceDate = this.itemRow.produce_date ? this.itemRow.produce_date : ""
      }

      if (!produceDate.match(reg) && produceDate !== "") {
        Toast.fail('日期格式错误')
        return
      }
      err = this.checkUnitInputValidity()
      if (err !== "") {
        Toast(err)
        return
      }
      if (this.itemRow.produce_date === "" && this.itemRow.batch_level !== "") {
        Toast.fail("请输入生产日期")
        return
      }
      if (this.itemRow.produce_date === "" && this.itemRow.batch_level != "") {
        Toast.fail("请输入生产日期")
        return
      } else if (this.itemRow.batch_level && this.itemRow.produce_date && this.itemRow.produce_date !== "无产期") {
        if (!/^\d{4}-\d{2}-\d{2}$/.test(this.itemRow.produce_date)) {
          Toast.fail("请输入格式正确的商品产期");
          return
        } else if (!this.isValidDate(this.itemRow.produce_date)) {
          Toast.fail("请输入有效的商品产期");
          return
        }

      }
      if (this.itemRow.produce_date == "" && this.itemRow.batch_no !== "") {
        Toast.fail("请输入生产日期")
        return
      }
      var appDbNoStockSave = window.getSettingValue('appDbNoStockSave').toLowerCase() == 'true'
      if (!this.fromBranchAllowNegativeStock && !appDbNoStockSave) {
        let curBatch = null
        for (let i = 0; i < this.fromBatchStock.length; i++) {
          let batch = this.fromBatchStock[i]
          let fromBranchPosition = this.itemRow.from_branch_position.toString()
          let produce_date = this.itemRow.produce_date === "" ? "无产期" : this.itemRow.produce_date
          // console.log(batch.produce_date,this.itemRow.produce_date,this.itemRow.batch_no, batch.batch_no,fromBranchPosition==batch.branch_position)
          if (this.produceDateEqual(produce_date, batch.produce_date) && this.itemRow.batch_no == batch.batch_no && fromBranchPosition == batch.branch_position) {
            curBatch = batch
            console.log(curBatch)
            break
          }
        }
        console.log(curBatch)
        let NegativeStockAccordance = "real";
        let typeText = "实际"
        this.fromBranchList.some(b => {
          console.log(b.branch_id, this.sheet.from_branch_id)
          if (b.branch_id.toString() == this.sheet.from_branch_id.toString()) {
            NegativeStockAccordance = b.negative_stock_accordance
            return
          }
        })
        let smallUnitStockQty = Number(this.itemRow.sunitSum) + Number(this.itemRow.munitSum) * Number(this.itemRow.mfactor) + Number(this.itemRow.bunitSum) * Number(this.itemRow.bfactor)
        //batchStock为空或者没有找到匹配的生产批次
        if (!curBatch) {
          if (smallUnitStockQty > 0) {
            Toast.fail("该商品库存不足")
            return
          }
        } else {
          let curStock = curBatch["stock_qty"]
          if (NegativeStockAccordance == "usable") {
            curStock = curBatch["usable_stock_qty"]
            typeText = "可用"
          }
          if (Number(curStock) - smallUnitStockQty < -0.001) {
            Toast.fail("该商品" + typeText + "库存不足")
            return
          }
        }
      }



      if (this.attrShowFlag) {
        // 将没有item_id的商品收集起来，点击返回的时候调用
        if (this.itemRow.item_id.startsWith("nanoid")) {
          this.handleNoItemIdToStore()
        } else {
          this.handleInputItemToSheetRows()
        }
        setTimeout(() => {
          this.$emit('onAddAttrRowOK')
        }, 1);
      } else {
        this.handleInputItemToSheetRows()

        window.g_moveSheet.saveCurSheetToCache()
        setTimeout(() => {
          this.$emit('onAddRow_OK')
        }, 1);
      }
    },
    btnDel_click() {
      if (!this._confirming) {
        this._confirming = true;
        Dialog.confirm({
          title: '删除',
          message: '确认删除改商品吗?',
          width: '320px'
        }).then(() => {
          // on confirm
          this.$emit("delIndexItem", this.editingItemRowIndex);
          this._confirming = false;
        }).catch(() => {
          this._confirming = false;
        });
      }
    },
    closePage() {
      let that = this
      if (this.attrShowFlag) {
        if (this.noItemIdSheetRows.length !== 0) {
          this.handleNoItemIdRowsToSheetRows()
        }
        setTimeout(() => {
          if (this.itemRow.singleChoice && this.attrShowFlag) {
            this.$emit("closeAttrSingleChoicePage")
          } else {
            this.$emit("closeAttrPage")
          }
        }, 1);
      } else {
        // 判断是否还存在为完成的商品
        //let finishFlagAll = ();
        if (this.itemRow.singleChoice && !this.attrShowFlag) {
          this.$emit("closeSingleChoicePage")
        } else {
          if (this.shoppingCarFinishFlag) {
            this.$emit("closePage")
          } else {
            if (!this._confirming) {
              this._confirming = true;
              Dialog.confirm({
                message: '存在未输入数量商品，是否继续退出？',
                width: '320px'
              }).then(() => {
                this.$emit("closePage");
                this._confirming = false;
              }).catch(() => {
                this._confirming = false;
              });
            }
          }
        }

      }
    },
    onClickNum(value, e) {
      if (this.curObject.includes("remark") || this.curObject == "batch_no") return
      if (this.$refs?.cursor !== undefined) {
        this.$refs.cursor.style.display = "block"
        $(e.currentTarget).css('background-color', '#ccc')

        if (e.preventDefault) {
          e.preventDefault()
          e.stopPropagation()
        }
        if (this.audio_num.play) {
          this.audio_num.play()
        }
        if (this.curInput && this.curInput.value && this.curInput.selectionStart == 0 && this.curInput.selectionEnd == this.curInput.value.length) {
          this.itemRow[this.curObject] = ''
        }
        this.itemRow[this.curObject] = this.itemRow[this.curObject] + value
        if (this.curObject === "bunitSum") {
          this.itemRow.bunitTotle = (Number(this.itemRow[this.curObject]) * Number(this.itemRow.bpprice)).toFixed(2)
        } else if (this.curObject === "munitSum") {
          this.itemRow.munitTotle = (Number(this.itemRow[this.curObject]) * Number(this.itemRow.mpprice)).toFixed(2)
        } else if (this.curObject === "sunitSum") {
          this.itemRow.sunitTotle = (Number(this.itemRow[this.curObject]) * Number(this.itemRow.spprice)).toFixed(2)
        }
        if(this.curObject.includes('wholesale_price')) {
          this.updateUnitPrice(checkInputFormatterPoint4(Number(checkInputFormatterPoint4(this.itemRow[this.curObject]) /Number( this.curFactor))),this.curObject[0])
      }
        this.handleStockChange(this.curObject)
        var f = $(this.curInput).css('font-size')
        var left = this.getStrWidth(f, this.itemRow[this.curObject]) * 1
        this.$refs.cursor.style.left = left + (isiOS ? 10 : 5) + 'px'
        this.invokeComputedVar = ""
        this.$forceUpdate()
      }
    },
    onDelNum(e) {
      $(e.currentTarget).css('background-color', '#e0e0e0')
      this.handleStockChange(this.curObject, -1)
      this.itemRow[this.curObject] = ''
      this.$refs.cursor.style.left = isiOS ? '10px' : '5px'
      //this.updateStock()
      this.invokeComputedVar = ""
      this.$forceUpdate()
    },
    onNumTouchEnd(e) {
      $(e.currentTarget).css('background-color', '#ddd')
    },
    onbtnOKClickedEnd(e) {
      console.log("确认数量")
      $(e.currentTarget).css('background-color', '#3a3a3a')
    },
    getStrWidth(fontSize, str) {
      const dom = document.createElement('span')
      dom.style.display = 'inline-block'
      dom.textContent = str
      $(dom).css('font-size', fontSize);
      document.body.appendChild(dom)
      const width = dom.clientWidth
      document.body.removeChild(dom)
      return width
    },
    async handleInputItemToSheetRows(item = {}) {
      let currentSheet = this.$store.state.currentSheet
      let tempSheetRows = []
      if (JSON.stringify(item) == '{}') {
        item = this.itemRow
      }
      var that=this
      rowUnitsProps2Array(item)
      console.log('tempSheetRows', tempSheetRows)
      for (let i = 0; i < tempSheetRows.length; i++) {
        let tempItem = tempSheetRows[i]
        let pushFlag = true
        for (let j = 0; j < currentSheet.sheetRows.length; j++) {
          let currentSheetRow = currentSheet.sheetRows[j]
          if (currentSheetRow.nanoid && currentSheetRow.nanoid.length > 2) {
            if (currentSheetRow.nanoid === tempItem.nanoid) {
              currentSheetRow.son_item_id = tempItem.son_item_id
              currentSheetRow.son_item_name = tempItem.son_item_name
              currentSheetRow.item_id = tempItem.item_id
              currentSheetRow.item_name = tempItem.item_name
              currentSheetRow.quantity = tempItem.quantity
              currentSheetRow.produce_date = tempItem.produce_date
              currentSheetRow.batch_no = tempItem.batc_no
              currentSheetRow.batch_id = tempItem.batch_id
              currentSheetRow.batch_level = tempItem.batch_level
              pushFlag = false
            }
          } else {
            if (!this.appSheetQtyMerge) {
              // 检查是否重复选品，需要提示
              const existingItem = currentSheet.sheetRows.find(sheetRow =>
                sheetRow.item_id === tempItem.item_id &&
                sheetRow.produce_date === tempItem.produce_date &&
                sheetRow.batch_no === tempItem.batch_no &&
                sheetRow.from_branch_position === tempItem.from_branch_position &&
                sheetRow.unit_type === tempItem.unit_type &&
                Number(sheetRow.contract_price) == Number(tempItem.contract_price)
              )

              if (existingItem) {
                // 显示重复选品提示
                if (!this._confirming) {
                  this._confirming = true;
                  const confirmed = await Dialog.confirm({
                    title: '重复选品提示',
                    message: `商品"${tempItem.item_name}"已存在，是否继续添加？`,
                    width: "320px"
                  }).catch(() => {
                    // 用户取消，不添加商品
                    this._confirming = false;
                    return false
                  });

                  if (!confirmed) {
                    this._confirming = false;
                    return // 用户取消，退出函数
                  }
                  this._confirming = false;
                }
              }

              pushFlag = true
            } else {
              if (tempItem.item_id === currentSheetRow.item_id && tempItem.produce_date === currentSheetRow.produce_date && tempItem.batch_no === currentSheetRow.batch_no && tempItem.from_branch_position === currentSheetRow.from_branch_position && tempItem.unit_type === currentSheetRow.unit_type && Number(currentSheetRow.contract_price) == Number(tempItem.contract_price)) {
                // tempItem.remark == currentSheetRow.remark
                if (this.itemRow.isSelectFlag) {
                  currentSheetRow.quantity = Number(tempItem.quantity)
                  currentSheetRow.remark = tempItem.remark
                  currentSheetRow.contract_price == tempItem.contract_price
                  pushFlag = false
                } else {
                  if (tempItem.remark === currentSheetRow.remark) {
                    currentSheetRow.quantity = Number(currentSheetRow.quantity) + Number(tempItem.quantity)
                    pushFlag = false
                  }
                }
                break
              }
            }

          }
        }
        if (pushFlag) {
          currentSheet.sheetRows.push(tempItem)
        }
      }

      console.log("currentSheetcurrentSheet",)
      this.$store.commit("currentSheet", currentSheet)
      if (item.singleChoice && !this.attrShowFlag) {
        this.$emit("closeSingleChoicePage")
      }
      
      function rowUnitsProps2Array(propRow) {
        console.log(propRow)
        //let isContractSeller = window.getRightValue('delicacy.isContractSeller.value') == "true" ? true : false;
        var arrRow = {};
        arrRow.item_id = propRow.item_id;
        arrRow.item_name = propRow.item_name;
        arrRow.b_unit_no = propRow.bunit;
        arrRow.b_unit_factor = propRow.bfactor;
        arrRow.b_wholesale_price = propRow.b_wholesale_price;
        
        arrRow.b_weight = propRow.b_weight;
        arrRow.m_unit_no = propRow.munit;
        arrRow.m_unit_factor = propRow.mfactor;
        
        arrRow.m_wholesale_price = propRow.m_wholesale_price;
        
        arrRow.m_weight = propRow.m_weight;
        arrRow.s_unit_no = propRow.sunit;
        arrRow.s_wholesale_price = propRow.s_wholesale_price;
        
        if(that.isContractSeller) {
          arrRow.b_contract_price = propRow.b_wholesale_price;
          arrRow.m_contract_price = propRow.m_wholesale_price;
          arrRow.s_contract_price = propRow.s_wholesale_price;
        }

        arrRow.s_weight = propRow.s_weight;
        arrRow.m_barcode = propRow.m_barcode;
        arrRow.s_barcode = propRow.s_barcode;
        arrRow.b_barcode = propRow.b_barcode;
        arrRow.remarkAll = propRow.remarkAll;
        // arrRow.produce_date = propRow.produce_date
        // arrRow.batch_no = propRow.batch_no
        // arrRow.batch_id = propRow.batch_id
        arrRow.item_total = 0;
        arrRow.from_stock_qty = propRow.from_stock_qty;
        arrRow.to_stock_qty = propRow.to_stock_qty;
        arrRow.bfactor = propRow.bfactor;
        arrRow.bunit = propRow.bunit;
        arrRow.mfactor = propRow.mfactor;
        arrRow.munit = propRow.munit;
        arrRow.sfactor = propRow.sfactor;
        arrRow.sunit = propRow.sunit;
        arrRow.rowUnits = [];
        if (propRow.bunit && propRow.bunitSum) {
          tempSheetRows.push({
            item_id: propRow.item_id,
            classId: propRow.classId,
            item_name: propRow.item_name,
            unit_no: propRow.bunit,
            wholesale_price: propRow.b_wholesale_price,
            contract_price: that.isContractSeller ? propRow.b_wholesale_price : null,
            unit_weight: propRow.b_weight,
            b_weight: propRow.b_weight,
            m_weight: propRow.m_weight,
            s_weight: propRow.s_weight,
            quantity: propRow.bunitSum,
            unit_factor: propRow.bfactor,
            b_unit_factor: propRow.bfactor,
            m_unit_factor: propRow.mfactor,
            s_unit_factor: propRow.sfactor,
            b_unit_no: propRow.bunit,
            m_unit_no: propRow.munit,
            s_unit_no: propRow.sunit,
            unit_type: "b",
            remark: propRow.bremark,
            son_mum_item: propRow?.son_mum_item !== undefined ? propRow.son_mum_item : propRow.item_id,
            m_barcode: propRow.m_barcode,
            s_barcode: propRow.s_barcode,
            b_barcode: propRow.b_barcode,
            barcode: propRow.b_barcode,
            item_images: propRow.item_images,
            showImages: propRow.showImages,
            produce_date: propRow.produce_date,
            batch_no: propRow.batch_no,
            batch_id: propRow.batch_id,
            batch_level: propRow.batch_level,
            from_stock_qty: propRow.from_stock_qty,
            to_stock_qty: propRow.to_stock_qty,
            showImages: propRow.showImages,
            bfactor: propRow.bfactor,
            bunit: propRow.bunit,
            mfactor: propRow.mfactor,
            munit: propRow.munit,
            sfactor: propRow.sfactor,
            sunit: propRow.sunit,
            from_branch_position: propRow.from_branch_position,
            to_branch_position: propRow.to_branch_position,
            from_branch_position_name: propRow.from_branch_position_name,
            to_branch_position_name: propRow.to_branch_position_name
          })
        }
        if (propRow.munit && propRow.munitSum) {
          tempSheetRows.push({
            item_id: propRow.item_id,
            item_name: propRow.item_name,
            classId: propRow.classId,
            unit_no: propRow.munit,
            unit_type: "m",
            unit_factor: propRow.mfactor,
            b_unit_factor: propRow.bfactor,
            m_unit_factor: propRow.mfactor,
            quantity: propRow.munitSum,
            wholesale_price: propRow.m_wholesale_price,
            contract_price: that.isContractSeller ? propRow.m_wholesale_price :null,
            unit_weight: propRow.m_weight,
            b_weight: propRow.b_weight,
            m_weight: propRow.m_weight,
            s_weight: propRow.s_weight,
            remark: propRow.mremark,
            son_mum_item: propRow?.son_mum_item !== undefined ? propRow.son_mum_item : propRow.item_id,
            m_barcode: propRow.m_barcode,
            s_barcode: propRow.s_barcode,
            b_barcode: propRow.b_barcode,
            barcode: propRow.m_barcode,
            item_images: propRow.item_images,
            showImages: propRow.showImages,
            b_unit_no: propRow.bunit,
            m_unit_no: propRow.munit,
            s_unit_no: propRow.sunit,
            nanoid: "m_" + (propRow.nanoid ? propRow.nanoid : ''),
            produce_date: propRow.produce_date,
            batch_no: propRow.batch_no,
            batch_id: propRow.batch_id,
            batch_level: propRow.batch_level,
            from_stock_qty: propRow.from_stock_qty,
            to_stock_qty: propRow.to_stock_qty,
            nanoid: "m_" + (propRow.nanoid ? propRow.nanoid : ''),
            bfactor: propRow.bfactor,
            bunit: propRow.bunit,
            mfactor: propRow.mfactor,
            munit: propRow.munit,
            sfactor: propRow.sfactor,
            sunit: propRow.sunit,
            from_branch_position: propRow.from_branch_position,
            to_branch_position: propRow.to_branch_position,
            from_branch_position_name: propRow.from_branch_position_name,
            to_branch_position_name: propRow.to_branch_position_name
          })
        }
        if (propRow.sunit && propRow.sunitSum) {
          tempSheetRows.push({
            item_id: propRow.item_id,
            item_name: propRow.item_name,
            unit_no: propRow.sunit,
            classId: propRow.classId,
            unit_type: "s",
            unit_factor: propRow.sfactor,
            b_unit_factor: propRow.bfactor,
            m_unit_factor: propRow.mfactor,
            quantity: propRow.sunitSum,
            wholesale_price: propRow.s_wholesale_price,
            contract_price: that.isContractSeller ? propRow.s_wholesale_price :null,
            unit_weight: propRow.s_weight,
            b_weight: propRow.b_weight,
            m_weight: propRow.m_weight,
            s_weight: propRow.s_weight,
            remark: propRow.sremark,
            son_mum_item: propRow?.son_mum_item !== undefined ? propRow.son_mum_item : propRow.item_id,
            m_barcode: propRow.m_barcode,
            s_barcode: propRow.s_barcode,
            b_barcode: propRow.b_barcode,
            barcode: propRow.s_barcode,
            item_images: propRow.item_images,
            showImages: propRow.showImages,
            b_unit_no: propRow.bunit,
            m_unit_no: propRow.munit,
            s_unit_no: propRow.sunit,
            nanoid: "s_" + (propRow.nanoid ? propRow.nanoid : ''),
            produce_date: propRow.produce_date,
            batch_no: propRow.batch_no,
            batch_id: propRow.batch_id,
            batch_level: propRow.batch_level,
            from_stock_qty: propRow.from_stock_qty,
            to_stock_qty: propRow.to_stock_qty,
            nanoid: "s_" + (propRow.nanoid ? propRow.nanoid : ''),
            bfactor: propRow.bfactor,
            bunit: propRow.bunit,
            mfactor: propRow.mfactor,
            munit: propRow.munit,
            sfactor: propRow.sfactor,
            sunit: propRow.sunit,
            from_branch_position: propRow.from_branch_position,
            to_branch_position: propRow.to_branch_position,
            from_branch_position_name: propRow.from_branch_position_name,
            to_branch_position_name: propRow.to_branch_position_name
          });
        }

      }
    },
    handleAttr() {
      this.$emit("handleAttr")
    },
    handleNoItemIdToStore() {
      // 进行数据合并
      let sheetRow = this.itemRow
      var row = this.noItemIdSheetRows.find((value, index, arr) => {
        return value.item_name === sheetRow.item_name &&
          value.bpprice === sheetRow.bpprice &&
          value.bunit === sheetRow.bunit &&
          value.bremark === sheetRow.bremark &&
          value.mpprice === sheetRow.mpprice &&
          value.munit === sheetRow.munit &&
          value.mremark === sheetRow.mremark &&
          value.spprice === sheetRow.spprice &&
          value.sunit === sheetRow.sunit &&
          value.nanoid === sheetRow.nanoid &&
          value.sremark === sheetRow.sremark
      })
      if (row) {
        row.bunitSum = sheetRow.bunitSum
        row.bunitTotle = sheetRow.bunitTotle
        row.munitSum = sheetRow.munitSum
        row.munitTotle = sheetRow.munitTotle
        row.sunitSum = sheetRow.sunitSum
        row.sunitTotle = sheetRow.sunitTotle
      } else {
        this.noItemIdSheetRows.push(sheetRow)
      }
      this.$store.commit("noItemIdSheetRows", this.noItemIdSheetRows)
      this.handleInputItemToSheetRows()
    },
    async handleNoItemIdRowsToSheetRows() {
      let that = this
      let item_id = this.noItemIdSheetRows[0].son_mum_item
      let mum_attributes = this.noItemIdSheetRows[0].mum_attributes
      let attrRows = []
      this.noItemIdSheetRows.forEach(item => {
        attrRows.push(typeof item.attr_qty == 'string' ? JSON.parse(item.attr_qty) : item.attr_qty)
      })
      let params = {
        item_id: item_id,
        attrRows: attrRows,
        operKey: this.$store.state.operKey,
        attrs: typeof mum_attributes == 'string' ? JSON.parse(mum_attributes) : mum_attributes
      }
      await CreateItemsForAttrRows(params).then(res => {
        if (res.result == "OK") {
          let sonRows = res.sonRows
          sonRows.forEach(son => {
            let row = that.noItemIdSheetRows.find(r => r.son_options_id === son.son_options_id)
            if (row) {
              row.son_item_id = son.son_item_id
              row.son_item_name = son.son_item_name
              row.item_id = row.son_item_id.split(",")[0]
              row.item_name = row.son_item_name
            }
          })
        }
      }).catch(err => {
        Toast(err)
      })
      this.noItemIdSheetRows.forEach(item => {
        delete item.attr_qty
        this.handleInputItemToSheetRows(item)
      })
      this.$store.commit("noItemIdSheetRows", [])
    },

    handleStockChange(unitChange, delFlag = 1) {
      let that = this
      unitChange == 'bunitSum' ? changeUnitSum('fb_qty', 'tb_qty', unitChange) :
        unitChange == 'munitSum' ? changeUnitSum('fm_qty', 'tm_qty', unitChange) :
          changeUnitSum('fs_qty', 'ts_qty', unitChange)

      function changeUnitSum(fQty, tQty, unitSum) {
        if (delFlag == 1) {
          that.itemRow[fQty] = toMoney(Number(that.itemRow[fQty]) - (Number(that.itemRow[unitSum])))
          that.itemRow[tQty] = toMoney(Number(that.itemRow[tQty]) + (Number(that.itemRow[unitSum])))
        } else {
          that.itemRow[fQty] = that.itemRowCopy[fQty]
          that.itemRow[tQty] = that.itemRowCopy[tQty]
        }
      }
    },
    checkUnitInputValidity() {
      let err = ''
      if (this.itemRow.bunitSum && !checkSignedInputValidity(this.itemRow.bunitSum)) {
        err = "【" + this.itemRow.bunit + "】【数量】输入错误,请检查"
        return err
      }
      if (this.itemRow.munitSum && !checkSignedInputValidity(this.itemRow.munitSum)) {
        err = "【" + this.itemRow.munit + "】【数量】输入错误,请检查"
        return err
      }
      if (this.itemRow.sunitSum && !checkSignedInputValidity(this.itemRow.sunitSum)) {
        err = "【" + this.itemRow.sunit + "】【数量】输入错误,请检查"
        return err
      }
      if (this.itemRow.b_contract_price && !checkPositiveInputValidity(this.itemRow.b_contract_price)) {
        err = "【" + this.itemRow.bunit + "】【价格】输入错误,请检查"
        return err
      }
      if (this.itemRow.m_contract_price && !checkPositiveInputValidity(this.itemRow.m_contract_price)) {
        err = "【" + this.itemRow.munit + "】【价格】输入错误,请检查"
        return err
      }
      if (this.itemRow.s_contract_price && !checkPositiveInputValidity(this.itemRow.s_contract_price)) {
        err = "【" + this.itemRow.sunit + "】【价格】输入错误,请检查"
        return err
      }
      if (this.itemRow.b_wholesale_price && !checkPositiveInputValidity(this.itemRow.b_wholesale_price)) {
        err = "【" + this.itemRow.bunit + "】【价格】输入错误,请检查"
        return err
      }
      if (this.itemRow.m_wholesale_price && !checkPositiveInputValidity(this.itemRow.m_wholesale_price)) {
        err = "【" + this.itemRow.munit + "】【价格】输入错误,请检查"
        return err
      }
      if (this.itemRow.s_wholesale_price && !checkPositiveInputValidity(this.itemRow.s_wholesale_price)) {
        err = "【" + this.itemRow.sunit + "】【价格】输入错误,请检查"
        return err
      }
      return err

    },
    setDateAndBatchNo(value) {
      this.produceDateShow = false
      this.itemRow.produce_date = value.produce_date ? value.produce_date : '无产期'
      this.itemRow.batch_no = value.batch_no
      this.itemRow.batch_id = value.batch_id
    },
    // 严格产期长按
    handlerTouchstart() {
      this.loop = setTimeout(() => {
        this.loop = 0
        this.produceDateShow = true
      }, 500) // 定时的时间
      return false
    },
    handlerTouchmove() {
      // 清除定时器
      clearTimeout(this.loop)
      this.loop = 0
    },
    handlerTouchend(event) {
      // 清除定时器
      clearTimeout(this.loop)
      if (this.loop !== 0) {
        // 单击操作
        this.handleInputClick(event,'', 'produce_date')
      }
    },
    handleCloseBranchInfo() {
      if (this.fromBranchPositionShow) this.fromBranchPositionShow = false
      if (this.toBranchPositionShow) this.toBranchPositionShow = false
    },
    setItemRowBranchInfo(branchPositionObj, branchObj) {
      if (this.fromBranchPositionShow) {
        if (this.itemRow.from_branch_position == branchPositionObj.branch_position) return
        this.itemRow.from_branch_position = branchPositionObj.branch_position
        this.itemRow.from_branch_position_name = branchPositionObj.branch_position_name
      }
      if (this.toBranchPositionShow) {
        if (this.itemRow.to_branch_position == branchPositionObj.branch_position) return
        this.itemRow.to_branch_position = branchPositionObj.branch_position
        this.itemRow.to_branch_position_name = branchPositionObj.branch_position_name
      }
      this.handleCloseBranchInfo()
      this.setBatchStockByBranch()
    },
    setDefaultPosition() {
      const setting = this.$store.state.operInfo.setting
      let defaultBranchPositionType = setting && setting.defaultBranchPositionType ? Number(setting.defaultBranchPositionType) : -1
      let bpFlagF = this.fromBranchPositionSource.some(bp => {
        if (bp.type_id == defaultBranchPositionType) {
          this.itemRow.from_branch_position = bp.branch_position
          this.itemRow.from_branch_position_name = bp.branch_position_name
          return true
        }
      })
      if (!bpFlagF) {
        this.itemRow.from_branch_position = "0"
        this.itemRow.from_branch_position_name = ""
      }
      let bpFlagT = this.toBranchPositionSource.some(bp => {
        if (bp.type_id == defaultBranchPositionType) {
          this.itemRow.to_branch_position = bp.branch_position
          this.itemRow.to_branch_position_name = bp.branch_position_name
          return true
        }
      })
      if (!bpFlagT) {
        this.itemRow.to_branch_position = "0"
        this.itemRow.to_branch_position_name = ""
      }
      this.setBatchStockByBranch()
    },
    setBatchStockByBranch() {
      let params = {
        item_id: this.itemRow.item_id,
        from_branch_id: this.itemRow.from_branch_id ? this.itemRow.from_branch_id : this.sheet.from_branch_id,
        to_branch_id: this.itemRow.to_branch_id ? this.itemRow.to_branch_id : this.sheet.to_branch_id,
        from_branch_position: this.itemRow.from_branch_position ? this.itemRow.from_branch_position : 0,
        to_branch_position: this.itemRow.to_branch_position ? this.itemRow.to_branch_position : 0,
        isShowNegativeStock: this.isShowNegativeStock
      }
      GetBatchStockForMove(params).then(res => {
        if (res.result == "OK") {
          this.fromBatchStock = res.data.fromBatchStock
          this.toBatchStock = res.data.toBatchStock
          this.batchStockListForShow = res.data.batchStock
          this.checkNoProduceDate(this.fromBatchStock, this.batchStockListForShow)
          //Mixin.methods.checkNoProduceDate(this.itemUnitsInfo, this.batchStock, this.batchStockListForShow, this.sheet.branch_id, this.sheet.sheetType)

          this.setOldestBatchInfo()
        }
      })
    },
    setOldestBatchInfo() {
      if (!this.itemRow.batch_level) return
      let newBatchStock = this.batchStockListForShow.filter(e => e.batch_id !== "0")
      newBatchStock.sort((pre, next) => {
        return Date.parse(pre.produce_date) - Date.parse(next.produce_date)
      })
      newBatchStock.sort((pre, next) => {
        if (pre.batch_no > next.batch_no) return 1
      })
      if (this.showNoProduceDate && newBatchStock.length === 0) {
        this.itemRow.produce_date = "无产期"  // 自动带出
      } else {
        this.itemRow.produce_date = newBatchStock.length ? newBatchStock[0].produce_date : ''
      }
      this.itemRow.batch_no = newBatchStock.length ? newBatchStock[0].batch_no : ''
      this.itemRow.batch_id = newBatchStock.length ? newBatchStock[0].batch_id : ""
    },
    clearFromBranchPosition() {
      this.itemRow.from_branch_position = 0
      this.itemRow.from_branch_position_name = ""
      this.setBatchStockByBranch()
    },
    clearToBranchPosition() {

      this.itemRow.to_branch_position = 0
      this.itemRow.to_branch_position_name = ""
      this.setBatchStockByBranch()
    }
  }
}

</script>

<style lang="less" scoped>
//该页面屏蔽大字体模式 px改为PX
[v-cloak] {
  display: none;
}

@flex_a_j: {
  display: flex;
  align-items: center;
  justify-content: center;
}

;

@flex_a_bw: {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

;

@flex_a_jend: {
  display: flex;
  align-items: center;
  justify-content: flex-end;
}

;

.add_goods_box {
  padding: 10px;

  li {
    height: auto;
    overflow: hidden;
    margin: 0 auto 10PX auto;
    background: #ffffff;
    border-radius: 4PX;
    background: #ffffff;

    h4 {
      height: auto;
      min-height: 35PX;
      padding: 0 10PX;
      border-bottom: 1PX solid #f2f2f2;
      @flex_a_j();

      span {
        font-size: 16PX;
        font-weight: normal;
        color: #333333;
      }
    }

    .add_boxs {
      height: auto;
      overflow: hidden;
      padding: 10px;

      .date_input {
        padding-left: 0PX;
        padding-right: 0PX;
      }

      .date_input_son {
        padding: 5PX 0;
      }
    }

    .add_boxs_footer {
      height: auto;
      overflow: hidden;
      @flex_a_bw();
      padding: 10px;

      .add_boxs_footer_l {
        h5 {
          font-size: 12PX;
          text-align: left;
          font-weight: normal;
        }

        h5:last-child {
          margin-top: 5PX;
        }
      }

      .add_boxs_footer_r {
        button:last-child {
          margin-left: 10PX;
        }
      }
    }
  }
}

.preservationItem {
  width: 95%;
  position: fixed;
  // height: 95;
  bottom: 0;
  float: right;
  padding: 5px 10px;

  button {
    //border-radius: 50;
    margin-bottom: 5PX;
  }
}

.add_goods_box_ms {
  width: 100%;
  height: 100%;
  //overflow-y: auto;
  //overflow-x: hidden;
}

/deep/.van-action-sheet__item {
  height: 60PX;
}

/deep/.van-action-sheet__cancel {
  margin-bottom: 20PX;
}

@flex_a_j: {
  display: flex;
  align-items: center;
  justify-content: center;
}

;

@flex_a_flex: {
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
}

;

@flex_a_bw: {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

;

@flex_cfs: {
  display: flex;
  align-items: center;
  justify-content: flex-start;
}

;

@span_input: {
  span {
    height: inherit;
    @flex_a_j();
  }

  input {
    width: 100%;
    height: 100%;
    outline: none;
    border: none;
    border-bottom: 1PX solid #dddddd;
    vertical-align: top;
    // text-align: center;
  }
}

;

.add_goods_box {
  display: flex;
  flex-direction: column;
  justify-content: flex-end;

  .item_name_content {
    border-bottom: 1PX solid #eee;
    height: 29PX;
    display: flex;
    align-items: center;

    .item_name {
      flex: 9;
      height: 100%;
      overflow-y: auto;
      font-size: 15PX;
      white-space: nowrap;
      display: flex;
      align-items: center;
      justify-content: center;
    }
  }

  .item_name_content_onlyshow {
    display: flex;
    width: 90%;
    text-align: center;
    justify-content: center;
    padding: 0 10PX;
  }

  .item_name_option {
    display: flex;
    justify-content: space-between;
    height: 40PX;
    align-items: center;

    .bReturn {
      flex: 1;
      display: flex;
      align-items: center;
      justify-content: center;
      height: 30px;

      .breturn_X {
        display: flex;
        align-items: center;
        justify-content: center;
        background: #fff;
        border: 1PX solid #d8d8d8;
        border-radius: 10PX;
        width: 65%;
        height: 26PX;
      }

      .breturn_T {
        display: flex;
        align-items: center;
        justify-content: center;
        background: #f66;
        color: #fff;
        border-radius: 10PX;
        width: 65%;
        height: 26PX;
      }
    }

    .bReturnHidden {
      visibility: hidden;
    }

    .more_option {
      flex: 3;
      display: flex;
      align-items: center;

      .more_option_1,
      .more_option_2,
      .more_option_3 {
        flex: 1;
        display: flex;
        justify-content: center;
        align-items: center;
      }

      .option_attr {
        display: flex;
        color: #000;
        align-items: center;
        justify-content: center;
        background-color: #fde3e4;

        border-radius: 10PX;
        width: 65%;
        height: 26PX;
      }

      .close_icon_font {
        display: flex;
        align-items: center;
        justify-content: center;
        background: #eee;
        border: 1PX solid #eee;
        border-radius: 10PX;
        width: 65%;
        height: 26PX;
      }
    }
  }

  .close_icon_font_wrapper {
    position: absolute;
    left: 5PX;
    top: 10PX;
    z-index: 999999;

    .close_icon_font {
      box-sizing: border-box;
      color: #fff;
      font-size: 15PX;
      display: flex;
      align-items: center;
      justify-content: flex-start;
      background: rgba(0, 0, 0, 0.7);
      font-weight: bolder;
      //border: 1px solid #aaa;
      border-radius: 10px;
      width: 40px;
      height: 26px;
      padding-left: 5px;

      .font_wrapper {
        font-size: 14PX;
      }
    }
  }

  .add_box {
    // padding: 0 10px;

    .add_box_title {
      height: 20PX;
      font-size: 15PX;
      @flex_cfs();
      margin: 10px 10px 25px 0;

      .van-col {
        height: inherit;
        vertical-align: top;
        @span_input();

        input {
          text-align: left;
          border-radius: 0;
        }
      }
    }

    .add_box_body {
      height: auto;
      font-size: 15PX;
      box-sizing: border-box;
      padding: 0 5px;

      .van-row {
        height: 30PX;
        margin: 5PX 0;
        @span_input();
      }

      .inputBranchPosition {
        padding: 0px;
        border-bottom: 1px solid #ddd;
      }

      margin-bottom: 10px;
    }

    .distinct_stock_wrapper {
      display: flex;
      height: 100%;
      flex-direction: column;
      justify-content: center;

      .distinct_msg_wrapper {
        flex: 1;
        display: flex;
        align-items: center;
        justify-content: center;
        color: #aaa;
      }

      .distinct_btn_wrapper {
        flex: 2;
        display: flex;
        align-items: flex-end;
        justify-content: center;

        .distinct_btn {
          min-width: 50%;
          height: 65%;
          padding: 0 15PX;
          display: flex;
          align-items: center;
          justify-content: center;
          // border: 1 solid #eee;
          background-color: #fde3e4;
          font-size: 20PX;
          //font-weight: bolder;
          border-radius: 10PX;
        }
      }
    }
  }

  .add_box_aside {
    width: 100%;
    height: 40PX;
    box-sizing: border-box;
    font-size: 12PX !important;
    font-weight: normal;
    @flex_a_bw();
    padding: 0 5PX;

    .aside_left {
      @flex_cfs();
      color: gray;
    }

    div {
      color: gray;
    }
  }

  .item_history_btn {
    display: flex;
    justify-content: flex-end;
    padding-right: 5PX;
  }
}

.show_order_msg {
  text-align: center;
  border-radius: 5PX;
  padding: 0 2PX;
  line-height: 30PX;
  font-size: 16PX;
  color: rgba(245, 108, 108, 0.8);
}

.numPad {
  width: 100%;
  height: 225PX;
  // position: fixed;
  // bottom: 0;
  font-family: numfont;
  background-color: #ddd;
  display: flex;

  .numPad_table {
    width: 100%;
    height: 100%;
    border-bottom: 1px solid #ccc;

    td {
      width: 25%;
      height: 25%;
      border-right: 1PX solid #ccc;
      border-top: 1PX solid #ccc;
      padding: 0;

      .numbtn1 {
        width: 100%;
        height: 100%;
        display: flex;
        font-size: 22PX;
        justify-content: center;
        align-items: center;
      }

      .numbtn2 {
        width: 100%;
        height: 100%;
      }

      .save_btn {
        background-color: #555;
        // font-family: font;
        font-weight: 400;
        color: rgb(245, 245, 245);
      }
    }
  }

  .shopping_car_finish {
    background-color: #19952c;
    border-color: #19952c;
    color: #fff;
  }
}

.goodes_no_box {
  height: 100%;
  @flex_a_flex();
  background: #f2f2f2;
  color: #999999;

  .whole_box_no_icon {
    font-size: 50PX;
  }

  p {
    font-size: 14PX;
  }
}

.popover-wrapper {
  width: 220PX;
  display: flex;
  flex-direction: column;
  box-sizing: border-box;

  .popover-item {
    border-bottom: 1PX solid #eee;
    height: 40PX;
    display: flex;
    align-items: center;
    align-items: center;
    padding: 0 10PX;
    box-sizing: border-box;
    justify-content: center;

    .popover-content-left {
      flex: 1;
      display: flex;
      align-items: center;
      justify-content: flex-end;
    }

    .popover-content-right {
      flex: 1;
      display: flex;
      align-items: center;
      justify-content: flex-start;
    }
  }
}

.picker_remark {
  width: 90%;
  height: 25PX;
  font-size: 15PX;
  outline: none;
  border: none;
  border: 2PX solid #cccccc;
  background: #f2f2f2;
  border-radius: 5PX;
  text-align: center;
  margin-bottom: 10PX;
}

::-webkit-input-placeholder {
  /* WebKit browsers */
  color: #d3d3d3;
  font-size: 14PX;
}

@-webkit-keyframes cursorPlay

/* Safari and Chrome */
  {
  0% {
    background: #00f;
  }

  50% {
    background: #00f;
  }

  51% {
    background: #ffffff;
  }

  100% {
    background: #ffffff;
  }
}

.virtualCursor {
  display: inline-block;
  width: 2PX;
  height: 20PX;
  position: relative;
  top: 0;
  left: 0;
  background: #000;
  -webkit-animation: cursorPlay 0.5s 0.5s infinite alternate;
}

.input_style {
  font-size: 16PX;
}

.input_style::-webkit-input-placeholder {
  font-size: 16PX;
}

// .input_style:active{
//   text-align: left;
// }
.flex {
  display: flex;
  justify-content: flex-start;
}

.product_delist {
  display: flex;
  justify-content: space-between;
  height: 25px;

  .product_delist_wrapper {
    flex: 1;

    .stockInfo-wrapper {
      display: flex;
      flex: 1;
      align-items: flex-end;
      // border: 1 dashed #cccccc;
      border-radius: 5PX;
      height: 25PX;
      box-sizing: border-box;
      padding: 0 5px 0 0;

      .stockInfo-title {
        width: 40PX;
        height: 25PX;
        display: flex;
        justify-content: center;
        align-items: center;
        font-size: 12PX;
        color: #888;
      }

      .stockInfo-content-wrapper {
        display: flex;
        width: 100%;
        height: 100%;
        font-size: 14px;

        .stockInfo-content {
          height: 100%;
          display: flex;
          align-items: center;
          color: #888;
          margin: 0 5PX 0 0;
          justify-content: flex-end;

          .stockInfo-content-num {
            font-family: numfont;
            font-size: 14PX;
            // line-height: 30;
          }

          .stockInfo-content-unit {
            font-size: 14PX;
            // font-size: 14;
            // line-height: 30;
          }
        }
      }
    }
  }
}

.product_unit {
  display: flex;
  color: #888;
  font-family: numfont;
  font-size: 14PX;
  justify-content: flex-end;
}</style>
