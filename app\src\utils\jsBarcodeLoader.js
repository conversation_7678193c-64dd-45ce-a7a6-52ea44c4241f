/**
 * JsBarcode 按需加载工具
 * 使用时动态加载JsBarcode库，避免在应用启动时就加载
 * <AUTHOR>
 * @date 2025-01-08
 */

class JsBarcodeLoader {
  constructor() {
    this.isLoaded = false;
    this.isLoading = false;
    this.loadPromise = null;
  }

  /**
   * 检查JsBarcode是否已经加载
   * @returns {boolean}
   */
  isJsBarcodeLoaded() {
    return typeof window.JsBarcode !== 'undefined';
  }

  /**
   * 动态加载JsBarcode库
   * @returns {Promise<void>}
   */
  loadJsBarcode() {
    // 如果已经加载，直接返回resolved promise
    if (this.isJsBarcodeLoaded()) {
      this.isLoaded = true;
      return Promise.resolve();
    }

    // 如果正在加载，返回已有的promise
    if (this.isLoading && this.loadPromise) {
      return this.loadPromise;
    }

    // 开始加载
    this.isLoading = true;
    this.loadPromise = new Promise((resolve, reject) => {
      try {
        // 创建script标签
        const script = document.createElement('script');
        script.type = 'text/javascript';
        script.async = true;
        
        // 设置加载成功回调
        script.onload = () => {
          this.isLoaded = true;
          this.isLoading = false;
          console.log('JsBarcode loaded successfully');
          resolve();
        };
        
        // 设置加载失败回调
        script.onerror = (error) => {
          this.isLoading = false;
          console.error('Failed to load JsBarcode:', error);
          reject(new Error('Failed to load JsBarcode library'));
        };
        
        // 设置脚本源路径
        script.src = './js/JsBarcode.all.min.js';
        
        // 添加到head中开始加载
        document.head.appendChild(script);
        
      } catch (error) {
        this.isLoading = false;
        console.error('Error loading JsBarcode:', error);
        reject(error);
      }
    });

    return this.loadPromise;
  }

  /**
   * 确保JsBarcode已加载，如果未加载则动态加载
   * @returns {Promise<void>}
   */
  async ensureJsBarcodeLoaded() {
    if (!this.isJsBarcodeLoaded()) {
      await this.loadJsBarcode();
    }
  }

  /**
   * 使用JsBarcode生成条码图像
   * @param {HTMLElement} element - 要渲染条码的DOM元素（img或canvas等）
   * @param {string} text - 条码内容
   * @param {Object} options - JsBarcode选项
   * @returns {Promise<void>}
   */
  async generateBarcode(element, text, options = {}) {
    // 确保JsBarcode已加载
    await this.ensureJsBarcodeLoaded();
    
    // 调用JsBarcode
    if (typeof window.JsBarcode !== 'undefined') {
      return window.JsBarcode(element, text, options);
    } else {
      throw new Error('JsBarcode library is not available');
    }
  }

  /**
   * 获取JsBarcode实例（需要先确保已加载）
   * @returns {Function|null}
   */
  getJsBarcode() {
    return typeof window.JsBarcode !== 'undefined' ? window.JsBarcode : null;
  }
}

// 创建单例实例
const jsBarcodeLoader = new JsBarcodeLoader();

// 导出单例实例和工具方法
export default jsBarcodeLoader;

// 导出便捷方法
export const loadJsBarcode = () => jsBarcodeLoader.loadJsBarcode();
export const ensureJsBarcodeLoaded = () => jsBarcodeLoader.ensureJsBarcodeLoaded();
export const generateBarcode = (element, text, options) => 
  jsBarcodeLoader.generateBarcode(element, text, options);
export const getJsBarcode = () => jsBarcodeLoader.getJsBarcode();