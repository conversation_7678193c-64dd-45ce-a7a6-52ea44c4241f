/**
 * Web端调试位置助手
 * 用于在浏览器中模拟不同的地理位置，方便测试考勤功能
 */

class DebugLocationHelper {
    constructor() {
        this.isDebugMode = !window.cordova; // 非cordova环境视为调试模式
        this.init();
    }

    init() {
        // 移除调试面板，避免影响用户操作
        // 只在控制台输出调试信息
        if (this.isDebugMode && process.env.NODE_ENV === 'development') {
            console.log('🌐 Web端调试模式已启用，使用模拟位置数据');
        }
    }

    // 设置自定义位置（通过控制台调用）
    setCustomPosition(position) {
        localStorage.setItem('debug_position', JSON.stringify(position));
        console.log('🔧 设置自定义调试位置:', position);
        console.log('💡 刷新页面后生效');
    }

    // 获取当前调试位置信息（供其他组件调用）
    getCurrentDebugPosition() {
        const customPosition = localStorage.getItem('debug_position');
        if (customPosition) {
            try {
                return JSON.parse(customPosition);
            } catch (e) {
                console.warn('自定义位置格式错误');
            }
        }

        const urlParams = new URLSearchParams(window.location.search);
        const debugLocationKey = urlParams.get('debug_location') || 'default';
        
        const positions = {
            default: { latitude: 34.602250, longitude: 119.228621, address: '江苏省连云港市海州区' },
            beijing: { latitude: 39.908823, longitude: 116.397470, address: '北京市东城区天安门广场' },
            shanghai: { latitude: 31.239663, longitude: 121.499809, address: '上海市黄浦区外滩' },
            shenzhen: { latitude: 22.559507, longitude: 114.025974, address: '广东省深圳市福田区市民中心' }
        };

        return positions[debugLocationKey] || positions.default;
    }
}

// 自动初始化（仅在开发环境输出提示信息）
let debugLocationHelper = null;
if (!window.cordova && process.env.NODE_ENV === 'development') {
    debugLocationHelper = new DebugLocationHelper();

    // 在控制台提供使用说明
    console.log(`
🌐 Web端调试模式使用说明：

1. 默认位置：连云港市区
2. 切换预设位置：在URL后添加参数
   ?debug_location=beijing   (北京天安门)
   ?debug_location=shanghai  (上海外滩)
   ?debug_location=shenzhen  (深圳市民中心)

3. 自定义位置：在控制台执行
   debugLocationHelper.setCustomPosition({
     latitude: 39.908823,
     longitude: 116.397470,
     address: "北京市东城区天安门广场"
   });

4. 清除自定义位置：
   localStorage.removeItem('debug_position');
`);
}

export default DebugLocationHelper;
