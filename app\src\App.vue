<template>
  <div id="app">
    <!-- <keep-alive>
        <router-view  v-if="$route.meta.keepAlive">

        </router-view>
    </keep-alive>
-->
    <!--  <transition :name="transitionName"> -->
    <transition :name="transitionName">
      <keep-alive :include="cachePageName">
        <router-view :key="getRouteKey($route)" v-if="$route.meta.keepAlive"></router-view>
        <router-view :key="getRouteKey($route)" v-if="!$route.meta.keepAlive"> </router-view>
      </keep-alive>

    </transition>

    <!--  <router-view :key='$route.fullPath' v-if="!$route.meta.keepAlive">

      </router-view>-->

    <!-- <router-view v-if="!$route.meta.keepAlive"></router-view>-->
  </div>
</template>

<script>
import { SaveSellerTrail, SaveTrailsAfterNetworkFail } from '@/api/api'
import { API_SERVER_STATUS } from '../apiserverConfig'
import { CHCP_CONFIG_FILE_ADDR } from '../chcp-config'
import Position from './components/Position.js'
import BarcodeScanner from './components/BarcodeScanner.js'
import VisitService from './views/Visit/VisitService'
import globalVars from "./static/global-vars";
import MQTT from './views/service/MQTT';
import fontSize from "./util/fontSize"
import Vue from 'vue'
import DataRecovery from '@/utils/dataRecovery'

export default {
  name: "App",
  data() {
    return {
      transitionName: 'slide-left',
      chcpServer: "",
      currentCity: ""
    }
  },
  computed: {
    key() {
      return this.$route.path;
    },
    cachePageName() {
      // console.log('缓存的页面:'+this.$store.state.cachePageName);
      return this.$store.state.cachePageName;
    }
  },
  watch: {
    '$route'(to, from) {
      const toIndex = to.meta.index
      const fromIndex = from.meta.index

      //this.transitionName = toIndex < fromIndex ? 'slide-right' : 'slide-left'
      var bGoBack = false
      if (window.g_bUseMyGoBack) {
        if (window.g_bGoBack) bGoBack = true
        else if (!window.g_bPushing) bGoBack = true
      }
      else if (!window.g_bPushing) bGoBack = true

      if (globalVars.mobileManufactor === 'alps') {
        this.transitionName = ''
      } else {
        this.transitionName = bGoBack ? 'slide-right' : 'slide-left'
        console.log('transitionName:'+this.transitionName)
      }

      if (!bGoBack && window.g_curPage) {
        // to.myParams = window.g_curPage.params
      }

      setTimeout(()=>{
        this.transitionName =''
      },500)
      
    }
  },
 
  mounted() {
    // 应用启动时检查数据完整性
    DataRecovery.autoRecover(this.$store)
    
    // 监听应用从后台恢复
    document.addEventListener('resume', () => {
      console.log('应用从后台恢复')
      DataRecovery.autoRecover(this.$store)
    })

 
    console.log(this.$store.state.operInfo.operRights)
    window.enableReportPosition = () => {
      this.enableReportPosition()
    }
    // 修改Vue原型上的getCurPosition函数，使用统一的Position.getPosition静态方法
    Vue.prototype.getCurPosition = async (params) => {
      return await Position.getPosition(params)
    }
    window.onresize = ()=> {
      // 定义窗口大小变更通知事件
      if(this.$store.state.operSetting.fontSize){
        setGlobalFontSize(this.$store.state.operSetting.fontSize)
      }
    };
    if (window.cordova) {
      document.addEventListener("deviceready", () => {
        console.log("后台运行模式启动")
        console.log("initiateUI");
        // if (window.plugins.MiPushPlugin) {
        //   window.plugins.MiPushPlugin.init();
        //   document.addEventListener("mipush.notificationMessageArrived", (res) => {
        //     console.log("mipush.notificationMessageArrived", res)
        //   }, false);
        //   document.addEventListener("mipush.notificationMessageClicked", (res) => {
        //     console.log("mipush.notificationMessageClicked", res)
        //   }, false);
        //   document.addEventListener("mipush.receiveRegisterResult", (res) => {
        //     console.log("mipush.receiveRegisterResult")
        //     window.push = {
        //       regId: res.regId,
        //       manufactor: res.manufactor
        //     }
        //   }, false);
        // }
        if (isiOS||isHarmony) {
          //启用inappbroswer
          window.open = cordova.InAppBrowser.open
        }
        // cordova.plugins.backgroundMode.enable();
      })

    }
    Vue.prototype.getDistance = this.getDistance
    Vue.prototype.scanBarcode = this.scanBarcode
    Vue.prototype.scanBarcodeNew = BarcodeScanner.scan
    Vue.prototype.getOperInfo= this.getOperInfo

    // console.log("store.fontsize",this.$store.state.setFontSize)
    // console.log("rootFontSize",document.documentElement.style.fontSize)
    // if(fontSize.fontLevel[this.$store.state.setFontSize]!=document.documentElement.style.fontSize){
    //   setGlobalFontSize(this.$store.state.setFontSize)
    // }


    // chcp.fetchUpdate((error, data) => {
    //       if (error) {
    //         console.log('--更新版本异常，或其他错误--', error.code, error.description);
    //         if (error.code === -2) {
    //           var dialogMessage = '有新的版本是否下载';
    //           //调用升级提示框 点击确认会跳转对应商店升级
    //           chcp.requestApplicationUpdate(dialogMessage, null, null);
    //         }
    //       }
    //       // 服务器版本信息
    //       // console.log('--更新的版本信息--', data.config);
    //       // 版本信息
    //       chcp.getVersionInfo((err, data) => {

    //         console.log('服务器应用时间版本: ' + data.readyToInstallWebVersion);

    //         console.log('当前应用时间版本： ' + data.currentWebVersion);

    //         console.log('当前应用version name: ' + data.appVersion);

    //       });

    //     });

  },
  methods: {
    getRouteKey(route) {
      var path = route.path
      if (path == '/Workbench' || path == '/Whole' || path == '/My' || path == '/Message') return ''
      if (window.g_bGoBack) {
        window.g_backResponsed = true
        if (window.g_curPage.name != route.name) {
          var curPage = window.g_curPage
          if (window.g_bUseMyGoBack) {
            setTimeout(function () {
              window.g_bGoBack = true
              g_router.replace({ path: curPage.path, query: curPage.query })
            }, 5000)
          }


          // window.g_backTime=new Date()
          //return window.g_nextPage.name+window.g_nextPage.id
          if (window.g_curPage.name == 'Workbench' || window.g_curPage.name == 'Whole' || window.g_curPage.name == 'My') return ''
          return window.g_curPage.name + window.g_curPage.id
        }
        //  window.g_nextPage=window.g_curPage
        if (window.g_curPage.name == 'Workbench' || window.g_curPage.name == 'Whole' || window.g_curPage.name == 'My') return ''
        if (window.g_curPage)
          return window.g_curPage.name + window.g_curPage.id

        //  return path + curRoute.id
      }
      else {
        if (window?.g_curPage == undefined) {  /// 处理IOS启动白屏问题（退出后台）
          setTimeout(function () {
            window.g_bGoBack = true
            g_router.replace({ path: 'Workbench' })
          }, 500)
        }
        if (window.g_curPage.name == 'Workbench' || window.g_curPage.name == 'Whole' || window.g_curPage.name == 'My') return ''
        if (window.g_curPage)
          return window.g_curPage.name + window.g_curPage.id
        // var curRoute=window.g_routerArray[window.g_routerArray.length-1]
        // return path + curRoute.id
      }

      // if(route.matched && route.matched.length>0 && route.matched[0].instances&&route.matched[0].instances.default){
      //    return path +  route.matched[0].instances.default._uid
      // }

      //if(route.matched && route.matched.length>0 && route.matched[0].components){
      //    return path +  route.matched[0].components.default._scopeId
      // }
      // matched[0].components.default._scopeId
      return path
      /*if(route.meta && route.meta.key){


          const key = route.meta.key
          if (typeof(key) === 'function'){
              return path+key(route)
          }
          return path+key
      }else{
          return path
      }*/
    },
    getOperInfo(){    
     return this.$store.state.operInfo
    },
    enableReportPosition() {
      let that_ = this;
      // console.log('enableReportPosition')
      // const watchID=navigator.geolocation.watchMyPosition(()=>{
      //       navigator.geolocation.openFrontLocationService(watchID)
      //       console.log("watchID:",watchID)
      // }
      // // )
      // let failSavedTrails=[]
      setInterval(async () => {
        const visitService = new VisitService()
        if (visitService.hasPositionPeriodSetting()) {
          const { positionPeriodStart, positionPeriodEnd } = visitService.getPositionPeriod()
          console.log( "定位时间",positionPeriodStart, positionPeriodEnd)
          if (!visitService.checkCurTimeInPeriod(positionPeriodStart, positionPeriodEnd)) {
            console.log('not in time')
            return
          }
        }
        let params_={
            message: "需要定位权限来上报行走轨迹",
            key: "positionTrail"
        }
        // 修改调用方式，使用Position.getPosition静态方法
        var res = await Position.getPosition(params_)
        console.log('after get position' + res.result)
        if (res.result != "OK") {
          return
        }
        if (that_.isIllegalPosition(res.latitude, res.longitude) || that_.twoPositionIsLess50Metres(res.latitude, res.longitude)) {
          return
        }
        let params = {
          operKey: that_.$store.state.operKey,
          longitude: res.longitude,
          latitude: res.latitude
        }
        // if(failSavedTrails.length!=0){
        // SaveTrailsAfterNetworkFail(failSavedTrails).then((res)=>{
        //   this.failSavedTrails=[]
        // })
        // }
        SaveSellerTrail(params).then((res) => {
          // if(res.req.status!=200){
          // console.log("没网络啦@@")
          // params.happen_time=new Date()
          // failSavedTrails.push(params)
          // }
        })

        that_.$store.state.lastPosition = {
          latitude: res.latitude,
          longitude: res.longitude
        }
      }, 60000)
    }, 
    isIllegalPosition(lat, lng) {
      return lng == undefined || lat == undefined || Math.floor(lng) == 0 || Math.floor(lat) == 0
    },
    //判断两点之间是否小于50m
    twoPositionIsLess50Metres(latitude, longitude) {
      const cacheLat = this.$store.state.lastPosition.latitude
      const cacheLon = this.$store.state.lastPosition.longitude
      //假如缓存中没有定位信息，证明第一次打开，放行
      if (!(cacheLat && cacheLon)) {
        return false;
      }
      return this.getDistance(cacheLat, cacheLon, latitude, longitude) < 50
    },
    getDistance(lat1, lng1, lat2, lng2) {
      if (!lat1 || !lng1 || !lat2 || !lng2) return -1
      var EARTH_RADIUS = 6378137.0 //单位M
      var radLat1 = this.getRad(lat1)
      var radLat2 = this.getRad(lat2)
      var a = radLat1 - radLat2
      var radLng1 = this.getRad(lng1)
      var radLng2 = this.getRad(lng2)
      var b = radLng1 - radLng2
      var s = 2 * Math.asin(Math.sqrt(Math.pow(Math.sin(a / 2), 2) + Math.cos(radLat1) * Math.cos(radLat2) * Math.pow(Math.sin(b / 2), 2)))
      s = s * EARTH_RADIUS
      s = Math.round(s * 10000) / 10000.0
      s=parseInt(s)
      return s
    },
    getRad(d) {
      var PI = Math.PI
      return d * PI / 180.0
    },
    scanBarcode(unit_type='') {
      return new Promise((resolve, reject) => {
        const supportFormat =  {
                Code128: true,
                Code39: true,
                Code93: true,
                CodaBar: true,
                DataMatrix: true,
                EAN13: true,
                EAN8: true,
                ITF: true,
                QRCode: false,
                UPCA: true,
                UPCE: true,
                PDF417: true,
                Aztec: true,
              }
        const androidconfig = {
              barcodeFormats:supportFormat,
              beepOnSuccess: true,
              vibrateOnSuccess: false,
              detectorSize: .6,
              rotateCamera: false,
          // preferFrontCamera: false, // iOS and Android
          // showFlipCameraButton: true, // iOS and Android
          // showTorchButton: true, // iOS and Android
          // torchOn: false, // Android, launch with the torch switched on (if available)
          // saveHistory: true, // Android, save scan history (default false)
          // prompt: "Place a barcode inside the scan area", // Android
          // resultDisplayDuration: 1000, // Android, display scanned text for X ms. 0 suppresses it entirely, default 1500
          // // formats: "", // default: all but PDF_417 and RSS_EXPANDED
          // orientation: "portrait", // Android only (portrait|landscape), default unset so it rotates with the device
          // disableAnimations: true, // iOS
          // disableSuccessBeep: false // iOS and Android
        }
        const iosconfig = {
          preferFrontCamera: false, // iOS and Android
          showFlipCameraButton: false, // iOS and Android
          showTorchButton: true, // iOS and Android
          torchOn: false, // Android, launch with the torch switched on (if available)
          disableAnimations: false, // iOS
          disableSuccessBeep: false // iOS and Android
        }
        let barcodeScannerAndroidConfig = {
          preferFrontCamera: false, // iOS and Android
          showFlipCameraButton: true, // iOS and Android
          showTorchButton: true, // iOS and Android
          torchOn: false, // Android, launch with the torch switched on (if available)
          saveHistory: true, // Android, save scan history (default false)
          prompt: "Place a barcode inside the scan area", // Android
          resultDisplayDuration: 1000, // Android, display scanned text for X ms. 0 suppresses it entirely, default 1500
          // formats: "", // default: all but PDF_417 and RSS_EXPANDED
          orientation: "portrait", // Android only (portrait|landscape), default unset so it rotates with the device
          disableAnimations: true, // iOS
          disableSuccessBeep: false, // iOS and Android
          barcodeFormats:supportFormat
        }


        const config = isiOS ? iosconfig : (typeof  cordova.plugins.mlkit?.barcodeScanner == 'undefined'? androidconfig : barcodeScannerAndroidConfig)
        const plugin = isiOS || typeof  cordova.plugins.mlkit?.barcodeScanner == 'undefined'? cordova.plugins.barcodeScanner : cordova.plugins.mlkit.barcodeScanner
        if(isiOS){
          plugin.scan(
            async (result) => {
              const res = { unit_type,code: result.text , format: result.format}
              resolve(res)
            },
            async (res) => {
              reject(res)
            },
            config
          );

        }else{
          const useOldPlugin = typeof cordova.plugins.mlkit?.barcodeScanner == 'undefined'
          console.log(useOldPlugin)
          if(useOldPlugin){
            plugin.scan(
                
                async (result) => {
                  const res = { unit_type,code: result.text , format: result.format}
                  resolve(res)
                },
                async (res) => {
                  reject(res)
                },
                config
            );
          }else{
             plugin.scan(
                config,
                async (result) => {
                  const res = { unit_type,code: result.text , format: result.format}
                  resolve(res)
                },
                async (res) => {
                  reject(res)
                }
              );
          }
        }
      })
    },
  }
}
</script>
<style lang="less">
:root {
  --blackBack: #ededed;
}
#app {
  font-family: Roboto, Avenir, Helvetica, Arial, sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: center;
  color: #000;

  @my-btn-common: {
    background: #fff;
    color: #333;
    border: 1px solid #ddd;
    height: 36px;
    min-width: 70px;
    border-radius: 12px;
    font-size: 15px;
  };
  .my-btn {
    @my-btn-common();
  }
  .my-btn:disabled{
    background-color: #f5f5f5;
    color: #999;
  }

  .main-btn {
    @my-btn-common();
    background-color: #ffcccc;
    border-width: 0px;
    color: #000;
  }
 
}

.van-picker-column__item--selected div {
  font-size: 20px;
}
#nav {
  padding: 30px;
}

#nav a {
  font-weight: bold;
  color: #2c3e50;
}

#nav a.router-link-exact-active {
  color: #42b983;
}
html,
body,
#app,
.pages {
  //background: #ededed;
  height: 100%;
  overflow: hidden;
  width: 100%;
}
//  公用包
//  公用样式
@padd_maig: {
  padding: 0;
  margin: 0;
};
@posAblot: {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
};
@flex_acent_jbw: {
  display: flex;
  align-items: center;
  justify-content: space-between;
};
@flex_acent_jend: {
  display: flex;
  align-items: center;
  justify-content: flex-end;
};
@flex_a_flex: {
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
};
@flex_jc_flex: {
  display: flex;
  justify-content: center;
  flex-direction: column;
};
@flex-left: {
  text-align: left;
};
@flex-right: {
  text-align: right;
};
h1,
h2,
h3,
h4,
h5,
h6 {
  @padd_maig();
}
.public_box {
  height: auto;
  overflow: hidden;
  background: #ffffff;
}
.public_box_margin {
  margin-top: 10px;
}
.public_h1 {
  padding: 0 10px;
  height: 50px;
  line-height: 50px;
  font-size: 16px;
  text-align: left;
  color: #333333;
}
.public_ul1 {
  height: auto;
  overflow: hidden;
  li {
    width: calc(33.33% - 10px);
    float: left;
    height: 60px;
    padding: 5px;
    h4 {
      font-size: 14px;
    }
    h5 {
      margin: 3px 0;
    }
    h5,
    h6 {
      font-size: 12px;
      font-weight: normal;
    }
  }
}
.pages {
  position: relative;

  //background-color:#f4f4f4;
}
button {
  color: #333;
  min-width: 40px;
  font-size: 15px;
  border: none;
}
button:active {
  background: #888888;
}
button:disabled {
  background-color: #f5f5f5;
  color: #999;
}
.van-button--normal {
  font-size: 15px !important;
}
.public_footer {
  height: 43px;
  @posAblot();
  @flex_acent_jbw();
  background: #ffffff;
  padding: 0 10px;
}
.van-nav-bar {
  // background: var(--blackBack) !important;
  background: #fff !important;
  box-shadow: 0 2px 5px #f2f6fc;
  color: #000 !important;
  font-weight: 600;
}
.van-nav-bar__title {
  color: #000 !important;
  font-weight: 600 !important;
}
.van-nav-bar .van-icon {
  color: #000 !important;
  font-weight: 600 !important;
}
.public_box1 {
  height: calc(100% - 96px);
  position: relative;
}
.public_box2 {
  height: calc(100% - 46px);
  position: relative;
}
.public_box3 {
  height: calc(100% - 106px);
  position: relative;
}

.noSubmitStyle {
  color: #000;
  font-size: 15px;
}
.submitStyle {
  color: #000;
  font-size: 15px;
}
.icon_right {
  color: #000;
}
.icon_filter {
  color: #000;
  font-size: 16px;
}

.title3 {
  @flex_acent_jbw();
  padding: 0 5px;
  background: #ffffff;
  border-top: 1px solid #f2f2f2;
  // border-bottom: 1px solid #f2f2f2;
  div {
    font-size: 16px;
    height: 40px;
    line-height: 40px;
    padding: 0 5px;
    font-weight: normal;
  }
  div:first-child {
    width: 120px;
    text-align: left;
  }
  div {
    width: 150px;
  }
  div:last-child {
    width: 120px;
    text-align: right;
  }
}
.van_list {
  overflow-x: hidden;
  overflow-y: auto;
  -webkit-overflow-scrolling: touch;
}
// ul>li>div*2
.receive_ul {
  padding: 0 5px;
  background: #ffffff;
  li {
    border-bottom: 1px solid #f2f2f2;
    @flex_acent_jbw();
    padding: 10px 0;
    div {
      flex: 1;
      overflow: hidden;
      font-size: 15px;
      padding: 0 5px;
    }
    div:first-child {
      @flex-left();
    }
    div:last-child {
      @flex-right();
    }
  }
  li:last-child {
    border: none;
  }
}
// right_icon
.icon_filter {
  color: #000;
  font-size: 14px;
}
// payTypes
.pay_ul {
  padding: 5px;
  height: auto;
  background-color: #ffffff;
  li {
    height: 40px;
    overflow: hidden;
    padding: 5px;
    border-bottom: 1px solid #f2f2f2;
    .pay_ul_t {
      font-size: 15px;
      @flex_acent_jbw();
      div {
        flex: 1;
      }
      div:first-child {
        text-align: left;
      }
      div:last-child {
        text-align: right;
      }
    }
    .pay_ul_b {
      font-size: 13px;
      color: red;
      text-align: right;
      margin-top: 5px;
    }
  }
  li:last-child {
    border-bottom: none;
  }
}
.public_header {
  background: #ffffff;
  .public_header_l {
    text-align: left;
  }
  .public_header_l,
  .public_header_r {
    height: 30px;
    padding: 0 10px;
    line-height: 44px;
    p {
      font-size: 15px;
      margin: 0;
    }
  }
  .public_header_r {
    text-align: right;
    padding: 0 10px;
  }
  .van-field__value {
    border-bottom: 1px solid #cccccc;
  }
  .field_icon {
    font-size: 20px;
    color: #999999;
  }
}


.slide-left-enter-active,
.slide-left-leave-active,
.slide-right-enter-active,
.slide-right-leave-active {
  will-change: transform;
  transition: all 400ms;
  position: absolute;
  width: 100%;
  z-index: 1;
}
.slide-right-enter-active,
.slide-right-leave-active {
  transition: all 600ms;
}

/* 新页面从右侧进入 */
.slide-left-enter {
  transform: translateX(100%);
 
  z-index: 2;
}

.slide-left-enter-to {
  transform: translateX(0);
   
  z-index: 2;
}

/* 旧页面向左移出 */
.slide-left-leave {
  transform: translateX(0%);
  z-index: -1;
}
.slide-left-leave-to {
  transform: translateX(-20%);
  z-index: -1;
}

/* 返回时当前页面向右移出 */
.slide-right-leave {
  transform: translateX(0%);
  z-index: 2;
}
.slide-right-leave-to {
  transform: translateX(100%);
   
  z-index: 2;
}

/* 返回时旧页面从左侧进入 */
.slide-right-enter {
  transform: translateX(-30%);
  z-index: 0;
}

.slide-right-enter-to {
  transform: translateX(0);
  z-index: 0;
}


*:not(input, textarea) {
  -webkit-touch-callout: none;
  -webkit-user-select: none;
  -khtml-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

input::-webkit-input-placeholder {
  color: #cccccc;
}
div {
  font-size: 16px;
}
.pages{
  background-color: #fff;
}
.van-radio__icon--checked {
  .van-icon {
    background-color: #444 !important;
    border-color: #444;
  }
}
.van-switch {
  background-color: #fff;
  /* .van-switch__node {
    width: 1.3em;
    height: 1.3em;
  } */
}
.van-switch--on {
  background-color: #444 !important;
  .van-switch__node {
    transform: translateX(1.4em) !important;
  }
}

van-divider {
  color: #969799;
  border-color: #ebedf0;
  padding: "0 8px";
}
van-divider {
  margin-bottom: 90px;
}
.van-dropdown-menu__bar {
  box-shadow: 0 5rem 5rem rgb(100 101 102 / 12%);
}

//.black-back{
//   background:var(--blackBack) !important;
//}
</style>