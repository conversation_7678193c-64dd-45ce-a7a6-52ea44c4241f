/*
       Licensed to the Apache Software Foundation (ASF) under one
       or more contributor license agreements.  See the NOTICE file
       distributed with this work for additional information
       regarding copyright ownership.  The ASF licenses this file
       to you under the Apache License, Version 2.0 (the
       "License"); you may not use this file except in compliance
       with the License.  You may obtain a copy of the License at

         http://www.apache.org/licenses/LICENSE-2.0

       Unless required by applicable law or agreed to in writing,
       software distributed under the License is distributed on an
       "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
       KIND, either express or implied.  See the License for the
       specific language governing permissions and limitations
       under the License.
*/

apply plugin: 'com.android.application'

if (cordovaConfig.IS_GRADLE_PLUGIN_KOTLIN_ENABLED) {
    apply plugin: 'kotlin-android'

    if(!cdvHelpers.isVersionGreaterThanEqual(cordovaConfig.KOTLIN_VERSION, '1.8.0')) {
        println "Kotlin version < 1.8.0 detected. Applying kotlin-android-extensions plugin."
        apply plugin: 'kotlin-android-extensions'
    }
}

buildscript {
    apply from: '../CordovaLib/cordova.gradle'

    // Checks if the kotlin version format is valid.
    if(cordovaConfig.IS_GRADLE_PLUGIN_KOTLIN_ENABLED) {
        if(!cdvHelpers.isVersionValid(cordovaConfig.KOTLIN_VERSION)) {
            throw new GradleException("The defined Kotlin version (${cordovaConfig.KOTLIN_VERSION}) does not appear to be a valid version.")
        }
    }

    apply from: 'repositories.gradle'
    repositories repos

    dependencies {
        classpath "com.android.tools.build:gradle:${cordovaConfig.AGP_VERSION}"

        if (cordovaConfig.IS_GRADLE_PLUGIN_KOTLIN_ENABLED) {
            classpath "org.jetbrains.kotlin:kotlin-gradle-plugin:${cordovaConfig.KOTLIN_VERSION}"
        }

        if(cordovaConfig.IS_GRADLE_PLUGIN_GOOGLE_SERVICES_ENABLED) {
            // Checks if the kotlin version format is valid.
            if(!cdvHelpers.isVersionValid(cordovaConfig.GRADLE_PLUGIN_GOOGLE_SERVICES_VERSION)) {
                throw new GradleException("The defined Google Services plugin version (${cordovaConfig.GRADLE_PLUGIN_GOOGLE_SERVICES_VERSION}) does not appear to be a valid version.")
            }

            // Create the Google Services classpath and set it.
            String gradlePluginGoogleServicesClassPath = "com.google.gms:google-services:${cordovaConfig.GRADLE_PLUGIN_GOOGLE_SERVICES_VERSION}"
            println "Adding classpath: ${gradlePluginGoogleServicesClassPath}"
            classpath gradlePluginGoogleServicesClassPath
        }
    }
}

// Allow plugins to declare Maven dependencies via build-extras.gradle.
allprojects {
    def hasRepositoriesGradle = file('repositories.gradle').exists()
    if (hasRepositoriesGradle) {
        apply from: 'repositories.gradle'
    } else {
        apply from: "${project.rootDir}/repositories.gradle"
    }

    repositories repos
}

// Configuration properties. Set these via environment variables, build-extras.gradle, or gradle.properties.
// Refer to: http://www.gradle.org/docs/current/userguide/tutorial_this_and_that.html
ext {
    apply from: '../CordovaLib/cordova.gradle'

    // Sets the versionCode to the given value.
    if (!project.hasProperty('cdvVersionCode')) {
        cdvVersionCode = null
    }
    // Whether to build architecture-specific APKs.
    if (!project.hasProperty('cdvBuildMultipleApks')) {
        cdvBuildMultipleApks = null
    }
    // Whether to append a 0 "abi digit" to versionCode when only a single APK is build
    if (!project.hasProperty('cdvVersionCodeForceAbiDigit')) {
        cdvVersionCodeForceAbiDigit = null
    }
    // .properties files to use for release signing.
    if (!project.hasProperty('cdvReleaseSigningPropertiesFile')) {
        cdvReleaseSigningPropertiesFile = null
    }
    // .properties files to use for debug signing.
    if (!project.hasProperty('cdvDebugSigningPropertiesFile')) {
        cdvDebugSigningPropertiesFile = null
    }
    // Set by build.js script.
    if (!project.hasProperty('cdvBuildArch')) {
        cdvBuildArch = null
    }

    // Plugin gradle extensions can append to this to have code run at the end.
    cdvPluginPostBuildExtras = []
}

// PLUGIN GRADLE EXTENSIONS START
apply from: "../cordova-hot-code-push-plugin/app-chcp.gradle"
apply from: "../phonegap-plugin-barcodescanner/app-barcodescanner.gradle"
apply from: "../cordova-plugin-mlkit-barcode-scanner/app-build-extras.gradle"
apply from: "../cordova-plugin-wechat/app-android-build.gradle"
// PLUGIN GRADLE EXTENSIONS END

def hasBuildExtras1 = file('build-extras.gradle').exists()
if (hasBuildExtras1) {
    apply from: 'build-extras.gradle'
}

def hasBuildExtras2 = file('../build-extras.gradle').exists()
if (hasBuildExtras2) {
    apply from: '../build-extras.gradle'
}

// Apply updates that might come from build-extra.
privateHelpers.applyCordovaConfigCustomization()

// Set property defaults after extension .gradle files.
if (ext.cdvDebugSigningPropertiesFile == null && file('../debug-signing.properties').exists()) {
    ext.cdvDebugSigningPropertiesFile = '../debug-signing.properties'
}
if (ext.cdvReleaseSigningPropertiesFile == null && file('../release-signing.properties').exists()) {
    ext.cdvReleaseSigningPropertiesFile = '../release-signing.properties'
}

// Cast to appropriate types.
ext.cdvBuildMultipleApks = cdvBuildMultipleApks == null ? false : cdvBuildMultipleApks.toBoolean();
ext.cdvVersionCodeForceAbiDigit = cdvVersionCodeForceAbiDigit == null ? false : cdvVersionCodeForceAbiDigit.toBoolean();
ext.cdvVersionCode = cdvVersionCode == null ? null : Integer.parseInt('' + cdvVersionCode)

def computeBuildTargetName(debugBuild) {
    def ret = 'assemble'
    if (cdvBuildMultipleApks && cdvBuildArch) {
        def arch = cdvBuildArch == 'arm' ? 'armv7' : cdvBuildArch
        ret += '' + arch.toUpperCase().charAt(0) + arch.substring(1);
    }
    return ret + (debugBuild ? 'Debug' : 'Release')
}

// Make cdvBuild a task that depends on the debug/arch-sepecific task.
task cdvBuildDebug
cdvBuildDebug.dependsOn {
    return computeBuildTargetName(true)
}

task cdvBuildRelease
cdvBuildRelease.dependsOn {
    return computeBuildTargetName(false)
}

task cdvPrintProps {
    doLast {
        println('cdvBuildToolsVersion=' + cdvBuildToolsVersion)
        println('cdvVersionCode=' + cdvVersionCode)
        println('cdvVersionCodeForceAbiDigit=' + cdvVersionCodeForceAbiDigit)
        println('cdvSdkVersion=' + cdvSdkVersion)
        println('cdvMinSdkVersion=' + cdvMinSdkVersion)
        println('cdvMaxSdkVersion=' + cdvMaxSdkVersion)
        println('cdvBuildMultipleApks=' + cdvBuildMultipleApks)
        println('cdvReleaseSigningPropertiesFile=' + cdvReleaseSigningPropertiesFile)
        println('cdvDebugSigningPropertiesFile=' + cdvDebugSigningPropertiesFile)
        println('cdvBuildArch=' + cdvBuildArch)
        println('computedVersionCode=' + android.defaultConfig.versionCode)
        println('cdvAndroidXAppCompatVersion=' + cdvAndroidXAppCompatVersion)
        println('cdvAndroidXWebKitVersion=' + cdvAndroidXWebKitVersion)
        android.productFlavors.each { flavor ->
            println('computed' + flavor.name.capitalize() + 'VersionCode=' + flavor.versionCode)
        }
    }
}

android {
    namespace 'com.yingjiang.app'  // 添加这行来指定命名空间
    compileSdkVersion cordovaConfig.COMPILE_SDK_VERSION
    buildToolsVersion cordovaConfig.BUILD_TOOLS_VERSION

    defaultConfig {
        applicationId "com.yingjiang.app"
        minSdkVersion cordovaConfig.MIN_SDK_VERSION
        targetSdkVersion cordovaConfig.SDK_VERSION
        versionCode 33800
        versionName "3.38"
    }
    
    signingConfigs {
        debug {
            storeFile file("../../../yingjiang_ok.keystore")
            storePassword "abcd1234"
            keyAlias "noalias"
            keyPassword "abcd1234"
        }
    }
    
    buildTypes {
        debug {
            signingConfig signingConfigs.debug
        }
        release {
            signingConfig signingConfigs.debug
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.pro'
        }
    }
 
    // 强制使用 EventBus 3.3.1 版本以解决兼容性问题
    configurations.all {
        resolutionStrategy {
            force 'org.greenrobot:eventbus:3.3.1'
            // 排除插件中可能引入的旧版本依赖
           // exclude group: 'org.greenrobot', module: 'eventbus'
        }
    }
}

/*
 * WARNING: Cordova Lib and platform scripts do management inside of this code here,
 * if you are adding the dependencies manually, do so outside the comments, otherwise
 * the Cordova tools will overwrite them
 */


dependencies {
    implementation fileTree(dir: 'libs', include: '*.jar')
    implementation "androidx.appcompat:appcompat:${cordovaConfig.ANDROIDX_APP_COMPAT_VERSION}"
    implementation "androidx.core:core-splashscreen:${cordovaConfig.ANDROIDX_CORE_SPLASHSCREEN_VERSION}"

    if (cordovaConfig.IS_GRADLE_PLUGIN_KOTLIN_ENABLED) {
        implementation "org.jetbrains.kotlin:kotlin-stdlib-jdk7:${cordovaConfig.KOTLIN_VERSION}"
    }

    // SUB-PROJECT DEPENDENCIES START
    implementation(project(path: ":CordovaLib"))
    implementation "androidx.core:core:1.6.+"
    implementation "androidx.webkit:webkit:1.4.0"
    implementation "androidx.annotation:annotation:*"
    implementation "androidx.localbroadcastmanager:localbroadcastmanager:1.1.0"
    implementation "androidx.legacy:legacy-support-v4:1.0.0"
    implementation "com.google.android.material:material:1.2.0"
    implementation "com.google.mlkit:barcode-scanning:17.3.0"
    implementation "androidx.camera:camera-core:1.3.1"
    implementation "androidx.camera:camera-camera2:1.3.1"
    implementation "androidx.camera:camera-lifecycle:1.3.1"
    implementation "androidx.camera:camera-view:1.3.1"
    implementation "androidx.constraintlayout:constraintlayout:2.0.4"
    implementation "com.tencent.tbs:tbssdk:44286"
    implementation "org.eclipse.paho:org.eclipse.paho.client.mqttv3:1.1.0"
    
    // 明确指定使用 EventBus 3.3.1 版本，确保HotCodePushPlugin可以正确编译
    implementation 'org.greenrobot:eventbus:3.3.1'
    // SUB-PROJECT DEPENDENCIES END
}



def addSigningProps(propsFilePath, signingConfig) {
    def propsFile = file(propsFilePath)
    def props = new Properties()
    propsFile.withReader { reader ->
        props.load(reader)
    }

    def storeFile = new File(props.get('key.store') ?: privateHelpers.ensureValueExists(propsFilePath, props, 'storeFile'))
    if (!storeFile.isAbsolute()) {
        storeFile = RelativePath.parse(true, storeFile.toString()).getFile(propsFile.getParentFile())
    }
    if (!storeFile.exists()) {
        throw new FileNotFoundException('Keystore file does not exist: ' + storeFile.getAbsolutePath())
    }
    signingConfig.keyAlias = props.get('key.alias') ?: privateHelpers.ensureValueExists(propsFilePath, props, 'keyAlias')
    signingConfig.keyPassword = props.get('keyPassword', props.get('key.alias.password', signingConfig.keyPassword))
    signingConfig.storeFile = storeFile
    signingConfig.storePassword = props.get('storePassword', props.get('key.store.password', signingConfig.storePassword))
    def storeType = props.get('storeType', props.get('key.store.type', ''))
    if (!storeType) {
        def filename = storeFile.getName().toLowerCase()
        if (filename.endsWith('.p12') || filename.endsWith('.pfx')) {
            storeType = 'pkcs12'
        } else {
            storeType = signingConfig.storeType // "jks"
        }
    }
    signingConfig.storeType = storeType
}

for (def func : cdvPluginPostBuildExtras) {
    func()
}

// This can be defined within build-extras.gradle as:
//     ext.postBuildExtras = { ... code here ... }
if (hasProperty('postBuildExtras')) {
    postBuildExtras()
}

if (cordovaConfig.IS_GRADLE_PLUGIN_GOOGLE_SERVICES_ENABLED) {
    apply plugin: 'com.google.gms.google-services'
}