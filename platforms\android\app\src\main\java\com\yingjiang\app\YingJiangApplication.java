package com.yingjiang.app;

import android.app.Application;
import android.util.Log;
import com.tencent.smtt.sdk.QbSdk;
import com.tencent.smtt.sdk.TbsListener;

public class YingJiangApplication extends Application {
    private static final String TAG = "YingJiangApplication";

    @Override
    public void onCreate() {
        super.onCreate();
        
        // 初始化X5内核
        initX5WebView();
    }
    
    private void initX5WebView() {
        try {
            // 设置X5内核监听器
            QbSdk.setTbsListener(new TbsListener() {
                @Override
                public void onDownloadFinish(int i) {
                    Log.d(TAG, "X5内核下载完成: " + i);
                }

                @Override
                public void onInstallFinish(int i) {
                    Log.d(TAG, "X5内核安装完成: " + i);
                }

                @Override
                public void onDownloadProgress(int i) {
                    Log.d(TAG, "X5内核下载进度: " + i);
                }
            });

            // 设置X5内核配置
            QbSdk.initX5Environment(this, new QbSdk.PreInitCallback() {
                @Override
                public void onViewInitFinished(boolean b) {
                    Log.d(TAG, "X5内核初始化完成: " + b);
                }

                @Override
                public void onCoreInitFinished() {
                    Log.d(TAG, "X5内核Core初始化完成");
                }
            });
        } catch (Exception e) {
            Log.e(TAG, "X5内核初始化失败", e);
        }
    }
}