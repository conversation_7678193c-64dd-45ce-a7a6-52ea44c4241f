<template>
  <div>
    <div class="add_goods_box" v-cloak>
      <!-- 商品操作区域 -->
      <div class="item_name_option">
        <div class="bReturn">
        </div>
        <div class="more_option">
          <div class="more_option_1" id="more_option_1">
          </div>
          <div class="more_option_2" id="more_option_2">
          </div>
          <div class="more_option_3" id="more_option_3">
            <!--                <div class="close_icon_font" v-show="this.itemRow.singleChoice == undefined || this.attrShowFlag" @click="closePage">完成</div>-->
          </div>
        </div>
      </div>
      <div class="close_icon_font_wrapper">
        <div class="close_icon_font" @click="closePage">
          <van-icon name="arrow-left" style="margin-left: 5" />
          <div class="font_wrapper"></div>
        </div>
      </div>
      <!-- 商品输入内容区域 -->
      <div class="add_box" v-cloak :style="{ 'height': '200px' }">
        <div v-if="distinctFlagDom" key="distinct_stock_wrapper" :style="{ 'height': '120px' }">
          <div class="distinct_stock_wrapper">
            <div class="distinct_msg_wrapper">
              <div>该商品包含区分库存的属性，请输入{{ showBtnAttrName }}数量</div>
            </div>
            <div class="distinct_btn_wrapper">
              <div class="distinct_btn" @click="handleAttr">输入{{ showBtnAttrName }}数量</div>
            </div>
          </div>
        </div>
        <div v-else-if="shoppingCarFinishFlag" :style="{ 'height': '120px' }" key="finish_stock_wrapper">
          <div class="distinct_stock_wrapper">
            <div class="distinct_btn_wrapper">
              <div class="distinct_btn" style="background-color:#ffcccc;color:#000" @click="closePage">完 成</div>
            </div>
          </div>
        </div>
        <div v-else :style="{ 'height': '200px' }">
          <div ref="cursor" id="virtualCursor" class="virtualCursor" style="color:transparent;">
            |
          </div>
          <div class="add_box_body">
            <van-row :style="{ 'height': '35px' }"  v-if="showBranchPosition">
              <van-col span="3" style="font-size:14px;">库位:</van-col>
              <van-col span="9" class="flex">
                <van-field
                  class="inputBranchPosition"
                  v-model="itemRow.branch_position_name"
                  right-icon="close"
                  placeholder="库位"
                  @click-input="branchPositionShow = true"
                  :readonly="true"
                  @click-right-icon = "clearBranchPosition"
                />
                <!-- <input type="text" class="input_style" readonly v-model="itemRow.branch_position_name" placeholder="库位"
                  id="inputBranchPosition" @click="branchPositionShow = true" /> -->
              </van-col>
              <van-col span="12" style="font-size:14px;"></van-col>
            </van-row>
            <van-row gutter="10" :style="{ 'height': '35px' }" v-if="itemRow.batch_level">
              <van-col span="5" style="font-size:14px;">产期:</van-col>
              <van-col span="7" class="flex">
                <input type="text" class="input_style" readonly v-model="itemRow.produce_date" :placeholder="'如230101'"
                  id="inputProduceDate" @click="handleInputClick($event, 'produce_date')" /></van-col>
              <van-col span="2" v-if="itemRow.batch_level !== ''"><van-icon size="24px" name="ellipsis" @click="produceDateShow = true" /></van-col>
              <van-col span="1"></van-col>
              <van-col span="3" style="font-size:14px;" v-if="itemRow.batch_level === '2'">批次:</van-col>
              <van-col span="6" class="flex" v-if="itemRow.batch_level === '2'">
                <input type="text" class="input_style" v-model="itemRow.batch_no" :placeholder="'批次'"
                  id="inputBatchNo" @click="handleInputClick($event, 'batch_no')" />
              </van-col>
            </van-row>
            <van-row gutter="10" v-if="itemRow.b_unit_no" :style="{ 'height': '35px' }">
              <van-col span="8" class="flex">
                <input type="text" class="input_style" readonly v-model="itemRow.b_unit_qty" :placeholder="'数量'"
                  id="inputQuantityBig" @click="handleInputClick($event, 'b_unit_qty')">
              </van-col>
              <van-col span="6">
                <span class="input_style">{{ itemRow.b_unit_no }}</span>
              </van-col>
              <van-col span="10">
                <input type="text" class="input_style" v-model="itemRow.bremark" :placeholder="'备注'"
                  @change="onInput('bremark', $event)">
              </van-col>
            </van-row>
            <van-row gutter="10" v-if="itemRow.m_unit_no" :style="{ 'height': '35px' }">
              <van-col span="8" class="flex">
                <input type="text" class="input_style" readonly v-model="itemRow.m_unit_qty" :placeholder="'数量'"
                  id="inputQuantityMin" @click="handleInputClick($event, 'm_unit_qty')">
              </van-col>
              <van-col span="6"><span class="input_style">{{ itemRow.m_unit_no }}</span></van-col>
              <van-col span="10">
                <input type="text" class="input_style" v-model="itemRow.mremark" :placeholder="'备注'"
                  @change="onInput('mremark', $event)">
              </van-col>
            </van-row>
            <van-row gutter="10" :style="{ 'height': '35px' }">
              <van-col span="8" class="flex">
                <input type="text" class="input_style" readonly v-model="itemRow.s_unit_qty" id="inputQuantitySmall"
                  :placeholder="'数量'" @click="handleInputClick($event, 's_unit_qty')">
              </van-col>
              <van-col span="6"><span class="input_style">{{ itemRow.unit_no }}</span></van-col>
              <van-col span="10">
                <input type="text" class="input_style" v-model="itemRow.sremark" :placeholder="'备注'"
                  @change="onInput('sremark', $event)">
              </van-col>
            </van-row>
            <van-row gutter="10" :style="{ height: '35px' }">
              <van-col span="10" class="flex">
                <span style="font-size:14px" class="input_style" v-show="!distinctFlagDom && !shoppingCarFinishFlag
                  ">库存:{{ unitStockQty }}</span>
              </van-col>
              <van-col span="2"></van-col>
              <van-col span="10">
                <span style="font-size:14px" class="input_style" v-show="!distinctFlagDom &&
                  !shoppingCarFinishFlag &&
                  itemRow.unit_conv
                  ">
                  {{ itemRow.unit_conv }}</span>
              </van-col>
            </van-row>
          </div>
        </div>

      </div>
      <!-- <div class="sotck-info" :style="{ 'height': '50px' }">
        <div class="product_delist" v-show="!distinctFlagDom && !shoppingCarFinishFlag">
          <div class="product_delist_wrapper" key="canSeeFromStock">
            <div class="stockInfo-wrapper">
              <div class="stockInfo-title">库存:</div>
              <div class="stockInfo-content-wrapper">
                <div class="stockInfo-content">
                  <div class="stockInfo-content-num">{{ itemRow.current_qty }}</div>
                  <div class="stockInfo-content-unit"></div>
                </div>
              </div>
            </div>
          </div>

        </div>
        <div class="product_unit" v-show="!distinctFlagDom && !shoppingCarFinishFlag">
          <div style="font-size: 14px">
            <span style="font-size: 14px" v-if="itemRow.unit_conv">{{ itemRow.unit_conv }}</span>
          </div>
        </div>
      </div> -->

    </div>
    <!-- 自定义键盘 -->
    <div class="numPad" id="numPad"
      :style="{ 'background-color': ((!distinctFlagDom && !shoppingCarFinishFlag) ? '#ddd' : '#fff') }">
      <div style="width: 100%;height: 100%" v-show="!distinctFlagDom && !shoppingCarFinishFlag">
        <table class="numPad_table" cellspacing="0px" cellpadding="0px">
          <tr>
            <td>
              <div class="numbtn1" @touchend="onNumTouchEnd($event)" @touchstart="onClickNum('1', $event, 0.6)">1</div>
            </td>
            <td>
              <div class="numbtn1" @touchend="onNumTouchEnd($event)" @touchstart="onClickNum('2', $event, 0.6)">2</div>
            </td>
            <td>
              <div class="numbtn1" @touchend="onNumTouchEnd($event)" @touchstart="onClickNum('3', $event, 0.6)">3</div>
            </td>
            <td style="border-right: 0;">
              <div class="numbtn1" @touchend="onNumTouchEnd($event)" @touchstart="onDelNum($event)"
                style="border-right: 0;">
                <svg width="30px" height="30px" fill="#666">
                  <use xlink:href="#icon-backspace"></use>
                </svg>
              </div>
            </td>
          </tr>
          <tr>
            <td>
              <div class="numbtn1" @touchend="onNumTouchEnd($event)" @touchstart="onClickNum('4', $event, 0.6)">4</div>
            </td>
            <td>
              <div class="numbtn1" @touchend="onNumTouchEnd($event)" @touchstart="onClickNum('5', $event, 0.6)">5</div>
            </td>
            <td>
              <div class="numbtn1" @touchend="onNumTouchEnd($event)" @touchstart="onClickNum('6', $event, 0.6)">6</div>
            </td>

            <td rowspan="3" style="border-right: 0;">
              <div style="border-right: 0;" class="numbtn1 save_btn" @touchstart="btnOK_clicked($event)"
                @touchend="onbtnOKClickedEnd($event)">确认
              </div>
            </td>
          </tr>
          <tr>
            <td>
              <div class="numbtn1" @touchend="onNumTouchEnd($event)" @touchstart="onClickNum('7', $event, 0.6)">7</div>
            </td>
            <td>
              <div class="numbtn1" @touchend="onNumTouchEnd($event)" @touchstart="onClickNum('8', $event, 0.6)">8</div>
            </td>
            <td>
              <div class="numbtn1" @touchend="onNumTouchEnd($event)" @touchstart="onClickNum('9', $event, 0.6)">9</div>
            </td>
          </tr>
          <tr>
            <td colspan="2">
              <div class="numbtn1" @touchend="onNumTouchEnd($event)" @touchstart="onClickNum('0', $event, 0.6)">0</div>
            </td>
            <td>
              <div class="numbtn1" @touchend="onNumTouchEnd($event)" @touchstart="onClickNum('.', $event, 0.6)">.</div>
            </td>
          </tr>
        </table>
      </div>
    </div>
    <!-- 自定义键盘 -->
    <van-popup v-model="produceDateShow" position="bottom" :style="{ width: '100%', height: '50%' }">
      <ProduceDate :ItemInfo="itemRow" :batchStock="batchStockListForShow" @setDateAndBatchNo="setDateAndBatchNo">
      </ProduceDate>
    </van-popup>
    <van-popup v-model="branchPositionShow" position="bottom" :style="{ width: '100%', height: '50%' }">
      <BranchInfoCascader :branchPositionSource="branchPositionSource"  :title="'请选择库位'" @handleCloseBranchInfo="handleCloseBranchInfo" @setItemRowBranchInfo="setItemRowBranchInfo">
      </BranchInfoCascader>
    </van-popup>
  </div>
</template>

<script>
import { CreateItemsForAttrRows,GetBatchStock,GetBranchPosition,GetBranchPositionForReturn } from "../../api/api";
import { Col, Row, Toast, Dialog, Icon,Field } from 'vant'
import InventoryMixin from "./InventoryMixin";
import mixins from '../SaleSheet/sheetMixin/mixin'
import ProduceDate from '../components/produceDate/ProduceDate.vue'
import BranchInfoCascader from '../components/BranchInfoCascader/BranchInfoCascader.vue'
export default {
  name: "AddInventoryChangeSheetRow",
  mixins: [InventoryMixin, mixins],
  data() {
    return {
      itemRow: {},
      itemRowCopy: {},// 通过对itemRow拷贝，记录信息，供还原使用
      editingItemRowIndex: null,
      curObject: {},
      curProp: '',
      curInput: null,
      audio_num: [], // iOS下audio_num设为数组已降低声音播放延迟
      audio_del: {},
      audio_comfirm: {},
      invokeComputedVar: '',
      smallUnitStock: '',
      produceDateShow: false,
      loop: 0,
      batchStock: [],
      branchPositionShow:false,
      showBranchPosition:false,
      branchPositionSource:[],
      curBranchId:"",
      batchStockListForShow:[],
    }
  },
  components: {
    "van-field": Field,
    "van-row": Row,
    "van-col": Col,
    "van-icon": Icon,
    ProduceDate,
    BranchInfoCascader,
  },
  props: {
    sheet: {
      type: Object
    },
  },
  watch: {},
  computed: {
    isShowNegativeStock(){
      return this.$store.state.operInfo.setting && this.$store.state.operInfo.setting.showNegativeStock && this.$store.state.operInfo.setting.showNegativeStock=="True"?true:false
    },
    attrShowFlag() {
      return this.$store.state.attrShowFlag
    },
    shoppingCarFinishFlag() {
      return this.$store.state.shoppingCarFinish
    },
    noItemIdSheetRows() {
      return this.$store.state.noItemIdSheetRows
    },
    distinctFlagDom() {
      return this.itemRow.distinctStockFlag && !this.attrShowFlag && !this.shoppingCarFinishFlag
    },
    showBtnAttrName() {
      let result = ""
      if (this.itemRow.mum_attributes) {
        result = this.itemRow.mum_attributes.map(obj => {
          return obj.attrName
        }).join("/");
      }
      return result
    },
    unitStockQty(){
      let branch_id = this.itemRow.branch_id
      let branch_position = this.itemRow.branch_position
      let produce_date = this.itemRow.produce_date
      let batch_no = this.itemRow.batch_no
      let stockQty = this.batchStock.filter(bs=>(bs.branch_id==branch_id&&bs.branch_position==branch_position&&bs.produce_date==produce_date&&bs.batch_no==batch_no))
      return stockQty.length?stockQty[0].stock_qty_unit:"0"
    },
  },
  mounted() {
    this.initAudio()
    this.branchPositionSource = this.$store.state.curBranchPositionList??[]
    this.showBranchPosition = this.branchPositionSource.length?true:false
  },
  methods: {
    initAudio() {
      if (window.isiOS && window.Media) {
        var that = this
        that.audio_num = []
        var audio_size = 3;
        for (let i = 0; i < audio_size; ++i) {
          that.audio_num.push(new Media(`num${i}.wav`))
        }
        that.audio_num.audio_size = audio_size
        that.audio_num.audio_index = 0
        that.audio_num.play = function () {
          console.log('i am played:-)' + this.audio_index)
          this[this.audio_index].play()
          this.audio_index = (this.audio_index + 1) % this.audio_size
        }
        that.audio_del = new Media(`del.mp3`)
        that.audio_comfirm = new Media(`OK.mp3`)
      } else {
        this.audio_num = new Howl({
          src: ['num-3.wav'],
          preload: true
        })
        this.audio_del = new Howl({
          src: ['del.mp3'],
          preload: true
        })
        this.audio_comfirm = new Howl({
          src: ['OK.mp3'],
          preload: true
        })
      }
    },
    loadData(item) {
      this.curObject = {}
      this.curProp = ''
      this.curInput = null
      item.produce_date = item.produce_date ? item.produce_date.slice(0,10): "";
      item.batch_no = item.batch_no ? item.batch_no : "";
      item.batch_id = item.batch_id?item.batch_id:"0"
      this.itemRow = item;
      this.itemRow = Object.assign({},this.itemRow,{
        branch_position:item.branch_position?item.branch_position:"0",
        branch_position_name:item.branch_position_name?item.branch_position_name:""
      })
      this.itemRowCopy = JSON.stringify((this.itemRow))
      if(!this.itemRow.b_unit_qty && !this.itemRow.m_unit_qty && !this.itemRow.s_unit_qty){
        this.setDefaultBranchPositionForSheet()
      }
      let that = this
      setTimeout(() => {
        if (that.itemRow.distinctStockFlag && !that.attrShowFlag) {
        } else {
          var $inputQty = $('#inputQuantityBig')
          if ($inputQty.length === 0) {
            $inputQty = $('#inputQuantitySmall')
            that.handleInputClick({ target: $inputQty[0] }, "s_unit_qty")
          } else {
            that.handleInputClick({ target: $inputQty[0] }, "b_unit_qty")
          }
          that.$forceUpdate()
        }
      }, 1);
      this.$forceUpdate()
    },
    formatDate() {
      if (this.curObject !== 'produce_date') {
        let dt = this.itemRow["produce_date"]
        if (this.itemRow["produce_date"] && this.itemRow["produce_date"].length == 6) {
          if(this.itemRow.batch_level == "2" && !this.itemRow.batch_no){
            this.itemRow.batch_no = dt
          }
          dt = '20' + dt.substr(0, 2) + '-' + dt.substr(2, 2) + '-' + dt.substr(4, 2) 
        }
       this.itemRow["produce_date"] = dt
      }
    },
    handleInputClick(event, key) {
      var value = event?.target?.value === undefined ? '' : event.target.value
      if (this.itemRow[key] === undefined) {
        this.itemRow[key] = ''
      }
      this.curObject = key
      this.curInput = event.target
      event.target.selectionStart = 0
      event.target.selectionEnd =value.length
      this.formatDate()
      if (value || key == "remark" || key == "batch_no") {
        this.$refs.cursor.style.display = "none"
      } else {
        this.$refs.cursor.style.display = "block"
      }
      this.curInput.before(this.$refs.cursor)
      this.$refs.cursor.style.left = isiOS ? '10px' : '5px'
      this.$forceUpdate()
    },
    onInput(obj, e) {
      if (obj === "bremark") {
        this.itemRow.bremark = e.target.value
      } else if (obj === "mremark") {
        this.itemRow.mremark = e.target.value
      } else if (obj === "sremark") {
        this.itemRow.sremark = e.target.value
      }
      this.handleInputClick(e,obj)
    },
    btnOK_clicked() {
      let err = ""
      this.curObject=''
      this.formatDate()
      var reg = /^(\d{4})-(\d{2})-(\d{2})$/;
      let produceDate = this.itemRow.produce_date? this.itemRow.produce_date:""
      if (!produceDate.match(reg) && produceDate !== "") {
        Toast.fail('日期格式错误')
        return
      }
      err = this.checkUnitInputValidity()
      if (err !== "") {
        Toast(err)
        return
      }
      
      console.log(this.attrShowFlag)
      if (this.attrShowFlag) {
        // 将没有item_id的商品收集起来，点击返回的时候调用
        if (this.itemRow.item_id.startsWith("nanoid")) {
          this.handleNoItemIdToStore()
        } else {
          
          this.handleInputItemToSheetRows()
        }
        setTimeout(() => {
          this.$emit('onAddAttrRowOK')
        }, 1);
      } else {
        
        this.handleInputItemToSheetRows()
        
        setTimeout(() => {
          this.$emit('onAddRow_OK')
        }, 1);
      }
      setTimeout(() => {
        this.saveCurSheetToCache(this.$store.state.currentSheet)
      }, 1)
    },
    btnDel_click() {
      Dialog.confirm({
        title: '删除',
        message: '确认删除改商品吗?',
        width:"320px"
      })
        .then(() => {
          // on confirm
          this.$emit("delIndexItem", this.editingItemRowIndex)
        })
    },
    closePage() {
      if (this.attrShowFlag) {
        if (this.noItemIdSheetRows.length !== 0) {
          this.handleNoItemIdRowsToSheetRows()
        }
        setTimeout(() => {
          if (this.itemRow.singleChoice && this.attrShowFlag) {
            this.$emit("closeAttrSingleChoicePage")
          } else {
            this.$emit("closeAttrPage")
          }
        }, 1);
      } else {
        // 判断是否还存在为完成的商品
        //let finishFlagAll = ();
        if (this.itemRow.singleChoice && !this.attrShowFlag) {
          this.$emit("closeSingleChoicePage")
        } else {
          if (this.shoppingCarFinishFlag) {
            this.$emit("closePage")
          } else {
            Dialog.confirm({
              message: '存在未输入数量商品，是否继续退出？',
              width:'320px'
            }).then(() => {
              this.$emit("closePage")
            })
          }

        }

      }
    },
    onClickNum(value, e) {
      if(this.curObject=="sremark"||this.curObject=="batch_no") return
      if (this.$refs?.cursor !== undefined) {
        this.$refs.cursor.style.display = "block"
        $(e.currentTarget).css('background-color', '#ccc')

        if (e.preventDefault) {
          e.preventDefault()
          e.stopPropagation()
        }
        if (this.audio_num.play) {
          this.audio_num.play()
        }
        if (this.curInput && this.curInput.value && this.curInput.selectionStart === 0 && this.curInput.selectionEnd === this.curInput.value.length) {
          this.itemRow[this.curObject] = ''
        }
        this.itemRow[this.curObject] = this.itemRow[this.curObject] + value
        if (this.curObject == "virtual_produce_date" || this.curObject == 'produce_date') {
          this.$forceUpdate()
          var dt = this.itemRow[this.curObject]
          if (dt.length == 6) {
            dt = '20' + dt.substr(0, 2) + '-' + dt.substr(2, 2) + '-' + dt.substr(4, 2) 
            this.itemRow[this.curObject] = dt
          }
        }
        var f = $(this.curInput).css('font-size')
        var left = this.getStrWidth(f, this.itemRow[this.curObject])
        this.$refs.cursor.style.left = left + (isiOS ? 10 : 5) + 'px'
        this.invokeComputedVar = ""
        this.$forceUpdate()
      }
    },
    onDelNum(e) {
      $(e.currentTarget).css('background-color', '#e0e0e0')
      this.itemRow[this.curObject] = ''
      this.$refs.cursor.style.left = isiOS ? '10px' : '5px'
      //this.updateStock()
      this.invokeComputedVar = ""
      this.$forceUpdate()
    },
    onNumTouchEnd(e) {
      $(e.currentTarget).css('background-color', '#ddd')
    },
    onbtnOKClickedEnd(e) {
      $(e.currentTarget).css('background-color', '#3a3a3a')
    },
    getStrWidth(fontSize, str) {
      const dom = document.createElement('span')
      dom.style.display = 'inline-block'
      dom.textContent = str
      $(dom).css('font-size', fontSize);
      document.body.appendChild(dom)
      const width = dom.clientWidth
      document.body.removeChild(dom)
      return width
    },
    handleInputItemToSheetRows(item = {}) {
      let currentSheet = this.$store.state.currentSheet
      let tempSheetRows = []
      if (JSON.stringify(item) === '{}') {
        item = this.itemRow
      }
      rowUnitsProps2Array(item)
      
      for (let i = 0; i < tempSheetRows.length; i++) {
        let tempItem = tempSheetRows[i]
        let pushFlag = true
        for (let j = 0; j < currentSheet.sheetRows.length; j++) {
          let currentSheetRow = currentSheet.sheetRows[j]
          if (currentSheetRow.nanoid && currentSheetRow.nanoid.length > 2) {
            if (currentSheetRow.nanoid === tempItem.nanoid) {
              currentSheetRow.son_item_id = tempItem.son_item_id
              currentSheetRow.son_item_name = tempItem.son_item_name
              currentSheetRow.item_id = tempItem.item_id
              currentSheetRow.item_name = tempItem.item_name
              currentSheetRow.quantity = tempItem.quantity

              // 此处需要测试
              currentSheetRow.wholesale_amount = tempItem.wholesale_amount
              currentSheetRow.cost_amount_avg = tempItem.cost_amount_avg
              currentSheetRow.buy_amount = tempItem.buy_amount
              pushFlag = false
            }
          } else {
            if (
              tempItem.item_id === currentSheetRow.item_id &&
              tempItem.unit_no === currentSheetRow.unit_no && 
              tempItem.produce_date === currentSheetRow.produce_date &&
              tempItem.batch_no === currentSheetRow.batch_no &&
              tempItem.branch_position ===currentSheetRow.branch_position
            ) {
              if (this.itemRow.isSelectFlag) {
                currentSheet.sheetRows[j] = { ...tempItem }
                pushFlag = false
              } else {
                if (tempItem.remark === currentSheetRow.remark) {
                  currentSheet.sheetRows[j].quantity = Number(currentSheet.sheetRows[j].quantity) + Number(tempItem.quantity)
                  currentSheet.sheetRows[j].wholesale_amount = Number(currentSheet.sheetRows[j].wholesale_amount) + Number(tempItem.wholesale_amount)
                  currentSheet.sheetRows[j].cost_amount_avg = Number(currentSheet.sheetRows[j].cost_amount_avg) + Number(tempItem.cost_amount_avg)
                  currentSheet.sheetRows[j].buy_amount = Number(currentSheet.sheetRows[j].buy_amount) + Number(tempItem.buy_amount)
                  pushFlag = false
                }
              }
              break
            }
          }

        }
        if (pushFlag) {
          currentSheet.sheetRows.push(tempItem)
        }
      }
      for (let i = 0; i < currentSheet.sheetRows.length; i++) {
        let row = tempSheetRows[i]
 

        // 后端已经根据 recentPriceTime 设置选择了对应的平均价，前端直接使用即可
        // row.cost_price_recent 已经是处理后的数值，无需再次解析

        var costPriceType =window.getSettingValue('costPriceType') 
        if(!costPriceType) costPriceType=this.sheet.cost_price_type
        if(!costPriceType) costPriceType='3'

        var cost_price=0
        if (costPriceType === '2') {
            cost_price = row.cost_price_avg
          }
          else if (costPriceType === '3') {
            cost_price = row.cost_price_buy
          }
          else if (costPriceType === '1') {
            cost_price = row.cost_price_prop
          }
          else if (costPriceType === '4') {
            cost_price = row.cost_price_recent
          }

          row.cost_price=cost_price
          row.cost_amount=cost_price * row.unit_factor * row.quantity
      }
      this.sheet.sheetRows = currentSheet.sheetRows
      this.$store.commit("currentSheet", currentSheet)
      this.handleSelectedSheetRows('BS')
      this.$forceUpdate()
      if (item.singleChoice && !this.attrShowFlag) {
        this.$emit("closeSingleChoicePage")
      }
      function rowUnitsProps2Array(item) {
        if (item.b_unit_no && item.b_unit_qty) {
          tempSheetRows.push({
            b_barcode: item.b_barcode,
            m_barcode: item.m_barcode,
            s_barcode: item.s_barcode,
            b_unit_factor: item.b_unit_factor,
            m_unit_factor: item.m_unit_factor,
            s_unit_factor: 1,
            item_id: item.item_id,
            item_name: item.item_name,
            unit_no: item.b_unit_no,
            unit_factor: item.b_unit_factor,
            inout_flag: -1,
            quantity: item.b_unit_qty,
            wholesale_price: item.b_wholesale_price,
            wholesale_amount: Number(item.b_unit_qty) * Number(item.b_wholesale_price),
            cost_price_prop: item.cost_price_spec,  
            cost_price_avg: Number(item.cost_price_avg),
            cost_price_buy: item.cost_price_buy,
            cost_price_recent: item.cost_price_recent,
            cost_price_prop_unit: (Number(item.cost_price_prop)*item.b_unit_factor)||0,
            cost_price_avg_unit: (Number(item.cost_price_avg)*item.b_unit_factor)||0,
            cost_price_buy_unit: (Number(item.cost_price_buy)*item.b_unit_factor)||0,
            cost_price_recent_unit:(Number(item.cost_price_recent)*item.b_unit_factor)||0,

            //cost_amount_avg: Number(item.b_unit_qty) * Number(item.b_unit_factor) * Number(item.cost_price_avg),
            //buy_price: item.b_buy_price,
           // buy_amount: Number(item.b_unit_qty) * Number(item.b_buy_price),
            stock_qty_unit: item.current_qty,
            remark: item.bremark,
            mum_attributes: item.mum_attributes,
            itemImages: item.item_images,
            unit_type: 'b',
            son_mum_item: item.son_mum_item,
            nanoid: "b_" + (item.nanoid ? item.nanoid : ''),
            produce_date: item.produce_date,
            batch_no: item.batch_no,
            batch_id: item.batch_id,
            batch_level:item.batch_level,
            branch_position:item.branch_position,
            branch_position_name:item.branch_position_name,
          })
        }
        if (item.m_unit_no && item.m_unit_qty) {
          tempSheetRows.push({
            b_barcode: item.b_barcode,
            m_barcode: item.m_barcode,
            s_barcode: item.s_barcode,
            b_unit_factor: item.b_unit_factor,
            m_unit_factor: item.m_unit_factor,
            s_unit_factor: 1,
            item_id: item.item_id,
            item_name: item.item_name,
            unit_no: item.m_unit_no,
            unit_factor: item.m_unit_factor,
            inout_flag: -1,
            quantity: item.m_unit_qty,
            wholesale_price: item.m_wholesale_price,
            wholesale_amount: Number(item.m_unit_qty) * Number(item.m_wholesale_price),

            cost_price_prop: item.cost_price_spec,  
            cost_price_avg: Number(item.cost_price_avg),
            cost_price_buy: item.cost_price_buy,
            cost_price_recent: item.cost_price_recent,
            cost_price_prop_unit: (Number(item.cost_price_prop)*item.b_unit_factor)||0,
            cost_price_avg_unit: (Number(item.cost_price_avg)*item.b_unit_factor)||0,
            cost_price_buy_unit: (Number(item.cost_price_buy)*item.b_unit_factor)||0,
            cost_price_recent_unit:(Number(item.cost_price_recent)*item.b_unit_factor)||0,

            stock_qty_unit: item.current_qty,
            remark: item.mremark,
            mum_attributes: item.mum_attributes,
            itemImages: item.item_images,
            unit_type: 'm',
            son_mum_item: item.son_mum_item,
            nanoid: "m_" + (item.nanoid ? item.nanoid : ''),
            produce_date: item.produce_date,
            batch_no: item.batch_no,
            batch_id: item.batch_id,
            batch_level:item.batch_level,
            branch_position:item.branch_position,
            branch_position_name:item.branch_position_name,
          })
        }
        if (item.unit_no && item.s_unit_qty) {
          tempSheetRows.push({
            b_barcode: item.b_barcode,
            m_barcode: item.m_barcode,
            s_barcode: item.s_barcode,
            b_unit_factor: item.b_unit_factor,
            m_unit_factor: item.m_unit_factor,
            s_unit_factor: 1,
            item_id: item.item_id,
            item_name: item.item_name,
            unit_no: item.unit_no,
            unit_factor: 1,
            inout_flag: -1,
            quantity: item.s_unit_qty,
            wholesale_price: item.s_wholesale_price,
            wholesale_amount: Number(item.s_unit_qty) * Number(item.s_wholesale_price),

            cost_price_prop: item.cost_price_spec,  
            cost_price_avg: Number(item.cost_price_avg),
            cost_price_buy: item.cost_price_buy,
            cost_price_recent: item.cost_price_recent,
            cost_price_prop_unit: (Number(item.cost_price_prop)*item.b_unit_factor)||0,
            cost_price_avg_unit: (Number(item.cost_price_avg)*item.b_unit_factor)||0,
            cost_price_buy_unit: (Number(item.cost_price_buy)*item.b_unit_factor)||0,
            cost_price_recent_unit:(Number(item.cost_price_recent)*item.b_unit_factor)||0,

            stock_qty_unit: item.current_qty,
            remark: item.sremark,
            mum_attributes: item.mum_attributes,
            itemImages: item.item_images,
            unit_type: 's',
            son_mum_item: item.son_mum_item,
            nanoid: "s_" + (item.nanoid ? item.nanoid : ''),
            produce_date: item.produce_date,
            batch_no: item.batch_no,
            batch_id: item.batch_id,
            batch_level:item.batch_level,
            branch_position:item.branch_position,
            branch_position_name:item.branch_position_name,
          })
        }
      }
    },
    handleAttr() {
      this.$emit("handleAttr")
    },
    handleNoItemIdToStore() {
      // 进行数据合并
      let sheetRow = this.itemRow
      var row = this.noItemIdSheetRows.find((value, index, arr) => {
        return value.item_name === sheetRow.item_name &&
          value.b_unit_no === sheetRow.b_unit_no &&
          value.bremark === sheetRow.bremark &&
          value.m_unit_no === sheetRow.m_unit_no &&
          value.mremark === sheetRow.mremark &&
          value.sunit === sheetRow.sunit &&
          value.unit_no === sheetRow.unit_no &&
          value.nanoid === sheetRow.nanoid
      })
      if (row) {
        row.b_unit_qty = sheetRow.b_unit_qty
        row.m_unit_qty = sheetRow.m_unit_qty
        row.s_unit_qty = sheetRow.s_unit_qty
      } else {
        this.noItemIdSheetRows.push(sheetRow)
      }
      this.$store.commit("noItemIdSheetRows", this.noItemIdSheetRows)
      this.handleInputItemToSheetRows()
    },
    async handleNoItemIdRowsToSheetRows() {
      let that = this
      let item_id = this.noItemIdSheetRows[0].son_mum_item
      let mum_attributes = this.noItemIdSheetRows[0].mum_attributes
      let attrRows = []
      this.noItemIdSheetRows.forEach(item => {
        attrRows.push(item.attr_qty)
      })
      let params = {
        item_id: item_id,
        attrRows: attrRows,
        operKey: this.$store.state.operKey,
        attrs: typeof mum_attributes == 'string' ? JSON.parse(mum_attributes) : mum_attributes
      }
      await CreateItemsForAttrRows(params).then(res => {
        if (res.result === "OK") {
          let sonRows = res.sonRows
          sonRows.forEach(son => {
            let row = that.noItemIdSheetRows.find(r => r.son_options_id === son.son_options_id)
            if (row) {
              row.son_item_id = son.son_item_id
              row.son_item_name = son.son_item_name
              row.item_id = row.son_item_id.split(",")[0]
              row.item_name = row.son_item_name
            }
          })
        }
      }).catch(err => {
        Toast(err)
      })
      this.noItemIdSheetRows.forEach(item => {
        delete item.attr_qty
        this.handleInputItemToSheetRows(item)
      })
      this.$store.commit("noItemIdSheetRows", [])
    },
    checkUnitInputValidity() {
      let err = ''
      if (this.itemRow.b_unit_qty && !checkInputValidity(this.itemRow.b_unit_qty)) {
        err = "【" + this.itemRow.b_unit_no + "】【数量】输入错误,请检查"
        return err
      }
      if (this.itemRow.m_unit_qty && !checkInputValidity(this.itemRow.m_unit_qty)) {
        err = "【" + this.itemRow.m_unit_no + "】【数量】输入错误,请检查"
        return err
      }
      if (this.itemRow.s_unit_qty && !checkInputValidity(this.itemRow.s_unit_qty)) {
        err = "【" + this.itemRow.unit_no + "】【数量】输入错误,请检查"
        return err
      }
      return err
    },
    setDateAndBatchNo(value) {
      this.produceDateShow = false
      this.itemRow.produce_date = value.produce_date
      this.itemRow.batch_no = value.batch_no
      this.itemRow.batch_id = value.produce_date + value.batch_no
    },
    // 严格产期长按
    handlerTouchstart() {
      this.loop = setTimeout(() => {
        this.loop = 0
        this.produceDateShow = true
      }, 500) // 定时的时间
      return false
    },
    handlerTouchmove() {
      // 清除定时器
      clearTimeout(this.loop)
      this.loop = 0
    },
    handlerTouchend(event) {
      // 清除定时器
      clearTimeout(this.loop)
      if (this.loop !== 0) {
        // 单击操作
        this.handleInputClick(event, 'produce_date')
      }
    },
    handleCloseBranchInfo(){
     this.branchPositionShow = false
    },
    setItemRowBranchInfo(branchPositionObj,branchObj){
      this.handleCloseBranchInfo()
      if( this.itemRow.branch_position == branchPositionObj.branch_position) return
      this.itemRow.branch_position = branchPositionObj.branch_position
      this.itemRow.branch_position_name = branchPositionObj.branch_position_name
      this.setBatchStockByBranch()
    },
    setDefaultBranchPositionForSheet(){
      const setting = this.$store.state.operInfo.setting
      let defaultBranchPositionType = setting&&setting.defaultBranchPositionType?Number(setting.defaultBranchPositionType):-1
      let bpFlag = this.branchPositionSource.some(bp=>{
        if(bp.type_id == defaultBranchPositionType){
          this.itemRow.branch_position = bp.branch_position
          this.itemRow.branch_position_name = bp.branch_position_name
          return true
        }
      })
      if(!bpFlag){
        this.itemRow.branch_position = "0"
        this.itemRow.branch_position_name = ""
      }
      this.setBatchStockByBranch()
    },
    setBatchStockByBranch(){
      let params = {
        item_id:this.itemRow.item_id,
        branch_id:this.itemRow.branch_id?this.itemRow.branch_id:this.sheet.branch_id,
        branch_position:this.itemRow.branch_position?this.itemRow.branch_position:0,
        isShowNegativeStock:this.isShowNegativeStock,
      }
      GetBatchStock(params).then(res=>{
        if(res.result=="OK"){
          this.batchStock = res.data.batchStockTotal
          this.batchStockListForShow = res.data.batchStock
        }
      })
    },
    clearBranchPosition(){
      this.itemRow.branch_position = 0
      this.itemRow.branch_position_name = ""
      this.setBatchStockByBranch()
    },
  }
}

</script>

<style lang="less" scoped>
//该页面屏蔽大字体模式 px改为PX
[v-cloak] {
  display: none;
}

@flex_a_j: {
  display: flex;
  align-items: center;
  justify-content: center;
}

;

@flex_a_bw: {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

;

@flex_a_jend: {
  display: flex;
  align-items: center;
  justify-content: flex-end;
}

;

.add_goods_box {
  //height: calc(100% - 100px);
  //overflow: auto;
  padding: 10PX;

  li {
    height: auto;
    overflow: hidden;
    margin: 0 auto 10PX auto;
    background: #ffffff;
    border-radius: 4PX;
    background: #ffffff;

    h4 {
      height: auto;
      min-height: 35PX;
      padding: 0 10PX;
      border-bottom: 1PX solid #f2f2f2;
      @flex_a_j();

      span {
        font-size: 16PX;
        font-weight: normal;
        color: #333333;
      }
    }

    .add_boxs {
      height: auto;
      overflow: hidden;
      padding: 10PX;

      .date_input {
        padding-left: 0;
        padding-right: 0;
      }

      .date_input_son {
        padding: 5PX 0;
      }
    }

    .add_boxs_footer {
      height: auto;
      overflow: hidden;
      @flex_a_bw();
      padding: 10PX;

      .add_boxs_footer_l {
        h5 {
          font-size: 12PX;
          text-align: left;
          font-weight: normal;
        }

        h5:last-child {
          margin-top: 5PX;
        }
      }

      .add_boxs_footer_r {
        button:last-child {
          margin-left: 10PX;
        }
      }
    }
  }
}

.preservationItem {
  width: 95%;
  position: fixed;
  // height: 95px;
  bottom: 0;
  float: right;
  padding: 5PX 10PX;

  button {
    //border-radius: 50px;
    margin-bottom: 5PX;
  }
}

.add_goods_box_ms {
  width: 100%;
  height: 100%;
  //overflow-y: auto;
  //overflow-x: hidden;
}

/deep/ .van-action-sheet__item {
  height: 60PX;
}

/deep/ .van-action-sheet__cancel {
  margin-bottom: 20PX;
}

@flex_a_j: {
  display: flex;
  align-items: center;
  justify-content: center;
}

;

@flex_a_flex: {
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
}

;

@flex_a_bw: {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

;

@flex_cfs: {
  display: flex;
  align-items: center;
  justify-content: flex-start;
}

;

@span_input: {
  span {
    height: inherit;
    @flex_a_j();
  }

  input {
    width: 100%;
    height: 100%;
    outline: none;
    border: none;
    border-bottom: 1PX solid #dddddd;
    vertical-align: top;
    // text-align: center;
  }
}

;

.add_goods_box {
  display: flex;
  flex-direction: column;
  justify-content: flex-end;

  .item_name_content {
    border-bottom: 1PX solid #eee;
    height: 29PX;
    display: flex;
    align-items: center;

    .item_name {
      flex: 9;
      height: 100%;
      overflow-y: auto;
      font-size: 15PX;
      white-space: nowrap;
      display: flex;
      align-items: center;
      justify-content: center;
    }
  }

  .item_name_content_onlyshow {
    display: flex;
    width: 90%;
    text-align: center;
    justify-content: center;
    padding: 0 10PX;
  }

  .item_name_option {
    display: flex;
    justify-content: space-between;
    height: 40PX;
    align-items: center;

    .bReturn {
      flex: 1;
      display: flex;
      align-items: center;
      justify-content: center;
      height: 30PX;

      .breturn_X {
        display: flex;
        align-items: center;
        justify-content: center;
        background: #fff;
        border: 1PX solid #d8d8d8;
        border-radius: 10PX;
        width: 65%;
        height: 26PX;
      }

      .breturn_T {
        display: flex;
        align-items: center;
        justify-content: center;
        background: #f66;
        color: #fff;
        border-radius: 10PX;
        width: 65%;
        height: 26PX;
      }
    }

    .bReturnHidden {
      visibility: hidden;
    }

    .more_option {
      flex: 3;
      display: flex;
      align-items: center;

      .more_option_1,
      .more_option_2,
      .more_option_3 {
        flex: 1;
        display: flex;
        justify-content: center;
        align-items: center;
      }

      .option_attr {
        display: flex;
        color: #000;
        align-items: center;
        justify-content: center;
        background-color: #fde3e4;

        border-radius: 10PX;
        width: 65%;
        height: 26PX;
      }

      .close_icon_font {
        display: flex;
        align-items: center;
        justify-content: center;
        background: #eee;
        border: 1px solid #eee;
        border-radius: 10PX;
        width: 65%;
        height: 26PX;
      }
    }
  }

  .close_icon_font_wrapper {
    position: absolute;
    left: 5PX;
    top: 10PX;
    z-index: 999999;

    .close_icon_font {
      box-sizing: border-box;
      color: #fff;
      font-size: 15PX;
      display: flex;
      align-items: center;
      justify-content: flex-start;
      background: rgba(0, 0, 0, 0.7);
      font-weight: bolder;
      //border: 1px solid #aaa;
      border-radius: 10px;
      width: 40PX;
      height: 26PX;
      padding-left: 5PX;

      .font_wrapper {
        font-size: 14PX;
      }
    }
  }

  .add_box {
    // padding: 0 10px;

    .add_box_title {
      height: 20PX;
      font-size: 15PX;
      @flex_cfs();
      margin: 10PX 10PX 25PX 0;

      .van-col {
        height: inherit;
        vertical-align: top;
        @span_input();

        input {
          text-align: left;
          border-radius: 0PX;
        }
      }
    }

    .add_box_body {
      height: auto;
      font-size: 15PX;
      box-sizing: border-box;
      padding: 0 5PX;

      .van-row {
        height: 30PX;
        margin: 5PX 0;
        @span_input();
      }
      .inputBranchPosition{
        padding:0px;
        border-bottom:1px solid #ddd;
      }

      margin-bottom: 10PX;
    }

    .distinct_stock_wrapper {
      display: flex;
      height: 100%;
      flex-direction: column;
      justify-content: center;

      .distinct_msg_wrapper {
        flex: 1;
        display: flex;
        align-items: center;
        justify-content: center;
        color: #aaa;
      }

      .distinct_btn_wrapper {
        flex: 2;
        display: flex;
        align-items: flex-end;
        justify-content: center;

        .distinct_btn {
          min-width: 50%;
          height: 65%;
          padding: 0 15PX;
          display: flex;
          align-items: center;
          justify-content: center;
          // border: 1px solid #eee;
          background-color: #fde3e4;
          font-size: 20PX;
          //font-weight: bolder;
          border-radius: 10PX;
        }
      }
    }
  }

  .add_box_aside {
    width: 100%;
    height: 40PX;
    box-sizing: border-box;
    font-size: 12PX !important;
    font-weight: normal;
    @flex_a_bw();
    padding: 0 5PX;

    .aside_left {
      @flex_cfs();
      color: gray;
    }

    div {
      color: gray;
    }
  }

  .item_history_btn {
    display: flex;
    justify-content: flex-end;
    padding-right: 5PX;
  }
}

.show_order_msg {
  text-align: center;
  border-radius: 5PX;
  padding: 0 2PX;
  line-height: 30PX;
  font-size: 16PX;
  color: rgba(245, 108, 108, 0.8);
}

.numPad {
  width: 100%;
  height: 225PX;
  // position: fixed;
  // bottom: 0;
  font-family: numfont;
  background-color: #ddd;
  display: flex;

  .numPad_table {
    width: 100%;
    height: 100%;
    border-bottom: 1PX solid #ccc;

    td {
      width: 25%;
      height: 25%;
      border-right: 1PX solid #ccc;
      border-top: 1PX solid #ccc;
      padding: 0;

      .numbtn1 {
        width: 100%;
        height: 100%;
        display: flex;
        font-size: 22PX;
        justify-content: center;
        align-items: center;
      }

      .numbtn2 {
        width: 100%;
        height: 100%;
      }

      .save_btn {
        background-color: #555;
        // font-family: font;
        font-weight: 400;
        color: rgb(245, 245, 245);
      }
    }
  }

  .shopping_car_finish {
    background-color: #19952c;
    border-color: #19952c;
    color: #fff;
  }
}

.goodes_no_box {
  height: 100%;
  @flex_a_flex();
  background: #f2f2f2;
  color: #999999;

  .whole_box_no_icon {
    font-size: 50PX;
  }

  p {
    font-size: 14PX;
  }
}

.picker_remark {
  width: 90%;
  height: 25PX;
  font-size: 15PX;
  outline: none;
  border: none;
  border: 2PX solid #cccccc;
  background: #f2f2f2;
  border-radius: 5PX;
  text-align: center;
  margin-bottom: 10PX;
}

::-webkit-input-placeholder {
  /* WebKit browsers */
  color: #d3d3d3;
  font-size: 14PX;
}

@-webkit-keyframes cursorPlay

/* Safari and Chrome */
  {
  0% {
    background: #00f;
  }

  50% {
    background: #00f;
  }

  51% {
    background: #ffffff;
  }

  100% {
    background: #ffffff;
  }
}

.virtualCursor {
  display: inline-block;
  width: 2PX;
  height: 20PX;
  position: relative;
  top: 0;
  left: 0;
  background: #000;
  -webkit-animation: cursorPlay 0.5s 0.5s infinite alternate;
}

.input_style {
  font-size: 16PX;
}

.input_style::-webkit-input-placeholder {
  font-size: 16PX;
}

// .input_style:active{
//   text-align: left;
// }
.flex {
  display: flex;
  justify-content: flex-start;
}

.product_delist {
  display: flex;
  justify-content: space-between;
  height: 25PX;

  .product_delist_wrapper {
    flex: 1;

    .stockInfo-wrapper {
      display: flex;
      flex: 1;
      align-items: flex-end;
      // border: 1px dashed #cccccc;
      border-radius: 5PX;
      height: 25PX;
      box-sizing: border-box;
      padding: 0 5PX 0 0;

      .stockInfo-title {
        width: 40PX;
        height: 25PX;
        display: flex;
        justify-content: center;
        align-items: center;
        font-size: 12PX;
        color: #888;
      }

      .stockInfo-content-wrapper {
        display: flex;
        width: 100%;
        height: 100%;
        font-size: 14PX;

        .stockInfo-content {
          height: 100%;
          display: flex;
          align-items: center;
          color: #888;
          margin: 0 5PX 0 0;
          justify-content: flex-end;

          .stockInfo-content-num {
            font-family: numfont;
            font-size: 14PX;
            // line-height: 30px;
          }

          .stockInfo-content-unit {
            font-size: 14PX;
            // font-size: 14px;
            // line-height: 30px;
          }
        }
      }
    }
  }
}

.product_unit {
  display: flex;
  color: #888;
  font-family: numfont;
  font-size: 14PX;
  justify-content: flex-end;
}</style>


<!--
b_barcode: ""
b_buy_price: "37"
b_unit_factor: "12"
b_unit_no: "箱"
b_wholesale_price: ""
branch_id: "464"
bstock: "-9"
cost_price_avg: ""
cost_price_spec: ""
current_qty: "-9箱-7桶"
item_id: "199851"
item_images: "{\"main\": \"item-images/company_240/item_199851_main.jpg\", \"tiny\": \"item-images/company_240/item_199851_tiny.jpg\", \"other\": [\"item-images/company_240/item_199851_other_2022-07-13_10-43-07-5610__0.jpg\", \"item-images/company_240/item_199851_other_2022-07-13_11-53-31-0100__0.jpg\", \"item-images/company_240/item_199851_other_2022-07-13_11-53-31-0100__1.jpg\", \"item-images/company_240/item_199851_other_2022-07-13_11-53-31-0100__2.jpg\", \"item-images/company_240/item_199851_other_2022-07-13_11-53-31-0100__3.jpg\", \"item-images/company_240/item_199851_other_2022-07-13_11-53-31-0100__4.jpg\", \"item-images/company_240/item_199851_other_2022-07-13_11-53-31-0100__5.jpg\", \"item-images/company_240/item_199851_other_2022-07-13_11-53-31-0100__6.jpg\", \"item-images/company_240/item_199851_other_2022-07-13_11-53-31-0100__7.jpg\"]}"
item_name: "来一桶卤肉面（卤香牛肉）桶(12入)"
item_order_index: "0"
m_barcode: ""
m_buy_price: ""
m_unit_factor: ""
m_unit_no: ""
m_wholesale_price: ""
mstock: ""
mum_attributes: ""
s_barcode: "6949123371137"
s_buy_price: "3.0833"
s_wholesale_price: ""
sstock: "-7"
stock_qty: "-115"
unit_conv: "1箱=12桶"
unit_no: "桶"



-->
