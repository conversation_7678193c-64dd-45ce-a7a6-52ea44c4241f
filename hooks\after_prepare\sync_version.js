#!/usr/bin/env node

/**
 * Cordova Hook: 同步 config.xml 版本到 AndroidManifest.xml 并设置 Application 类
 *
 * 这个 hook 会在每次 cordova prepare 之后自动执行，
 * 确保 AndroidManifest.xml 中的配置正确
 *
 * 功能：
 * 1. 自动从 config.xml 读取版本号
 * 2. 同步 versionName 和 versionCode 到 AndroidManifest.xml
 * 3. 支持自定义 versionCode 计算规则
 * 4. 确保 Application 类正确设置为 com.yingjiang.app.YingJiangApplication
 * 5. 提供详细的日志输出
 */

var fs = require('fs');
var path = require('path');

/**
 * 计算 Android versionCode
 * 默认规则：版本号 * 1000，例如 3.36 -> 33600
 * 您可以根据需要修改这个函数
 */
function calculateVersionCode(version) {
    // 支持更复杂的版本号格式，如 3.36.1
    var parts = version.split('.');
    var major = parseInt(parts[0]) || 0;
    var minor = parseInt(parts[1]) || 0;
    var patch = parseInt(parts[2]) || 0;

    // 计算方式：major * 10000 + minor * 100 + patch
    // 例如：3.36.1 -> 30000 + 3600 + 1 = 33601
    return major * 10000 + minor * 100 + patch;
}

/**
 * 从 config.xml 提取版本信息
 */
function extractVersionFromConfig(configContent) {
    // 支持多种格式的 widget 标签
    var patterns = [
        /<widget[^>]+version="([^"]+)"/,
        /<widget[^>]*\s+version="([^"]+)"/,
        /version="([^"]+)"[^>]*>/
    ];

    for (var i = 0; i < patterns.length; i++) {
        var match = configContent.match(patterns[i]);
        if (match) {
            return match[1];
        }
    }

    return null;
}

module.exports = function(context) {
    var platforms = context.opts.platforms;

    // 只处理 Android 平台
    if (!platforms || platforms.indexOf('android') === -1) {
        console.log('⏭️  跳过版本同步：未包含 Android 平台');
        return;
    }

    console.log('🔄 [版本同步] 开始同步版本信息到 AndroidManifest.xml...');

    try {
        // 获取项目根目录
        var projectRoot = context.opts.projectRoot;

        // 读取 config.xml
        var configXmlPath = path.join(projectRoot, 'config.xml');
        if (!fs.existsSync(configXmlPath)) {
            console.error('❌ [版本同步] config.xml 文件不存在');
            return;
        }

        var configXmlContent = fs.readFileSync(configXmlPath, 'utf8');

        // 提取版本信息
        var version = extractVersionFromConfig(configXmlContent);
        if (!version) {
            console.error('❌ [版本同步] 无法从 config.xml 中找到版本信息');
            return;
        }

        console.log('📋 [版本同步] 从 config.xml 读取到版本: ' + version);

        // AndroidManifest.xml 路径
        var manifestPath = path.join(projectRoot, 'platforms/android/app/src/main/AndroidManifest.xml');

        if (!fs.existsSync(manifestPath)) {
            console.log('⚠️  [版本同步] AndroidManifest.xml 文件不存在，跳过版本同步');
            return;
        }

        // 读取 AndroidManifest.xml
        var manifestContent = fs.readFileSync(manifestPath, 'utf8');

        // 计算 versionCode
        var versionCode = calculateVersionCode(version);

        // 检查当前版本
        var currentVersionMatch = manifestContent.match(/android:versionName="([^"]+)"/);
        var currentVersionCodeMatch = manifestContent.match(/android:versionCode="([^"]+)"/);

        var currentVersion = currentVersionMatch ? currentVersionMatch[1] : '';
        var currentVersionCode = currentVersionCodeMatch ? currentVersionCodeMatch[1] : '';

        if (currentVersion === version && currentVersionCode === versionCode.toString()) {
            console.log('✅ [版本同步] AndroidManifest.xml 版本已经是最新的，无需更新');
            return;
        }

        // 更新版本信息
        var updated = false;
        var changes = [];

        if (currentVersion !== version) {
            manifestContent = manifestContent.replace(
                /android:versionName="[^"]*"/,
                'android:versionName="' + version + '"'
            );
            changes.push('versionName: ' + currentVersion + ' → ' + version);
            updated = true;
        }

        if (currentVersionCode !== versionCode.toString()) {
            manifestContent = manifestContent.replace(
                /android:versionCode="[^"]*"/,
                'android:versionCode="' + versionCode + '"'
            );
            changes.push('versionCode: ' + currentVersionCode + ' → ' + versionCode);
            updated = true;
        }

        // 检查并添加 Application 类
        var appClassUpdated = false;
        if (!manifestContent.includes('android:name="com.yingjiang.app.YingJiangApplication"')) {
            var applicationTagRegex = /<application([^>]*?)>/;
            var appMatch = manifestContent.match(applicationTagRegex);

            if (appMatch) {
                var originalTag = appMatch[0];
                var attributes = appMatch[1];

                // 添加 android:name 属性如果不存在
                if (!attributes.includes('android:name=')) {
                    var newTag = originalTag.replace('<application', '<application android:name="com.yingjiang.app.YingJiangApplication"');
                    manifestContent = manifestContent.replace(originalTag, newTag);
                    changes.push('Application类: 添加 com.yingjiang.app.YingJiangApplication');
                    updated = true;
                    appClassUpdated = true;
                }
            }
        }

        if (updated) {
            // 写回文件
            fs.writeFileSync(manifestPath, manifestContent, 'utf8');

            console.log('✅ [版本同步] 成功更新 AndroidManifest.xml:');
            changes.forEach(function(change) {
                console.log('   ' + change);
            });

            if (appClassUpdated) {
                console.log('✅ [应用类] 成功添加自定义 Application 类');
            }
        } else {
            // 即使版本没有更新，也要检查 Application 类
            if (!manifestContent.includes('android:name="com.yingjiang.app.YingJiangApplication"')) {
                var applicationTagRegex = /<application([^>]*?)>/;
                var appMatch = manifestContent.match(applicationTagRegex);

                if (appMatch) {
                    var originalTag = appMatch[0];
                    var attributes = appMatch[1];

                    if (!attributes.includes('android:name=')) {
                        var newTag = originalTag.replace('<application', '<application android:name="com.yingjiang.app.YingJiangApplication"');
                        manifestContent = manifestContent.replace(originalTag, newTag);

                        fs.writeFileSync(manifestPath, manifestContent, 'utf8');
                        console.log('✅ [应用类] 成功添加自定义 Application 类');
                    }
                }
            } else {
                console.log('✅ [应用类] Application 类已正确设置');
            }
        }

    } catch (error) {
        console.error('❌ [版本同步] 同步版本时出错:', error.message);
        console.error('   堆栈信息:', error.stack);
        // 不要抛出错误，避免中断构建过程
    }
};


