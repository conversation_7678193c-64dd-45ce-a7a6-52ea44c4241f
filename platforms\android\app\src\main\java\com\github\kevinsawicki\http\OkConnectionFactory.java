package com.github.kevinsawicki.http;

import java.net.URL;
import java.net.HttpURLConnection;
import java.net.Proxy;
import java.io.IOException;

import okhttp3.OkHttpClient;
import com.synconset.cordovahttp.CordovaOkHttp;
import com.silkimen.http.HttpRequest;

/**
 * Connection factory that uses OkHttp as the underlying HTTP client
 * This provides better performance and more reliable networking compared to HttpURLConnection
 *
 * Note: Since OkUrlFactory was removed in OkHttp 4.x and internal classes are not accessible,
 * we use the okhttp-urlconnection dependency which provides HttpURLConnection implementation
 * backed by OkHttp through the JavaNetCookieJar and other mechanisms.
 */
public class OkConnectionFactory implements HttpRequest.ConnectionFactory {

    private final OkHttpClient client;

    public OkConnectionFactory() {
        this.client = CordovaOkHttp.getClient();
    }

    public OkConnectionFactory(OkHttpClient client) {
        this.client = client;
    }

    @Override
    public HttpURLConnection create(URL url) throws IOException {
        // Use the okhttp-urlconnection bridge
        try {
            // Try to use OkUrlFactory from okhttp-urlconnection dependency
            Class<?> okUrlFactoryClass = Class.forName("okhttp3.OkUrlFactory");
            Object factory = okUrlFactoryClass.getConstructor(OkHttpClient.class).newInstance(client);
            return (HttpURLConnection) okUrlFactoryClass.getMethod("open", URL.class).invoke(factory, url);
        } catch (Exception e) {
            // If OkUrlFactory is not available, try JavaNetHttpURLConnection
            try {
                Class<?> javaNetClass = Class.forName("okhttp3.JavaNetHttpURLConnection");
                return (HttpURLConnection) javaNetClass.getConstructor(URL.class, OkHttpClient.class).newInstance(url, client);
            } catch (Exception e2) {
                // Final fallback to standard HttpURLConnection
                return (HttpURLConnection) url.openConnection();
            }
        }
    }

    @Override
    public HttpURLConnection create(URL url, Proxy proxy) throws IOException {
        // Create a new client with the specified proxy
        OkHttpClient proxyClient = client.newBuilder()
            .proxy(proxy)
            .build();

        try {
            // Try to use OkUrlFactory from okhttp-urlconnection dependency
            Class<?> okUrlFactoryClass = Class.forName("okhttp3.OkUrlFactory");
            Object factory = okUrlFactoryClass.getConstructor(OkHttpClient.class).newInstance(proxyClient);
            return (HttpURLConnection) okUrlFactoryClass.getMethod("open", URL.class).invoke(factory, url);
        } catch (Exception e) {
            // If OkUrlFactory is not available, try JavaNetHttpURLConnection
            try {
                Class<?> javaNetClass = Class.forName("okhttp3.JavaNetHttpURLConnection");
                return (HttpURLConnection) javaNetClass.getConstructor(URL.class, OkHttpClient.class).newInstance(url, proxyClient);
            } catch (Exception e2) {
                // Final fallback to standard HttpURLConnection with proxy
                return (HttpURLConnection) url.openConnection(proxy);
            }
        }
    }
}
