<template>
  <div class="bluetooth-test-page">
    <van-nav-bar
      left-arrow
      title="蓝牙权限测试"
      @click-left="$router.go(-1)"
      safe-area-inset-top
    />
    
    <div class="test-content">
      <van-cell-group>
        <van-cell title="权限状态" :value="permissionStatus" />
        <van-cell title="蓝牙状态" :value="bluetoothStatus" />
        <van-cell title="设备数量" :value="deviceCount" />
      </van-cell-group>
      
      <div class="button-group">
        <van-button 
          type="primary" 
          block 
          @click="testPermissions"
          :loading="testing"
        >
          测试权限
        </van-button>
        
        <van-button 
          type="info" 
          block 
          @click="testDeviceSearch"
          :loading="searching"
        >
          测试设备搜索
        </van-button>
        
        <van-button 
          type="warning" 
          block 
          @click="clearResults"
        >
          清除结果
        </van-button>
      </div>
      
      <div class="log-section">
        <h3>测试日志</h3>
        <div class="log-content">
          <div v-for="(log, index) in logs" :key="index" class="log-item">
            <span class="log-time">{{ log.time }}</span>
            <span class="log-message" :class="log.type">{{ log.message }}</span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { NavBar, CellGroup, Cell, Button, Toast } from 'vant'
import BluetoothPermissionHelper from './BluetoothPermissionHelper'
import BlePrinter from './BlePrinter'
import ClassicPrinter from './ClassicPrinter'

export default {
  name: 'BluetoothTestPage',
  components: {
    'van-nav-bar': NavBar,
    'van-cell-group': CellGroup,
    'van-cell': Cell,
    'van-button': Button
  },
  data() {
    return {
      permissionHelper: new BluetoothPermissionHelper(),
      permissionStatus: '未知',
      bluetoothStatus: '未知',
      deviceCount: 0,
      testing: false,
      searching: false,
      logs: []
    }
  },
  methods: {
    addLog(message, type = 'info') {
      const now = new Date()
      const time = `${now.getHours().toString().padStart(2, '0')}:${now.getMinutes().toString().padStart(2, '0')}:${now.getSeconds().toString().padStart(2, '0')}`
      this.logs.unshift({
        time,
        message,
        type
      })
      // 限制日志数量
      if (this.logs.length > 50) {
        this.logs = this.logs.slice(0, 50)
      }
    },
    
    async testPermissions() {
      this.testing = true
      this.addLog('开始测试权限...', 'info')
      
      try {
        // 测试蓝牙是否启用
        if (window.isiOS) {
          bluetoothSerial.isEnabled(
            () => {
              this.bluetoothStatus = '已启用'
              this.permissionStatus = '已授权'
              this.addLog('iOS蓝牙已启用', 'success')
            },
            () => {
              this.bluetoothStatus = '未启用'
              this.addLog('iOS蓝牙未启用', 'error')
            }
          )
        } else {
          this.addLog('Android平台，尝试获取设备列表测试权限', 'info')
          
          // 测试经典蓝牙权限
          try {
            const classicPrinter = new ClassicPrinter()
            const devices = await classicPrinter.list()
            this.addLog(`经典蓝牙权限正常，获取到${devices.length}个设备`, 'success')
            this.permissionStatus = '已授权'
          } catch (error) {
            this.addLog(`经典蓝牙权限测试失败: ${error}`, 'error')
            if (this.permissionHelper.isPermissionError(error)) {
              this.permissionStatus = '权限被拒绝'
            }
          }
          
          // 测试BLE权限
          try {
            const blePrinter = new BlePrinter()
            const devices = await blePrinter.list()
            this.addLog(`BLE权限正常，获取到${devices.length}个设备`, 'success')
          } catch (error) {
            this.addLog(`BLE权限测试失败: ${error}`, 'error')
          }
        }
      } catch (error) {
        this.addLog(`权限测试异常: ${error}`, 'error')
      } finally {
        this.testing = false
      }
    },
    
    async testDeviceSearch() {
      this.searching = true
      this.addLog('开始测试设备搜索...', 'info')
      
      try {
        await this.permissionHelper.safeSearchDevices({
          successCallback: () => {
            this.addLog('设备搜索完成', 'success')
          },
          errorCallback: (message) => {
            this.addLog(`设备搜索失败: ${message}`, 'error')
          },
          searchFunction: async () => {
            // 模拟搜索过程
            const classicPrinter = new ClassicPrinter()
            const blePrinter = new BlePrinter()
            
            const [classicDevices, bleDevices] = await Promise.all([
              classicPrinter.list().catch(() => []),
              blePrinter.list().catch(() => [])
            ])
            
            this.deviceCount = classicDevices.length + bleDevices.length
            this.addLog(`找到${classicDevices.length}个经典蓝牙设备，${bleDevices.length}个BLE设备`, 'info')
          },
          isFirstTime: false
        })
      } catch (error) {
        this.addLog(`搜索测试异常: ${error}`, 'error')
      } finally {
        this.searching = false
      }
    },
    
    clearResults() {
      this.logs = []
      this.permissionStatus = '未知'
      this.bluetoothStatus = '未知'
      this.deviceCount = 0
      this.addLog('结果已清除', 'info')
    }
  }
}
</script>

<style scoped>
.bluetooth-test-page {
  height: 100vh;
  background-color: #f7f8fa;
}

.test-content {
  padding: 16px;
}

.button-group {
  margin: 16px 0;
}

.button-group .van-button {
  margin-bottom: 12px;
}

.log-section {
  margin-top: 20px;
}

.log-section h3 {
  margin-bottom: 10px;
  color: #323233;
}

.log-content {
  background: white;
  border-radius: 8px;
  padding: 12px;
  max-height: 300px;
  overflow-y: auto;
}

.log-item {
  display: flex;
  margin-bottom: 8px;
  font-size: 12px;
}

.log-time {
  color: #969799;
  margin-right: 8px;
  min-width: 60px;
}

.log-message {
  flex: 1;
}

.log-message.success {
  color: #07c160;
}

.log-message.error {
  color: #ee0a24;
}

.log-message.info {
  color: #1989fa;
}
</style>
