<?xml version='1.0' encoding='utf-8'?>
<widget id="com.yingjiang.app" version="3.38" xmlns="http://www.w3.org/ns/widgets" xmlns:cdv="http://cordova.apache.org/ns/1.0" xmlns:android="http://schemas.android.com/apk/res/android">
    <name>营匠</name>
    <description>
        懂行的ERP
    </description>
    <author email="<EMAIL>" href="http://cordova.io">
        Apache Cordova Team
    </author>
    <content src="index.html" />
    <access origin="*" />
    <!-- Allow webview navigations/iframes to http/https targets -->
    <allow-navigation href="*" />
    <!--解决 The connection to the server wasunsuccessful.(file:///android_asset/www/index.html 问题-->
    <preference name="loadUrlTimeoutValue" value="70000" />
    <preference name="orientation" value="portrait" />
    <preference name="StatusBarOverlaysWebView" value="false" />
    <preference name="StatusBarBackgroundColor" value="#ffffff" />
    <preference name="StatusBarStyle" value="darkcontent" />
    <!-- 智能 WebView 选择：新设备使用系统 WebView，老设备使用兼容方案 -->
    <preference name="AndroidWebViewTarget" value="auto" />
    <!-- 为老设备启用硬件加速 -->
    <preference name="AndroidHardwareAcceleration" value="true" />
    <!-- 优化老设备内存使用 -->
    <preference name="AndroidLaunchMode" value="singleTop" />

    <!-- 老设备兼容性优化 -->
    <preference name="AndroidPersistentFileLocation" value="Compatibility" />
    <preference name="AndroidExtraFilesystems" value="files,files-external,documents,sdcard,cache,cache-external,assets,root" />

    <!-- WebView 性能优化 -->
    <preference name="DisallowOverscroll" value="true" />
    <preference name="BackgroundColor" value="0xffffffff" />
    <preference name="HideKeyboardFormAccessoryBar" value="true" />
    <preference name="KeyboardDisplayRequiresUserAction" value="false" />

    <!-- Android 版本兼容配置统一 -->
    <preference name="AndroidTargetSdkVersion" value="34" />
    <preference name="AndroidPrivacyMode" value="safe" />

    <!-- X5 WebView 控制开关 - 通过SmartWebViewSelector.java代码控制 -->
    <preference name="EnableX5WebView" value="true" />
    <preference name="X5SafeMode" value="true" />


    <allow-intent href="http://*/*" />
    <allow-intent href="https://*/*" />
    <allow-intent href="tel:*" />
    <allow-intent href="sms:*" />
    <allow-intent href="androidamap://*/*" />
    <allow-intent href="iosamap://*/*" />
    <allow-intent href="bdapp://*/*" />
    <allow-intent href="baidumap://*/*" />
    <allow-intent href="mailto:*" />
    <allow-intent href="geo:*" />
    <!--解决在极少数机器上打开APP时直接报错: the connection to the server was unsuccessful 问题-->
    <!-- <preference name="loadUrlTimeoutValue" value="2000" /> -->
    <platform name="android">
        <preference name="SplashScreenDelay" value="2000" />
        <preference name="android-minSdkVersion" value="22" />
        <preference name="android-targetSdkVersion" value="34" />
        <preference name="android-compileSdkVersion" value="34" />
        <preference name="MixedContentMode" value="always" />
        <!-- 允许系统 WebView 访问本地文件（解决热更新文件访问问题） -->
        <preference name="AndroidInsecureFileModeEnabled" value="true" />
        <allow-intent href="market:*" />
        <plugin name="cordova-plugin-bd-geolocation" spec="^8.5.0">
            <variable name="API_KEY" value="uinN8rdNWfpZEtEFudy6GPOgd5UdZoZs" />
        </plugin>
        <!-- Allow cleartext HTTP only for specific domains via Network Security Config -->
        <resource-file src="configs/android/network_security_config.xml" target="app/src/main/res/xml/network_security_config.xml" />

        <!-- Custom Java source files -->
        <source-file src="configs/android/YingJiangApplication.java" target-dir="app/src/main/java/com/yingjiang/app" />
        <source-file src="configs/android/CustomExceptionHandler.java" target-dir="app/src/main/java/com/exception" />

        <!-- Fix missing default drawable resource -->
        <resource-file src="configs/android/screen.png" target="app/src/main/res/drawable/screen.png" />

        <!-- Persist application attributes via edit-config to survive prepare/regenerate -->
        <edit-config file="app/src/main/AndroidManifest.xml" mode="merge" target="/manifest/application">
            <application android:name="com.yingjiang.app.YingJiangApplication" android:networkSecurityConfig="@xml/network_security_config" android:usesCleartextTraffic="true" />
        </edit-config>
        <!-- Persist POST_NOTIFICATIONS permission -->
        <edit-config file="app/src/main/AndroidManifest.xml" mode="merge" target="/manifest">
            <uses-permission android:name="android.permission.POST_NOTIFICATIONS" />
        </edit-config>

        <!-- Fix Android 12+ exported attribute requirements -->
        <edit-config file="app/src/main/AndroidManifest.xml" mode="merge" target="/manifest/application/activity[@android:name='com.google.zxing.client.android.encode.EncodeActivity']">
            <activity android:exported="false" />
        </edit-config>

        <edit-config file="app/src/main/AndroidManifest.xml" mode="merge" target="/manifest/application/activity[@android:name='com.mobisys.cordova.plugins.mlkit.barcode.scanner.CaptureActivity']">
            <activity android:exported="false" />
        </edit-config>

    </platform>
    <platform name="ios">
        <plugin name="cordova-plugin-geolocation" spec="^4.0.1" />
    </platform>
    <platform name="ios">
        <allow-intent href="itms:*" />
        <allow-intent href="itms-apps:*" />
        <allow-intent href="iosamap://*/*" />
    </platform>
    <chcp>
        <auto-download enabled="false" />
        <auto-install enabled="true" />
        <native-interface version="5" />
        <config-file url="https://www.yingjiang168.com/download/YingJiangApp/www/chcp.json" />
    </chcp>

    <!-- 版本同步 Hook -->
    <hook type="after_prepare" src="hooks/after_prepare/sync_version.js" />
</widget>
