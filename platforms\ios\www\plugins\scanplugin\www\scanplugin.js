cordova.define("scanplugin.scanplugin", function(require, exports, module) {
var exec = require('cordova/exec');

var scanplugin = {
    coolMethod: function(message, success, error) {
        exec(success, error, "scanplugin", "coolMethod", [message]);
    },
    
    Scancode: function(success, error) {
        exec(success, error, "scanplugin", "Scancode", []);
    }
};

module.exports = scanplugin;

});
