// Toast调试工具 - 用于诊断扫码Toast不消失问题
import { Toast } from "vant"

class ToastDebugger {
  constructor() {
    this.logs = []
    this.toastInstances = new Set()
    this.originalToast = {}
    this.init()
  }
  
  init() {
    // 保存原始Toast方法
    this.originalToast.loading = Toast.loading
    this.originalToast.clear = Toast.clear
    this.originalToast.fail = Toast.fail
    this.originalToast.success = Toast.success
    
    // 包装Toast方法以进行调试
    Toast.loading = (options) => {
      const timestamp = new Date().toISOString()
      const instance = this.originalToast.loading(options)
      this.toastInstances.add(instance)
      this.log(`[${timestamp}] Toast.loading created`, { options, instanceCount: this.toastInstances.size })
      return instance
    }
    
    Toast.clear = () => {
      const timestamp = new Date().toISOString()
      const beforeCount = this.toastInstances.size
      this.toastInstances.clear()
      this.originalToast.clear()
      this.log(`[${timestamp}] Toast.clear called`, { beforeCount, afterCount: 0 })
    }
    
    Toast.fail = (message) => {
      const timestamp = new Date().toISOString()
      this.originalToast.clear() // 先清除loading
      this.toastInstances.clear()
      const result = this.originalToast.fail(message)
      this.log(`[${timestamp}] Toast.fail called`, { message })
      return result
    }
    
    Toast.success = (message) => {
      const timestamp = new Date().toISOString()
      this.originalToast.clear() // 先清除loading
      this.toastInstances.clear()
      const result = this.originalToast.success(message)
      this.log(`[${timestamp}] Toast.success called`, { message })
      return result
    }
  }
  
  log(message, data = {}) {
    const logEntry = {
      timestamp: Date.now(),
      message,
      data,
      stack: new Error().stack
    }
    this.logs.push(logEntry)
    console.log(`[ToastDebugger] ${message}`, data)
    
    // 保持最近100条日志
    if (this.logs.length > 100) {
      this.logs.shift()
    }
  }
  
  getLogs() {
    return this.logs
  }
  
  getActiveToastCount() {
    return this.toastInstances.size
  }
  
  clearLogs() {
    this.logs = []
  }
  
  // 强制清除所有Toast
  forceCleanup() {
    const timestamp = new Date().toISOString()
    this.log(`[${timestamp}] Force cleanup initiated`, { activeCount: this.toastInstances.size })
    
    // 清除所有记录的实例
    this.toastInstances.forEach(instance => {
      try {
        if (instance && typeof instance.clear === 'function') {
          instance.clear()
        }
      } catch (e) {
        console.warn('Failed to clear toast instance:', e)
      }
    })
    
    this.toastInstances.clear()
    this.originalToast.clear()
    
    // 强制清除DOM中的Toast元素
    setTimeout(() => {
      const toastElements = document.querySelectorAll('.van-toast, .van-overlay')
      toastElements.forEach(el => {
        try {
          el.remove()
        } catch (e) {
          console.warn('Failed to remove toast element:', e)
        }
      })
    }, 100)
    
    this.log(`[${timestamp}] Force cleanup completed`)
  }
  
  // 生成调试报告
  generateReport() {
    const report = {
      timestamp: new Date().toISOString(),
      activeToastCount: this.toastInstances.size,
      totalLogs: this.logs.length,
      recentLogs: this.logs.slice(-10),
      domToastElements: document.querySelectorAll('.van-toast, .van-overlay').length
    }
    
    console.log('Toast Debug Report:', report)
    return report
  }
}

// 创建全局调试器实例
const toastDebugger = new ToastDebugger()

// 暴露到全局作用域以便调试
if (typeof window !== 'undefined') {
  window.toastDebugger = toastDebugger
}

export default toastDebugger
