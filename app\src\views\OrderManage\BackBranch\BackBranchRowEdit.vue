<template>
  <div  class="wrapper-edit">
    <div class="edxit_name_box">
      <div class="edxit_wrapper">
        <h4 class="edxit_name">{{ sheetRow.item_name }}</h4>
        <van-field
          v-model="sheetRow.back_unit_no"
          v-if="qtyChangeType=='move'"
          label="商品单位:"
          placeholder=""
          readonly
          @click="handleUnitNoClick"

        />
        <van-field
          v-model="sheetRow.sale_unit_no"
          v-if="qtyChangeType=='sale'"
          label="商品单位:"
          placeholder=""
          readonly
          @click="handleUnitNoClick"

        />
        <van-field
          v-model="sheetRow.move_qty"
          v-if="qtyChangeType=='move'"
          label="回库数量:"
          placeholder=""
          :readonly="this.readonly ? 'readonly' : false"
          @input="onQuantityInput"
          type="number"
           @click="handleNoclick"
          :disabled="attrItemFlag"
        />
        <van-field
          v-model="sheetRow.sale_qty"
          v-else
          label="销售数量:"
          placeholder=""
          :readonly="this.readonly ? 'readonly' : false"
          @input="onQuantityInput"
          type="number"
           @click="handleNoclick"
          :disabled="attrItemFlag"
        />
        <van-field
          v-if="qtyChangeType=='sale'"
          v-model="sheetRow.remark"
          label="销售备注:"
          placeholder=""
          :readonly="this.readonly ? 'readonly' : false"
          @input="onRemarkInput"
          type="textarea"
          @click="handleRemarkNoClick"
          :disabled="attrItemFlag"
        />

      </div>
      <div class="attr_wrapper" v-if="attrList.length > 0">

        <div class="attr_content">
         <div v-for="(item,index) in attrList" :key="index" class="attr_item">
           <div class="attr_item_name">{{handleAttrNameShow(item)}}</div>
           <div class="attr_item_qty">
             <!-- <input v-model="item.qty"> -->
                <input type="number" @input="handleAttrQty(item)" v-model="item.qty">
            </div>
           <div class="attr_item_unit">{{sheetRow.unit_no}}</div>
         </div>
        </div>


      </div>
      <div style="margin: top 20px;overflow-y:auto;height: 45vh;" v-if="sheetsInfo.length>0" >
        <h4 style="text-align: left;margin-left:10px;">商品库存使用情况:</h4>
        <div style="margin-top:5px;display: flex;flex-direction: row;flex-wrap: nowrap;justify-content: space-around;align-items: center;" >
          <div>总回库数:{{underStockItem.need_move_qty_conv }}<span style="color:#ccc;margin-left:5px;">(小:{{underStockItem.need_move_qty+underStockItem.unit_no}})</span></div>
          <div><span>单据数:{{underStockItem.rows.length}}个</span></div>
        </div>
        
        <div style="margin-top:5px;display: flex;flex-direction: row;flex-wrap: nowrap;justify-content: space-around;align-items: center;">
          <div>库存数:{{underStockItem.stock_qty_unit }}<span style="color:#ccc;margin-left:5px;">(小:{{underStockItem.stock_qty+underStockItem.unit_no}})</span></div>
          <div style="color:#FF0000"> 库存不足</div>
        </div>
        <!-- <div>*本次回库,库存不足库存少:{{underStockItem.left_qty_conv }}<span style="color:#ccc;margin-left:5px;">(小:{{underStockItem.left_qty+underStockItem.unit_no}})</span></div> -->
        <h4 style="text-align: left;margin-left:10px;margin-top:20px">本单据之后车仓库存使用情况</h4>
        <van-steps direction="vertical" :active="-1" >
          <van-step v-for="(sht,num) in sheetsInfo" :key="num" style="margin-top:10px">
            <h3 style="color: #1989fa; text-decoration: none" @click.stop="onRowClick(sht)">{{ sht.sheet_no }}</h3>
            <h4>
              <span>{{sht.trade_type}}:{{sht.quantity+sht.unit_no}}</span>
              <span v-if="sht.order_qty" style="margin-left:10px;">订单:{{sht.order_qty+sht.order_unit_no}}</span>
            </h4>
            <h4>审:{{ sht.approve_time }}</h4>
          </van-step>
        </van-steps>
      </div>
    </div>
    <div class="footerEdxit">
      <van-button  round @click="btnDel_click">退出</van-button>
      <van-button style="color: #333;background:#ffcccc ;" round @click="btnOK_click">确认</van-button>
    </div>
    <van-popup v-model="bPopupUnitSelectDialog" position="bottom">
      <van-picker title="请选择单位" show-toolbar :columns="unitNoList" columns-field-name='unit_no' @confirm="onUnitSelected" @cancel="bPopupUnitSelectDialog = false" />
    </van-popup>
  </div>
</template>
<script>
import {
  Field,
  Col,
  Row,
  DatetimePicker,
  Popup,
  Button,
  Icon,
  Picker,
  Toast,
  Dialog,
  Step, 
  Steps,
} from "vant";
import { GetItemUnit ,GetUnderStockItemsSheetsInfo} from "../../../api/api";

export default {

  data() {
    return {
      sheetRow: {},
      readonly: false,
      qtyChangeType:'',
      sncodesShow:false,
      sncodeInputed: "",
      sn_codes:[],
      dataShow: false,
      dataMark: false,
      minDate: new Date(2020, 0, 1),
      maxDate: new Date(2025, 10, 1),
      currentDate: new Date(),
      remarksTime: "",
      bPopupUnitSelectDialog: false,
      onloadBrief: [],
      itemId: "",
      unitsJson: [],
      unitNoList:[],
      remarkInputed: '',
      attrList:[],
      fromBranch:'',
      sheetsInfo:[],
      //underStockItems:[],
      underStockItem:{},
    }
  },
  props: {
    editingRow: Object,
    sheet:Object
  },
  watch: {
    editingRow: {
      immediate: true,
      handler(obj) {
        if (obj) {
          
          this.sheetRow = JSON.parse(JSON.stringify(obj.datas));
          this.onloadBrief = obj.onloadBrief;
          this.readonly = obj.readonly;
          this.qtyChangeType = obj.qtyChangeType;
          this.fromBranch = obj.fromBranch
          this.underStockItem = obj.underStockItem
          // if(obj.underStockItems && obj.underStockItems.length>0){
          //   obj.underStockItems.forEach(item=>{
          //     if(item.item_id == this.sheetRow.item_id && item.branch_position == this.sheetRow.branch_position && item.batch_id == this.sheetRow.batch_id){
          //       this.underStockItem = item
          //     }
          //   })
          // }
          //this.underStockItems = obj.underStockItems
          //this.unitNoList = obj.itemUnits;
          this.onloadData();
          if(this.sheetRow.underStock){
           this.GetUnderStockItemsSheetsInfo();
          }
          

        }
      }
    }
  },
  mounted(){
  },
  computed: {
    attrItemFlag() {
      return this.attrList.length > 0 ? true : false
    },
    allowChangeSaleSheetPrice() {
      return !hasRight('delicacy.allowChangeSaleSheetPrice.value') && (this.sheet.sheetType ==='X' || this.sheet.sheetType ==='XD' )
    }
  },
  beforeDestroy() {
    this.attrList = []
  },
  components: {
    "van-field": Field,
    "van-row": Row,
    "van-col": Col,
    "van-datetime-picker": DatetimePicker,
    "van-popup": Popup,
    "van-button": Button,
    "van-icon": Icon,
    "van-picker": Picker,
    "van-steps":Steps,
    "van-step":Step, 
    
  },
  methods: {
    onRowClick(sht){
      var path = '/SaleSheet'
      if(sht.sheet_type =="DB"){
        path ='/MoveSheet'
      }
      
      this.$router.push({ path: path, query: { sheetID: sht.sheet_id, sheetType: sht.sheet_type } })
    },
    onUnitSelected(unit_no) {
      
      if (this.readonly) {
        this.bPopupUnitSelectDialog = false
        Toast.fail("已审单据不能修改")
        return
      }
      var unit_factor = this.unitsJson[unit_no].unit_factor
      if (!unit_factor) {
        this.$toast('该单位没设置包装率，不能使用')
        return
      }
      if(this.qtyChangeType === "move"){
        this.sheetRow.back_unit_no = unit_no
        this.sheetRow.back_unit_factor = unit_factor
      }else if(this.qtyChangeType ==="sale"){
        this.sheetRow.sale_unit_no = unit_no
        this.sheetRow.sale_unit_factor = unit_factor
      }
      this.bPopupUnitSelectDialog = false
    },
    async GetUnderStockItemsSheetsInfo(){
      let params ={
        row:this.sheetRow,
        fromBranch:this.fromBranch,
      }
      var self = this
      await GetUnderStockItemsSheetsInfo(params).then((res)=>{
        if(res.result==="OK"){
          self.sheetsInfo = res.records
          console.log('**************')
          console.log(self.sheetsInfo)
          self.$forceUpdate()
        }
      })
    },
    onloadData() {
      if(this.sheetRow?.attr_qty !== undefined && this.sheetRow.attr_qty !== '' && this.sheetRow.attr_qty !== '[]') {
          this.attrList = (typeof this.sheetRow.attr_qty == "string" ? JSON.parse(this.sheetRow.attr_qty) : this.sheetRow.attr_qty)
      } else {
        this.attrList = []
      }
      this.unitList = [];
      let params = {
        itemID: this.sheetRow.item_id,
      };
      GetItemUnit(params).then((res) => {
        if (res.result === "OK") {
          var row=res.data
          const unitNoList=[]
          if (row.b_unit_no && row.b_unit_factor) {
            unitNoList.push(row.b_unit_no);
            this.unitsJson[row.b_unit_no]={unit_no:row.b_unit_no,unit_factor:row.b_unit_factor}
          }
          if (row.m_unit_no && row.m_unit_factor) {
             unitNoList.push(row.m_unit_no)
             this.unitsJson[row.m_unit_no]={unit_no:row.m_unit_no,unit_factor:row.m_unit_factor}
          }
          if (row.s_unit_no && row.s_unit_factor) {
             unitNoList.push(row.s_unit_no);
             this.unitsJson[row.s_unit_no]={unit_no:row.s_unit_no,unit_factor:row.s_unit_factor}
          }
          this.unitNoList=unitNoList
        }
      })
      console.log("readonly:"+this.readonly)
      if(this.readonly){

      }
    },
    onRemarkClick(){

    },
    setPopUpHeight() {
			let h = document.getElementById('ft-btn').clientHeight,
			  itemOffTop = this.$refs.itemBox.offsetTop
			this.$refs.itemBox.style.height = document.documentElement.clientHeight - h - itemOffTop + 'px'
			this.$forceUpdate()
		},
    onSnCodesSave() {
      if(this.readonly){
        Toast.fail("已审单据不能修改")
        this.sncodesShow = false;
        return
      }
      this.sheetRow.sn_code =  this.sn_codes.join(',');
      this.sncodeInputed = '';
      this.sncodesShow = false;
    },
    async btnScanBarcode_click(unit_type){
      try {
        const result = await this.scanBarcodeNew({
          unit_type: `back_branch_row_edit_${unit_type}`
        })

        if (!result.code) {
          return
        }

        var code = result.code;
        // if the code is like "http(s)://.../...?code=xxxxx"
        if(code.match(/code=(\w+)/)) {
          code = code.match(/code=(\w+)/)[1];
        }
        // else if the code is like "http(s)://.../...?cd=xxxxx"
        else if(code.match(/cd=(\w+)/)) {
          code = code.match(/cd=(\w+)/)[1];
        }
        this.sncodeInputed = code;
        //onSnCodeInputed()
        this.$forceUpdate()
      } catch (error) {
        console.error('扫码失败:', error)
        this.$toast('扫码失败，请重试')
      }
    },

    btnDelSnCode_click(index) {
      this.sn_codes.splice(index, 1)
      this.$forceUpdate()
    },
    appUseSn(){
      const setting=this.$store.state.operInfo.setting
      var b =false
      if(setting) b = setting.appSaleUseSn
      return b && b.toString().toLowerCase()=='true'
    },
    onQuantityInput(value) {
    },
    onRemarkInput(value){

    },
    handleAttrQty(item) {
      let qty = 0
      this.attrList.forEach(element => {
        qty += Number(element.qty)
      });
      this.sheetRow.quantity = qty
      this.sheetRow.attr_qty = JSON.stringify(this.attrList)
    },
    handleUnitNoClick() {

      this.bPopupUnitSelectDialog = true
    },
    btnOK_click() {
      if(this.readonly){
        Toast.fail("已审单据不能修改")
        return
      }
      if(this.sheetRow.remark==""&& Number(this.sheetRow.sale_qty)>0){
        Toast.fail("请输入处理备注");
        return
      }
      var needMoveQty = Number(this.sheetRow.need_move_qty)*Number(this.sheetRow.unit_factor)
      var moveQty = Number(this.sheetRow.move_qty)*Number(this.sheetRow.back_unit_factor)
      var saleQty = Number(this.sheetRow.sale_qty)*Number(this.sheetRow.sale_unit_factor)
      if(needMoveQty<moveQty){
        Toast.fail("回库数量大于待回库数量");
        return
      }
      if(needMoveQty<saleQty){
        Toast.fail("处理数量大于待回库数量");
        return
      }

      if(needMoveQty<moveQty+saleQty){
        Toast.fail("回库数量与处理数量的合计大于待回库数量");
        return
      }
      // if(Number(this.sheetRow.sale_qty)>0&&Number(this.sheetRow.sale_qty)+Number(this.sheetRow.backQty)-Number(this.sheetRow.quantity)>0){
      //   Toast.fail("回库数量与销售数量的合计大于待回库数量");
      //   return
      // }
      // 处理为零的不区分库存的属性
      if(this.sheetRow.attr_qty !== undefined) {
        this.sheetRow.attr_qty = JSON.stringify(this.attrList.filter(item => Number(item.qty)!== 0))
      }
      if(this.sheetRow.unit_no ===this.sheetRow.b_unit_no)this.sheetRow.b_quantity=this.sheetRow.quantity
      if(this.sheetRow.unit_no ===this.sheetRow.m_unit_no)this.sheetRow.m_quantity=this.sheetRow.quantity
      if(this.sheetRow.unit_no ===this.sheetRow.s_unit_no)this.sheetRow.s_quantity=this.sheetRow.quantity
      this.$emit("onRowEditDone", this.sheetRow);
    },
    btnDel_click() {
      this.$emit("onRowEditExit");
    },
    handleRemarkNoClick(){

    },
    handleNoclick(){
      if(this.attrItemFlag) {
        Toast("请从下方修改")
        return
      }
    },
    handleAttrNameShow(attr){
      let keyName = 'optName_'
      let tempName = []
      let temp = typeof attr == 'string' ? JSON.parse(attr) : attr
      Object.keys(temp).forEach(item => {
        if(item.substr(0, keyName.length) == keyName) {
          tempName.push(temp[item])
        }
      })
      return tempName.sort().join('_')
    }

  },
};
</script>
<style lang="less" scoped>
@flex_a_j: {
  display: flex;
  align-items: center;
  justify-content: center;
};
@flex_a_flex: {
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
};
@flex_a_bw: {
  display: flex;
  align-items: center;
  justify-content: space-between;
};
@flex_a_jend: {
  display: flex;
  align-items: center;
  justify-content: flex-end;
};
/deep/.van-cell {
  font-size: 15px;
}

.save_button{
  width: 100%;
  height: 50px;
  position: fixed;
  bottom: 0px;
  background-color: #444;
  // box-sizing: border-box;
  // vertical-align: top;
  button{
    width: 100%;
    height: 100%;
    // vertical-align: top;
  }
}
.wrapper-edit{
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}
.edxit_name {
  font-size: 16px;
  padding: 0 30px;
  height: 40px;
  background: #ffffff;
  line-height: 40px;

}
.edxit_name_box {
  //height: calc(100% - 60px);
  display: flex;
  flex-direction: column;

  .edxit_wrapper{
     flex: 1;

  }
  .attr_wrapper{
    width: 100%;
    height: 100%;
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow-y: auto;
    .attr_title{
      margin: 10px 0;
    }
    .attr_content{
      height: 80%;
      display: flex;
      flex-direction: column;
      overflow-y: auto;

      padding: 10px 20px 20px;
      .attr_item{
        margin: 2px 0;
        width: 100%;
        min-height: 30px;
        line-height: 30px;
        background-color: #fff;
        border-radius: 10px;
        display: flex;

        .attr_item_name{
          flex: 2;
          display: flex;
          justify-content: center;
          align-items: center;
        }
        .attr_item_qty{
          flex: 2;
          font-family: numfont;
          font-size: 20px;
          input {
            width: 100%;
            outline: none;
            border: none;
            border-bottom: 1px solid #dddddd;
            vertical-align: top;
          }
        }
        .attr_item_unit{
          flex: 1;
        }
      }
    }
  }
}
.footerEdxit {
  position: absolute;
  bottom: 0;
  width: 100%;
  display: flex;
  justify-content: space-around;
  button {
    width: 40%;
  }
}
.picker_remark{
  width: 90%;
  height: 25px;
  font-size: 15px;
  outline: none;
  border: none;
  border: 2px solid #cccccc;
  background: #f2f2f2;
  border-radius: 5px;
  text-align: center;
  margin-bottom: 10px;
}

</style>
