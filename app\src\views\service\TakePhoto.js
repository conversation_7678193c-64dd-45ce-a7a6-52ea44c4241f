class TakePhoto{

    static async takePhotos(sourceType = Camera.PictureSourceType.CAMERA){
        const promise=new Promise((resolve,reject)=>{
            let CameraOptions = {
                // eslint-disable-next-line no-undef
                destinationType: Camera.DestinationType.DATA_URL, //返回FILE_URI类型
                // eslint-disable-next-line no-undef
                sourceType: sourceType, //返回FILE_URI类型
              };
              navigator.camera.getPicture(cameraSuccess, cameraError, CameraOptions);
              // eslint-disable-next-line no-inner-declarations
              function cameraSuccess(data) {
                // //原图
                if(data.indexOf("base64") == -1){
                    data = "data:image/jpeg;base64," + data;
                }
                resolve(data);
              }
              // eslint-disable-next-line no-inner-declarations
              function cameraError() {
                resolve("摄像头异常")
              }
        })
        return promise
           }
           
}
export default TakePhoto