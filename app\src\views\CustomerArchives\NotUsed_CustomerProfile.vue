<template>
  <div class="public_query">
    <div class="public_query_title">
      <van-form>
        <div class="public_query_titleSrc">
          <div class="public_query_titleSrc_item">
            <input
              type="text"
              v-model="searchStr"
              placeholder="请输入客户名"
              @input="onSrcStomerName"
            />
            <van-icon name="user-o" />
          </div>
          <YjSelectTree
            ref="selectTreeRef"
            :target="target"
            :title="title"
            :confirmColor="confirmColor"
            :rangeKey="rangeKey"
            :rootNode="rootNode"
		        :idKey="idKey"
            :sonNode="sonNode"
            :multipleCheck="multipleCheck"
            :showClearBtn="showClearBtn"
            :parentSelectable="parentSelectable"
            :popupHeight="'85%'"
            @getRootNode="getRootNode"
            @handleConfirm="onRegionSelected"
          >
          <template #select-tree-content>
            <div class="public_query_titleSrc_item">
              <input
                type="text"
                v-model="region_name"
                placeholder="请选择片区"
                readonly
              />
              <van-icon name="location-o" />
            </div>
          </template>
          </YjSelectTree>
        </div>
      </van-form>
    </div>
    <div class="sales_list_boxs" v-if="customerList.length>0">
      <div class="public_list_title">
        <div>客户名称</div>
        <div>联系电话</div>
      </div>
      <van-list
          class="van_list"
          v-model="loading"
          :finished="finished"
          finished-text="到底了~"
          @load="loadNextPage"
      >
        <ul class="sales_ul">
          <li v-for="(item, index) in customerList" :key="index">
            <div class="sales_ul_t">
              <div class="sales_ul_tl" @click="onSelectActive(item)">{{ item.sup_name }}</div>
              <a class="sales_ul_tr" :href="'tel:'+ item.sup_tel">{{ item.sup_tel }}</a>
            </div>
            <div class="sales_ul_b">
              <div class="sales_ul_bl"  @click="onSelectActive(item)">{{ item.sup_addr }}</div>
              <div class="sales_ul_br" style="color:#666;size:0.5rem">
                <div v-if="item.distance==-1">
                   {{item.distanceStr}}
                 </div>
                  <div v-else @click="openNavigationAppDialog(item)">
                 <!-- <div v-else @click="onJumpNavigationAppClick(item.sup_addr,item.addr_lng,item.addr_lat)"> -->
                  {{item.distanceStr}}<i class="iconfont">&#xe640;</i>
                </div>
              </div>
            </div>
  
          </li>
        </ul>
      </van-list>
    </div>
    <div class="sales_list_boxs_no" v-if="customerList.length<=0">
      <div class="whole_box_no_icon iconfont">
        &#xe664;
      </div>
      <p>暂无数据</p>
    </div>
    <!-- <van-popup style="overflow: hidden!important;" v-model="regionShow"  position="bottom" :style="{ height: '90%', width: '100%' }" >
      <region-selection @onRegionSelected="onRegionSelected"></region-selection>
    </van-popup> -->
    <van-popup v-model="showChooseNavigationApp" position="bottom" :style="{ height: '30%' }" duration="0.3">
      <div class="navi-select-item" @click="onNaviSelectItem(item)" v-for="(item,index) in navigationAppList" :key="index">{{item.name}}</div>
    </van-popup>
        <div class="wrapper">
      <div class="content">共<span class="record">{{total}}</span>条</div>
    </div>
  </div>
</template>
<script>
import { Form, Icon, List,Popup } from "vant";
import { GetClientList, GetClientById } from "../../api/api";
import YjSelectTree from "../components/yjTree/YjSelectTree.vue";
// import RegionSelection from "../components/RegionSelection"

export default {
  name: 'CustomerProfile',
  data() {
    return {
      region_id: "",
      region_name: "",
      searchStr: "",
      loading: false,
      finished: false,
      pageSize: 50,
      startRow: 0,
      distanceStr:"",
      getTotal: true,
      supcust_id: '',
      customerList: [],
      currentLat:"",
      currentLng:"",
            navigationAppList:[
        {
          id:1,
          name:"高德地图"
        },
                {
          id:2,
          name:"百度地图"
        }
      ],
      showChooseNavigationApp:false,
      total: 0,
      target:'region',
      rangeKey: 'name',
			idKey: 'id',
      sonNode:'subNodes',
      asPage:true,
			multipleCheck: false,
			parentSelectable: true,
      showClearBtn:false,
			foldAll: true,
			confirmColor:'#e3a2a2',
			title: '片区选择',
      rootNode:{}
    };
  },
  mounted() {
    var that=this
      if (isiOS) {
      this.getiOSdGpsPosition(res=>{
      console.log("getiOSdGpsPosition"+{res})
      that.currentLng=res.longitude
      that.currentLat=res.latitude      
      that.openNewPage();

      },error=>{

      })
    }else{
    this.getAndroidGpsPosition(
      res=>{
      that.currentLng=res.longitude
      that.currentLat=res.latitude      
      that.openNewPage();
      },error=>{

      })
    }

  },
  components: {
    "van-form": Form,
    "van-icon": Icon,
    "van-list": List,
    "van-popup":Popup,
    // "region-selection":RegionSelection
    YjSelectTree
  },
  methods: {
    openNewPage() {
      this.startRow = 0
      this.finished = false
      this.customerList = []
      setTimeout(() => {
        this.loadNextPage()
      },300)
    },  
    onNaviSelectItem(item){
      console.log(item)
      if(isiOS){
          this.jumpiOSNaviUrlBySelectAppName(item.name)
      }
      else{
          this.jumpAndroidNaviUrlBySelectAppName(item.name)
      }
      //隐藏弹出框
      this.showChooseNavigationApp=false
    },
    openNavigationAppDialog(item){
      //打开弹出框并且把选中商家的导航信息存入全局变量
      this.selectedSupcustNavigatorInfo={
       sup_addr:item.sup_addr,
       addr_lng:item.addr_lng,
       addr_lat:item.addr_lat
      }
      this.showChooseNavigationApp=true
    },
    isDistanceMoreOneKM(distance){
        return distance>1000;
    },
    jumpiOSNaviUrlBySelectAppName(name){
        //从全局变量中取选中商家的导航信息
        const navigationInfo= this.selectedSupcustNavigatorInfo
        if (name=="百度地图") {
          window.open(this.generateiOSBaiduJumpNavigationAppUrl(navigationInfo.sup_addr,navigationInfo.addr_lng,navigationInfo.addr_lat),"_system")
        }
        if (name=="高德地图") {
         window.open(this.generateiOSGaoDeJumpNavigationAppUrl(navigationInfo.sup_addr,navigationInfo.addr_lng,navigationInfo.addr_lat),"_system")
        }
    },
    jumpAndroidNaviUrlBySelectAppName(name){
          //从全局变量中取选中商家的导航信息
          const navigationInfo= this.selectedSupcustNavigatorInfo
        if (name=="百度地图") {
          window.location.href=this.generateAndroidBaiduJumpNavigationAppUrl(navigationInfo.sup_addr,navigationInfo.addr_lng,navigationInfo.addr_lat)
        }
        if (name=="高德地图") {
          window.location.href=this.generateAndroidGaoDeJumpNavigationAppUrl(navigationInfo.sup_addr,navigationInfo.addr_lng,navigationInfo.addr_lat)
        }
    },
    generateiOSGaoDeJumpNavigationAppUrl(supAddr,addr_lng,addr_lat){
      const naviurl=this.generateGaoDeJumpNavigationAppUrl("iosamap",supAddr,addr_lng,addr_lat)
      return  `${naviurl}&src=ios.baidu.openAPIdemo`
    },
    generateAndroidGaoDeJumpNavigationAppUrl(supAddr,addr_lng,addr_lat){
      const naviurl=this.generateGaoDeJumpNavigationAppUrl("androidamap",supAddr,addr_lng,addr_lat)
      return naviurl
    },
    generateGaoDeJumpNavigationAppUrl(protocol,supAddr,addr_lng,addr_lat){
    const naviurl=`${protocol}://navi?sourceApplication=appname&poiname=${supAddr}&lat=${addr_lat}&lon=${addr_lng}&dev=1&style=2`
    return naviurl
    },
    generateiOSBaiduJumpNavigationAppUrl(supAddr,addr_lng,addr_lat){
      const naviurl=this.generateBaiduJumpNavigationAppUrl("baidumap",supAddr,addr_lng,addr_lat)
      return  `${naviurl}&type=BLK&src=ios.baidu.openAPIdemo`
    },
    generateAndroidBaiduJumpNavigationAppUrl(supAddr,addr_lng,addr_lat){
      const naviurl=this.generateBaiduJumpNavigationAppUrl("bdapp",supAddr,addr_lng,addr_lat)
      return `${naviurl}&src=andr.baidu.openAPIdemo`
    },
    generateBaiduJumpNavigationAppUrl(protocol,supAddr,addr_lng,addr_lat){
    const naviurl=`${protocol}://map/navi?query=${supAddr}&coord_type=bd09ll&location=${addr_lat},${addr_lng}`
    return naviurl
    },
    getiOSdGpsPosition(cbSuccess,cbFail){
      
      var options = {
      enableHighAccuracy: true,  // 是否使用 GPS
      coorType: 'bd09ll'
      }
      window.baidumap_location.getCurrentPosition(res=>{
        const longitude=res.longitude
        const latitude=res.latitude
        const currentPosition={
            longitude,latitude
        }
        cbSuccess(currentPosition)
      },error=>{

      })
    },
    processDistanceAndFormatUnit(distance){
      let distanceStr=''
      if(this.isDistanceMoreOneKM(distance)) {
            distance=distance/1000
            distanceStr=distance.toFixed(2)+" km "
            }else{
            distanceStr=parseInt(distance)+" m "
            }
            return distanceStr
    },
    loadNextPage() {
      if(this.finished) return
      let params = {
        pageSize: this.pageSize,
        startRow: this.startRow,
        getTotal: this.getTotal,
        currentLat:this.currentLat,
        currentLng:this.currentLng,
        searchStr: this.searchStr,
        region_id: this.region_id
      }
      console.log(params)
      GetClientList(params).then((res) => {
        if (res.result === "OK") {
          console.log(res)
          res.data.map(item=>{
            if(item.distance===''){
               const ILLEGAL_DISTANCE=-1
               item.distance=ILLEGAL_DISTANCE
               item.distanceStr="未知"
            }
            else{
                item.distanceStr= this.processDistanceAndFormatUnit(parseFloat(item.distance))
            }
            this.customerList.push(item)
          })
          this.loading = false
          this.total = res.total
          this.startRow = Number(this.startRow)+this.pageSize
          if (this.customerList.length >= Number(res.total)){
            this.finished = true
          }
        } 
      });
    },
    
    onSelectActive(obj){
      let customerList = []
      let params = {
        supcustId: obj.supcust_id,
      }
      this.supcust_id = obj.supcust_id
      GetClientById(params).then(res => {
        if(res.result === 'OK'){
          customerList = res.data
          customerList.supcust_id = this.supcust_id
          this.$router.push({ path: '/CustomerArchivesSon', query: customerList })
        }else{
          this.$router.push({ path: '/CustomerArchivesSon' })
        }
      });
    },
    onAddressHide(objs){
      this.regionShow = objs.isShow
      if(objs.itemObjs !== "") {
        this.region_id = objs.itemObjs.ids
        this.region_name = objs.itemObjs.titles
      }else{
        this.region_id = ''
        this.region_name = ''
      }
      this.openNewPage()
    },
    async getGpsPosition(cbSuccess, cbFail) {
      try {
        const params = {
          message: "需要定位权限来获取客户位置",
          key: "positionCustomerProfile"
        };
        const result = await Position.getPosition(params);
        if (result.result === 'OK') {
          const currentPosition = {
            longitude: result.longitude,
            latitude: result.latitude
          };
          if (cbSuccess) cbSuccess(currentPosition);
        } else {
          if (cbFail) cbFail();
        }
      } catch (error) {
        console.error("获取位置失败:", error);
        if (cbFail) cbFail();
      }
    },
    getRootNode(node) {
      this.rootNode=node
    },
    onRegionSelected(obj){
      if (obj.length>0) {
        this.region_name = obj[0].name;
        this.region_id = obj[0].id;
      } else {
        this.region_id = "";
        this.region_name = "";
      }
      // this.region_id = obj.regionID
      // this.region_name = obj.regionName     
      this.openNewPage()
    },
    onSrcStomerName() {
      this.openNewPage();
    }
  },
};
</script>
<style lang="less" scoped>
// height:136px
@flex_w: {
  display: flex;
  flex-wrap: wrap;
};
@flex_a_j: {
  display: flex;
  align-items: center;
  justify-content: center;
};
@flex_a_bw: {
  display: flex;
  align-items: center;
  justify-content: space-between;
};
@flex_a_end: {
  display: flex;
  align-items: center;
  justify-content: flex-end;
};
@flex_a_flex: {
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column
};
.navi-select-item{
  font-size: 0.65rem;
  color: #1887f7;
  border-bottom: solid 0.025rem #ccc;
  padding-top: .5rem;
  padding-bottom: .5rem;
}
.public_query {
  height: 100%;
  .public_query_title {
    padding-top: 5px;
    background: #ffffff;
    .public_query_titleSrc {
      padding: 0 10px;
      height: 35px;
      @flex_a_bw();
      margin-top: 5px;
      .public_query_titleSrc_item {
        width: 48%;
        height: 100%;
        border: 1px solid #cccccc;
        border-radius: 4px;
        overflow: hidden;
        position: relative;
        vertical-align: top;
        div {
          height: 100%;
          width: calc(100% - 40px);
          padding: 0 30px 0 10px;
          border: none;
          font-size: 14px;
          line-height: 35px;
          color: #333333;
          text-align: left;
        }
        input {
          height: 100%;
          width: calc(100% - 40px);
          padding: 0 30px 0 10px;
          border: none;
          font-size: 14px;
          line-height: 35px;
          color: #333333;
          vertical-align: top;
        }
        .van-icon {
          position: absolute;
          right: 0;
          top: 0;
          bottom: 0;
          width: 30px;
          font-size: 20px;
          @flex_a_j();
          color: #666666;
        }
      }
    }
  }
}
.sales_list_boxs_no{
  height: calc(100% - 54px);
  @flex_a_flex();
  background: #f2f2f2;
  color: #999999;
  .whole_box_no_icon{
    font-size: 50px;
  }
  p{
    font-size: 14px;
  }
}
.sales_list_boxs {
  height: calc(100% - 54px);
  .public_list_title {
    height: 40px;
    @flex_a_bw();
    padding: 0 5px;
    border-bottom: 1px solid #f2f2f2;
    background: #ffffff;
    div {
      height: 40px;
      line-height: 40px;
      font-size: 16px;
      text-align: center;
      width: calc(25% - 10px);
      padding: 0 5px;
      font-weight: 500;
      color: #333333;
    }
    div:first-child {
      width: calc(50% - 10px);
      text-align: left;
    }
    div:last-child {
      width: calc(25% - 10px);
      text-align: right;
    }
  }
}
.van_list{
  height: calc(100% - 40px);
  overflow-x: hidden;
  overflow-y: auto;
  .sales_ul {
    padding: 0 5px;
    background: #ffffff;
    li {
      height: auto;
      overflow: hidden;
      padding: 10px 5px;
      border-bottom: 1px solid #f2f2f2;
      .sales_ul_t {
        overflow: hidden;
        display: flex;
        height: auto;
        div {
          font-size: 15px;
        }
        .sales_ul_tl {
          color: #333333;
          width: 75%;
          text-align: left;
        }
        .sales_ul_tr {
          font-size: 14px;
          color: #1989fa;
          width: 25%;
          text-align: right;
        }
      }
      .sales_ul_b {
        display: flex;
        flex-direction: row;
        justify-content: space-between;
        text-align: left;

        height: auto;
        margin-top: 5px;
        .sales_ul_bl {
          font-size: 13px;
          width:70%;
          overflow: hidden;
          color: #666666;
        }
        .sales_ul_br {
        font-size: 13px;
        width: 30%;
        color: #666666;
        height: 30px;
        @flex_a_end();
        i {
          font-size: 22px;
          color: #1989fa;
          margin-left: 10px;
        }
      }
      }
    }
    li:last-child {
      border-bottom: none;
    }
  }
}
	
	.wrapper{ 
      position: fixed; 
      left: 0px; 
      bottom: 0px; 
      width: 100%; 
      height: 35px; 
      font-size: 0.5em;
      color: #EBEEF5;
      background-color: #555; 
      z-index: 9999;
      display: flex;
      align-items: center;
      justify-content: flex-end;
        .content{
          padding-right: 15px;
        } 
        .record{
          padding: 0 10px;
        }
    }
</style>

