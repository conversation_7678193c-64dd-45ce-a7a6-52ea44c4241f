// 增强版 HTTP 网络请求封装
import smartHttp from './smart-http'

// 导出增强版 HTTP 请求函数
export const enhancedApiGet = (url, params = {}, options = {}) => {
  // 设置默认超时时间
  const enhancedOptions = {
    timeout: 20000, // 20秒超时
    ...options
  }
  
  return smartHttp.get(url, params, enhancedOptions)
}

export const enhancedApiPost = (url, params = {}, options = {}) => {
  // 设置默认超时时间
  const enhancedOptions = {
    timeout: 20000, // 20秒超时
    ...options
  }
  
  return smartHttp.post(url, params, enhancedOptions)
}