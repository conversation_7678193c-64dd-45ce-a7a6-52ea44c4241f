<template>
  <div class="pages">
    <van-nav-bar
      left-arrow
      title="蓝牙打印机"
      @click-left=" myGoBack($router)" 
      safe-area-inset-top
    />
    <div class="public_box2">
      <div class="public_box2_t">
        <van-cell title="开启蓝牙">
          <template #right-icon>
            <van-switch v-model="checked" size="22" @change="open_bluetooth" />
          </template>
        </van-cell>
        <van-cell title="纸张样式" class="paperSize">
          <van-radio-group v-model="radio" direction="horizontal" class="radio_group" @change="changeValue">
            <van-radio name="58">58mm</van-radio>
            <van-radio name="76">76mm</van-radio>
            <van-radio name="80">80mm</van-radio>
          </van-radio-group>
        </van-cell>
        <ul class="public_box2_b" >
          <li v-if="deviceList.length === 0 && checked" style="text-align: center; padding: 20px; color: #999;">
            <div v-if="buttonName === '正在搜索'">正在搜索设备...</div>
            <div v-else>
              <div>未找到设备</div>
              <div style="font-size: 12px; margin-top: 5px;">请确保设备已开启并可被发现，或点击下方按钮重新搜索</div>
            </div>
          </li>
          <li v-for="(item,index) in deviceList" :key="index" @click="onBlueToothSelect(item)">
            <h5>{{ item.name }}({{item.class}})</h5>
            <div>
              <span>{{ item.id }}</span>
              <span v-if="selectedID==item.id" style="color:blue;">已选中</span>
              <span v-else></span>
            </div>
          </li>
        </ul>
      </div>
      <div class="search_button" @click="searchBlueToothDevicesWithPermissionCheck"><van-button type="info" ref="button">{{buttonName}}</van-button></div>
    </div>
  </div>
</template>
<script>
import {NavBar, Cell, Switch, RadioGroup, Radio, Button, Toast} from 'vant'
import BlePrinter from './BlePrinter'
import ClassicPrinter from './ClassicPrinter'
import BluetoothPermissionHelper from './BluetoothPermissionHelper'

export default {
  name: "BluetoothPrint",
  data() {
    return {
      checked: false,
      radio: '',
      open_search: false,
      buttonName: '搜索蓝牙',
      deviceList:[],
      firstSearch: true,
      bluetoothType:'classic', //classic or ble
      permissionHelper: new BluetoothPermissionHelper(),
      isFirstLoad: true // 标记是否是第一次加载页面
    }
  },
  components: {
    "van-nav-bar": NavBar,
    "van-cell": Cell,
    "van-switch": Switch,
    "van-radio-group": RadioGroup,
    "van-radio": Radio,
    "van-button": Button
  },
  computed:{
    selectedID(){return this.$store.state.printerID}
  },
  mounted() {
    this.radio = this.$store.state.paperSize?this.$store.state.paperSize:'1'
    if(this.$store.state.openBluetooth === true){
      this.checked = true
    }
    // 第一次加载页面时，延迟初始化，给权限申请留出时间
    if(this.checked){
       this.permissionHelper.delayExecution(() => {
         this.searchBlueToothDevicesWithPermissionCheck(true) // 传入true表示是第一次搜索
       }, 800) // 增加延迟时间，确保权限申请完成
    }
    this.isFirstLoad = false
  },
  methods: {
  async  open_bluetooth(bluetoothOpen) {
      if (bluetoothOpen){
        let that = this
        if(window.isiOS){
          bluetoothSerial.isEnabled(
            function() {
              Toast.success("蓝牙已启动")
              // 延迟一下再搜索，确保蓝牙完全启动
              setTimeout(() => {
                that.searchBlueToothDevicesWithPermissionCheck()
              }, 300)
            },
            function() {
              Toast.fail("请重试或检查设备蓝牙是否已打开")
              that.checked = false
              that.$store.commit("openBluetooth", false)
            }
          );
        }else{
          that.initClassicBlutoothPrinter()
        }
        this.$store.commit("openBluetooth", bluetoothOpen)
      } else {
        this.deviceList = []
        this.$store.commit("openBluetooth", false)
      }
    },
    async initClassicBlutoothPrinter(){
      try {
        const classicPrinter=new ClassicPrinter()
        const res= await classicPrinter.open_bluetooth()
        if (res.ret===1) {
          // 延迟一下再搜索，给权限申请留出时间
          setTimeout(() => {
            this.searchClassicBlutoothDevice()
          }, 500)
        }else{
          Toast.fail("请重试或检查设备蓝牙是否已打开")
          this.checked = false
          this.$store.commit("openBluetooth", false)
        }
      } catch (error) {
        console.error("初始化经典蓝牙失败:", error)
        Toast.fail("蓝牙初始化失败，请重试")
        this.checked = false
        this.$store.commit("openBluetooth", false)
      }
    },
    async initBleBlutoothPrinter(){
      try {
        const blePrinter=new BlePrinter()
        const res= await blePrinter.open_bluetooth()
        if (res.ret===1) {
          // 延迟一下再搜索，给权限申请留出时间
          setTimeout(() => {
            this.searchBleBlutoothDevice()
          }, 500)
        }else{
          Toast.fail("请重试或检查设备蓝牙是否已打开")
          this.checked = false
          this.$store.commit("openBluetooth", false)
        }
      } catch (error) {
        console.error("初始化BLE蓝牙失败:", error)
        Toast.fail("BLE蓝牙初始化失败，请重试")
        this.checked = false
        this.$store.commit("openBluetooth", false)
      }
    },
    // 新增：带权限检查的设备搜索方法
    async searchBlueToothDevicesWithPermissionCheck(isFirstTime = false){
      // 如果蓝牙未开启，提示用户先开启蓝牙
      if (!this.checked) {
        if (!isFirstTime) { // 第一次加载时不显示提示
          Toast.fail("请先开启蓝牙开关")
        }
        return
      }

      try {
        // 使用权限助手进行安全搜索
        await this.permissionHelper.safeSearchDevices({
          successCallback: () => {
            console.log("设备搜索完成")
          },
          errorCallback: (message) => {
            if (!isFirstTime) { // 第一次加载时不显示错误提示
              Toast.fail(message)
            }
            // 如果是蓝牙相关错误，重置开关状态
            if (message.includes("蓝牙")) {
              this.checked = false
              this.$store.commit("openBluetooth", false)
            }
          },
          searchFunction: () => this.searchBlueToothDevices(),
          isFirstTime: isFirstTime
        })
      } catch (error) {
        console.error("搜索设备时发生错误:", error)
        if (!isFirstTime && !this.permissionHelper.isPermissionError(error)) {
          Toast.fail("搜索设备失败，请稍后重试")
        }
      }
    },
    async searchBlueToothDevices(){
    this.buttonName = '正在搜索'
    if(window.isiOS){
      console.log("iOS search")
       bluetoothSerial.list((data) => {
         this.deviceList = data.filter(device => device.name)
         this.buttonName = '搜索蓝牙'
         console.log('list sccess:', JSON.stringify(data))
       }, (error) => {
         console.error('iOS获取设备列表失败:', error)
         Toast.fail('获取列表失败，请检查蓝牙权限')
         this.buttonName = '搜索蓝牙'
       })
    }else{
      console.log("Android search")
      if (this.bluetoothType==='classic') {
        this.searchClassicBlutoothDevice()
      }
      if (this.bluetoothType==='ble') {
        this.searchBleBlutoothDevice()
      }
    }
    },
   async searchBleBlutoothDevice(){
      try {
        if(this.firstSearch){
         ble.scan([],15,(device) => {
           this.addDeviceToDeviceList(device)
         },(e)=>{
           console.error('BLE扫描失败:', e)
           // 权限相关错误不显示错误提示，因为可能正在申请权限
           if (!e.toString().includes('permission') && !e.toString().includes('Permission')) {
             Toast.fail('BLE设备扫描失败')
           }
         })
        }
        console.log(this.deviceList)
        this.deviceList = []
        const deviceList=await new BlePrinter().list()
        this.deviceList=deviceList
      } catch (error) {
        console.error('搜索BLE设备失败:', error)
        // 权限相关错误不显示错误提示
        if (!error.toString().includes('permission') && !error.toString().includes('Permission')) {
          Toast.fail('搜索BLE设备失败，请稍后重试')
        }
      }
    },
  async  searchClassicBlutoothDevice(){
      try {
        if(this.firstSearch){
         bluetoothSerial.setDeviceDiscoveredListener((device) => {
           this.addDeviceToDeviceList(device)
         },(e)=>{
           console.error('经典蓝牙设备发现失败:', e)
           // 权限相关错误不显示错误提示
           if (!e.toString().includes('permission') && !e.toString().includes('Permission')) {
             Toast.fail('蓝牙设备发现失败')
           }
         })
        }
        console.log(this.deviceList)
        this.deviceList = []
        const deviceList=await new ClassicPrinter().list()
        this.deviceList=deviceList
      } catch (error) {
        console.error('搜索经典蓝牙设备失败:', error)
        // 权限相关错误不显示错误提示
        if (!error.toString().includes('permission') && !error.toString().includes('Permission')) {
          Toast.fail('搜索蓝牙设备失败，请稍后重试')
        }
      }
    },
    addDeviceToDeviceList(device){
      if (this.deviceList.some(_device=>_device.name===device.name)) {
        return
      }
      this.deviceList.push(device)
      this.$forceUpdate() 
    },
    onBlueToothSelect(device){
      if (this.bluetoothType==='classic') {
          this.connectClassicPrinter(device)
      }
      else{
          this.connectBlePrinter(device)
      }
    },
    connectClassicPrinter(device){
      // eslint-disable-next-line no-undef
      bluetoothSerial.connect(device.id, connectClassicSuccess, connectClassicFailure)
      var that = this
      function connectClassicSuccess(){
        that.$store.commit("printerID", device.id);
        that.$store.commit("printerName", device.name);
        that.$store.state.bluetoothType='classic'
        Toast.success("连接成功，请返回打印")
        setTimeout(function(){ bluetoothSerial.disconnect() },500)
      }
      function connectClassicFailure(){
        that.bluetoothType='ble'
        that.initBleBlutoothPrinter()
        that.searchBleBlutoothDevice()
      }
    },
    connectBlePrinter(device){
    console.log("ble",ble,device)
    ble.connect(device.id, connectBleSuccess, connectBleFail)
    var that = this
    function connectBleSuccess(e){
      console.log("peripheralData",e)
      const peripheralData=e
      let printerUUID={}
      peripheralData.characteristics.forEach(element => {
        if (element.properties.indexOf("Write")!==-1) {
          printerUUID=element
        }
      });
      console.log("printerUUID",printerUUID)
      that.$store.commit("printerID", device.id);
      that.$store.commit("printerUUID", printerUUID);
      that.$store.commit("printerName", device.name);
      that.$store.state.bluetoothType='ble'
      Toast.success("连接成功，请返回打印")
      console.log(device)
      setTimeout(function(){ ble.disconnect(device.id,()=>{},()=>{}) },500)
    }
    function connectBleFail(){
      Toast.fail("连接失败,请等待片刻后重试或检查设备蓝牙是否已打开")
    }
    },
    changeValue() {
      let radioValue = this.radio
      this.$store.commit("paperSize", radioValue)
    }
  }
}
</script>
<style lang="less" scoped>
/deep/.van-cell__title{
  text-align: left;
  vertical-align: middle;
}
/deep/.van-cell{
  font-size: 16px;
}
/deep/.van-button--normal{
  font-size: 16px;
}
.public_box2_t{
  height: calc(100% - 35px);
}
.public_box2_b{
  overflow-x: hidden;
  overflow-y: auto;
  padding: 5px;
  background: #ffffff;
  li{
    height: 100%;
    overflow: hidden;
    padding: 5px;
    border-bottom: 1px solid #f2f2f2;
    h5{
      font-size: 14px;
      font-weight: normal;
      text-align: left;
    }
    div{
      font-size: 13px;
      display: flex;
      justify-content: space-between;
      margin-top: 8px;
    }
  }
  li:last-child{
    border-bottom: none;
  }
  :hover{
    background: #f2f2f2;
    color: #1989fa;
  }
}
.paperSize{
  display: inline-block;
  .radio_group{
    display: flex;
    justify-content: space-between;
    padding-top: 8px;
  }
}
.search_button{
  width: 100%;
  height: 35px;
  box-sizing: border-box;
  vertical-align: top;
  button{
    width: 100%;
    height: 100%;
    vertical-align: top;
  }
}
</style>