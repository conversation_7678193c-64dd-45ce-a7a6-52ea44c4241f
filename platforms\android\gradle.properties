# 优化Gradle性能配置
org.gradle.jvmargs=-Xmx2048m
org.gradle.parallel=true
org.gradle.caching=true
org.gradle.configureondemand=true
org.gradle.daemon=true

android.useAndroidX=true
android.enableJetifier=true

# 网络超时优化
systemProp.http.connectionTimeout=60000
systemProp.http.socketTimeout=60000
systemProp.https.connectionTimeout=60000
systemProp.https.socketTimeout=60000

# 代理设置（通常不需要，因为使用了国内镜像）
# 如果确实需要代理，可以取消注释下面的行：
# systemProp.http.proxyHost=127.0.0.1
# systemProp.http.proxyPort=7890

# 现在使用系统环境变量中的 Gradle 8.7
# Gradle 8.7放在e:/gradle/gradle-8.7
# 如需跳过 Gradle Wrapper，使用系统 Gradle，可在构建前设置：
# PowerShell: $env:USE_SYSTEM_GRADLE = "true"
#$ env:CORDOVA_ANDROID_GRADLE_DISTRIBUTION_URL = "file:///E:/gradle/gradle-8.7-bin.zip"

#这样gradle wrapper 就不生效了，gradle wrapper会读gradle-wrapper.properties中的gradle文件地址并安装，但是我们访问不了在线地址
# 然后运行: cordova build android

# 仓库镜像配置在 repositories.gradle 中