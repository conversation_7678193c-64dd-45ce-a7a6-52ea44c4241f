<template>
  <div class="pages">
    <div id="baiduMapScript"></div>
    <van-nav-bar title="行驶轨迹" left-arrow safe-area-inset-top @click-left="myGoBack($router)">
      <!-- <template #right>
        <van-icon name="friends-o" size="22" @click="showUserChoosePop = true" />
      </template>  -->
    </van-nav-bar>
    <div class="public_box2">
      <VisitInfoCard :index="0"  v-if="showVisitInfoFlag" :clickVisitInfoList="clickVisitInfoList"></VisitInfoCard>
      <div class="visitrecord-row" v-if="!realTimePositionMode && !showVisitInfoFlag">
        <VisitRecordItem :style="{background: item.isActive?'#f0f0f0':'#333',color:item.isActive?'#333':'#f0f0f0'}" @clickVisitRecord="clickVisitRecord"  v-for="item,index in visitRecordList" :key="index" :visitInfoItem="item"></VisitRecordItem>
      </div>
    <div v-if="showTipContainer" class="tip-container">
      <div class="tip-item-container">        
        <svg width="30px" height="30px" fill="#666">
           <use xlink:href="#icon-journey_dash"></use>
         </svg>
         <div  style="font-size:12px;">
           距离大于5KM
         </div>
         </div>
      <div class="tip-item-container">
        <svg  width="30px" height="30px" fill="#666">
           <use xlink:href="#icon-journey_solid"></use>
         </svg>
         <div style="font-size:12px;">
            距离小于5KM
         </div>
        </div>
        <div class="tip-item-container">
        <svg  width="24px" height="24px" fill="#666">
           <use xlink:href="#icon-journey_radar"></use>
         </svg>
         <div style="font-size:12px;">
            查看业务员当前位置开关
         </div>
        </div>
    </div>
    <!-- <div @click="showTipContainer=!showTipContainer" style="position:absolute;z-index:2;right:12px;top:12px;">
         <svg width="30px" height="30px" fill="#666">
         <use xlink:href="#icon-question"></use>
      </svg>
    </div>
    <div style="position:absolute;z-index:2;right:12px;top:46px;">
      <svg width="30px" height="30px" fill="#666">
         <use xlink:href="#icon-settings"></use>
      </svg>
    </div> -->

    <div  @click="hideBar=!hideBar" style="position:absolute;z-index:999;left:12px;bottom:150px;">
      <svg  width="30px" height="30px" fill="#666">
         <use :xlink:href="hideBar?'#icon-journey_radar_unactive':''"></use>
      </svg>
    </div>
    <div v-if="realTimePositionMode" style="position:absolute;left:10px;top:10px;z-index:99;display:flex;flex-direction:row;">
      <div style="width:10px;height:10px;background:#67C23A;border-radius:50%;"></div>
      <div style="font-size:16px;color:#67C23A;line-height:10px;margin-left:5px;">实时位置</div>
    </div>
    <div id="baidumap"></div>
    <transition leave-active-class="bar_close_ani">
    <div v-if="!hideBar"  class="bill_list">
      <div class="bottom-mask"></div>
      <ul v-if="isBoss()"  class="bill_list_ul" >
        <li v-for="(item, index) in userDatas" :class="item.isActive ? 'bill_Active' : ''" :key="index" @click="userChooseClick(item,index)">
          <div class="name">{{ item.oper_name }}</div>
          <div class="visitcount">拜访：{{item.visit_count}}</div>
        </li>
      </ul>
    <div @click="realTimePositionMode=!realTimePositionMode" class="bird-icon">
      <svg width="30px" height="30px" fill="#666">
         <use :xlink:href="realTimePositionMode===true?'#icon-bird_unactive':'#icon-bird_active'"></use>
      </svg>
    </div>
      <div    class="select-time-container" @click="timeShow=true">
         {{formatDate(selectTime,"mm-dd")}}
      </div>
      </div> 
    </transition>
      <!-- <div class="bill_list">
        <ul class="bill_list_ul">
          <li
            v-for="(item, index) in userDatas"
            :class="item.isActive ? 'bill_Active' : ''"
            :key="index"
            @click="onUserSrc(item)"
          >
            {{ item.oper_name }}
          </li>
        </ul>
      </div> -->
      <!-- <div v-if="this.arrPoisList.length > 0" class="speed-slider">
        小车速度:{{ this.speedValue }}
        <van-slider
          min="100"
          step="100"
          max="10000"
          style="width: 80%; margin-left: 0.5rem; margin-bottom: 0.5rem"
          v-model="speedValue"
          @change="speedChange"
        />
      </div> -->
      <!-- <div  v-if="isPlaying" style="position:absolute;bottom:50%;left:30%;"> 
        <div class="journey_play iconfont" @click="onPause()"  style="position:absolute;z-index:2;right:12px;top:116px;">
          <svg width="30px" height="30px" fill="#666">
            <use xlink:href="#icon-journey_pause"></use>
          </svg>
        </div>
      </div> -->
      <!-- <div  v-if="isPlaying" style="position:absolute;bottom:50%;right:30%;"> 
        <div class="journey_play iconfont" @click="onReplay()"  style="position:absolute;z-index:2;right:50%;top:116px;">
          <svg width="30px" height="30px" fill="#666">
            <use xlink:href="#icon-journey_replay"></use>
          </svg>
        </div>
      </div> -->
      <div v-if="!hideBar" > 
      <div class="journey_play iconfont" v-if="!isPlaying&&arrPoisList.length>0" @click="onRun">
        <svg width="32px" height="32px">
          <use :xlink:href="'#icon-journey_play'"></use>
        </svg>
      </div>
      <div class="journey_play iconfont" v-if="isPlaying" @click="onPause">
        <svg width="32px" height="32px">
          <use :xlink:href="'#icon-journey_pause'"></use>
        </svg>
      </div>

      <!-- <div class="journey_play_no iconfont" v-else>
        <svg width="22px" height="22px">
          <use :xlink:href="'#icon-journey_play'"></use>
        </svg>
      </div> -->
      </div>
    </div>
    <van-calendar :min-date="minDate" :max-date="maxDate" v-model="timeShow" @confirm="journeyTimeConfirm"></van-calendar>
    <van-popup class="van_popups" v-model="showUserChoosePop" :style="{ height: '100%', width: '100%' }" position="right">
      <div class="right-icon-row">
      <div>收起</div>
      <van-icon name="arrow" @click="showUserChoosePop=false"/>
      </div>
      <div class="mapPopup_box">
          <div style="border-bottom:1px solid #f2f2f2;background-color: white;flex: 0 0 32%;margin: 1px;height: 50px;text-align: left; padding-left:10px;line-height: 50px;" @click="onUserSrcPopup(item)" v-for="(item, index) in userDatas" :key="index"> {{ item.oper_name }}</div>
        <!-- <van-list
            v-model="loading"
            :finished="finished"
            finished-text="到底了~"
            @load="onLoad"
          >
            <ul>
              <li
                v-for="(item, index) in userDatas"
                :class="item.isActive ? 'popupActive' : ''"
                :key="index"
                @click="onUserSrcPopup(item)"
              >
                {{ item.oper_name }}
              </li>
            </ul>
          </van-list> -->
      </div>
    </van-popup>
    <!--        :formatter="formatter"-->
  </div>
</template>
<script>
import {
  NavBar,
  Popup,
  Search,
  List,
  Slider,
  Calendar,
  Icon,
  Popover,
  Divider,
  Field,
} from "vant";
import { GetSellerRealPosition, GetSellersInTrail, GetTrail,GetVisitDetailLists } from "../../api/api";
import BaiduArrowPolyLine from "../components/BaiduArrowPolyLine";
import { ImagePreview } from "vant";
import VisitInfoCard from "./VisitedInfoCard";
import VisitRecordItem from "./VisitRecordItem";
import globalVars from "../../static/global-vars";
import Position from '../../components/Position';
import { UseGLMap, UseLushu, UseMapV, UseMapvGL } from '../service/MapLoader';

export default {
  name: "journey",
  data() {
    return {
      showUserChoosePop: false,
      journeytDate: false,
      operId: "",
      list: [],
      userDatas: [],
      visitPointList: [],
      visitRecordList:[],
      timeShow: false,
      minDate: new Date(2010, 0, 1),
      maxDate: new Date(),
      selectTime: this.formatDate(new Date()),
      realTimePositionMode:true,
      isPlaying: false,
      planPath: [],
      map: null,
      lushu: Object,
      showVisitInfoFlag: false,
      clickVisitInfoList: [],
      clickVisitIndex: 0,
      arrPoisList: [],
      hideBar:false,
      imgList: Array,
      speedValue: 1000,
      icon: {
        url: "http://api.map.baidu.com/library/LuShu/1.2/examples/car.png",
        size: { width: 52, height: 26 },
        opts: { anchor: { width: 27, height: 13 } },
      },
      showTipContainer:false,
    };
  },
  async mounted() {
    await UseGLMap()
    await UseMapV()
    await UseMapvGL()
    await UseLushu()
    var map = new BMapGL.Map("baidumap");
    let params={
      message: "需要定位权限将地图显示在当前位置",
      key: "positionJourney"
    }
    Position.getPosition(params).then(({longitude,latitude})=>{
       map.centerAndZoom(new BMapGL.Point(longitude,latitude),15)
    })
    map.enableScrollWheelZoom(true);
    map.addEventListener("dragstart",e=>{
          this.hideBar=true
          this.showVisitInfoFlag = false;
    })
    this.map=map

    this.onloadDatas();

  },
  beforeRouteLeave(to, from, next) {
      this.map.destroy()
      next()
    
  },

  watch:{
    selectTime(){
      GetSellersInTrail({queryDay:this.selectTime}).then((res) => {
        if (res.result === "OK") {
         const normalStatusUserData=res.data.filter(e=>Number(e.status)===1 || e.status==='')
         if (normalStatusUserData.length===0) {
           this.$toast("暂无可选业务员")
           return
         }
        this.userDatas=normalStatusUserData     
      }
      });  
    },
realTimePositionMode(realTimeMode){
  if(realTimeMode===true){
      this.doSellerRealPositionRequest()
  }
  if(realTimeMode===false){
      this.onSrcJourney()
  }
}
  },
  components: {
    "van-nav-bar": NavBar,
    "van-popup": Popup,
    "van-search": Search,
    "van-list": List,
    "van-icon": Icon,
    "van-divider": Divider,
    "van-field": Field,
    "baidu-arrow-polyline": BaiduArrowPolyLine,
    "van-slider": Slider,
    "van-calendar": Calendar,
    "van-popover":Popover,
    VisitInfoCard: VisitInfoCard,
    VisitRecordItem
  },
  methods: {
    clickVisitRecord(item){
      console.log(item)
      this.map.centerAndZoom(new BMapGL.Point(item.longitude,item.latitude),15)
      this.visitRecordList.map(record=>{
        if(item.visit_id===record.visit_id){
          record.isActive = true
        }else{
          record.isActive = false
        }
        return record
      })
      if(window.g_lushu){
        this.onPause()
      }
      this.$forceUpdate()
    },
    isBoss(){
      return window.isBoss()
    },
    calNextDay(date){
      let d=new Date(date)
      d=+d+1000*60*60*24
      d=new Date(d)
      return d.format("yyyy-MM-dd hh:mm:ss")
    },
    userChooseClick(item,index){
      this.userDatas.map(e=>{
          e.isActive=false      
      })
      item.isActive=true
      this.operId=item.oper_id
      this.loadTopVisitRecordList()
      this.realTimePositionMode=false
      // this.onSrcJourney()
    },
    loadTopVisitRecordList(){
      const selectDay=this.selectTime
      const nextDay=this.calNextDay(selectDay)
      const seller_id=this.operId
      GetVisitDetailLists(({seller_id:seller_id,pageSize:20,startRow:0,startTime:selectDay,endTime:nextDay})).then(res=>{
        this.visitRecordList=res.data
        this.visitRecordList.map(record=>{
          record.isActive = false
          return record
        })
        console.log(this.visitRecordList)
      })
    },
    closeVisitInfoClick() {
      this.showVisitInfoFlag = false;
    },
    journeyTimeConfirm(e) {
      this.selectTime = this.formatDate(new Date(e));
      this.timeShow = false;
      this.realTimePositionMode=false
      this.resetTrail()
      // this.onSrcJourney();
    },
    preiviewPic(imgList, index) {
      ImagePreview({
        images: imgList,
        startPosition: index,
      });
    },
    onloadDatas() {
      if (!this.isBoss()) {
        console.log("我不是老板")
        this.operId=this.$store.state.operInfo.oper_id
        this.onSrcJourney();
        return
      }
      this.selectTime = this.formatDate(new Date());
      this.doGetSellerDataRequest()
      this.doSellerRealPositionRequest()
    },
    doGetSellerDataRequest(){
      GetSellersInTrail({queryDay:this.selectTime}).then((res) => {
        if (res.result === "OK") {
         const normalStatusUserData=res.data.filter(e=>Number(e.status)===1 || e.status==='')
         if (normalStatusUserData.length===0) {
           this.$toast("暂无可选业务员")
           return
         }
        this.selectTime = this.formatDate(new Date());
        this.userDatas=normalStatusUserData     
      }
      });
    },
    doSellerRealPositionRequest(){
      GetSellerRealPosition().then(res=>{
        const sellerRealTimeDataList=res.data
        if(sellerRealTimeDataList.length==0){
          this.$toast("暂无员工实时点位")
          return
        }
        this.resetTrail()
        this.showSellerRealTimePosition(sellerRealTimeDataList)
      })
    },
    resetTrail(){
        this.arrPoisList=[]
        this.userDatas.map(e=>{
          e.isActive=false
        })
    },

    showSellerRealTimePosition(sellerRealTimeDataList){
      this.arrPoisList=[]
      this.map.clearOverlays()	
      this.map.reset()
      window.g_lushu=null
      this.map.centerAndZoom(new BMapGL.Point(sellerRealTimeDataList[0].longitude,sellerRealTimeDataList[0].latitude), 15);
      sellerRealTimeDataList.map(e=>{
        const label=this.createLabel(e.oper_name+"\n"+e.happen_time.split(" ")[1])
        var icon = new BMapGL.Icon( this.getServerUri()  +"images/seller_cur_icon.png", new BMapGL.Size(24,24));
        const marker=this.createVisitorMarker(new BMapGL.Point(e.longitude, e.latitude),label,icon)
        this.map.addOverlay(marker)
      })
    },
    speedChange(e) {
      this.speedValue = e;
    },
    onRun() {
      if(!window.g_lushu){
        var lushu = new BMapGLLib.LuShu(this.map, this.arrPoisList, {
        defaultContent: "", //"从天安门到百度大厦"
        autoView: true, //是否开启自动视野调整，如果开启那么路书在运动过程中会根据视野自动调整
        icon: new BMapGL.Icon(this.getServerUri() +"images/position_icon.png",new BMapGL.Size(24, 24),{ anchor: new BMapGL.Size(10, 10) }),speed: this.speedValue,landmarkPois: []});
        window.g_lushu=lushu
      }
      window.g_lushu.start();
      this.isPlaying=true
    },
    onPause() {
      window.g_lushu.stop();
      this.isPlaying=false
    },
    onUserSrcPopup(obj) {
      this.userDatas.map((item) => {
        if (item.oper_id === obj.oper_id) {
          item.isActive = true;
        } else {
          item.isActive = false;
        }
      });
      this.selectTime = this.formatDate(new Date());
      this.operId = obj.oper_id;
      // this.onSrcJourney();
      this.showUserChoosePop = false;
      this.timeShow = true;
    },
    generateColorCode() {
        var colorcode = ""
        var str = "ABCDEF123456789"
        for (var i = 0; i < 6; i++) {
            colorcode += str.charAt(parseInt((Math.random() * 100)) % 15);
        }
        return "#" + colorcode;
    },
    formatImageUrl(prefix, url) {
      return prefix + url;
    },
    formatImageListFormJson(doorPicStr, displayPicsStr) {
      var server_url =  globalVars.obs_server_uri + "/uploads";
      let imgList = [];
      const doorPicUrl = this.formatImageUrl(server_url, doorPicStr);
      imgList.push(doorPicUrl);
      if (displayPicsStr === "") {
        displayPicsStr = "[]";
      }
      const displayPics = JSON.parse(displayPicsStr);
      displayPics.forEach((e) => {
        imgList.push(this.formatImageUrl(server_url, e));
      });
      return imgList;
    },
  onSrcJourney() {
    this.map.clearOverlays()	
    this.map.reset()
    this.isPlaying=false
    console.log(window.g_lushu)
    if(window.g_lushu){
      console.log("有路书")
      this.arrPoisList=[]
      window.g_lushu.stop()
      window.g_lushu=null
    }
    if(!this.operId){
        this.$toast("请先选择业务员")
        this.realTimePositionMode=''
        return
      }
      let params = {
        seller_id: this.operId,
        happen_day: this.selectTime,
      };
      let that_ = this;
      this.realTimePositionMode=""
      GetTrail(params).then((res) => {
        if (res.result !== "OK") {
          // this.$toast("请求失败");
          return;
        }
        if (res.data.length===0) {
            this.$toast("该日无轨迹")
            this.resetTrail()
            return
          }
        if (that_.arrPoisList.length != 0) {
          that_.arrPoisList = [];
        }

        let supNameDic = [];
        const visitMarkers=[]
        if(typeof this.view!='undefined'){
            this.view.destroy()
            this.map.clearOverlays()
        }
        this.view = new mapvgl.View({
              map: this.map
        });
        res.data.forEach((e) => {
          //标记点位去重
          if (e.visit_id != "" && supNameDic.indexOf(e.sup_name) === -1) {
            supNameDic.push(e.sup_name);
            var label = this.createLabel(e.sup_name);
            const point = new BMapGL.Point(e.longitude, e.latitude);
            const icon=  new BMapGL.Icon( this.getServerUri() +"images/journey_shopicon.png", new BMapGL.Size(24,24));
            var marker = this.createVisitorMarker(point, label,icon);
            //visitMarkers.push(marker)
            this.map.addOverlay(marker)
            marker.addEventListener("click", function () {
              //列出一天多次访问一家门店
              const theSameDayVisitorList = res.data.filter(
                (res) => res.sup_name === e.sup_name
              );
              that_.clickVisitInfoList = theSameDayVisitorList.map(
                (clickVisitInfo) => {
                  const imgListArray = that_.formatImageListFormJson(clickVisitInfo.door_picture,clickVisitInfo.showcase_pictures);
                  clickVisitInfo.imgList = imgListArray;
                  return clickVisitInfo;
                }
              );
              that_.showVisitInfoFlag = true;
            });
          }
        });
        
        this.map.centerAndZoom(new BMapGL.Point(res.data[0].longitude,res.data[0].latitude), 13);
        let task=[]
        for (let index = 0; index < res.data.length - 1; index++) {
          const startPoint = res.data[index];
          const endPoint = res.data[index + 1];
          const distance = this.getDistance(startPoint.latitude,startPoint.longitude,endPoint.latitude,endPoint.longitude);
          const isDashed = distance >= 5000 ? true : false;
          const arrPoisPromise=  this.searchRoute(this.map, startPoint, endPoint,distance,isDashed)
          // this.arrPoisList=this.arrPoisList.concat(arrPois)
          task.push(arrPoisPromise)
        }
        //解决 有时轨迹无法运动的问题 假如出现小车乱跑，请将其改成 Promise.all(task)
        Promise.all(task).then(promiseArrPois => {
          this.arrPoisList = [];
          let dashedLinePoints = [];
          let solidLinePoints = [];
          let pointMarkers = [];
          
          promiseArrPois.forEach((arrPois, index) => {
            if (arrPois.length > 0) {
              // Store points with their type (dashed or solid)
              const startPoint = res.data[index];
              const endPoint = res.data[index + 1];
              console.log(endPoint,startPoint)
              const distance = this.getDistance(startPoint.latitude, startPoint.longitude, 
                                               endPoint.latitude, endPoint.longitude);
              
              // Add trajectory points to map with timestamps
              const pointIcon = new BMapGL.Icon(this.getServerUri() + "images/journey_point.png", new BMapGL.Size(16, 16));
              const pointMarker = new BMapGL.Marker(arrPois[0], {icon: pointIcon});
              
              // Add click event to show timestamp
              pointMarker.addEventListener("click", () => {
                const infoWindow = new BMapGL.InfoWindow(
                  `<div style="padding: 8px;">时间: ${startPoint.happen_time}</div>`, 
                  {width: 200, height: 60, enableMessage: false}
                );
                this.map.openInfoWindow(infoWindow, arrPois[0]);
              });
              
              this.map.addOverlay(pointMarker);
              
              // Categorize points based on distance
              if (distance >= 5000 || (new Date(endPoint.happen_time).getTime() - new Date(startPoint.happen_time).getTime()) > 5*60*1000) {
                dashedLinePoints = dashedLinePoints.concat(arrPois);
              } else {
                solidLinePoints = solidLinePoints.concat(arrPois);
              }
              
              this.arrPoisList = this.arrPoisList.concat(arrPois);
            }
          });
          
          if (this.arrPoisList.length > 0) {
            // Add start and end markers
            var startPointIcon = new BMapGL.Icon(this.getServerUri() + "images/startpoint_icon.png", new BMapGL.Size(24, 24));
            const startMarker = new BMapGL.Marker(this.arrPoisList[0], {icon: startPointIcon});
            this.map.addOverlay(startMarker);
            
            var endPointIcon = new BMapGL.Icon(this.getServerUri() + "images/endpoint_icon.png", new BMapGL.Size(24, 24));
            const endMarker = new BMapGL.Marker(this.arrPoisList[this.arrPoisList.length-1], {icon: endPointIcon});
            this.map.addOverlay(endMarker);
            
            // Create MapV view if not exists
            if (!this.view) {
              this.view = new mapvgl.View({map: this.map});
            }
            
            // Create solid line layer
            if (solidLinePoints.length > 0) {
              const solidLineData = this.convertToMapVFormat(solidLinePoints);
              var solidLineLayer = new mapvgl.LineLayer({
                width: 6,
                color: '#67C23A',
                style: 'road',
                enablePicked: true
              });
              this.view.addLayer(solidLineLayer);
              solidLineLayer.setData(solidLineData);
            }
            
            // Create dashed line layer
            if (dashedLinePoints.length > 0) {
              const dashedLineData = this.convertToMapVFormat(dashedLinePoints);
              var dashedLineLayer = new mapvgl.LineLayer({
                width: 6,
                color: '#c40000',
                style: 'dash',
                dashArray: [10, 10],
                enablePicked: true
              });
              this.view.addLayer(dashedLineLayer);
              dashedLineLayer.setData(dashedLineData);
            }
          }
        })
        .catch((msg) => {
          console.log(msg);
        });
      });
    },
    createLabel(content) {
      var label = new BMapGL.Label(content, { offset: new BMapGL.Size(-10, 10) });
      label.setStyle({
            color: '#fff',
            borderRadius: '5px',
            borderColor: '#fff',
            padding: '1px',
            fontSize: '8px',
            height: '16px',
            backgroundColor:"#333",
            opacity:"70%",
            lineHeight: '16px',
            fontFamily: '微软雅黑'
      })
      return label;
    },
    createVisitorMarker(point, label,icon={}) {
      var marker = new BMapGL.Marker(point,{icon});
      marker.setLabel(label);
      return marker;
    },
    searchRoute(map, startPoint, endPoint, distance, isDashed = false) {
      const promise = new Promise((resolve, reject) => {
        const onSearchCompleteFunc = function (res) {
          if (isDashed) {
            resolve([{lng:startPoint.longitude,lat:startPoint.latitude,...startPoint},{lng:endPoint.longitude,lat:endPoint.latitude,...endPoint}]);
          }
          if (drv.getStatus() == BMAP_STATUS_SUCCESS) {
            var plan = res.getPlan(0);
            var arrPois = [];
            for (var j = 0; j < plan.getNumRoutes(); j++) {
              var route = plan.getRoute(j);
              arrPois = arrPois.concat(route.getPath());
            }
            
            // Add timestamp to each point for later display
            arrPois = arrPois.map(point => {
              point.timestamp = startPoint.happen_time;
              return point;
            });
            if (arrPois.length > 0) {
              resolve(arrPois);
            } else {
              resolve([]);
            }
          } else {
            resolve([]);
          }
        };
        
        var drv;
        var start = new BMapGL.Point(startPoint.longitude, startPoint.latitude);
        var end = new BMapGL.Point(endPoint.longitude, endPoint.latitude);
        drv = new BMapGL.WalkingRoute("北京", {
          onSearchComplete: onSearchCompleteFunc
        });
        drv.search(start, end);
      });

      return promise;
    },
    getDistance(lat1, lng1, lat2, lng2) {
      var EARTH_RADIUS = 6378137.0; //单位M
      var radLat1 = this.getRad(lat1);
      var radLat2 = this.getRad(lat2);
      var a = radLat1 - radLat2;
      var radLng1 = this.getRad(lng1);
      var radLng2 = this.getRad(lng2);
      var b = radLng1 - radLng2;
      var s =
        2 *
        Math.asin(
          Math.sqrt(
            Math.pow(Math.sin(a / 2), 2) +
              Math.cos(radLat1) *
                Math.cos(radLat2) *
                Math.pow(Math.sin(b / 2), 2)
          )
        );
      s = s * EARTH_RADIUS;
      s = Math.round(s * 10000) / 10000.0;
      return s;
    },
    getRad(d) {
      var PI = Math.PI;
      return (d * PI) / 180.0;
    },
    convertToMapVFormat(points) {
      // Group consecutive points into line segments
      const segments = [];
      let currentSegment = [];
      
      for (let i = 0; i < points.length; i++) {
        currentSegment.push([points[i].lng, points[i].lat]);
        
        // Start a new segment every 100 points to avoid performance issues
        if (currentSegment.length >= 100 || i === points.length - 1) {
          segments.push({
            geometry: {
              type: 'LineString',
              coordinates: currentSegment
            }
          });
          currentSegment = [];
        }
      }
      
      return segments;
    },
  },
};
</script>
<style lang="less" scoped>
.bird-icon{
    z-index:999;
  line-height:30px;
  width:60px;
  top: calc( 100% - 120px );
  position: absolute;
  height:30px;
  border-radius:4px;
  font-size: 14px;
  left: 10px;
}
.select-time-container{
  z-index:999;
  line-height:30px;
  width:60px;
  top: calc( 100% - 120px );
  position: absolute;
  height:30px;
  border-radius:4px;
  background:#e0e0e0;
  font-size: 14px;
  right: 10px;
}
.tip-container{
z-index:2;
position:absolute;
background:#fff;
display:flex;
flex-direction:column;
padding:3px;
right: 54px;
top:10px;
box-shadow: 8px 8px 5px #888888;
border-radius: 3px;
}
// /deep/.van-toast{
//   z-index: 1000;
// }
.tip-item-container{
    display: flex;
  flex-direction: row;
  color: #333333;
  align-items:center;
  
}
.visit-info-container {
  display: flex;
  flex-direction: column;
  height: 4rem;
  padding: 0.4rem;
  color: #f0f0f0;
  opacity: 0.8;
  align-items: flex-start;
  background: #333;
}
@flex_a_j: {
  display: flex;
  align-items: center;
  justify-content: center;
};
@flex_a_bw: {
  display: flex;
  align-items: center;
  justify-content: space-between;
};
.speed-slider {
  background: #fff;
  position: absolute;
  height: 40px;
  bottom: 90px;
  width: 100%;
}
.bar_close_ani{
  animation: close 1500ms linear 0s;
}
@keyframes close {
  0%{
    bottom: 0;
  }
  100%{
    bottom: -100vh;
  }
}
.bill_list {
  width: 100%;
  height: 150px;
  position: fixed !important;
  background: none ;
  bottom: 10px;
  left: 0;
  z-index: 998;
  right: 0;
  .bill_list_ul {
    height: 32px;
    overflow-x: scroll;
    width: 100%;
    height: 100%;
    position: absolute;
    top: calc( 100% - 80px );
    z-index: 999;
    margin-top: 20px;
    display: flex;
    flex-direction: row;
    li {
      z-index: 999;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      height: 40px;
      padding: 10px;
      width:100px;
      opacity: 0.5;
      color: #fff;
      background: #333;
      font-size: 14px;
      flex-shrink: 0;
      z-index: 999;
      text-align: center;
      .name{
        font-size: 16px;
      }
      .visitcount{
        padding: 2px;
        font-size: 14px;
      }
    }
    .bill_Active {
      // background-image: linear-gradient(to bottom right, #333, #c2c2c2);
      background: #f0f0f0;
      color: #333;
      opacity: 0.9;
    }
  }
}
.bill_list::-webkit-scrollbar {
  display: none;
}
.visitrecord-row{
  position: absolute;
  top: 0px;
  z-index: 999;
  width: 100%;
  display:flex
  ;flex-direction:row;
  overflow-x:scroll;
  opacity:0.6;
}
.visitrecord-row::-webkit-scrollbar{
    display: none;

}
.journey_play {
  position: absolute;
  width: 30px;
  height: 30px;
  // background: red;
  z-index: 999;
  bottom:100px;
  right:50%;
  border-radius: 50%;
  font-size: 64px;
  
  color: #ffffff;
}
.journey_play,
.journey_list,
.journey_play_no {
  position: absolute;
  @flex_a_j();
}
.journey_play_no {
  width: 60px;
  height: 60px;
  background: red;
  z-index: 99;
  bottom: 20px;
  left: 50%;
  margin-left: -30px;
  border-radius: 50%;
  font-size: 30px;
  background: #cccccc;
  color: #ffffff;
}
.journey_list {
  width: 40px;
  height: 40px;
  right: 20px;
  bottom: 30px;
  background: radial-gradient(
    rgba(41, 160, 247, 1) 40%,
    rgba(41, 160, 247, 0.5) 100%
  );
  border-radius: 50%;
  color: #ffffff;
  font-size: 22px;
  z-index: 99;
}
.mapPopup_box {
  width: 100%;
  height: 100%;
  padding-top: 30px;
  background: #fff;
  overflow: scroll;
}
.custom_h5 {
  height: 46px;
  line-height: 46px;
  font-size: 16px;
  font-weight: normal;
  color: #333333;
  background: #f2f2f2;
  color: #333333;
  .icon_h5 {
    position: fixed;
    height: 20px;
    width: 20px;
    right: 15px;
    top: 12px;
    display: block;
    @flex_a_j();
    font-size: 20px;
  }
}
.journey_input {
  padding: 10px;
  .van-cell {
    border-bottom: 1px solid #f2f2f2;
  }
}
.mapPopup_box_b {
  height: 100%;
  width: 100%;
  //55px
  overflow-y: auto;
  overflow-x: hidden;
  ul {
    height: auto;
    overflow: hidden;
    background: #ffffff;
    padding: 0 10px;
    li {
      font-size: 14px;
      padding: 10px 0;
      border-bottom: 1px solid #f2f2f2;
      text-align: left;
      color: #333333;
    }
    li:last-child {
      border-bottom: none;
    }
    .popupActive {
      color: #1989fa;
    }
  }
}
.van_popups {
  display: flex;
  flex-direction: column;
}

#baidumap {
width: 100%;
  height: 100%;
}
#mask{
  height: 10000px;
}
.bottom-mask{
  background: #333333;
  height: 150px;
  border-radius:10px 10px 0px 0px;
  width: 100%;
  position: absolute;
  bottom: -10px;
  z-index: 99;
  opacity: 0.5;
}
.right-icon-row>i{
  line-height: 30px;
}
.right-icon-row{
  top: 0px;
  display: flex;
  padding: 10px;
  flex-direction: row;
  line-height: 30px;
  right: 10px;
  position: fixed;
}
</style>
