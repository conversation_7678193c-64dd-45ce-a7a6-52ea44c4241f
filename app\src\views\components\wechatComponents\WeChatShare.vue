<template>
  <div class="we-chat-share-wrapper" v-if="showShare">
    <van-share-sheet v-model="showShare" title="分享单据" :options="isHarmony?optionsForHarmony:options" @select="shareSheetSelect" />
  </div>

</template>

<script>
import { ShareSheet, Toast } from "vant";
// import jsSHA from "jssha"
import globalVars from "../../../static/global-vars";
// import {GetWeChatAuthInfo} from "../../../api/api";

export default {
  name: "WeChatShare",
  components: {
    "van-share-sheet": ShareSheet,
  },
  props: {
    sheet: {
      type: Object,
      default: function () {
        return {}
      }
    },

  },
  data() {
    return {
      showShare: false,
      options: [
        { name: '图片', icon: 'poster' }
      ],
      optionsForHarmony: [
        { name: '二维码', icon: 'qrcode' }
      ],
      weChatConfigInfo: {
        appId: "",
        jsapiTicket: ""
      },
      signInfo: {},
    }
  },
  computed: {
    isHarmony(){
      return window.isHarmony
    },
    allowWeChatShareWebPage() {
      return this.sheet.sheetType === 'X' ||
        this.sheet.sheetType === 'XD' ||
        this.sheet.sheetType === 'T' ||
        this.sheet.sheetType === 'DH' ||
        this.sheet.sheetType === 'TD' ||
        this.sheet.sheetType === 'DB'
    },
    wechatConf() {
      return globalVars.wechatConf;
    }

  },
  mounted() {
    console.log("this.allowWeChatShareWebPage-----------------------",this.allowWeChatShareWebPage,this.sheet.sheetType)

  },
  methods: {
    changeShowShare() {
      // 调试：检查微信插件是否可用
      console.log("微信插件状态检查:", {
        wechatAvailable: typeof window.Wechat !== 'undefined',
        isInstalledAvailable: typeof window.Wechat?.isInstalled === 'function',
        shareAvailable: typeof window.Wechat?.share === 'function'
      });

      this.showShare = true
      if(this.allowWeChatShareWebPage){
        let isContainWx=this.options.find(item=>item.name==='微信')
        // console.log("isContaionWx",isContainWx)
        if(!isContainWx){
          this.options.push( { name: '微信', icon: 'wechat' })
          this.options.push({  name: '二维码', icon: 'qrcode' })
        }
      }
    },
    shareSheetSelect(option) {
      let that = this
      // that.wechatshare()
      if (option.name === "微信") {
        console.log("开始检测微信安装状态...");
        window.Wechat.isInstalled(function (installed) {
          console.log("微信安装检测结果:", installed);
          if (installed) {
            console.log("微信已安装，开始分享...");
            that.wechatshare()
          } else {
            console.log("微信未安装");
            Toast("您还没有安装微信，请先安装微信。");
          }
        }, function (reason) {
          console.error("检测安装微信失败:", reason);
          Toast("检测安装微信失败: " + reason);
        });
      }
      if(option.name === '图片'){
        this.$emit("shareSheetSelected",{
          type:"sharePicture"
        })
      }
      if(option.name === '二维码'){
        this.$emit("shareSheetSelected",{
          type:"shareQRCode",
          webpageUrl:this.getShareLink().webpageUrl
        })
      }
      if(option.name === '微信'){
        this.$emit("shareSheetSelected",{
          type:"shareWechatWebpage"
        })
      }
      this.showShare = false;
    },
    wechatshare() {
      let imgUrl = this.wechatConf.imgUrl
      let title = "", webpageUrl = "";
      let baseUrl = this.wechatConf.baseUrl
      let companyName = this.$store.state.operInfo?.setting?.companyName || this.$store.state.account.companyName
      let appShareRetailPrice = this.$store.state.operInfo?.setting?.appShareRetailPrice || "True"
      switch (this.sheet.sheetType) {
        case "X":
          title = "销售单";
          webpageUrl = baseUrl + "/WeChat/SheetPages/SaleSheet"
          break
        case "XD":
          title = "销售订单";
          webpageUrl = baseUrl + "/WeChat/SheetPages/SaleSheet"
          break
        case "T":
          title = "退货单";
          webpageUrl = baseUrl + "/WeChat/SheetPages/SaleSheet"
          break
        case "TD":
          title = "退货订单";
          webpageUrl = baseUrl + "/WeChat/SheetPages/SaleSheet"
          break
        case "DH":
          title = "定货会";
          webpageUrl = baseUrl + "/WeChat/SheetPages/SaleSheet"
          break
        case "YS":
          title = "预收款单";
          webpageUrl = baseUrl + "/WeChat/SheetPages/PreSheet"
          break
        case "SK":
          title = "收款单";
          webpageUrl = baseUrl + "/WeChat/SheetPages/GetAndPayArrearsSheet?sheetType=SK"
          break
        case "DB":
          title = "调拨单";
          webpageUrl = baseUrl + "/WeChat/SheetPages/SaleSheet"
          break
      }
      webpageUrl += `?sheetID=${this.sheet.sheet_id}&sheetType=${this.sheet.sheetType}&companyID=${this.sheet.company_id}&companyName=${companyName}&appShareRetailPrice=${appShareRetailPrice}`;
      let status = "【已审核】"
      if (this.sheet.red_flag === '2') {
        status = "【已红冲】"
      }
      console.log(webpageUrl)
      window.Wechat.share({
        message: {
          title: title + status,
          description: `来自【${companyName}】`,
          thumb: imgUrl,
          media: {
            type: window.Wechat.Type.WEBPAGE,
            webpageUrl: webpageUrl
          }
        },
        scene: window.Wechat.Scene.SESSION
      }, function () {
        Toast.success("分享成功")
      }, function (reason) {
        Toast.fail("分享失败: " + reason)
      });
    },
    getShareLink(){
      let baseUrl = this.wechatConf.baseUrl
      let webpageUrl = ""
      let companyName = this.$store.state.operInfo?.setting?.companyName || this.$store.state.account.companyName
      var title = ""
      switch (this.sheet.sheetType) {
        case "X":
          title = "销售单";
          webpageUrl = baseUrl + "/WeChat/SheetPages/SaleSheet"
          break
        case "XD":
          title = "销售订单";
          webpageUrl = baseUrl + "/WeChat/SheetPages/SaleSheet"
          break
        case "T":
          title = "退货单";
          webpageUrl = baseUrl + "/WeChat/SheetPages/SaleSheet"
          break
        case "TD":
          title = "退货订单";
          webpageUrl = baseUrl + "/WeChat/SheetPages/SaleSheet"
          break
        case "DH":
          title = "定货会";
          webpageUrl = baseUrl + "/WeChat/SheetPages/SaleSheet"
          break
        case "YS":
          title = "预收款单";
          webpageUrl = baseUrl + "/WeChat/SheetPages/PreSheet"
          break
        case "SK":
          title = "收款单";
          webpageUrl = baseUrl + "/WeChat/SheetPages/GetAndPayArrearsSheet?sheetType=SK"
          break
      }
      webpageUrl += `?sheetID=${this.sheet.sheet_id}&sheetType=${this.sheet.sheetType}&companyID=${this.sheet.company_id}&companyName=${companyName}`;
      return {
        title,
        webpageUrl
      }
    },

    // weChatShare(param) {
    //   if (!param) {
    //     return;
    //   }
    //   this.getWeChatAuthInfo(param)
    // },
    // // 向后端请求权限验证配置的基本数据
    // getWeChatAuthInfo(param) {
    //  GetWeChatAuthInfo({
    //     token:'yingjiang168'
    //   }).then((res) => {
    //     this.weChatConfigInfo = res
    //     this.sign(param)
    //   }).catch(err => {
    //    console.log(err)
    //  })
    // },
    // // 签名算法
    // sign(param) {
    //   var ret = {
    //     jsapi_ticket: this.weChatConfigInfo.jsapiTicket,
    //     nonceStr: this.createNonceStr(),
    //     timestamp: this.createTimestamp(),
    //     url: "http://localhost:8080/"
    //   };
    //   let shaObj = new jsSHA("SHA-512", 'TEXT');
    //   shaObj.update(this.raw(ret))
    //   ret.signature = shaObj.getHash('HEX');
    //   this.signInfo = ret
    //   this.initAPIs(param)
    //
    // },
    // raw(args) {
    //   var keys = Object.keys(args);
    //   keys = keys.sort()
    //   var newArgs = {};
    //   keys.forEach(function (key) {
    //     newArgs[key.toLowerCase()] = args[key];
    //   });
    //   var string = '';
    //   for (var k in newArgs) {
    //     string += '&' + k + '=' + newArgs[k];
    //   }
    //   string = string.substr(1);
    //   return string;
    // },
    // createNonceStr() {
    //   return Math.random().toString(36).substr(2, 15);
    // },
    // createTimestamp () {
    //   return parseInt(new Date().getTime() / 1000) + '';
    // },
    //
    //
    // initAPIs(param) {
    //   let that = this
    //  // 通过config接口注入权限验证配置
    //   wx.config({
    //     debug: true, // 开启调试模式,调用的所有api的返回值会在客户端alert出来，若要查看传入的参数，可以在pc端打开，参数信息会通过log打出，仅在pc端时才会打印。
    //     appId: this.weChatConfigInfo.appId, // 必填，公众号的唯一标识
    //     timestamp: this.signInfo.timestamp, // 必填，生成签名的时间戳
    //     nonceStr: this.signInfo.nonceStr, // 必填，生成签名的随机串
    //     signature: this.signInfo.signature,// 必填，签名
    //     jsApiList: param.APIs // 必填，需要使用的JS接口列表
    //   });
    //   // 判断客户端是否支持指定js的接口
    //   wx.checkJsApi({
    //     jsApiList:param.APIs,
    //     success: function (res) {
    //       console.log("res",res);
    //       return res
    //     }
    //   })
    //   // 验证成功后执行的方法
    //   wx.ready(function () {
    //     that.share(param.callback)
    //   })
    // },
    // share(param) {
    //   var title = param.title
    //   var imgUrl = param.imgUrl
    //   var description =param.description
    //   var url=param.url
    //   var successMethod = param.successMethod
    //   var tempParam = {
    //     'title': title,
    //     'desc': description,
    //     'link': url,
    //     'imgUrl': imgUrl,
    //     success: successMethod
    //   };
    //   //分享给朋友
    //   wx.onMenuShareAppMessage(tempParam);
    //   //分享给朋友圈
    //   wx.onMenuShareTimeline(tempParam);
    // }

  }


}
</script>

<style lang='less' scoped>
/deep/ .van-share-sheet {
  min-height: 210px;
}
.we-chat-share-wrapper {
  height: 100%;
}
</style>
