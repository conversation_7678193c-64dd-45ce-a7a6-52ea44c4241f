#!/usr/bin/env node

/**
 * 修复百度地理位置插件的 AMD 模块包装问题
 * 这个 hook 会在每次 prepare 后自动运行，确保插件被正确包装
 */

const fs = require('fs');
const path = require('path');

module.exports = function(context) {
    console.log('🔧 [BD Geolocation Fix] 开始修复百度地理位置插件...');
    
    const platformRoot = path.join(context.opts.projectRoot, 'platforms');
    const platforms = ['android', 'ios'];
    
    platforms.forEach(platform => {
        const pluginPath = path.join(
            platformRoot, 
            platform, 
            platform === 'android' ? 'app/src/main/assets/www' : 'www',
            'plugins/cordova-plugin-bd-geolocation/www/Geolocation.js'
        );
        
        if (fs.existsSync(pluginPath)) {
            try {
                let content = fs.readFileSync(pluginPath, 'utf8');
                
                // 检查是否已经被包装
                if (!content.startsWith('cordova.define(')) {
                    console.log(`📋 [BD Geolocation Fix] 修复 ${platform} 平台的插件文件...`);
                    
                    // 添加 AMD 模块包装
                    content = `cordova.define("cordova-plugin-bd-geolocation.Geolocation", function(require, exports, module) {\n${content}\n});`;
                    
                    // 写回文件
                    fs.writeFileSync(pluginPath, content, 'utf8');
                    console.log(`✅ [BD Geolocation Fix] ${platform} 平台修复完成`);
                } else {
                    console.log(`✅ [BD Geolocation Fix] ${platform} 平台插件已正确包装，无需修复`);
                }
            } catch (error) {
                console.error(`❌ [BD Geolocation Fix] 修复 ${platform} 平台时出错:`, error.message);
            }
        } else {
            console.log(`⚠️  [BD Geolocation Fix] ${platform} 平台插件文件不存在，跳过`);
        }
    });
    
    console.log('🎉 [BD Geolocation Fix] 百度地理位置插件修复完成');
};
