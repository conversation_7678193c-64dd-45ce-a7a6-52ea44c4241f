package com.yingjiang.app;

import android.content.Context;
import android.util.Log;
import android.os.Build;

/**
 * X5 WebView 初始化帮助类
 * 简化版本，避免编译时依赖问题
 */
public class X5InitHelper {
    private static final String TAG = "X5InitHelper";
    private static boolean isInitialized = false;
    private static boolean isInitializing = false;
    
    /**
     * 安全初始化 X5 内核
     */
    public static void initX5(Context context) {
        if (isInitialized || isInitializing) {
            return;
        }
        
        isInitializing = true;
        Log.i(TAG, "开始初始化 X5 内核");
        
        // Android 10+ 直接禁用 X5，避免权限问题
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
            Log.i(TAG, "检测到 Android 10+，禁用 X5 以避免权限问题");
            forceSystemWebView();
            isInitializing = false;
            return;
        }
        
        // 对于老版本 Android，尝试初始化 X5
        try {
            // 使用反射调用 X5 初始化
            Class<?> qbSdkClass = Class.forName("com.tencent.smtt.sdk.QbSdk");
            
            // 简单的初始化，不设置复杂参数
            java.lang.reflect.Method initMethod = qbSdkClass.getMethod("initX5Environment", 
                Context.class, Class.forName("com.tencent.smtt.sdk.QbSdk$PreInitCallback"));
            
            // 创建简单的回调
            Object callback = createSimpleCallback();
            
            initMethod.invoke(null, context.getApplicationContext(), callback);
            Log.i(TAG, "X5 初始化调用成功");
            
        } catch (Exception e) {
            Log.e(TAG, "X5 初始化失败: " + e.getMessage());
            isInitializing = false;
            forceSystemWebView();
        }
    }
    
    /**
     * 创建简单的初始化回调
     */
    private static Object createSimpleCallback() {
        try {
            Class<?> callbackClass = Class.forName("com.tencent.smtt.sdk.QbSdk$PreInitCallback");
            
            return java.lang.reflect.Proxy.newProxyInstance(
                callbackClass.getClassLoader(),
                new Class[]{callbackClass},
                (proxy, method, args) -> {
                    String methodName = method.getName();
                    if ("onViewInitFinished".equals(methodName)) {
                        boolean success = args != null && args.length > 0 ? (Boolean) args[0] : false;
                        Log.i(TAG, "X5 内核初始化结果: " + (success ? "成功" : "失败"));
                        isInitialized = success;
                        isInitializing = false;
                        if (!success) {
                            Log.i(TAG, "X5 初始化失败，将使用系统 WebView");
                        }
                    } else if ("onCoreInitFinished".equals(methodName)) {
                        Log.i(TAG, "X5 内核加载完成");
                    }
                    return null;
                }
            );
        } catch (Exception e) {
            Log.e(TAG, "创建回调失败: " + e.getMessage());
            return null;
        }
    }
    
    /**
     * 检查 X5 内核是否可用
     */
    public static boolean isX5Available() {
        try {
            Class<?> qbSdkClass = Class.forName("com.tencent.smtt.sdk.QbSdk");
            java.lang.reflect.Method canLoadMethod = qbSdkClass.getMethod("canLoadX5", Context.class);
            return (Boolean) canLoadMethod.invoke(null, (Context) null);
        } catch (Exception e) {
            return false;
        }
    }
    
    /**
     * 获取 X5 内核信息
     */
    public static String getX5Info(Context context) {
        try {
            Class<?> qbSdkClass = Class.forName("com.tencent.smtt.sdk.QbSdk");
            java.lang.reflect.Method getVersionMethod = qbSdkClass.getMethod("getTbsVersion", Context.class);
            Object version = getVersionMethod.invoke(null, context);
            return version != null ? version.toString() : "未知版本";
        } catch (Exception e) {
            return "X5 不可用";
        }
    }
    
    /**
     * 重置 X5 内核
     */
    public static void resetX5(Context context) {
        try {
            Log.i(TAG, "重置 X5 内核");
            Class<?> qbSdkClass = Class.forName("com.tencent.smtt.sdk.QbSdk");
            java.lang.reflect.Method resetMethod = qbSdkClass.getMethod("reset", Context.class);
            resetMethod.invoke(null, context);
            isInitialized = false;
            isInitializing = false;
        } catch (Exception e) {
            Log.e(TAG, "重置 X5 内核失败: " + e.getMessage());
        }
    }
    
    /**
     * 强制使用系统 WebView
     */
    public static void forceSystemWebView() {
        Log.i(TAG, "强制使用系统 WebView");
        try {
            Class<?> qbSdkClass = Class.forName("com.tencent.smtt.sdk.QbSdk");
            java.lang.reflect.Method forceMethod = qbSdkClass.getMethod("forceSysWebView");
            forceMethod.invoke(null);
        } catch (Exception e) {
            Log.w(TAG, "设置强制系统 WebView 失败: " + e.getMessage());
        }
    }
}
