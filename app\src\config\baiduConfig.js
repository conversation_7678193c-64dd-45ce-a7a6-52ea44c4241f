/**
 * 百度地图API配置统一管理
 * 统一管理所有百度地图相关的API Key和配置
 */

// 百度地图API Key配置
export const BAIDU_CONFIG = {
  // 前端统一使用的百度地图API Key（用于地图加载、天气等前端服务）
  MAP_API_KEY: 'zbQcVkpGET83cqhFyGIrmqrmkJMiAY4F',

  // 后端API Key（仅用于文档说明，前端不使用）
  BACKEND_API_KEY: 'nBIBdYlwIUiTU3Trtq5kXOSxBNm3lPhz', // 后端专用，前端不使用

  // Cordova插件使用的API Key
  PLUGIN_API_KEY: 'CqFD7sijWVodDTgIsnR7hTnLAEdZxirx'
};

// 百度地图API基础URL配置
export const BAIDU_API_URLS = {
  // 地图脚本加载URL
  MAP_SCRIPT_V2: `http://api.map.baidu.com/api?v=2.0&ak=${BAIDU_CONFIG.MAP_API_KEY}`,
  MAP_SCRIPT_WEBGL: `http://api.map.baidu.com/api?type=webgl&v=1.0&ak=${BAIDU_CONFIG.MAP_API_KEY}`,
  
  // API服务URL
  GEOCODING_API: 'http://api.map.baidu.com/geocoding/v3/',
  PLACE_SEARCH_API: 'https://api.map.baidu.com/place/v2/search',
  PLACE_SUGGESTION_API: 'https://api.map.baidu.com/place/v2/suggestion',
  WEATHER_API: 'http://api.map.baidu.com/weather/v1/',
  BATCH_API: 'http://api.map.baidu.com/batch'
};

/**
 * 获取带API Key的完整URL
 * @param {string} baseUrl 基础URL
 * @param {string} apiKey API Key类型 (默认使用 'MAP_API_KEY')
 * @param {Object} params 额外参数
 * @returns {string} 完整的URL
 */
export function getBaiduApiUrl(baseUrl, apiKey = 'MAP_API_KEY', params = {}) {
  const key = BAIDU_CONFIG[apiKey];
  const url = new URL(baseUrl);

  // 添加API Key
  url.searchParams.set('ak', key);

  // 添加其他参数
  Object.keys(params).forEach(paramKey => {
    url.searchParams.set(paramKey, params[paramKey]);
  });

  return url.toString();
}

/**
 * 获取地理编码API URL（注意：前端不使用，仅供参考）
 * @param {string} address 地址
 * @returns {string} 地理编码API URL
 */
export function getGeocodingUrl(address) {
  return getBaiduApiUrl(BAIDU_API_URLS.GEOCODING_API, 'MAP_API_KEY', {
    address: address,
    output: 'json'
  });
}

/**
 * 获取POI搜索API URL（注意：前端不使用，仅供参考）
 * @param {string} query 搜索关键词
 * @param {string} region 搜索区域
 * @returns {string} POI搜索API URL
 */
export function getPlaceSuggestionUrl(query, region = '北京') {
  return getBaiduApiUrl(BAIDU_API_URLS.PLACE_SUGGESTION_API, 'MAP_API_KEY', {
    query: query,
    region: region,
    output: 'json',
    city_limit: 'false'
  });
}

/**
 * 获取天气API URL
 * @param {string} districtId 区域ID
 * @returns {string} 天气API URL
 */
export function getWeatherUrl(districtId) {
  return getBaiduApiUrl(BAIDU_API_URLS.WEATHER_API, 'MAP_API_KEY', {
    district_id: districtId,
    data_type: 'all'
  });
}

export default BAIDU_CONFIG;
