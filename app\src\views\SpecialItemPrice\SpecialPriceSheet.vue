<template>
  <div class="wrapper">
    <div class="wrapper-head" v-if="sheet.red_flag!='2'">
      <van-nav-bar
          left-arrow
          safe-area-inset-top
          title="特价申请单"
          @click-left="goback">
        <template #right>
          <div class="submitSalesSlip" @click="handleSubmitClick">
            <svg style="margin:2px 2px 0 0 " width="30px" height="30px" stroke-width="1.3" class="black">
              <use :xlink:href="'#icon-plane'"></use>
            </svg>
          </div>
        </template>
      </van-nav-bar>
    </div>
    <div class="wrapper-head"  v-else>
      <van-nav-bar
          left-arrow
          safe-area-inset-top
          title="特价申请单(红字单)"
          @click-left="goback">
        <template #right>
          <div class="submitSalesSlip" @click="handleSubmitClick">
            <svg style="margin:2px 2px 0 0 " width="30px" height="30px" stroke-width="1.3" class="black">
              <use :xlink:href="'#icon-plane'"></use>
            </svg>
          </div>
        </template>
      </van-nav-bar>
    </div>
    
    <div class="content">
      <div class="content-query">
        <div class="content-query-item">
          <van-icon name="user-o" @click="onSelectClient"/>
          <input
              type="text"
              v-model="sheet.sup_name"
              placeholder="客户"
              readonly
              @click="onSelectClient"
          />
        </div>
        <!--          时间-->
        <div style="width: 100%;box-sizing: border-box;display: flex;flex-direction: row;">
            <div class="sheet-info-item" @click="handleSelectTime('start')">
              <div class="info-item-icon">
                <van-icon name="underway-o" />
              </div>
              <div class="info-item-content">
                <input type="text" v-model="sheet.startTimeShow" placeholder="开始月份" readonly/>
              </div>
            </div>
            <div class="sheet-info-item" @click="handleSelectTime('end')">
              <div class="info-item-icon" style="padding:0 15px; font-size: 20px; color:#ddd">
                ~
              </div>
              <div class="info-item-content">
                <input type="text" v-model="sheet.endTimeShow" placeholder="结束月份" readonly/>
              </div>
            </div>
          </div>
<!--          月数-->
          <div class="sheet-info-item" >
            <div class="info-item-icon-font">
              天数
            </div>
            <div class="info-item-content" >
              <input @click="handleInputSelect" type="text" v-model="showTimeConfig.Day" placeholder="天数" @input="handleSelectDay" />
            </div>
          </div>
      </div>
      <div class="public_query_title" v-if="sheet.sheet_no" style="margin-bottom: 1px">
        <div class="public_query_title_t">
          <span>{{ sheet.sheet_no }}</span>
          <span>{{ sheet.approve_time }}</span>
        </div>
      </div>
      <ConcaveDottedCenter/>
      <div class="content-sheet-rows">
        <SpecialPriceSheetRows ref="SpecialPriceSheetRows" :sheet="sheet" @handleItemDelete="handleItemDelete"
           @handleSheetRowsSort="handleSheetRowsSort"
                            @handleItemEdit="handleItemEdit"/>
        <SpecialPriceSheetRowsTotalInformation ref="SpecialPriceSheetRowsTotalInformation" :sheet="sheet"/>
      </div>
    </div>
    <div class="footer">
      <input
          id="codes"
          type="text"
          style="
              width: 160px;
              font-size: 14px;
              border-style: none;
              border-bottom:1px solid #eee;
              background-color: #eee;
              height: 35px;
              padding-left: 3px;
            "
          v-model="searchStr"
          @keydown="onSearchInputKeyDown($event)"
          placeholder="名称/简拼/条码/货号"
          :disabled="!!sheet.approve_time"
          :style="{ backgroundColor: sheet.approve_time ? '#f2f2f2' : '' }"
      />
      <van-button
          class="footer_iconBt"
          :disabled="sheet.approve_time !== '' || IsSubmiting"
          @click="btnClassView_click"
      >
        <svg width="35px" height="35px" fill="#F56C6C">
          <use xlink:href="#icon-add"></use>
        </svg>
      </van-button>
      <van-button
          class="footer_iconBt"
          type="info"
          @click="btnScanBarcode_click"
      >
        <svg width="30px" height="30px" fill="#555">
          <use xlink:href="#icon-barcodeScan"></use>
        </svg>
      </van-button>
    </div>
    <van-popup
      v-model="bPopupClientSelectDialog"
      duration="0.4"
      position="bottom"
      :lock-scroll="false"
      :style="{ height: '100%',position:'absolute', width: '100%', overflow: 'hidden'}"
      >
      <SelectCustomer
        canHide='true'
        @handleHide='bPopupClientSelectDialog=false'
        ref='selectCustomer'
        @onClientSelected="onClientSelected">
      </SelectCustomer>
    </van-popup>
    <van-popup
        v-model="popupEditSheetRows"
        position="bottom"
        :style="{ height: '415px' }"
    >
      <SpecialPriceEditSheetRows
          ref="editSheetRows"
          @closeEdit="closeEdit"
          :sheet="sheet"
          :editSheetRowsInfo="editSheetRowsInfo"/>
    </van-popup>
    <van-popup
        v-model="popupAddSpecialPriceSheetRow"
        :lazy-render="true"
        position="bottom"
        style="height:100vh;overflow:hidden">
      <SpecialPriceMultiSelect
          v-if="popupAddSpecialPriceSheetRow"
          ref="multiSelect"
          :sheet="sheet"
          @closePage="closePage"
          @closeSingleChoicePage="closeSingleChoicePage"
      >
      </SpecialPriceMultiSelect>
    </van-popup>
<!--    选择起始结束时间-->
    <van-popup v-model="showTimeConfigFlag" position="bottom" :close-on-click-overlay="false" >
      <van-datetime-picker
          v-model="showTimeConfig.time"
          type="date"
          title="选择年月日"
          :min-date="showTimeConfig.minDate"
          :max-date="showTimeConfig.maxDate"
          @confirm="handleTimeConfirm"
          @cancel="handleTimeCancel"
      />
    </van-popup>
    <van-popup v-model="showSubmitPopup" duration="0.4" :style="{ height: '100%', width: '80%' }" class="van_popup" position="right">
      <div style="height:30px;border-top:1px solid #ccc"></div>
      <div class="other_operate">
        <van-field
            v-model="sheet.make_brief"
            label="备注"
            label-width="40px"
            placeholder="请输入备注"
            :disabled="sheet.approve_time ? true : false"
        />
        <div style="height:30px"></div>
        <div class="other_operate_content">
          <button
              v-if="canMake"
              :disabled="IsSubmiting || sheet.approve_time !== ''"
              @click="handleSheetSave"
              style="height: 45px;border-radius:12px;background-color: #ffcccc;">保存</button>
          <button
              v-if="canApprove"
              :disabled="IsSubmiting ||sheet.approve_time !== ''"
              @click="handleSheetApprove"
              style="height: 45px;border-radius:12px;background-color: #ffcccc;">审核</button>
        </div>
        <div class="other_operate_content">
          <button
              style="height: 45px;border-radius:12px"
              :style="{
              color: sheet.red_flag !== '' ? '#ff9999' : '#ee0a24',
              borderColor: sheet.red_flag !== '' ? '#ff9999' : '#ee0a24',
            }"
              v-if="canRed && sheet.approve_time"
              :disabled="sheet.red_flag !== ''"
              @click="handleSheetRed"
          >红冲</button
          >
          <button
              v-if="sheet.sheet_id && !sheet.approve_time && canDelete"
              style="height: 45px;border-radius:12px"
              @click="handleSheetDelete"
          >删除</button
          >
          <button @click="handleSheetPrint"
                  v-if="canPrint"
                  style="height: 45px;border-radius:12px"
                  :disabled="(sheetStatusForPrint === 'saved' && !sheet.sheet_id) || (sheetStatusForPrint === 'approved' && !sheet.approve_time)||isPrinting"
          >打印</button
          >
        </div>
        <template  v-if="canPrint">
          <van-divider>打印条码</van-divider>
          <van-radio-group
              v-model="printBarcodeStyle"
              style="font-size: 12px; margin-left: 0; display: flex; justify-content: center;padding-top: 10px;">
            <van-radio name="noBarcode" style="margin-right: 10px">不打印</van-radio>
            <van-radio name="actualUnit" style="margin-right: 10px">实际单位</van-radio>
            <van-radio name="smallUnit" style="margin-right: 10px">小单位</van-radio>
          </van-radio-group>
          <van-checkbox
              v-if="printBarcodeStyle === 'actualUnit' || printBarcodeStyle === 'smallUnit' "
              shape="square"
              v-model="printBarcodePic"
              icon-size="20px"
              style="font-size: 12px; margin-left: 50px; margin-top: 10px">打印条码图</van-checkbox>
        </template>
        <van-divider style="margin: 20px 0" @click="moreOptions = !moreOptions">

          <svg v-if="moreOptions" width="26px" height="26px" fill="#d8d8d8">
            <use xlink:href="#icon-double_arrow"></use>
          </svg>

          <svg style="-webkit-transform: rotate(180deg);" v-else width="26px" height="30px" fill="#d8d8d8">
            <use  xlink:href="#icon-double_arrow"></use>
          </svg>
        </van-divider>
        <template v-if="moreOptions">
          <div class="other_operate_content">
            <van-button
                type="default"
                :disabled="IsSubmiting || sheet.approve_time !== ''"
                @click="onEmpty"
            >清空</van-button
            >
            <van-button  style="border-radius:12px;" type="default" @click="btnCopySheet_click" >复制</van-button>
            <van-button type="default" @click="btnOut">退出</van-button>
          </div>
        </template>
      </div>
    </van-popup>
  </div>
</template>
<script>
import {NavBar,Field, Icon, Picker,SwipeCell,DatetimePicker, Toast, Button, Dialog, RadioGroup, Radio, Divider, Checkbox} from "vant"
import SelectCustomer from "../components/SelectCustomer";
import ConcaveDottedCenter from "../components/ConcaveDottedCenter"
import SpecialPriceSheetRows from "./SpecialPriceSheetRows";
import SpecialPriceSheetRowsTotalInformation from "./SpecialPriceSheetRowsTotalInformation";
import SpecialPriceMultiSelect from "./SpecialPriceMultiSelect";
import SpecialPriceEditSheetRows from "./SpecialPriceEditSheetRows";
import mixins from '../SaleSheet/sheetMixin/mixin'
import SpecialPriceMixin from "./SpecialPriceMixin";
import {SaleSheetGetItemsInfo,AppSheetSpecialPriceLoad,AppSheetSpecialPriceSave,AppSheetSpecialPriceSubmit,AppSheetSpecialPriceDelete,AppSheetSpecialPriceRed} from "../../api/api"
import Printing from "../Printing/Printing";
export default {
  name:"SpecialPriceSheet",
  mixins: [SpecialPriceMixin, mixins],
  data() {
    return {
      sheet: {
        sheetType: 'TJSP',
        sheet_no: "",
        sheet_id: "",
        approve_time: "",
        make_time: "",
        sheetRows: [],
        start_time: "",
        startTimeShow: "",  
        end_time: "",
        endTimeShow: "", 
        make_brief:""
      },
      showSubmitPopup:false,
      bPopupClientSelectDialog: false,
      searchStr: '',
      IsSubmiting: false,
      editSheetRowsInfo: [],
      bPopupBranchPicker: false,
      showUnsubmitedSheets: false,
      unsubmitedSheets: [],
      popupEditSheetRows: false,
      printBarcodeStyle:"noBarcode",
      moreOptions: false,
      printBarcodePic:false,
      isPrinting:false,
      popupAddSpecialPriceSheetRow: false,
      showTimeConfig: {
        minDate: new Date(),
        maxDate: new Date(2050, 10, 1),
        startTimeShowFlag: false,
        endTimeShowFlag: false,
        time: new Date(),
        startOrEnd: 'start',
        Day:3,
      },

    }
  },
  mounted() {
    this.loadSheet()

  },
  components:{
    SpecialPriceSheetRows,
    SpecialPriceSheetRowsTotalInformation,
    SpecialPriceMultiSelect,
    SpecialPriceEditSheetRows,
    "van-nav-bar": NavBar,
    "van-icon": Icon,
    "van-button": Button,
    "van-picker": Picker,
    "van-swipe-cell": SwipeCell,
    "van-datetime-picker": DatetimePicker,
    ConcaveDottedCenter,
    "van-radio-group": RadioGroup,
    "van-radio": Radio,
    "van-divider": Divider,
    "van-checkbox": Checkbox,
    "van-field": Field,
    SelectCustomer
  },
  watch:{
    "sheet.sheetRows": {
      handler: function () {
        console.log('111')
        this.handleSelectedSheetRows('TJSP')
      },
      deep: true
    },
    popupEditSheetRows: {
      handler: function (newVal) {
        if (newVal === false) {
          this.$refs.SpecialPriceSheetRows.handleUpdate()
          this.$refs.SpecialPriceSheetRowsTotalInformation.handleUpdate()
        }
      },
      deep: true
    },
  },
  computed:{
    showTimeConfigFlag: {
      get() {
        return this.showTimeConfig.startTimeShowFlag || this.showTimeConfig.endTimeShowFlag
      },
      set() {}
    },
    canEdit() {
      if (this.sheet.approve_time)
        return false
      if (!this.canApprove && this.sheet.sheet_id) return false
      return true
    },
    canRed() {
      return hasRight("sale.sheetSpecialPrice.red");
    },
    canMake() {
      return hasRight("sale.sheetSpecialPrice.make");
    },
    canDelete() {
      return hasRight("sale.sheetSpecialPrice.delete");
    },
    canPrint() {
      return hasRight("sale.sheetSpecialPrice.print");
    },
    canApprove() {
      return hasRight("sale.sheetSpecialPrice.approve");
    },
    allowPrintBeforeApprove() {
      return hasRight("delicacy.allowPrintBeforeApprove.value");
    },
    allowPrintBeforeSave() {
      return hasRight("delicacy.allowPrintBeforeSave.value");
    },
    sheetStatusForPrint() {
      return window.getRightValue('delicacy.moveSheetStatusForPrint.value');
    },
  },
  methods: {
    async loadSheet() {
      
      const sheetID = this.$route.query.sheetID || "";
      if (sheetID) {
        this.showUnsubmitedSheets = false;
      }
      let params = {
        sheetID: sheetID,
      }
      await AppSheetSpecialPriceLoad(params).then(res => {
        if (res.result !== "OK") {
          return
        }
        if (!res.sheet) {
          Toast.fail("无表单数据")
          return
        }
        
        this.sheet = res.sheet
        if(this.sheet.sheetRows.length>0){
          this.sheet.sheetRows.forEach(row=>{
            if(row.unit_no === row.b_unit_no){
              row.b_recent_price = row.recent_price
              row.b_real_price = row.real_price
              row.b_wholesale_price = row.wholesale_price
              row.b_special_price = row.special_price

              row.m_recent_price = Number(row.recent_price)/Number(row.unit_factor)*Number(row.m_unit_factor)
              row.m_real_price = Number(row.real_price)/Number(row.unit_factor)*Number(row.m_unit_factor)
              row.m_wholesale_price = Number(row.wholesale_price)/Number(row.unit_factor)*Number(row.m_unit_factor)
              row.m_special_price = Number(row.special_price)/Number(row.unit_factor)*Number(row.m_unit_factor)

              row.s_recent_price = Number(row.recent_price)/Number(row.unit_factor)
              row.s_real_price = Number(row.real_price)/Number(row.unit_factor)
              row.s_wholesale_price = Number(row.wholesale_price)/Number(row.unit_factor)
              row.s_special_price = Number(row.special_price)/Number(row.unit_factor)


            }else if(row.unit_no === row.m_unit_no){
              row.b_recent_price = Number(row.recent_price)/Number(row.unit_factor)*Number(row.b_unit_factor)
              row.b_real_price = Number(row.real_price)/Number(row.unit_factor)*Number(row.b_unit_factor)
              row.b_wholesale_price = Number(row.wholesale_price)/Number(row.unit_factor)*Number(row.b_unit_factor)
              row.b_special_price = Number(row.special_price)/Number(row.unit_factor)*Number(row.b_unit_factor)

              row.m_recent_price = row.recent_price
              row.m_real_price = row.real_price
              row.m_wholesale_price = row.wholesale_price
              row.m_special_price = row.special_price

              row.s_recent_price = Number(row.recent_price)/Number(row.unit_factor)
              row.s_real_price = Number(row.real_price)/Number(row.unit_factor)
              row.s_wholesale_price = Number(row.wholesale_price)/Number(row.unit_factor)
              row.s_special_price = Number(row.special_price)/Number(row.unit_factor)

            }else {
              row.b_recent_price = Number(row.recent_price)*Number(row.b_unit_factor)
              row.b_real_price = Number(row.real_price)*Number(row.b_unit_factor)
              row.b_wholesale_price = Number(row.wholesale_price)*Number(row.b_unit_factor)
              row.b_special_price = Number(row.special_price)*Number(row.b_unit_factor)

              row.m_recent_price = Number(row.recent_price)*Number(row.m_unit_factor)
              row.m_real_price = Number(row.real_price)*Number(row.m_unit_factor)
              row.m_wholesale_price = Number(row.wholesale_price)*Number(row.m_unit_factor)
              row.m_special_price = Number(row.special_price)*Number(row.m_unit_factor)

              row.s_recent_price = row.recent_price
              row.s_real_price = row.real_price
              row.s_wholesale_price = row.wholesale_price
              row.s_special_price = row.special_price

            }
          })
        }
        //this.handleSheetRowsForLoad()
        this.$store.commit("attrOptions", res.attrOptions)
        this.handleInitSheetRows()
        const sheetID = this.sheet.sheet_id
        // if (!sheetID) {
        //   this.showSheetsFromCache();
        // }
      }).catch(err => {
        console.log('???')
        Toast(err)
      })
    },
    handleTimeConfirm(obj){
      let data = new Date(obj)
      if (this.showTimeConfig.startOrEnd === 'start') {
        this.showTimeConfig.startTimeShowFlag = false
        this.handleStartTimeChange(data)
      }else{
        let startTime = new Date(this.sheet.start_time.myReplace('-','/'))
        if (data < startTime) {
          Toast('结束时间应大于开始时间')
          return
        }
        this.handleEndTimeChange(data)
        this.showTimeConfig.endTimeShowFlag = false

      }
      this.handleCalcDay()
    },
    handleStartTimeChange(data) {
      this.sheet.startTimeShow = data.format("yyyy-MM-dd")
      this.sheet.start_time = data.format("yyyy-MM-dd hh:mm:ss")
    },
    handleEndTimeChange(data) {
      this.sheet.endTimeShow = data.format("yyyy-MM-dd")
      this.sheet.end_time =this.sheet.endTimeShow + ' 23:59:59'
    },
    handleInitSheetRows() {
      if (this.sheet.approve_time === '' && this.sheet.sheetRows.length === 0) {
        this.sheet.startTimeShow = new Date().format("yyyy-MM-dd")
        this.sheet.start_time = this.sheet.startTimeShow + ' 00:00:00'
        this.sheet.endTimeShow = new Date(this.commonAddDay(this.sheet.start_time,Number(this.showTimeConfig.Day)).myReplace('-','/')).format("yyyy-MM-dd")
        this.sheet.end_time = this.sheet.endTimeShow + ' 23:59:59'
      }
      if (this.sheet.approve_time !== '' || this.sheet.make_time !== '') {
        this.sheet.startTimeShow = new Date(this.sheet.start_time.myReplace('-','/')).format("yyyy-MM-dd")
        this.sheet.endTimeShow = new Date(this.sheet.end_time.myReplace('-','/')).format("yyyy-MM-dd")
        this.handleCalcDay()
      }
    },
    // 处理日期的变化
    handleSelectTime(startOrEndFlag) {
      if (this.sheet.approve_time) return
      if (startOrEndFlag === 'start') {
        this.showTimeConfig.startTimeShowFlag = true
        this.showTimeConfig.startOrEnd = 'start'
      } else if (startOrEndFlag === 'end') {
        this.showTimeConfig.endTimeShowFlag = true
        this.showTimeConfig.startOrEnd = 'end'
      }
    },
    handleCalcDay() {
      let startTime = new Date(this.sheet.start_time.myReplace('-','/')).format("yyyy-MM-dd hh:mm:ss")
      startTime = Date.parse(startTime)
      let endTime = new Date(this.sheet.end_time.myReplace('-','/')).format("yyyy-MM-dd hh:mm:ss")
      endTime = Date.parse(endTime)
      var ms = endTime - startTime
      ms = (ms+1)/(1000*60*60*24)
      this.showTimeConfig.Day = Math.ceil(ms)
    },
    // 月份
    handleSelectDay() {
      if (this.sheet.approve_time) return

      if(Number(this.showTimeConfig.Day)>0){
        this.sheet.end_time = this.commonAddDay(this.sheet.start_time,Number(this.showTimeConfig.Day)) 
        this.sheet.startTimeShow = new Date(this.sheet.start_time.myReplace('-','/')).format("yyyy-MM-dd")
        this.sheet.endTimeShow = new Date(this.sheet.end_time.myReplace('-','/')).format("yyyy-MM-dd")
      }
    },
    commonAddDay(dateStr,num){
      let date = new Date(dateStr.myReplace('-','/')).format("yyyy-MM-dd hh:mm:ss")
      date = Date.parse(date)
      var ms = num*(1000*60*60*24)-1
      var newDate = new Date(date+ms).format("yyyy-MM-dd hh:mm:ss")
      return newDate
    },
    handleTimeCancel() {
      this.showTimeConfig.startTimeShowFlag = false
      this.showTimeConfig.endTimeShowFlag = false
    },
    goback() {
      myGoBack(this.$router)
    },
    onClientSelected(obj){
      let isShowChangeDialog = false
      var that = this
      if(this.sheet.sheetRows.length > 0) {
        isShowChangeDialog = true
      }
      if(isShowChangeDialog){
        Dialog.confirm({
        title: '',
        message: '变更客户后,已选普通商品价格发生改变,是否继续？',
        confirmButtonText: '是',
        cancelButtonText: '否',
        width:"320px"
        })
        .then(() => {
          this.sheet.sup_name = obj.titles;
          this.sheet.supcust_id = obj.ids;
          this.bPopupClientSelectDialog = false;
          var items_id =""
          that.sheet.sheetRows.forEach(sheetRow => {
            if (items_id != '') items_id += ','
            items_id += sheetRow.item_id
          })
          if (items_id !== "") {
            that.getItemsInfo(items_id);
          }
        })
        .catch(()=>{
          this.bPopupClientSelectDialog = false;
        })

      }else{
        this.sheet.sup_name = obj.titles;
        this.sheet.supcust_id = obj.ids;
        this.bPopupClientSelectDialog = false;
        this.$store.commit("clearPartOfSheetState",'')
        this.$store.commit("shoppingCarObj",{
          clearFlag: true,
          sheetType: this.sheet.sheetType
        })
      }
    },
    getItemsInfo(items_id) {
      var that = this
      let params = {
        items_id:items_id,
        supcust_id:this.sheet.supcust_id,
      }
      SaleSheetGetItemsInfo(params).then(res=>{
        if(res.result=="OK"){
          that.sheet.sheetRows.forEach(sheetRow=>{
            res.items[sheetRow.item_id].units.forEach(item => {
              if(sheetRow.unit_no ===item.unit_no){
                sheetRow.real_price = item.price
                sheetRow.wholesale_price = item.wholesale_price
                sheetRow.recent_price = item.recent_price
              }
              if(sheetRow.s_unit_no ===item.unit_no){
                sheetRow.s_real_price = item.price
                sheetRow.s_wholesale_price = item.wholesale_price
                sheetRow.s_recent_price = item.recent_price
              }
              if(sheetRow.m_unit_no ===item.unit_no){
                sheetRow.m_real_price = item.price
                sheetRow.m_wholesale_price = item.wholesale_price
                sheetRow.m_recent_price = item.recent_price
              }
              if(sheetRow.b_unit_no ===item.unit_no){
                sheetRow.b_real_price = item.price
                sheetRow.b_wholesale_price = item.wholesale_price
                sheetRow.b_recent_price = item.recent_price
              }
            })

          })
        }else{
          Toast.fail("获取商品失败,请重新刷新或者手动删除")
        }
      })
    },
    handleSubmitClick(){
      if(!this.sheet&&this.sheet.sheetRows.length<=0){
        Toast.fail("请添加商品")
        return
      }
      this.showSubmitPopup = true
    },
    onSelectClient() {
      if (this.sheet.approve_time) return;
      this.bPopupClientSelectDialog = true;

    },
    handleItemDelete(item, index) {
      this.sheet.sheetRows.splice(index, 1)
    },
    handleSheetRowsSort() {
      this.sheet.sheetRows = this.sheet.sheetRows.sort((a, b)=> a.item_name.localeCompare(b.item_name, 'zh')); //a~z 排序
      this.$forceUpdate()
    },
    handleItemEdit(item) {
      if (!this.canEdit) return;
      this.editSheetRowsInfo = [item]
      this.popupEditSheetRows = true
      setTimeout(() => {
        this.$refs.editSheetRows.loadData()
      }, 350);
    },
    closeEdit() {
      this.$refs.SpecialPriceSheetRows.handleUpdate()
      this.$refs.SpecialPriceSheetRowsTotalInformation.handleUpdate()
      this.popupEditSheetRows = false
      this.$refs.editSheetRows.clearState()
    },
    handleRegisterSayCode() {
      window.sayCode = result => {
        this.pageSayCode(result)
      }
    },
    pageSayCode(result) {
      this.searchStr = result;
      this.queryScan()
    },
    onSearchInputKeyDown(e) {
      if (e.keyCode === 13) {
        this.queryScan()
      }
    },
    queryScan() {

    },

    async btnScanBarcode_click() {
      try {
        const result = await this.scanBarcodeNew({
          unit_type: 'special_price'
        })

        if (!result.code) {
          return
        }

        this.searchStr = result.code
        this.btnClassView_click()
      } catch (error) {
        console.error('扫码失败:', error)
        this.$toast('扫码失败，请重试')
      }
    },
    handelGetSheet() {
      this.sheet.IsFromWeb = false
      

      if(!this.sheet.seller_id) this.sheet.seller_id=this.$store.state.operInfo.oper_id
      this.sheet.maker_id = this.sheet.maker_id === '' ? this.$store.state.operInfo.oper_id : this.sheet.maker_id
      this.sheet.maker_name = this.sheet.maker_name === '' ? this.$store.state.operInfo.oper_name : this.sheet.maker_name
      this.sheet.operKey = this.$store.state.operKey
      this.sheet.sheetType = 'TJSP'
    },
    btnClassView_click(){
      if(!this.sheet.supcust_id){
        Toast.fail("请选择客户");
      }else{
        let query = {
          searchStr: this.searchStr || "",
          sheet: this.sheet,
        }
        this.$store.commit("currentSheet",this.sheet)
        this.$router.push({path:"/SpecialPriceSelectItems", query: query})
      }
    },
    onEmpty() {
      Dialog.confirm({
        title: "清空单据",
        message: "请确认是否清空",
        width:"320px"
      }).then(() => {
        this.sheet.sheet_no = ''
        this.sheet.sheet_id = ''
        this.sheet.approve_time = ''
        this.sheet.make_time = ''
        this.sheet.sheetRows = []
        this.sheet.happen_time=''
      }).catch(() => {
        Toast("取消清空");
      });
    },
    closePage(flag) {
      this.popupAddSpecialPriceSheetRow = false
      this.$store.commit("multiSelectOpenFlag",false)
      this.$store.commit("shoppingCarFinish",false)
      this.$store.commit('shoppingCarObj',{

        sheetType:this.moveType,
        clearFlag: true
      })
    },
    closeSingleChoicePage() {
      this.$store.commit("multiSelectOpenFlag",false)
      this.$store.commit("shoppingCarFinish",false)
      this.popupAddSpecialPriceSheetRow = false
      this.$forceUpdate()
    },
    async handleSheetSave() {
      // 保存单据
      if (this.sheet.supcust_id === '') {
        Toast.fail('请选择客户');
        return
      }
      this.IsSubmiting = true
      this.handelGetSheet()
      console.log("特价单",this.sheet)
      AppSheetSpecialPriceSave(this.sheet).then((res)=>{
        this.IsSubmiting = false
        if(res.result==="OK"){
          this.sheet.sheet_no = res.sheet_no
          this.sheet.sheet_id = res.sheet_id
          this.sheet.make_time = res.currentTime
          this.sheet.happen_time = res.happen_time
          Toast.success("保存成功");
        }else{
          Toast.fail(res.msg)
        }
      }).catch((err)=>{
        console.log(err)
        this.IsSubmiting = false
      })
      
    },
    handleSheetDelete() {
      Dialog.confirm({
        title: "删除单据",
        message: "请确认是否删除",
        width:"320px"
      }).then(() => {
        this.IsSubmiting= true
        let params = {
          operKey:this.$store.state.operKey,
          sheet_id:this.sheet.sheet_id,
        }
        AppSheetSpecialPriceDelete(params).then((res)=>{
          if(res.result==="OK"){
            Toast.success("删除成功,即将退出该页面");
            //this.removeCurSheetFromCache()
            setTimeout( () => {
              this.btnOut();
            }, 1000);
            this.IsSubmiting= false
          }else{
            Toast.fail("删除失败:"+res.msg)
            this.IsSubmiting= false
          }
        })

      }).catch(() => {
        Toast("取消删除");
      });
    },
    handleSheetRed() {
      Dialog.confirm({
        title: "红冲单据",
        message: "请确认是否红冲",
        width:"320px"
      }).then(() => {
        let params ={
          operKey:this.$store.state.operKey,
          sheetID:this.sheet.sheet_id,
        }
        AppSheetSpecialPriceRed(params).then((res)=>{
          if(res.result ==="OK"){
            Toast.success("红冲成功")
            this.sheet.red_flag = "1"
          }else{
            Toast.fail("红冲失败:"+res.msg)
          }
        })
      }).catch(() => {
        Toast("取消红冲");
      });
    },
    async handleSheetApprove() {
      Dialog.confirm({
        title: "审核单据",
        message: "请确认是否审核?",
        width:"320px"
      }).then(async () => {
        if (this.sheet.supcust_id === '') {
          Toast.fail('请选择客户');
          return
        }
        
        this.handelGetSheet()
        if(!this.sheet.approver_id){
          this.sheet.approver_id = this.$store.state.operInfo.oper_id
          this.sheet.approver_name=this.$store.state.operInfo.oper_name;
        }
        this.IsSubmiting = true
        AppSheetSpecialPriceSubmit(this.sheet).then(res=>{
          this.IsSubmiting = false
          if(res.result==="OK"){
            this.sheet.sheet_no = res.sheet_no;
            this.sheet.sheet_id = res.sheet_id;
            this.sheet.make_time = res.currentTime;
            this.sheet.approve_time = res.approve_time;
            Toast.success("审核成功");
          }else{
            Toast.fail(res.msg)
          }
        })
      }).catch(() => {
        Toast("取消审核");
      })
    },
    handleSheetPrint() {
      this.isPrinting = true
      const showTotalSheetInfo = this.$refs.SpecialPriceSheetRowsTotalInformation.showTotalSheetInfo
      this.handelGetSheet()
      const printSheet = JSON.parse(JSON.stringify(this.sheet))
      const defaultPrinter = window.getDefaultPrinter()
      if (defaultPrinter.type === "cloud") {
        Toast("云打印功能持续开发中，敬请期待，请选择蓝牙打印")
      } else {
        Printing.printSpecialPriceSheet(printSheet,showTotalSheetInfo,this.printBarcodeStyle,  this.printBarcodePic, null,res=>{
          this.isPrinting = false
          if (res.result === "OK") {
            Toast.success("打印成功");
          } else {
            Toast.fail(res.msg);
          }
        });
      }
    },
    btnOut() {
      myGoBack(this.$router);
    },
    btnCopySheet_click() {
      logUserAction({tm:new Date().format("yyyy-MM-dd h:m"),act:"copyclick" ,sn:this.sheet.sheet_no})
      Dialog.confirm({
        title: "复制单据",
        message: "请确认是否复制?",
        width:"320px"
      })
        .then(async () => {
          this.sheet.sheet_id = "";
          this.sheet.sheet_no = "";

          this.sheet.approve_time = "";
          this.sheet.approver_id = "";
          this.sheet.happen_time = "";
          this.sheet.make_time = "";
          this.sheet.maker_id = "";
          this.sheet.red_flag = "";
          this.sheet.red_sheet_id = "";
          this.sheet.print_count = "";
          this.sheet.submit_time = "";
          Toast("复制成功");
        })
        .catch(() => {
          Toast("取消复制");
        });
    },
    handleInputSelect(e) {
      e.currentTarget.select()
    }
  },
}
</script>

<style lang="less" scoped>

@flex_w: {
  display: flex;
  flex-wrap: wrap;
};
@flex_a_j: {
  display: flex;
  align-items: center;
  justify-content: center;
};
@flex_a_bw: {
  display: flex;
  align-items: center;
  justify-content: space-between;
};
// /deep/.van-nav-bar__title {
//   color: red !important;
// }
.wrapper {
  width: 100%;
  height: 100%;
  box-sizing: border-box;
  background-color: #fff;
  display: flex;
  flex-direction: column;
  .content {
    width: 100%;
    box-sizing: border-box;
    background-color: #fff;
    overflow-y: auto;
    display: flex;
    flex-direction: column;
    padding: 10px 10px 60px 10px;
    .content-query {
      box-sizing: border-box;
      width: 100%;
      display: flex;
      flex-direction: column;
      padding: 10px 15px 3px 15px;
      margin-bottom: 10px;
      .content-query-item {
        height: 25px;
        line-height: 25px;
        display: flex;

        input {
          height: 100%;
          width: 100%;
          padding: 0 10px 4px 0px;
          border: none;
          font-size: 15px;
          line-height: 35px;
          color: #333333;
          vertical-align: top;
          border-bottom: 1px solid #eee;
          text-align: right;
        }

        .van-icon {
          width: 30px;
          text-align: center;
          font-size: 22px;
          display: flex;
          align-items: center;
          justify-content: center;
          color: #aaa;
          background-color: #ffffff;
        }
      }
    }
    .public_query_title {
      background: #ffffff;
    }
    .public_query_title_t {
      height: 25px;
      line-height: 25px;
      font-size: 15px;
      color: #000000;
      padding: 10px 10px 0;
      @flex_a_bw();
    }
    .sheet-info-item {
          box-sizing: border-box;
          width: 100%;
          display: flex;
          align-items: center;
          margin-top: 10px;
          .info-item-icon{
            .van-icon {
              width: 30px;
              font-size: 22px;
              color: #aaa;
              background-color: #ffffff;
            }
          }
          .info-item-icon-font {
            border: 1px solid #aaa;
            color: #aaaaaa;
            padding: 0 4px;
            border-radius: 5px;
          }
          .info-item-content {
            box-sizing: border-box;
            flex: 1;
            input {
              width: 100%;
              border: none;
              font-size: 15px;
              line-height: 30px;
              color: #333333;
              vertical-align: top;
              border-bottom: 1px solid #eee;
              text-align: right;
            }
          }
        }
  }
  .lowItem {
    width: 300px;
    height: auto;
    overflow: hidden;
    padding: 10px;

    h4 {
      height: 40px;
      font-size: 16px;
      font-weight: 500;
      color: #333333;
      @flex_a_j();
    }

    .lowItem_ul {
      height: auto;
      overflow: hidden;
      margin-bottom: 10px;

      li {
        height: auto;
        overflow: hidden;
        padding: 10px;
        font-size: 14px;
        @flex_a_bw();
        border-bottom: 1px solid #f2f2f2;
      }

      li:last-child {
        border-bottom: none;
      }
    }
  }
  .footer {
    width: 100%;
    height: 50px;
    position: absolute;
    bottom: 0;
    box-shadow: 0 1px 5px rgba(100, 100, 100, 0.2);
    background-color: #fff;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 10px;
    box-sizing: border-box;

    .footer_input {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 190px;
      height: 50px;
    }

    .footer_iconBt {
      display: flex;
      align-items: center;
      justify-content: center;
      background-color: transparent;
      border: none;
      margin-left: 20px;
      width: 50px;
      height: 50px;
    }
  }
  .van_popup {
    height: 100%;
    overflow: hidden;
  }
  .other_operate {
    width: 100%;
    height: auto;
    .other_operate_content {
      height: 40px;
      vertical-align: top;
      margin-bottom: 15px;
      @flex_a_j();
      button {
        width: 100px;
        height: 100%;
        vertical-align: top;
        margin: 0 15px;
      }
    }
  }

  
}
</style>
