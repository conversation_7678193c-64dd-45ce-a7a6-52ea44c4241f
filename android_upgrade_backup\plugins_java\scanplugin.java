package com.olc.scan;

import org.apache.cordova.CordovaPlugin;
import org.apache.cordova.CallbackContext;
import org.json.JSONArray;
import org.json.JSONException;

import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.os.Build;

import org.apache.cordova.CordovaInterface;
import org.apache.cordova.CordovaWebView;

import java.util.ArrayList;
import java.util.List;
import android.os.Build.VERSION_CODES;
/**
 * This class echoes a string called from JavaScript.
 */
public class scanplugin extends CordovaPlugin {

    //public CallbackContext callbackContext;
    public Context mcontext;
    public CallbackContext mcallbackContext;
    public static List<MyAction> g_lstActions=new ArrayList<MyAction>();

    public class MyAction{
        public String Action="";
        public String BarcodeString="";
        public MyAction(String action,String barcodeString){
            this.Action=action;
            this.BarcodeString=barcodeString;
        }
    }



    @Override
    public void initialize(CordovaInterface cordova, CordovaWebView webView) {
        super.initialize(cordova, webView);
        mcontext = this.cordova.getActivity().getApplicationContext();
        g_lstActions.add(new MyAction("com.barcode.sendBroadcast","BARCODE"));//富立叶
        g_lstActions.add(new MyAction("android.intent.ACTION_DECODE_DATA","barcode_string"));//优博讯
        g_lstActions.add(new MyAction("android.intent.action.SCANRESULT","value"));//盈达
    }

    @Override
    protected void pluginInitialize() {
        super.pluginInitialize();
        //  final IntentFilter intentFilter = new IntentFilter();
        //  m_Broadcastname = "com.barcode.sendBroadcast";
        //  intentFilter.addAction(m_Broadcastname);
        // mcontext.registerReceiver(receiver, intentFilter, Context.RECEIVER_NOT_EXPORTED);
    }


    @Override
    public boolean execute(String action, JSONArray args, CallbackContext callbackContext) throws JSONException {
        if (action.equals("coolMethod")) {
            String message = args.getString(0);
            this.coolMethod(message, callbackContext);
            return true;
        } else if (action.equals("Scancode")) {
            mcallbackContext = callbackContext;
            SendScanBroadcast();
            return true;
        }
        return false;
    }

    private void coolMethod(String message, CallbackContext callbackContext) {
        if (message != null && message.length() > 0) {
            callbackContext.success(message);
        } else {
            callbackContext.error("Expected one non-empty string argument.");
        }
    }
//    @Override
//    public void onDestroy() {
//        // deregister receive
//        if(receiver!=null) {
//            mcontext.unregisterReceiver(receiver);
//        }
//        super.onDestroy();
//
//    }

    @Override
    public void onPause(boolean multitasking) {
        super.onPause(multitasking);
        try {
            mcontext.unregisterReceiver(receiver);
        } catch (IllegalArgumentException e) {
            // Receiver was not registered, ignore
        }
    }

    @Override
    public void onResume(boolean multitasking) {
        super.onResume(multitasking);

        final IntentFilter intentFilter = new IntentFilter();
        for(MyAction action : g_lstActions){
            intentFilter.addAction(action.Action);
        }

        // For Android 13+ (API level 33+), we need to specify RECEIVER_NOT_EXPORTED
        // since this receiver is only for internal app communication
        if (Build.VERSION.SDK_INT >= 33) { // API level 33 = Android 13 (TIRAMISU)
            // Use reflection to call registerReceiver with RECEIVER_NOT_EXPORTED flag
            // Context.RECEIVER_NOT_EXPORTED = 4
            try {
                java.lang.reflect.Method method = Context.class.getMethod("registerReceiver",
                    BroadcastReceiver.class, IntentFilter.class, int.class);
                method.invoke(mcontext, receiver, intentFilter, 4); // RECEIVER_NOT_EXPORTED = 4
            } catch (Exception e) {
                // Fallback to old method if reflection fails
                mcontext.registerReceiver(receiver, intentFilter);
            }
        } else {
            mcontext.registerReceiver(receiver, intentFilter);
        }

    }

    BroadcastReceiver receiver = new BroadcastReceiver() {
        @Override
        public void onReceive(Context arg0, Intent arg1) {
            for(MyAction action: g_lstActions){
                if (arg1.getAction().equals(action.Action)) {
                    // String str = arg1.getStringExtra("BARCODE");
                    String str = arg1.getStringExtra(action.BarcodeString);
                    if (!str.equals("")) {

                        // ((CordovaWebView)mcontext).loadUrl("javascript:sayHello('" + str + "')");
//                    JSONArray localJSONArray = new JSONArray();
//                    localJSONArray.put("setTransmissionPower");
//                    localJSONArray.put("false");
//                    callbackContext.success(localJSONArray);
                        // method = String.format("javascript:sayHello( '%s');", str );
                        scanplugin.this.webView.loadUrl("javascript:sayCode('" + str + "\r\n"+"')");
                        // mcallbackContext.success(str + "\r\n");
                    }
                }
            }

        }
    };

    private void SendScanBroadcast() {
        Intent intent = new Intent();
        intent.setAction("com.barcode.sendBroadcastScan");
        mcontext.sendBroadcast(intent);
    }

}

