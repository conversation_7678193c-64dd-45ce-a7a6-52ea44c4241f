<template>
  <div class="network-test">
    <van-nav-bar title="网络请求测试" left-arrow @click-left="$router.go(-1)" />
    
    <div class="test-section">
      <h3>网络请求模式</h3>
      <van-radio-group v-model="networkMode" @change="onNetworkModeChange">
        <van-radio name="enhanced">增强模式（推荐）</van-radio>
        <van-radio name="pure">纯网络优化</van-radio>
        <van-radio name="smart">智能模式</van-radio>
        <van-radio name="axios">仅Axios请求</van-radio>
      </van-radio-group>

      <div v-if="networkInfo" class="network-info">
        <h4>当前网络能力</h4>
        <div class="capability-item">
          <span>Advanced HTTP:</span>
          <span :class="networkInfo.capabilities.hasAdvancedHttp ? 'available' : 'unavailable'">
            {{ networkInfo.capabilities.hasAdvancedHttp ? '可用' : '不可用' }}
          </span>
        </div>
        <div class="capability-item">
          <span>Native Fetch:</span>
          <span :class="networkInfo.capabilities.hasNativeFetch ? 'available' : 'unavailable'">
            {{ networkInfo.capabilities.hasNativeFetch ? '可用' : '不可用' }}
          </span>
        </div>
        <div class="capability-item">
          <span>首选方法:</span>
          <span class="preferred-method">{{ networkInfo.preferredMethod }}</span>
        </div>
      </div>
    </div>

    <div class="test-section">
      <h3>测试按钮</h3>
      <van-button type="primary" block @click="testGetRequest" :loading="loading.get">
        测试GET请求
      </van-button>
      <van-button type="success" block @click="testPostRequest" :loading="loading.post" style="margin-top: 10px;">
        测试POST请求
      </van-button>
      <van-button type="warning" block @click="testConcurrentRequests" :loading="loading.concurrent" style="margin-top: 10px;">
        测试并发请求
      </van-button>
    </div>

    <div class="test-section">
      <h3>测试结果</h3>
      <div class="result-container">
        <div v-for="(result, index) in testResults" :key="index" class="result-item">
          <div class="result-header">
            <span class="result-time">{{ result.time }}</span>
            <span :class="['result-status', result.success ? 'success' : 'error']">
              {{ result.success ? '成功' : '失败' }}
            </span>
          </div>
          <div class="result-content">
            <div><strong>方法:</strong> {{ result.method }}</div>
            <div><strong>URL:</strong> {{ result.url }}</div>
            <div><strong>耗时:</strong> {{ result.duration }}ms</div>
            <div><strong>网络类型:</strong> {{ result.networkType }}</div>
            <div v-if="result.error" class="error-message">
              <strong>错误:</strong> {{ result.error }}
            </div>
            <div v-if="result.response" class="response-preview">
              <strong>响应:</strong> {{ JSON.stringify(result.response).substring(0, 100) }}...
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { hybridApiGet, hybridApiPost, setNetworkMode } from '@/api/api'
import { cordovaApiGet, cordovaApiPost } from '@/api/cordova-http'
import { webApiGet, webApiPost } from '@/api/api'
import { smartApiGet, smartApiPost, getNetworkInfo } from '@/api/smart-http'
import { pureApiGet, pureApiPost, getPureNetworkInfo } from '@/api/pure-native-http'
import { enhancedApiGet, enhancedApiPost, getEnhancedNetworkInfo } from '@/api/enhanced-http'

export default {
  name: 'NetworkTest',
  data() {
    return {
      networkMode: 'enhanced',
      networkInfo: null,
      pureNetworkInfo: null,
      enhancedNetworkInfo: null,
      loading: {
        get: false,
        post: false,
        concurrent: false
      },
      testResults: []
    }
  },
  mounted() {
    this.updateNetworkInfo()
    this.updatePureNetworkInfo()
    this.updateEnhancedNetworkInfo()
  },
  methods: {
    updateNetworkInfo() {
      this.networkInfo = getNetworkInfo()
    },

    updatePureNetworkInfo() {
      this.pureNetworkInfo = getPureNetworkInfo()
    },

    updateEnhancedNetworkInfo() {
      this.enhancedNetworkInfo = getEnhancedNetworkInfo()
    },

    onNetworkModeChange(mode) {
      switch (mode) {
        case 'enhanced':
          this.$toast('切换到增强模式 - 原生HTTP插件 + 网络优化，最强稳定性')
          break
        case 'pure':
          this.$toast('切换到纯网络优化模式 - 专注网络稳定性，无插件依赖')
          break
        case 'smart':
          this.$toast('切换到智能模式 - 自动选择最佳网络方案')
          break
        case 'axios':
          setNetworkMode(false, false)
          this.$toast('切换到Axios模式 - 仅使用传统请求')
          break
      }
    },

    async testGetRequest() {
      this.loading.get = true
      const startTime = Date.now()
      
      try {
        let response
        let networkType = 'unknown'
        
        if (this.networkMode === 'enhanced') {
          response = await enhancedApiGet(process.env.VUE_APP_API_URL + 'AppApi/Login/HeartBeat', {})
          networkType = 'enhanced'
        } else if (this.networkMode === 'pure') {
          response = await pureApiGet(process.env.VUE_APP_API_URL + 'AppApi/Login/HeartBeat', {})
          networkType = 'pure'
        } else if (this.networkMode === 'smart') {
          response = await smartApiGet(process.env.VUE_APP_API_URL + 'AppApi/Login/HeartBeat', {})
          networkType = 'smart'
        } else {
          response = await webApiGet('AppApi/Login/HeartBeat', {})
          networkType = 'axios'
        }
        
        const duration = Date.now() - startTime
        
        this.addTestResult({
          method: 'GET',
          url: 'AppApi/Login/HeartBeat',
          success: true,
          duration,
          networkType,
          response
        })
        
        this.$toast.success(`GET请求成功 (${duration}ms)`)
      } catch (error) {
        const duration = Date.now() - startTime
        
        this.addTestResult({
          method: 'GET',
          url: 'AppApi/Login/HeartBeat',
          success: false,
          duration,
          networkType: this.networkMode,
          error: error.message
        })
        
        this.$toast.fail(`GET请求失败: ${error.message}`)
      } finally {
        this.loading.get = false
      }
    },

    async testPostRequest() {
      this.loading.post = true
      const startTime = Date.now()
      
      try {
        let response
        let networkType = 'unknown'
        const testData = { test: 'data', timestamp: Date.now() }
        
        if (this.networkMode === 'hybrid') {
          response = await hybridApiPost('AppApi/Login/Login', testData)
          networkType = 'hybrid'
        } else if (this.networkMode === 'native') {
          response = await cordovaApiPost(process.env.VUE_APP_API_URL + 'AppApi/Login/Login', testData)
          networkType = 'native'
        } else {
          response = await webApiPost('AppApi/Login/Login', testData)
          networkType = 'axios'
        }
        
        const duration = Date.now() - startTime
        
        this.addTestResult({
          method: 'POST',
          url: 'AppApi/Login/Login',
          success: true,
          duration,
          networkType,
          response
        })
        
        this.$toast.success(`POST请求成功 (${duration}ms)`)
      } catch (error) {
        const duration = Date.now() - startTime
        
        this.addTestResult({
          method: 'POST',
          url: 'AppApi/Login/Login',
          success: false,
          duration,
          networkType: this.networkMode,
          error: error.message
        })
        
        this.$toast.fail(`POST请求失败: ${error.message}`)
      } finally {
        this.loading.post = false
      }
    },

    async testConcurrentRequests() {
      this.loading.concurrent = true
      const startTime = Date.now()
      
      try {
        const promises = []
        for (let i = 0; i < 5; i++) {
          if (this.networkMode === 'hybrid') {
            promises.push(hybridApiGet('AppApi/Login/HeartBeat', {}))
          } else if (this.networkMode === 'native') {
            promises.push(cordovaApiGet(process.env.VUE_APP_API_URL + 'AppApi/Login/HeartBeat', {}))
          } else {
            promises.push(webApiGet('AppApi/Login/HeartBeat', {}))
          }
        }
        
        const results = await Promise.allSettled(promises)
        const duration = Date.now() - startTime
        const successCount = results.filter(r => r.status === 'fulfilled').length
        
        this.addTestResult({
          method: 'CONCURRENT',
          url: '5个并发请求',
          success: successCount === 5,
          duration,
          networkType: this.networkMode,
          response: `${successCount}/5 成功`
        })
        
        this.$toast.success(`并发请求完成: ${successCount}/5 成功 (${duration}ms)`)
      } catch (error) {
        const duration = Date.now() - startTime
        
        this.addTestResult({
          method: 'CONCURRENT',
          url: '5个并发请求',
          success: false,
          duration,
          networkType: this.networkMode,
          error: error.message
        })
        
        this.$toast.fail(`并发请求失败: ${error.message}`)
      } finally {
        this.loading.concurrent = false
      }
    },

    addTestResult(result) {
      this.testResults.unshift({
        ...result,
        time: new Date().toLocaleTimeString()
      })
      
      // 只保留最近20条记录
      if (this.testResults.length > 20) {
        this.testResults = this.testResults.slice(0, 20)
      }
    }
  }
}
</script>

<style scoped>
.network-test {
  padding: 16px;
}

.test-section {
  margin-bottom: 24px;
}

.test-section h3 {
  margin-bottom: 12px;
  color: #323233;
}

.result-container {
  max-height: 400px;
  overflow-y: auto;
}

.result-item {
  border: 1px solid #ebedf0;
  border-radius: 8px;
  padding: 12px;
  margin-bottom: 8px;
  background: #fff;
}

.result-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.result-time {
  font-size: 12px;
  color: #969799;
}

.result-status {
  padding: 2px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: bold;
}

.result-status.success {
  background: #f0f9ff;
  color: #1989fa;
}

.result-status.error {
  background: #fff1f0;
  color: #ee0a24;
}

.result-content div {
  margin-bottom: 4px;
  font-size: 14px;
}

.error-message {
  color: #ee0a24;
}

.response-preview {
  color: #969799;
  font-size: 12px;
}

.network-info {
  background: #f7f8fa;
  padding: 12px;
  border-radius: 8px;
  margin-top: 12px;
}

.network-info h4 {
  margin: 0 0 8px 0;
  color: #323233;
  font-size: 14px;
}

.capability-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 4px;
  font-size: 13px;
}

.available {
  color: #07c160;
  font-weight: bold;
}

.unavailable {
  color: #ee0a24;
}

.preferred-method {
  color: #1989fa;
  font-weight: bold;
}
</style>
