<template>
  <div class="contentWrapper">
    <van-nav-bar title="拜访行程" style="z-index:10" left-arrow safe-area-inset-top @click-left="myGoBack($router);">
      <template #right>
        <div style="margin-right:10px;" v-if="showMode==='list'" class="iconfont icon_filter" @click="searchMinDistanceSupcust()">
          <svg class="visit-icon">
            <use :xlink:href="'#icon-min_distance'"></use>
          </svg>
        </div>
        <div v-if="showMode==='list'" class="iconfont icon_filter" @click="showMode='map'">
          <svg class="visit-icon">
            <use :xlink:href="'#icon-map'"></use>
          </svg>
        </div>
        <div v-if="showMode==='map'" class="iconfont icon_filter" @click="showMode='list'">
          <svg class="visit-icon">
            <use :xlink:href="'#icon-list'"></use>
          </svg>
        </div>
        <yj-dot-menu class="custom-left" @menuGo='menuClick' :menuVals="[{name:'门店拜访',url:'/VisitUser'},{name:'计划拜访',url:'/VisitPlan'}]"></yj-dot-menu>
      </template>
    </van-nav-bar>
    <van-popup @close="showAddDayRow=false" v-model="selectRoadShow" position="bottom" :style="{width:'100%', height:'30%',background:'#F2F3F5' }">
      <div>
        <div style="display:flex;flex-direction:row;">
            <div v-if="schedules && schedules.length!=0" class="day-item-container">
                <div @click="()=>{
                  clearCacheVisitDay()
                  selectSchedule(item.schedule_id)
                }"  :key="index" v-for="item,index in schedules" 
                        class="day-item-list">
                    <div class="day-item-name" >
                      {{item.schedule_name}}
                    </div>
                    <van-icon style="position:bottom;right:10px;line-height:40px;" name="arrow" />
              </div>
            </div>  
            <div class="day-item-container"> 
              <div class="day-item-add" v-if="!showAddDayRow" @click="addDayBtnClick">+</div>
              <div @click="()=>{
                  clearCacheVisitDay()
                  selectDayRoad(item)
                }" :key="index" v-for="item,index in visitDays" 
                      class="day-item-list">
                  <div class="day-item-name" >
                    {{item.day_name}}
                  </div>
                  <van-icon style="position:bottom;right:10px;line-height:40px;" name="arrow" />
              </div>              
            </div>
        </div>
        <div>
        </div>
      </div>
    </van-popup>
    <van-popup :lock-scroll="false" v-model="selectSupcustShow" position="bottom" :style="{ height:'100%' }">
      <select-customer :canHide='true' @handleHide='selectSupcustShow=false' @onClientSelected="insertSupcust"></select-customer>
    </van-popup>
    <van-popup :lock-scroll="false" v-model="tempModeShow" position="bottom" :style="{ height:'100%' }">
      <select-customer :canHide='true' @handleHide='tempModeShow=false;mode="schedule";this.$nextTick(()=>{this.$refs.myWrapper.refresh();});' @onClientSelected="onClientRowClick"></select-customer>
    </van-popup>
    <van-popup  class="new_day-pop" v-model="showAddDayRow" >
            <input v-model="editDayForm.day_name" class="day_name-input" type="text" placeholder="请输入"/>
            <div class="add_row-operBtns">  
              <div class="cancel" @click="showAddDayRow=false">
                取消
              </div>
              <div class="save" @click="confirmAddVisitDay">
                保存
              </div>
            </div>
    </van-popup>
    <van-popup :lock-scroll="false" v-model="showEditMode" position="bottom" :style="{ height:'100%',width:'100%'}">
      <visit-day-edit @switchVisitMode="switchVisitMode" :day_id="selectedDayRoad.day_id" :theDaySupcusts='theDaySupcusts'></visit-day-edit>
    </van-popup>
    <div v-if="mode==='schedule'&&workMode==='visit'&&showMode==='map'">
      <visit-day-map ref="visitDayMap" :activeIndex="activeIndex" @goVisitClick="mapMarkerVisitClick" :day_id="selectedDayRoad.day_id" :supcusts='theDaySupcusts'></visit-day-map>
    </div>
    <div v-show="mode==='schedule'&&workMode==='visit'&&showMode==='list'&&activeIndex<=2" class="addBtn" @click="addBtnClick">+</div>

    <div :lock-scroll="false" v-show="mode==='schedule'&&workMode==='visit'&&showMode==='list'">
      <div class="content-container relative visit-mode-container" style="margin-top: 300px;" v-show="mode==='schedule'&&workMode=='visit'&&showMode==='list'">
        <div class="mask-top absolute"></div>
        <div class="mask-bottom absolute"></div>
        <div v-show="mode==='schedule'" ref="myWrapper" class="wheel-wrapper">
          <!-- <div class="sales_list_boxs" v-if="theDaySupcusts.length > 0"> -->
          <ul class="wheel-scroller">
            <li class="wheel-item" v-for="(item, index) in theDaySupcusts" :key="index">
              <van-swipe-cell>
                <div :class="item.isVisited?'sheet_wrapper sheet_wrapper_visited':'sheet_wrapper'">
                  <div class="sup_info">
                    <div class="sup_name_wrapper" @click="onClientRowClick(item)">
                      <div class="sup_name">{{ item.sup_name }}</div>
                      <div style="display:flex;">
                        <div class="sale_days">
                          <div class="visit-tag-sale" :style="`color:${item.saleDaysForeColor}; background-color:${item.saleDaysColor};`" v-show="item.saleDays>=0">{{item.saleDays==0 ? '已销售': '销: '+item.saleDays+'日'}}</div>
                        </div>
                        <div class="visit_days" style="margin-left:4px;">
                          <div class="visit-tag" :style="`color:${item.visitDaysForeColor}; background-color:${item.visitDaysColor};`" v-show="item.visitDays>=0">{{item.isVisited?'已拜访':'访: '+ item.visitDays+'日'}}</div>
                        </div>
                      </div>
                    </div>
                    <div class="sup_contact">
                      <div class="sup_tel">
                        <a :href="'tel:' + item.sup_tel">{{item.sup_tel}}</a>
                      </div>
                      <div class="sup_add">
                        <div v-if="item.distance == -1">{{ item.distanceStr }}</div>
                        <div v-else @click="openNavigationAppDialog(item)">{{ item.distanceStr }}<!--<i class="iconfont">&#xe640;</i>--></div>
                      </div>
                    </div>
                  </div>
                  <div class="sheet_info">
                    <div class="sheet_addr" @click="onClientRowClick(item)">{{ item.sup_addr }}</div>
                  </div>
                </div>
                <template #right>
                  <van-button square :style="{background:'#d1edc4',color:'#529b2e',fontWeight:600}" text="导航" style="height:inherit" @click="openNavigationAppDialog(item)" />
                  <van-button square text="插入" :style="{background:'#a0cfff',color:'#337ecc',fontWeight:600}" style="height:inherit"  @click="insertBtnClick(item)" />
                  <van-button square :style="{background:'#fcd3d3',color:'#c45656',fontWeight:600}" text="删除" style="height:inherit" @click="removeSupcust(item)" />
                </template>
              </van-swipe-cell>
            </li>
          </ul>
        </div>
        <div class="sales_list_boxs_no" v-if="theDaySupcusts.length === 0">
          <div class="whole_box_no_icon iconfont">&#xe664;</div>
          <p>未查询到客户信息</p>
        </div>
      </div>
      <van-popup v-model="showChooseNavigationApp" position="bottom" :style="{ height: '30%' }">
        <div class="navi-select-item" @click="onNaviSelectItem(item)" v-for="(item, index) in navigationAppList" :key="index">
          {{ item.name }}
        </div>
      </van-popup>
    </div>
    <div v-if="workMode==='visit'" class="wrapper">
      <div class="tab-btn" style="padding: 0px 10px;">
        <!-- <div @click="selectDayRoad(item)" v-for="item,index in visitRoads" :key="index">{{item.day_name}}</div> -->
        <div style="width:calc(33% - 25px);border: 1px solid #ebedf0;" @click="skip" v-if="mode=='schedule'" class="skip">跳过</div>
        <div style="width:calc(33% - 25px);color: #ad2c2c;background: #ffd9d9;" class="visit" v-if="canClickVisitBtn&&mode=='schedule'&&showMode==='list'" @click="goVisit(theDaySupcusts[activeIndex])">拜访</div>
        <div style="width:calc(33% - 25px);" class="disabled-visit" v-if="!canClickVisitBtn&&mode=='schedule'&&showMode==='list'">拜访</div>
        <div style="width:calc(33% - 25px);border: 1px solid #ebedf0;" id="switchModeBtn" @click="changeMode" :class="mode==='schedule'?'schedule-visit':'temp-visit'">{{mode==='schedule'?'临时拜访':'计划拜访'}}</div>
      </div>
      <div class="info">
        <!-- <div v-if="activeIndex>0">{{theDaySupcusts[activeIndex-1].sup_name}}</div>
          <div v-else>即将跳转下个日程</div> -->
        <div v-if="selectedDayRoad.day_id" @click="selectRoadShow=true" class="currentDayRoad">
              <div>已选日程</div>
              <div>{{selectedDayRoad.day_name}}</div>
        </div>
        <div v-else @click="selectRoadShow=true" class="currentDayRoad">
              <div>选择日程</div>
        </div>
      </div>
    </div>
    <div class="my-toast" ref="myToast" v-show="showMyToast">
          
    </div>
  </div>
</template>
<script>
import BetterScroll from 'better-scroll'
import SelectCustomer from '../components/SelectCustomer'
import DayIndexer from './DayIndex.js'
import { Switch, SwipeCell, Cell, Picker, RadioGroup, Radio, NavBar, Toast, PullRefresh, Tag, Form, Icon, List, Popup, Button, Dialog } from "vant";
import { LoadVisitDayList, LoadTheDaySupcusts, InsertTheDaySupcust, RemoveTheDaySupcust, SaveOrUpdateVisitDay, LoadVisitSchedule } from "../../api/api";
import RegionSelection from "../components/RegionSelection";
import YJDotMenu from "../components/YJDotMenu";
import MyPicker from '../components/MyPicker'
import Navi from '../service/Navi'
import VisitDayEdit from './VisitDayEdit'
import VisitDayMap from './VisitDayMap'
import EasyMouse from '../service/EasyMouse'
import pinyinCode from "../../util/PinyinCode.js";
import Position from '../../components/Position';
export default {
  name: "VisitDay",
  data() {
    return {
      selectRoadShow: false,

      map: {},
      list: [],
      selectedDOM: {
        clientHeight: 103
      },
      navigationAppList: [
        {
          id: 1,
          name: "高德地图",
        },
        {
          id: 2,
          name: "百度地图",
        },
      ],
      leftSlideSupInfo: {
        order_index: -1,
        day_id: -1
      },
      showChooseNavigationApp: false,
      selectSupcustShow: false,
      tempModeShow: false,
      canClickVisitBtn: true,
      selectedSupcustNavigatorInfo: {},
      schedules:[],
      visitDays: [],
      selectedDayRoad: {},
      theDaySupcusts: [],
      activeIndex: 0,
      insertTypeFlag: "",
      mode: "schedule",
      bFirstTime:true,
      showMyToast:false,
      workMode: "visit",
      showMode: "list",
      showAddDayRow: false,
      finishVisit: false,
      // screenHeight:0,
      editDayForm: {
        day_id: "",
        day_name: "",
        schedule_id: ""
      }
    };
  },

  watch: {
    activeIndex(newVal, oldVal) {
      DayIndexer.writeCacheVisitDay(this.selectedDayRoad.day_id, newVal)
    },

  },
  computed: {
    showEditMode() {
      return this.mode === 'schedule' && this.workMode === 'edit' && this.showMode === 'list'
    },
    allowSkipVisit(){
      const allowSkipVisit = window.getRightValue("delicacy.allowSkipVisit.value")
      console.log(allowSkipVisit)
      if(!allowSkipVisit || allowSkipVisit == ""){
          return true
      }
      return  allowSkipVisit == 'true'

    }
  },

  beforeRouteEnter(to, from, next) {

    next(async vm => {
      var finishVisit = from.params.finishVisit
      console.log("beforeRouteEnter",from)
      if (from.name === "Visit" && typeof finishVisit !== 'undefined' && finishVisit == true) {  
        // if(!this.bFirstTime){
        const { day_id, index } = DayIndexer.getCacheVisitDay()
        console.log(day_id,index)
        const visitDays = await vm.loadVisitDays()
        if (day_id) {
          const cacheDay = visitDays.filter(visitDay => visitDay.day_id == day_id)[0]
          await vm.selectDayRoad(cacheDay)
          if (index) {
            vm.activeIndex = Number(index)
            vm.wheels.wheelTo(Number(index))
          }
          if (vm.$refs.visitDayMap) {
            vm.$refs.visitDayMap.supcusts_ = vm.theDaySupcusts
            vm.$refs.visitDayMap.activeIndex_ = index
          }
          // var cacheVisitRecord=this.$store.state.visitRecord
          // if (cacheVisitRecord && !JSON.stringify(cacheVisitRecord) == '{}'&&this.theDaySupcusts[this.activeIndex].supcust_id===cacheVisitRecord.supcust_id) {
          //   return
          // }
          // var finishVisit=this.$route.params.finishVisit
          console.log("activited")
          vm.skip()
          return
        }
        else {
          await this.selectDayRoad(visitDays[0])
        }
      }

    });
  },

  async mounted() {
    this.withPressListener(".skip")
    this.withPressListener(".visit")
    // this.screenHeight=window.screen.height
    this.withPressListener('#switchModeBtn')
    // this.withPressListener(".schedule-visit")
    this.registLongPress()
    const scheduleResp =await LoadVisitSchedule()
    this.schedules = scheduleResp.data
    const visitDays = await this.loadVisitDays()
    const { day_id, index } = DayIndexer.getCacheVisitDay()
    if (day_id) {
      const cacheDay = visitDays.filter(visitDay => visitDay.day_id === day_id)[0]
      //在缓存后，如果缓存的日程已经不存在，则选择第一个日程
      if (cacheDay) {
        await this.selectDayRoad(cacheDay)
        if (index) {
          this.wheels.wheelTo(index)
          this.activeIndex = index
        }
        return
      }    
    }
    console.log(visitDays)
    await this.selectDayRoad(visitDays[0])


  },
  components: {
    "van-form": Form,
    "van-icon": Icon,
    "van-list": List,
    "van-popup": Popup,
    "van-tag": Tag,
    "van-pull-refresh": PullRefresh,
    "van-swipe-cell": SwipeCell,
    "van-cell": Cell,
    "van-button": Button,
    "van-nav-bar": NavBar,
    "van-radio-group": RadioGroup,
    "van-radio": Radio,
    "van-switch": Switch,
    "visit-day-edit": VisitDayEdit,
    "visit-day-map": VisitDayMap,
    RegionSelection,
    "my-picker": MyPicker,
    SelectCustomer,
    "yj-dot-menu": YJDotMenu
  },
async activated() {
     this.withPressListener(".skip")
     this.withPressListener(".visit")
     this.withPressListener("#switchModeBtn")
  },
  methods: {
   async selectSchedule(schedule_id){
      this.visitDays =  await this.loadVisitDays(schedule_id)
    },
    clearCacheVisitDay(){
      DayIndexer.clearCacheVisitDay()
    },
    async switchVisitMode() {
      this.workMode = 'visit'
      console.log(this.activeIndex)
      const { day_id, index } = DayIndexer.getCacheVisitDay()
      const visitDays = await this.loadVisitDays()
      const cacheDay = visitDays.filter(visitDay => visitDay.day_id == day_id)[0]
      await this.selectDayRoad(cacheDay)
      if (index) {
        this.activeIndex = Number(index)
        this.wheels.wheelTo(Number(index))
      }
      if (this.$refs.visitDayMap) {
        this.$refs.visitDayMap.supcusts_ = this.theDaySupcusts
        this.$refs.visitDayMap.activeIndex_ = index
      }
    },
    searchMinDistanceSupcust() {
      var min_distance = Number.MAX_VALUE
      var nearestSupcust = {}
      var min_index = -1
      this.theDaySupcusts.filter(supcust => supcust.addr_lng && supcust.addr_lat && !supcust.isVisited).map((supcust) => {
        if (Number(supcust.distance) < Number(min_distance)) {
          nearestSupcust = supcust
          min_distance = supcust.distance
        }
      })
      this.theDaySupcusts.map((supcust, index) => {
        console.log(index)
        if (supcust.supcust_id === nearestSupcust.supcust_id) {
          min_index = index
        }
      })
      this.wheels.wheelTo(min_index)
    },
    menuClick(selected_menu) {
      if (selected_menu.name === '门店拜访') {
        this.$store.commit("visitShowMode", 'visitUser')
        this.$router.push(selected_menu.url)
      }
      if (selected_menu.name === '计划拜访') {
        this.$store.commit("visitShowMode", 'visitPlan')
        this.$router.push(selected_menu.url)
      }
    },
    withPressListener(domClsName) {
      const clsDOM = document.querySelector(domClsName)
      var clsDOMEvent = new EasyMouse(clsDOM)
      clsDOMEvent.MouseDown((e) => {
        clsDOM.style.setProperty("background", "#ff8d1a")
        clsDOM.style.setProperty("color", "#fff")
      })
      clsDOMEvent.MouseUp((e) => {
        clsDOM.style.setProperty("background", "#fff")
        clsDOM.style.setProperty("color", "#000")
      }, 200)
    },
    registLongPress() {
      let that_ = this;
      // Get a reference to an element
      var container = document.querySelector(".visit-mode-container");
      // Create a manager to manager the element
      var manager = new Hammer.Manager(container);
      // Create a recognizer
      var Press = new Hammer.Press({
        time: 1000,
      });
      // Add the recognizer to the manager
      manager.add(Press);
      // Subscribe to desired event
      manager.on("press", function () {

        Dialog.confirm({
          message: "前往编辑模式",
          width:"320px"
        }).then(() => {
          that_.workMode = 'edit'
        })
      })
    },
    async loadVisitDays(schedule_id = '') {
      const res = await LoadVisitDayList({schedule_id})
      console.log(res)
      if (res.result != 'OK') {
        this.$toast("您还未添加日程，请点击左下角添加日程")
        return
      }
      this.visitDays = res.data
      return res.data
    },
    confirmAddVisitDay() {
      this.editDayForm.py_str = pinyinCode(this.editDayForm.day_name)
      this.editDayForm.operKey = this.$store.state.operKey
      this.editDayForm.schedule_id = this.$store.state.operInfo.visit_schedule_id
      SaveOrUpdateVisitDay(this.editDayForm).then(async (res) => {
        const visitDays = await this.loadVisitDays()
        this.selectDayRoad(visitDays[visitDays.length - 1])
        this.editDayForm = {}
      })
    },
    addDayBtnClick() {
      const schedule_id = this.$store.state.operInfo.visit_schedule_id
      if (!schedule_id) {
        this.$toast("未分配行程，请联系老板在后台分配行程。")
        return
      }
      this.showAddDayRow = true
    },
    addBtnClick() {
      if(!this.selectedDayRoad.day_id){
        Toast.fail("未选择日程")
        return
      }
      this.insertTypeFlag = 'add'
      this.selectSupcustShow = true;
    },
    insertBtnClick(item) {
      this.selectSupcustShow = true;
      this.insertTypeFlag = 'insert'
      const { order_index, day_id } = item
      this.leftSlideSupInfo = {
        order_index,
        day_id
      }
    },
    removeBtnClick(item) {
      const { order_index, day_id } = item
      this.leftSlideSupInfo = {
        order_index,
        day_id
      }
    },
    generateColorCode() {
      var colorcode = ""
      var str = "ABCDEF123456789"
      for (var i = 0; i < 6; i++) {
        colorcode += str.charAt(parseInt((Math.random() * 100)) % 15);
      }
      return "#" + colorcode;
    },
    onClientRowClick(item) {
      if (this.mode === 'schedule') {
        this.$router.push({
          path: '/Visit', query: {
            shop_id: item.supcust_id,
            shop_name: item.sup_name,
            visit_day_id: this.selectedDayRoad.day_id,
            source: 'visitDay',
            mode: 'schedule',
            addr_lng: item.addr_lng,
            addr_lat: item.addr_lat
          }
        })
      }
      if (this.mode === 'temp') {
        const cacheVisitRecord = this.$store.state.visitRecord
        if (cacheVisitRecord && JSON.stringify(cacheVisitRecord) !== '{}') {
          checkHasCacheVisitRecordAndShowAlertDialog()
          return
        }
        this.$router.push({
          path: '/Visit', query: {
            acct_type: item.acct_type,
            shop_id: item.ids,
            shop_name: item.titles,
            addr_lng: item.addr_lng,
            addr_lat: item.addr_lat,
            source: 'visitDay',
            mode: 'temp',
            visit_day_id: this.selectedDayRoad.day_id,
            visit_day_order_index: this.theDaySupcusts.length + 1
          }
        }
        )
      }
    },

    changeMode() {
      console.log(this.mode)
      if (this.mode === 'temp') {
        this.mode = 'schedule'
        this.selectDayRoad(this.selectedDayRoad)
        this.tempModeShow = false
      }
      else if (this.mode === 'schedule') {
        this.mode = 'temp'
        this.tempModeShow = true

      }
    },
    // async skip(){
    //   console.log({"activeIndex":this.activeIndex,"length":this.theDaySupcusts.length})
    //   if(this.activeIndex<0){
    //     // this.$toast("已跳到最后一个，拜访序号将重置")
    //     // this.activeIndex=0
    //     // this.wheels.wheelTo(this.activeIndex)
    //     const visitDay=this.nextVisitDay(this.selectedDayRoad)
    //     this.selectedDayRoad=visitDay
    //    await this.selectDayRoad(this.selectedDayRoad)
    //     this.activeIndex=0
    //      this.wheels.wheelTo(this.activeIndex)
    //     return
    //   }
    //   var nextVistedSupcust=this.theDaySupcusts[this.activeIndex-1]
    //   let skipStep=-1
    //   while(nextVistedSupcust.isVisited&&this.activeIndex+skipStep>=this.theDaySupcusts.length){
    //     skipStep-=1
    //     nextVistedSupcust=this.theDaySupcusts[this.activeIndex+skipStep]
    //   }
    //   this.activeIndex-=skipStep
    //   this.wheels.wheelTo(this.activeIndex)
    // },
    // async skip(){
    //   console.log({"activeIndex":this.activeIndex,"length":this.theDaySupcusts.length})
    //   if(this.activeIndex>=this.theDaySupcusts.length-1){
    //     // this.$toast("已跳到最后一个，拜访序号将重置")
    //     // this.activeIndex=0
    //     // this.wheels.wheelTo(this.activeIndex)
    //     const visitDay=this.nextVisitDay(this.selectedDayRoad)
    //     this.selectedDayRoad=visitDay
    //    await this.selectDayRoad(this.selectedDayRoad)
    //     this.activeIndex=0
    //      this.wheels.wheelTo(this.activeIndex)
    //     return
    //   }
    //   var nextVistedSupcust=this.theDaySupcusts[this.activeIndex+1]
    //   let skipStep=1
    //   console.log(nextVistedSupcust)
    //   while(nextVistedSupcust.isVisited&&this.activeIndex+skipStep<this.theDaySupcusts.length){
    //     skipStep+=1
    //     nextVistedSupcust=this.theDaySupcusts[this.activeIndex+skipStep]
    //   }
    //   this.activeIndex+=skipStep
    //   this.wheels.wheelTo(this.activeIndex)
    // },
    async skip() {
      console.log({ "activeIndex": this.activeIndex, "length": this.theDaySupcusts.length })
      if ((this.activeIndex < 0)) {
        // this.$toast("已跳到最后一个，拜访序号将重置")
        // this.activeIndex=0
        // this.wheels.wheelTo(this.activeIndex)
        const visitDay = this.nextVisitDay(this.selectedDayRoad)
        this.selectedDayRoad = visitDay
        await this.selectDayRoad(this.selectedDayRoad)
        this.activeIndex = this.theDaySupcusts.length - 1
        this.wheels.wheelTo(this.activeIndex)
        return
      }
      var nextVistedSupcust = this.theDaySupcusts[this.activeIndex - 1]
      let skipStep = -1
      while (typeof nextVistedSupcust != 'undefined' && nextVistedSupcust.isVisited) {
        skipStep += -1
        nextVistedSupcust = this.theDaySupcusts[this.activeIndex + skipStep]
      }
      this.activeIndex += skipStep
      if (this.$refs.visitDayMap) {
        this.$refs.visitDayMap.supcusts_ = this.theDaySupcusts
        this.$refs.visitDayMap.activeIndex_ = this.activeIndex
      }
      this.wheels.wheelTo(this.activeIndex)
    },

    // var nextVistedSupcust=this.theDaySupcusts[this.activeIndex+1]
    // let skipStep=1
    // console.log(nextVistedSupcust)
    // while(nextVistedSupcust.isVisited&&this.activeIndex+skipStep<this.theDaySupcusts.length){
    //   skipStep+=1
    //   nextVistedSupcust=this.theDaySupcusts[this.activeIndex+skipStep]
    // }
    // this.activeIndex+=skipStep

    // },

    nextVisitDay(curDay) {
      var index = 0
      for (var i = 0; i < this.visitDays.length; i++) {
        if (curDay.day_id === this.visitDays[i].day_id) {
          index = i;
        }
      }
      return this.visitDays[(index + 1) % (this.visitDays.length - 1)]
    },
    mapMarkerVisitClick(sup) {
      console.log(sup)
      this.onClientRowClick(sup)
    },
    goVisit(sup) {
      const cacheVisitRecord = this.$store.state.visitRecord
      if (cacheVisitRecord && JSON.stringify(cacheVisitRecord) !== '{}') {
        checkHasCacheVisitRecordAndShowAlertDialog()
        return
      }
      this.onClientRowClick(sup)
    },
    async selectDayRoad(dayItem) {
      this.selectRoadShow = false
      this.selectedDayRoad = dayItem
      var position = new Position(isiOS)
      let cur_geopoint = {}
      try {
        cur_geopoint = await Position.getPosition()
      } catch {
        cur_geopoint = { longitude: 0, latitude: 0 }
      }
      console.log(cur_geopoint)
      const res = await LoadTheDaySupcusts({        
        dayID: dayItem.day_id,
        //记得改！！！！
        cur_lng: cur_geopoint.longitude,
        cur_lat: cur_geopoint.latitude
        // cur_lng:"123.34567",
        // cur_lat:"23.45678"
      })
      this.theDaySupcusts = res.data.reverse()
      console.log("看看传过来的",res)
      //------------------------------------------
      if (this.theDaySupcusts.length === 0) {
        this.activeIndex = this.theDaySupcusts.length - 1
        this.$toast("该日程暂无客户，请添加")
        return
      }
      //-------------------------------------------
      // this.initMap()
      this.theDaySupcusts.map(supcust => {
        // window.visitedSupcustIdList=res.visitedSupcustIdList
        var getColor = function (days, rgb, lowClr = 20) {
            if (days > 130) days = 130
            var clr = days / 180 * 200
            clr = 255 - clr

            if (clr > 255) clr = 255
            let clr1
            var r = parseFloat(clr) / 255
            clr1 = parseInt(clr * 0.9 * r * r)

            let s = ''
            if (rgb == 'r') s = `rgb(${clr},${clr1},${clr1})`
            else if (rgb == 'g') s = `rgb(${clr1},${clr},${clr1})`
            else if (rgb == 'b') s = `rgb(${clr1},${clr1},${clr})`
            var f = 'rgb(255,255,255)'
            if (clr > 230) f = 'rgb(90,90,90)'
            return { back: s, fore: f }
          }
        if (supcust.last_visit_time) {
          var today = new Date(this.getDatePart(new Date()))
          if (new Date(supcust.last_visit_time) > today) {
            supcust.isVisited = true
          }
          supcust.visitDays = this.dateDiff(supcust.last_visit_time, new Date(), 'd')
          supcust.visitDaysColor = getColor(supcust.visitDays, 'r').back
          supcust.visitDaysForeColor = getColor(supcust.visitDays, 'r').fore
        }
        if (supcust.last_sale_time) {
          supcust.saleDays = this.dateDiff(supcust.last_sale_time, new Date(), 'd')
          supcust.saleDaysColor = getColor(supcust.saleDays, 'r').back
          supcust.saleDaysForeColor = getColor(supcust.saleDays, 'r').fore
        }
        supcust.distanceStr = this.processDistanceAndFormatUnit(supcust.distance)
      })
      const sortByDistanceFlag = this.$store.state.visitDay_sortByDistanceFlag
      if (typeof sortByDistanceFlag != 'undefined' && sortByDistanceFlag === true) {
        this.theDaySupcusts.sort((a, b) => Number(a.distance) - Number(b.distance)).reverse()
      }
      this.activeIndex = this.theDaySupcusts.length - 1
      this.$nextTick(() => {
        this._createWheel().enable()
      })
    },

    insertSupcust(item) {
      if (this.theDaySupcusts.some(supcust => supcust.supcust_id === item.ids)) {
        //this.$toast("该客户已加入此日程中，请勿重复添加！")
        this.showMyToast = true
        this.$refs.myToast.textContent = "该客户已加入此日程中，请勿重复添加！"
        setTimeout(()=>{
           this.showMyToast = false
        },1000)
        return
      }
      let param = {
        operKey: this.$store.state.operKey,
        supcust_id: item.ids,
      }
      if (this.insertTypeFlag === 'insert') {
        const { order_index, day_id } = this.leftSlideSupInfo
        param.order_index = order_index
        param.day_id = day_id
        param.operType = 'insert'
      }
      if (this.insertTypeFlag === 'add') {
        param.order_index = this.theDaySupcusts.length + 1
        param.day_id = this.selectedDayRoad.day_id
        param.operType = 'add'
      }
      InsertTheDaySupcust(param).then(res => {
        this.selectDayRoad(this.selectedDayRoad)
        this.selectSupcustShow = false
      })
    },
    addSupcust(item) {
      console.log(item)
      if (this.theDaySupcusts.some(supcust => supcust.supcust_id === item.ids)) {
        this.showMyToast = true
        this.$refs.myToast.textContent = "该客户已加入此日程中，请勿重复添加！"
        setTimeout(()=>{
           this.showMyToast = false
        },1000)
        //this.$toast("该客户已加入此日程中，请勿重复添加！")
        return
      }
      const param = {
        operKey: this.$store.state.operKey,
        order_index: theDaySupcusts.length,
        supcust_id: item.ids,
        oper_type: "add",
        day_id
      }
      InsertTheDaySupcust(param).then(res => {
        this.selectDayRoad(this.selectedDayRoad)
        this.selectSupcustShow = false
      })
    },
    removeSupcust(item) {
      console.log(item)
      const { order_index, day_id } = item
      const param = {
        operKey: this.$store.state.operKey,
        order_index,
        day_id
      }
      RemoveTheDaySupcust(param).then(res => {
        this.theDaySupcusts = this.theDaySupcusts.filter(supcust => supcust.order_index != order_index)
      })
    },
    openNavigationAppDialog(naviInfo) {
      //打开弹出框并且把选中商家的导航信息存入全局变量
      this.selectedSupcustNavigatorInfo = naviInfo
      this.showChooseNavigationApp = true;
    },
    onNaviSelectItem(item) {
      if (isiOS) {
        this.jumpiOSNaviUrlBySelectAppName(item.name);
      } else if(isHarmony){
        this.jumpHarmonyNaviUrlBySelectAppName(item.name);
      } else{
        this.jumpAndroidNaviUrlBySelectAppName(item.name);
      }
      //隐藏弹出框
      this.showChooseNavigationApp = false;
    },
    async getCurPosition(){
        const params = {
            message: "需要定位权限来获取当前位置",
            key: "positionVisitDay"
        };
        const position = await Position.getPosition(params);
        console.log(position);
        return position;
    },
    async jumpHarmonyNaviUrlBySelectAppName(name) {
      //从全局变量中取选中商家的导航信息
      const navigationInfo = this.selectedSupcustNavigatorInfo;
      const { addr_lng, addr_lat, sup_addr } = navigationInfo
      const { longitude, latitude }=await this.getCurPosition()
      if (name == "百度地图") {
        const navi = new Navi("baidu", isiOS, addr_lng + "," + addr_lat, sup_addr, isHarmony)
        var ref = cordova.InAppBrowser.open(navi.getBaiduUrl(), "_system");
        ref.show();
      }
      if (name == "高德地图") {
        const navi = new Navi("gaode", isiOS,addr_lng + "," + addr_lat, sup_addr, isHarmony, longitude + "," + latitude)
        var ref = cordova.InAppBrowser.open(await navi.getGaoDeUrl(), "_system");
        ref.show();
      }
    },
    async jumpiOSNaviUrlBySelectAppName(name) {
      //从全局变量中取选中商家的导航信息
      const navigationInfo = this.selectedSupcustNavigatorInfo;
      const { addr_lng, addr_lat, sup_addr } = navigationInfo
      if (name == "百度地图") {
        const navi = new Navi("baidu", isiOS, addr_lng + "," + addr_lat, sup_addr)
        cordova.InAppBrowser.open(navi.getBaiduUrl(), "_system");
      }
      if (name == "高德地图") {
        const navi = new Navi("gaode", isiOS, addr_lng + "," + addr_lat, sup_addr)
        cordova.InAppBrowser.open(await navi.getGaoDeUrl(), "_system");

      }
    },

    async jumpAndroidNaviUrlBySelectAppName(name) {
      //从全局变量中取选中商家的导航信息
      const navigationInfo = this.selectedSupcustNavigatorInfo;
      const { addr_lng, addr_lat, sup_addr } = navigationInfo
      if (name == "百度地图") {
        const navi = new Navi("baidu", isiOS, addr_lng + "," + addr_lat, sup_addr)
        window.location.href = navi.getBaiduUrl()
      }
      if (name == "高德地图") {
        // const position = await this.baiduPoi2gaodePoi(addr_lng,addr_lat);
        // const lng = position.split(",")[0];
        // const lat = position.split(",")[1];
        const navi = new Navi("gaode", isiOS, addr_lng + "," + addr_lat, sup_addr)
        window.location.href = await navi.getGaoDeUrl()
      }
    },


    _createWheel() {

      console.log(this.activeIndex)
      if (!this.wheels) {
        const wheel = this.wheels = new BetterScroll(this.$refs.myWrapper, {
          wheel: {
            selectedIndex: this.activeIndex,
            wheelWrapperClass: 'wheel-scroller',
            rotate: 0
          },
          observeDOM: false
        })
        wheel.on("scrollStart", () => {
          this.canClickVisitBtn = false
        })
        wheel.on('scrollEnd', () => {
          console.log(this.activeIndex)
          console.log(this.allowSkipVisit)
          if(!this.allowSkipVisit && wheel.getSelectedIndex() != this.activeIndex){
          //禁止跳点
             this.$toast("禁止跳点！")
             wheel.wheelTo(this.activeIndex,0)
             this.canClickVisitBtn = true
             return
           }
          this.canClickVisitBtn = true
          this.$emit("change", wheel.getSelectedIndex())
          this.activeIndex = wheel.getSelectedIndex()

          //const theSupcust=this.theDaySupcusts[ this.activeIndex]
          // this.map.centerAndZoom(new BMap.Point(theSupcust.addr_lng,theSupcust.addr_lat),13)
          //滚动完成之后获取当前选取的索引值
          // this.$emit(EVENT_CHANGE)
        })
      } else {
        this.wheels.refresh()
      }
      return this.wheels
    },


    isDistanceMoreOneKM(distance) {
      return distance > 1000;
    },
    processDistanceAndFormatUnit(distance) {
      let distanceStr = "";
      if (this.isDistanceMoreOneKM(distance)) {
        distance = distance / 1000;
        distanceStr = distance.toFixed(2) + " km ";
      } else {
        distanceStr = parseInt(distance) + " m ";
      }
      return distanceStr;
    },


  },
};
</script>
<style lang="less" scoped>
// height:136px
@flex_w: {
  display: flex;
  flex-wrap: wrap;
};
@flex_a_j: {
  display: flex;
  align-items: center;
  justify-content: center;
};
@flex_a_bw: {
  display: flex;
  align-items: center;
  justify-content: space-between;
};
@flex_a_end: {
  display: flex;
  align-items: center;
  justify-content: flex-end;
};
@flex_a_flex: {
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
};
.contentWrapper{
  height: calc(100% - 150px);
}
.custom-left :deep(.content) {
  left: -10px !important;
}

.visit-icon{
  width: 22px;
  height: 22px;
}
.navi-select-item {
  font-size: 0.65rem;
  color: #1887f7;
  border-bottom: solid 0.025rem #ccc;
  padding-top: 0.5rem;
  padding-bottom: 0.5rem;
}
.temp-content-container {
  height: calc(30rem);
}
.public_query {
  background: #ffffff;
  height: 100%;
  .public_query_title {
    padding-top: 5px;
    border-bottom: 1px solid #f2f2f2;
    .public_query_titleSrc {
      padding: 0 10px;
      height: 35px;
      @flex_a_bw();
      margin-top: 5px;
      .public_query_titleSrc_item {
        width: 48%;
        height: 100%;
        border: 1px solid #cccccc;
        border-radius: 4px;
        overflow: hidden;
        position: relative;
        vertical-align: top;
        // div {
        //   height: 100%;
        //   width: calc(100% - 40px);
        //   padding: 0 30px 0 10px;
        //   border: none;
        //   font-size: 15px;
        //   line-height: 35px;
        //   color: #333333;
        //   text-align: left;
        // }
        input {
          height: 100%;
          width: calc(100% - 45px);
          padding: 0 45px 0 5px;
          border: none;
          font-size: 15px;
          line-height: 35px;
          color: #333333;
          vertical-align: top;
        }
        span {
          position: absolute;
          right: 0;
          top: 0;
          bottom: 0;
          width: 45px;
          font-size: 16px;
          color: #000;
          @flex_a_j();
          background: #4c99e7;
        }
        .van-icon {
          position: absolute;
          right: 0;
          top: 0;
          bottom: 0;
          width: 30px;
          font-size: 22px;
          @flex_a_j();
          color: #666666;
        }
      }
    }
    .public_list_title {
      height: 40px;
      @flex_a_bw();
      margin-top: 5px;
      padding: 0 5px;
      div {
        height: 40px;
        line-height: 40px;
        font-size: 15px;
        text-align: center;
        width: calc(25% - 10px);
        padding: 0 5px;
        font-weight: 500;
        color: #333333;
      }
      div:first-child {
        width: calc(50% - 10px);
        text-align: left;
      }
      div:last-child {
        width: calc(25% - 10px);
        text-align: right;
      }
    }
  }
}
.sales_list_boxs {
  height: 12.875rem;
  overflow-x: hidden;
  overflow-y: auto;
  background: #f2f2f2;
}
.sales_list_boxs_no {
  height: calc(100% - 54px);
  @flex_a_flex();
  background: #f2f2f2;
  color: #999999;
  .whole_box_no_icon {
    font-size: 50px;
  }
  p {
    font-size: 15px;
  }
}
.sales_ul {
  width: 100%;
  height: auto;
  overflow: hidden;
  box-sizing: border-box;
  padding: 0 4px;

  background: #f2f2f2;
  li {
    height: auto;
    overflow: hidden;
    box-sizing: border-box;
    border-bottom: 1px solid #f2f2f2;
    .sales_ul_t {
      overflow: hidden;
      @flex_a_bw();
      height: auto;
      div {
        font-size: 15px;
      }
      .sales_ul_tl {
        color: #333333;
        width: 73%;
        text-align: left;
      }
      .sales_ul_tr {
        font-size: 15px;
        color: #1989fa;
        width: 27%;
        text-align: right;
      }
    }
    .sales_ul_b {
      overflow: hidden;
      @flex_a_bw();
      height: auto;
      margin-top: 5px;
      .sales_ul_bl {
        font-size: 13px;
        color: #666666;
        width: 66%;
        min-height: 15px;
        text-align: left;
      }
      .sales_ul_br {
        font-size: 13px;
        width: 32%;
        color: #666666;

        @flex_a_end();
        i {
          font-size: 22px;
          color: #1989fa;
          margin-left: 10px;
        }
      }
    }
  }
  .history-btn {
    margin-left: 10px;
    display: flex;
    color: #fff;
    height: 100%;

    font-size: 18px;
    align-items: center;
    justify-content: center;
    font-size: 12px;
    background: #1989fa;
  }
  li:last-child {
    border-bottom: none;
  }
}
.wrapper {
  left: 0px;
  bottom: 0px;
  width: 100%;
  height: 150px;
  color: #333;
  border-top: 1px solid #f2f2f2;
  box-shadow: 0 -2px 5px #e3e9f2;
  background-color: #ffffff;
  z-index: 10;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  position: fixed;
  .tab-btn {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    width: 100%;
    box-sizing: border-box;
    div {
      padding: 4px;
      height: 80px;
      margin-top: 10px;
      line-height: 80px;
      border-radius: 16px;
    }
    .skip {
      // position: absolute;
      // left: 30px;
    }
    .temp-visit {
      // position: absolute;
      // right: 30px;
    }
    .schedule-visit {
      // position: absolute;
      // right: 30px;
      // width: 80px;
    }
    .visit {
      // position: absolute;
      padding: 4px;
      // left: 30%;
      // margin-left: 1rem;
      font-size: 24px;
      color: #ad2c2c;
      font-weight: 600;
      background: #ffd9d9;
      border-radius: 16px;
    }
    .disabled-visit {
          // position: absolute;
      padding: 4px;
      // left: 30%;
      // margin-left: 1rem;
      color: #303133;
      font-weight: 600;
      background: #E6E8EB;
      border-radius: 16px;
      // position: absolute;
      // padding: 4px;
      // left: 30%;
      // margin-left: 1rem;
      // background: #aaa;
      // border-radius: 16px;
      // color: #eee;
    }
  }
  .info {
    position: absolute;
    bottom: 0.5rem;
    left: 30%;
  }

  .info > .currentDayRoad {
    color: #409EFF;
    text-align: center;
    display: flex;
    flex-direction: row;
    border-bottom: 1px solid;
    div{
      margin-right: 8px;
      font-size: 18px;
    }
  }
}

.sheet_wrapper_visited {
  color: #b2b2b2;
}
.sheet_wrapper {
  width: 100%;
  border-radius: 5px;
  height: 120px;
  padding: 6px;
  z-index: 1;
  box-sizing: border-box;
  border-bottom: 1px solid #ccc;
  // animation-name: example;
  // animation-duration: 4s;
  .sup_info {
    margin-bottom: 2px;
    margin: 0 5px;
    padding-bottom: 4px;
    display: flex;
    div{
      margin: 2px;
    }
    .sup_name_wrapper {
      flex: 3;
      display: flex;
      align-items: flex-start;
      flex-direction: column;
      justify-content: center;
      .visit-tag {
        padding: 3px;
        //border: 2px solid #d9ecff;
        font-size: 14px;
        border-radius: 16px;        
        }
      .visit-tag-sale {
        color:#ad2c2c;
        background: #f7d5d1;
        padding: 3px;
        //border: 2px solid #ffd9d9;
        font-size: 14px;
        border-radius: 16px;
      }
      .sup_name {
        font-size: 18px;
        font-weight: bolder;
        text-align: left;
      }
      .sale_days{
        min-width: 60px;
        .visit-tag{
          min-width:50px;
        }
      }
      .visit_days{
        min-width: 50px;
        .visit-tag{
          min-width:50px;
        }
      }
    }
    .sup_contact {
      flex: 2;
      font-size: 16px;
      display: flex;
      flex-direction: column;
      align-items: flex-end;
      justify-content: center;
      margin-right: 15px;
      .sup_tel {
        margin: 2px 0;
        a {
          color: #337ecc;
        }
      }
    }
    .sheet_no_tag {
      display: flex;
      justify-content: space-between;
      .sheet_no {
        font-size: 17px;
      }
    }
  }
  .sheet_info {
    margin: 2px 5px;
    .sheet_tag {
      flex: 1;
      max-width: 35px;
      display: flex;
      align-items: center;
      justify-content: center;
      span {
        padding: 2px;
      }
    }
    .sheet_addr {
      display: flex;
      justify-content: flex-start;
      flex-wrap: wrap;
      text-align: left;
      margin-bottom:4px;
      margin-left: 4px;
      font-size: 14px;
    }
  }
  .seller_senders_info {
    margin: 4px 5px 0;
    display: flex;
    justify-content: space-between;
    margin-right: 20px;
    color: #000;
  }
}

.day-item-container {
  background: #F2F3F5;
  height: 100%;
  display: flex;
  padding-left: 10px;
  flex-direction: column;
  align-items: flex-start;
  width: 50%;
  //border-bottom: 1px solid;
  
}
.day-item-list{
    display:flex;
    flex-direction:row;
    justify-content:space-between;
    width:100%;
  }
.day-item-name{
    margin-bottom:10px;
    margin-top:10px;
  }
.day-item-add{
  width: 100%;
  box-shadow: 0px 4px 6px rgba(0, 0, 0, 0.1);
  background-color: #ffffff;
  height: 30px;
  line-height: 30px;
  font-size: 20px;
}
.day_name-input {
  margin-top: 20px;
  margin-right: 4px;
  width: 80%;
  height: 35px;
  padding: 2px 10px 5px 10px;
  border-radius: 6px;
  background-color: #f2f2f2;
  border: #f2f2f2 solid 1px;
  font-size: 20px;
}
.new_day-pop{
  height: 13%;
  width: 80%;
  border-radius: 10px;
}
.add_row-operBtns {
  display: flex;
  flex-direction: row;
  position: absolute;
  bottom: 10px;
  right: 5%;
  .save{
    width: 45px;
    background: #fdd3d4;
  }
  .cancel{
    width: 45px;
    background: #e7e7e7;
  }
}
.add_row-operBtns > div {
  padding: 4px;
  border: 1px #ccc solid;
  background: #ccc;
  margin-right: 16px;
  margin-left: 16px;
  border-radius: 4px;
}
.add-day-popup {
  background: #F2F3F5;
  height: 100%;
}
</style>
<style lang="less" scoped>
.content-container {
  display: flex;
  flex-direction: column;
}
.container {
  display: flex;
  flex-direction: column;
}
.addBtn {
  justify-content: center;
  position: fixed;
  font-size: 26px;
  background: #fcdcdc;
  height: 40px;
  width: 95%;
  border-radius: 6px;
  margin: 2% 3% 0px 3%; 
  line-height: 40px;
  opacity: 1 !important;
  z-index: 1;
}
.relative {
  position: relative;
  height: 80%;
  max-height: 600px;
  // position: relative;
}
.absolute {
  position: absolute;
}
.mask-top {
  width: 100%;
  top: calc(35px);
  border-bottom: 1px solid #D4D7DE;
}
.mask-bottom {
  height: 3.2rem;
  width: 100%;
  top: calc(35px);
  border-bottom: 1px solid #D4D7DE;
  background: #F0F2F5;
  z-index: -999;
}
.wheel-wrapper {
  height: 100%;
  .wheel-scroller {
    margin-top: 36px;
    .wheel-item {
      //line-height: 36px;
    }
  }
}
.van-switch {
  background-color: #fff;
  .van-switch__node {
    width: 0.2rem !important;
    height: 0.2rem !important;
  }
}
.van-switch--on {
  background-color: #f99 !important;
  .van-switch__node {
    transform: translateX(0) !important;
  }
}
.my-toast{
    z-index: 9999;
    position: absolute;
    top: 50%;
    left: 20%;
    width: 200px;
    padding: 10px;
    color: #f0f0f0;
    background: #00000090;
}
</style>
