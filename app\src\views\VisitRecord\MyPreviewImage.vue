<template>
  <div id="imgContainer">
    <canvas id="myCanvas" style="position: absolute; z-index: -999; visibility: hidden"></canvas>
    <van-image-preview v-if="!isShowImg" id="preview-container" style="width: 100%; height: 100%;background:#000" @close="closePreview" :start-position="this.index" v-model="isShow" :images="images" @change="onChange">

    </van-image-preview>
    <van-action-sheet v-model="showImageSelect" :actions="[{ name: '保存' }, { name: '分享' }]" cancel-text="取消" description="操作"
      close-on-click-action @select="onImageWaySelected" style="height: auto;" />
    <!-- <img v-if="isShowImg" id="preview-img-container" /> -->
  </div>
</template>
<script>
import Hammer from "hammerjs";
import { Popup, Dialog, ActionSheet } from "vant";
import globalVars from '../../static/global-vars';
export default {
  components: {
    "van-popup": Popup,
    "van-action-sheet":ActionSheet
  },
  name: "MyImagePreview",
  //   components: {
  //     "van-image-preview": ImagePreview,
  //   },
  props: {
    images: {
      type: Array,
      default: () => ([])
    },
    index: {
      type: Number,
      default: () => (0)
    }
  },
  data() {
    return {
      index: 0,
      isShow: true,
      isShowImg: false,
      showImageSelect:false,
      images: [
        "https://img01.yzcdn.cn/vant/apple-1.jpg",
        "https://img01.yzcdn.cn/vant/apple-2.jpg",
      ],
    };
  },
  mounted() {
    let that_ = this;
    // Get a reference to an element
    var img = document.querySelector("#imgContainer");
    // Create a manager to manager the element
    var manager = new Hammer.Manager(img);
    // Create a recognizer
    var Press = new Hammer.Press({
      time: 1000,
    });
    // Add the recognizer to the manager
    manager.add(Press);
    // Subscribe to desired event
    manager.on("press", function () {
      that_.showImageSelect = true
      // Dialog.confirm({
      //   message: "是否保存图片？",
      //   width:"320px"
      // }).then(() => {
      //   that_.saveToPhone();
      // })
    })
  },
  methods: {
    onImageWaySelected(e){
      switch(e.name){
        case '保存':
          this.saveToPhone()
          break
        case '分享':
          window.Wechat.share({
                message: {
                  title: "拜访照片",
                  description: `来自【公司名称】`,
                  thumb: globalVars.wechatConf.imgUrl,
                  media: {
                    type: window.Wechat.Type.IMAGE,
                    image: this.images[this.index]
                  }
                },
                scene: window.Wechat.Scene.SESSION
              }, function () {
                Toast.success("分享成功")
              }, function (reason) {
                Toast.fail("分享失败: " + reason)
              });
      }
    },
    saveToPhone() {
      // eslint-disable-next-line no-unused-vars
      let canvas, context
      // eslint-disable-next-line prefer-const
      canvas = document.getElementById('myCanvas')
      const img = new Image()
      img.src = this.images[this.index]
      img.onload = () => {
        canvas.width = img.width
        canvas.height = img.height
        context = canvas.getContext('2d')
        // eslint-disable-next-line no-undef
        context.drawImage(img, 0, 0)
        try {
          window.canvas2ImagePlugin.saveImageDataToLibrary(
            (msg) => {
              this.$toast("保存成功！")
              // eslint-disable-next-line handle-callback-err
            }, (err) => {
              console.log(err)
              this.$toast("保存失败！")
            },
            document.getElementById('myCanvas')
          )
        } catch (e) {
          console.log(e.message)
        }
      }
    },

    pressImage() { },
    onChange(index) {
      this.index = index;
    },
    //这个组件 model部分写的有点迷，有时候需要双击才能关闭
    //所以暂时设置isShow常在，监听close方法，父组件if控制展不展示给用户
    //--zxk
    closePreview() {
      this.isShow = true;
      this.$emit("closePreviewEvent");
    },
  },
};
</script>


<style lang='less'>
.imgContainer {
  width: 100%;
  height: 100%;
  background-color: #000;
}
.savePhotoBtn {
  position: absolute;
  margin-left: 50px;
  margin-top: 2.5rem;
}
.hideBtn {
  position: absolute;
  margin-top: 16rem;
  margin-left: 8rem;
  width: 60px;
  height: 40px;
  line-height: 40px;
  background-color: #1989fa;
  color: #fff;
}
</style>