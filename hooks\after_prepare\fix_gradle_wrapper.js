#!/usr/bin/env node

/**
 * 修复 gradle-wrapper.properties 文件，使用阿里云镜像
 * 在每次 cordova prepare 后自动执行
 */

const fs = require('fs');
const path = require('path');

module.exports = function(context) {
    // 如果直接运行脚本（用于测试），创建模拟的context
    if (!context) {
        context = {
            opts: {
                projectRoot: __dirname + '/../..',
                platforms: ['android']
            }
        };
    }

    const projectRoot = context.opts.projectRoot;
    const platformRoot = path.join(projectRoot, 'platforms', 'android');

    console.log('🔄 [Gradle Hook] Starting gradle wrapper fix...');
    console.log(`   Project root: ${projectRoot}`);
    console.log(`   Platform root: ${platformRoot}`);

    // 检查是否是 Android 平台
    if (!context.opts.platforms || context.opts.platforms.indexOf('android') === -1) {
        console.log('🔄 [Gradle Hook] Skipping - Android platform not found');
        return;
    }
    
    // 需要同步的两个gradle-wrapper配置文件
    const wrapperPropsPaths = [
        {
            name: 'Android Studio (主项目)',
            path: path.join(platformRoot, 'gradle', 'wrapper', 'gradle-wrapper.properties')
        },
        {
            name: 'Cordova Tools',
            path: path.join(platformRoot, 'tools', 'gradle', 'wrapper', 'gradle-wrapper.properties')
        }
    ];
    
    // 从 cdv-gradle-config.json 读取Gradle版本配置
    let GRADLE_VERSION = '8.9'; // 默认版本
    const configPath = path.join(platformRoot, 'cdv-gradle-config.json');

    try {
        if (fs.existsSync(configPath)) {
            const configContent = fs.readFileSync(configPath, 'utf8');
            const config = JSON.parse(configContent);
            if (config.GRADLE_VERSION) {
                GRADLE_VERSION = config.GRADLE_VERSION;
                console.log(`📋 [Gradle Hook] 从配置文件读取Gradle版本: ${GRADLE_VERSION}`);
            } else {
                console.log(`⚠️  [Gradle Hook] 配置文件中未找到GRADLE_VERSION，使用默认版本: ${GRADLE_VERSION}`);
            }
        } else {
            console.log(`⚠️  [Gradle Hook] 配置文件不存在，使用默认版本: ${GRADLE_VERSION}`);
        }
    } catch (error) {
        console.log(`⚠️  [Gradle Hook] 读取配置文件失败，使用默认版本: ${GRADLE_VERSION}，错误: ${error.message}`);
    }

    const distributionType = 'all';
    const USE_LOCAL_GRADLE = false;
    const localPath = 'E:/gradle';

    let distributionUrl;
    if (USE_LOCAL_GRADLE) {
        distributionUrl = `file:///${localPath}/gradle-${GRADLE_VERSION}-${distributionType}.zip`;
    } else {
        distributionUrl = `https://services.gradle.org/distributions/gradle-${GRADLE_VERSION}-${distributionType}.zip`;
    }

    console.log(`🎯 [Gradle Hook] 统一Gradle版本: ${GRADLE_VERSION}-${distributionType}`);
    console.log(`🎯 [Gradle Hook] Distribution URL: ${distributionUrl}`);

    // 匹配各种可能的 distributionUrl 格式（支持bin和all版本）
    const patterns = [
        /distributionUrl=https\\?:\/\/services\.gradle\.org\/distributions\/gradle-[\d\.]+-(?:bin|all)\.zip/g,
        /distributionUrl=https:\/\/services\.gradle\.org\/distributions\/gradle-[\d\.]+-(?:bin|all)\.zip/g,
        /distributionUrl=http:\/\/services\.gradle\.org\/distributions\/gradle-[\d\.]+-(?:bin|all)\.zip/g,
        /distributionUrl=https\\?:\/\/maven\.aliyun\.com\/repository\/gradle-plugin\/gradle\/gradle\/[\d\.]+\/gradle-[\d\.]+-(?:bin|all)\.zip/g,
        /distributionUrl=file:\/\/\/[^\/]+\/gradle\/gradle-[\d\.]+-(?:bin|all)\.zip/g
    ];

    let updatedCount = 0;

    // 处理每个gradle-wrapper配置文件
    wrapperPropsPaths.forEach(wrapper => {
        console.log(`\n🔧 处理: ${wrapper.name}`);

        if (!fs.existsSync(wrapper.path)) {
            console.log(`❌ 文件不存在: ${wrapper.path}`);
            return;
        }

        try {
            let content = fs.readFileSync(wrapper.path, 'utf8');
            console.log(`📋 当前内容:`);
            console.log(content);

            let wasModified = false;
            patterns.forEach(pattern => {
                if (pattern.test(content)) {
                    content = content.replace(pattern, `distributionUrl=${distributionUrl}`);
                    wasModified = true;
                }
            });

            // 确保禁用分发URL验证（避免本地文件验证问题）
            if (USE_LOCAL_GRADLE) {
                if (content.includes('validateDistributionUrl=true')) {
                    content = content.replace('validateDistributionUrl=true', 'validateDistributionUrl=false');
                    wasModified = true;
                } else if (!content.includes('validateDistributionUrl=false')) {
                    content += '\nvalidateDistributionUrl=false';
                    wasModified = true;
                }
            }

            if (wasModified) {
                fs.writeFileSync(wrapper.path, content, 'utf8');
                console.log(`✅ 已更新到 Gradle ${GRADLE_VERSION}-${distributionType}`);
                updatedCount++;
            } else {
                console.log(`✅ 已经是 Gradle ${GRADLE_VERSION}-${distributionType}，无需更新`);
            }

        } catch (error) {
            console.error(`❌ 更新失败: ${error.message}`);
        }
    });

    if (updatedCount > 0) {
        console.log(`\n🎉 同步完成！已更新 ${updatedCount} 个gradle-wrapper配置`);
        console.log(`✅ Android Studio和Cordova现在使用相同的Gradle版本: ${GRADLE_VERSION}-${distributionType}`);
    } else {
        console.log(`\n✅ 所有gradle-wrapper配置已经同步，无需更新`);
    }
};

// 如果直接运行此脚本，执行hook函数
if (require.main === module) {
    module.exports();
}
