/**
 * JsBarcode按需加载功能测试
 * 
 * 这个文件用于测试JsBarcode按需加载功能是否正常工作
 * 使用方法：
 * 1. 在浏览器控制台运行这些测试函数
 * 2. 或者在Vue组件中导入并调用测试函数
 */

import jsBarcodeLoader, { 
  loadJsBarcode, 
  ensureJsBarcodeLoaded, 
  generateBarcode, 
  getJsBarcode 
} from '../utils/jsBarcodeLoader.js';

// 测试函数：基础加载测试
export async function testBasicLoading() {
  console.log('开始测试JsBarcode基础加载...');
  
  try {
    // 测试是否未加载
    console.log('加载前JsBarcode是否存在:', typeof window.JsBarcode !== 'undefined');
    
    // 加载JsBarcode
    await loadJsBarcode();
    
    // 测试是否已加载
    console.log('加载后JsBarcode是否存在:', typeof window.JsBarcode !== 'undefined');
    
    const JsBarcode = getJsBarcode();
    console.log('获取到的JsBarcode实例:', JsBarcode ? '有效' : '无效');
    
    console.log('✅ 基础加载测试通过');
    return true;
  } catch (error) {
    console.error('❌ 基础加载测试失败:', error);
    return false;
  }
}

// 测试函数：条码生成测试
export async function testBarcodeGeneration() {
  console.log('开始测试条码生成功能...');
  
  try {
    // 创建测试用的image元素
    const img = document.createElement('img');
    img.id = 'test-barcode';
    img.style.display = 'none';
    document.body.appendChild(img);
    
    // 生成条码
    await generateBarcode(img, '1234567890', {
      height: 100,
      width: 2,
      fontSize: 20,
      textMargin: 2
    });
    
    // 检查是否生成成功
    if (img.src && img.src.startsWith('data:image')) {
      console.log('✅ 条码生成测试通过');
      console.log('生成的条码图片长度:', img.src.length);
      
      // 清理测试元素
      document.body.removeChild(img);
      return true;
    } else {
      console.error('❌ 条码生成测试失败：未生成有效的条码图片');
      document.body.removeChild(img);
      return false;
    }
  } catch (error) {
    console.error('❌ 条码生成测试失败:', error);
    return false;
  }
}

// 测试函数：重复加载测试
export async function testMultipleLoading() {
  console.log('开始测试重复加载功能...');
  
  try {
    // 多次调用加载函数
    const promises = [];
    for (let i = 0; i < 5; i++) {
      promises.push(ensureJsBarcodeLoaded());
    }
    
    // 等待所有加载完成
    await Promise.all(promises);
    
    // 检查JsBarcode是否正常
    const JsBarcode = getJsBarcode();
    if (JsBarcode) {
      console.log('✅ 重复加载测试通过');
      return true;
    } else {
      console.error('❌ 重复加载测试失败：JsBarcode未正确加载');
      return false;
    }
  } catch (error) {
    console.error('❌ 重复加载测试失败:', error);
    return false;
  }
}

// 运行所有测试
export async function runAllTests() {
  console.log('🔧 开始运行JsBarcode按需加载功能测试...');
  
  const results = [];
  
  results.push(await testBasicLoading());
  results.push(await testBarcodeGeneration());
  results.push(await testMultipleLoading());
  
  const passed = results.filter(r => r).length;
  const total = results.length;
  
  if (passed === total) {
    console.log(`🎉 所有测试通过！(${passed}/${total})`);
  } else {
    console.log(`⚠️  部分测试失败 (${passed}/${total})`);
  }
  
  return passed === total;
}

// 便捷的全局测试函数（可在浏览器控制台直接调用）
window.testJsBarcodeLoader = runAllTests;