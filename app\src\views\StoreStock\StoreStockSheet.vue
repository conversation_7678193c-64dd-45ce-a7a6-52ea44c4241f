<template>
  <div class="wrapper">
    <div class="wrapper-head">
      <van-nav-bar
        left-arrow
        safe-area-inset-top
        title="门店库存上报单"
        @click-left="goback"
      >
        <template #right>
          <div class="submitSalesSlip" @click="handleSubmitClick">
            <svg width="30px" height="30px" style="margin:2px 2px 0 0 " stroke-width="1.3" class="black">
              <use :xlink:href="'#icon-plane'"></use>
            </svg>
          </div>
        </template>
      </van-nav-bar>
    </div>
   
    <div class="content" style="padding: 10px 10px 60px 10px;">
      <div class="public_query_wrapper">
            <div class="public_query_titleSrc_item" style="width:100%">
              <van-icon name="user-o" @click="onSelectClient" />
              <input type="text" style=" width: calc(100% - 60px);" v-model="sheet.client_name" placeholder="客户" readonly @click="onSelectClient" />
            </div>
        </div>
      <div class="content-query" style="padding: 10px 15px 3px 15px;display: none;">
        <div class="content-query-item">
          <van-icon name="wap-home-o" @click="onSelectBranch" />
          <input style="padding: 0 10px 4px 0px;" type="text" v-model="sheet.branch_name" placeholder="仓库" readonly @click="onSelectBranch" />
        </div>
      </div>
      <div class="public_query_title" v-if="sheet.sheet_no" style="margin-bottom: 1px">
        <div class="public_query_title_t" style="padding: 10px 10px 0;">
          <span>{{ sheet.sheet_no }}</span>
          <span>{{ sheet.approve_time }}</span>
        </div>
      </div>
      <ConcaveDottedCenter />
      <div class="content-sheet-rows">
        <StoreStockSheetRows ref="StoreStockSheetRows" :sheet="sheet" @handleItemDelete="handleItemDelete"
          @handleSheetRowsSort="handleSheetRowsSort" @handleItemEdit="handleItemEdit" />
        <!-- <StoreStockSheetRowsTotalInformation ref="StoreStockSheetRowsTotalInformation" :sheet="sheet" /> -->
      </div>
    </div>
    <div class="footer">
      <input class="footer_input" id="codes" type="text" style="padding-left: 3px;" v-model="searchStr" @keydown="onSearchInputKeyDown($event)" placeholder="名称/简拼/条码/货号"
        :disabled="!!sheet.approve_time" :style="{ backgroundColor: sheet.approve_time ? '#f2f2f2' : '' }" />
      <van-button class="footer_iconBt" :disabled="sheet.approve_time !== ''" @click="btnClassView_click">
        <svg width="35px" height="35px" fill="#F56C6C">
          <use xlink:href="#icon-add"></use>
        </svg>
      </van-button>
      <van-button
        class="footer_iconBt"
        type="info"
        @click="btnScanBarcode_click"
      >
        <svg width="30px" height="30px" fill="#555">
          <use xlink:href="#icon-barcodeScan"></use>
        </svg>
      </van-button>
    </div>
    <van-popup v-model="bPopupBranchPicker" round position="bottom">
      <van-picker show-toolbar title="选择仓库" :columns="branchList" value-key="branch_name"
        @cancel="bPopupBranchPicker = false" @confirm="onConfirmBranch" />
    </van-popup>
    <van-popup v-model="showUnsubmitedSheets"  round>
      <div class="lowItem" style="width: 320px;">
        <h4>点击打开未提交单据</h4>
        <ul class="lowItem_ul">
          <li v-for="(item, index) in unsubmitedSheets" :key="index">
            <div class="lowItem_ull" @click="onUnsubmitedSheetSelected(item)">
              {{ item.branch_name }}
            </div>
            <div class="lowItem_ulr" @click="onUnsubmitedSheetSelected(item)">
              {{ item.saveTime }}
            </div>
            <div  class="btn-delete" @click="onUnsubmitedSheetDelete(index)">
              删除
            </div>
          </li>
        </ul>
        <van-button type="default" @click="showUnsubmitedSheets = false"
          >新建单据
        </van-button>
      </div>
    </van-popup>
    <van-popup v-model="popupEditSheetRows" :close-on-click-overlay="false" position="bottom"
      :style="{ height: '480px' }">
      <StoreStockEditSheetRows ref="editSheetRows" @closeEdit="closeEdit" :sheet="sheet"
        :editSheetRowsInfo="editSheetRowsInfo" />
    </van-popup>
    <van-popup v-model="bPopupClientSelectDialog" duration="0.4" position="bottom" :lock-scroll="false" :style="{ height: '100%', position: 'absolute', width: '100%', overflow: 'hidden' }">
      <SelectCustomer canHide='true' @handleHide='bPopupClientSelectDialog = false' ref='selectCustomer'  @onClientSelected="onClientSelected">
      </SelectCustomer>
    </van-popup>
   
    <van-popup v-model="popupAddStoreStockSheetRow" :lazy-render="true" position="bottom"
      style="height:100vh;overflow:hidden">
      <StoreStockMultiSelect v-if="popupAddStoreStockSheetRow" ref="multiSelect" :sheet="sheet" @closePage="closePage"
        @closeSingleChoicePage="closeSingleChoicePage">
      </StoreStockMultiSelect>
    </van-popup>
    <van-popup v-model="showSubmitPopup" duration="0.4" :style="{ height: '100%', width: '80%' }" class="van_popup"
      position="right">
      <div style="height:30px;border-top:1px solid #ccc"></div>
      <div class="other_operate">
        <van-field v-model="sheet.make_brief" label="备注" style="white-space: nowrap;" label-width="40px" placeholder="请输入备注"
          :disabled="sheet.approve_time ? true : false" />
        <div style="height:30px"></div>
        <div class="other_operate_content">
          <button v-if="canMake" :disabled="sheet.approve_time !== ''" @click="handleSheetSave"
            style="height: 45px;border-radius:12px;background-color: #ffcccc;">保存</button>
          <button v-if="canApprove" :disabled="sheet.approve_time !== ''" @click="handleSheetApprove"
            style="height: 45px;border-radius:12px;background-color: #ffcccc;">审核</button>
        </div>
        <div class="other_operate_content">
          <button style="height: 45px;border-radius:12px" :style="{
            color: sheet.red_flag !== '' ? '#ff9999' : '#ee0a24',
            borderColor: sheet.red_flag !== '' ? '#ff9999' : '#ee0a24',
          }" v-if="canRed && sheet.approve_time" :disabled="sheet.red_flag !== ''"
            @click="handleSheetRed">红冲</button>
          <button v-if="sheet.sheet_id && !sheet.approve_time && canDelete" style="height: 45px;border-radius:12px"
            @click="handleSheetDelete">删除</button>
          <button @click="handleSheetPrint" v-if="canPrint" style="height: 45px;border-radius:12px"
            :disabled="(sheetStatusForPrint === 'saved' && !sheet.sheet_id) || (sheetStatusForPrint === 'approved' && !sheet.approve_time) || isPrinting">打印</button>          
        </div>
        <div class="other_operate_content">
          <button v-if="sheet.sheet_id  && (canDelete||canRed)" style="height: 45px;border-radius:12px;"
          @click="showDialog = true">生成单据</button>
          <!-- 弹出窗口 -->
          <div v-if="showDialog" class="custom-dialog-overlay">
            <div class="custom-dialog">
              <p style="margin-bottom: 15px;font-weight: 500;;">选择生成类型</p>
              <button @click="handleCreate('SHEET_SALE_DD')" style="height: 45px;border-radius:28px;margin-bottom: 15px;width: 80%;background-color: #f9ebe9;border: 2px solid #A11E3A; ">生成销售订单</button>
              <button @click="handleCreate('SHEET_SALE')" style="height: 45px;border-radius:28px;margin-bottom: 15px;width: 80%;background-color: #f9ebe9;border: 2px solid #A11E3A;">生成销售单</button>
              <button @click="showDialog = false" style="height: 45px;border-radius:28px;width: 80%;background-color: #f9ebe9;border: 2px solid #A11E3A;">取  消</button>
            </div>
          </div>
      </div>
        <template v-if="canPrint">
          <van-divider>打印条码</van-divider>
          <van-radio-group v-model="printBarcodeStyle"
            style="font-size: 12px; margin-left: 0; display: flex; justify-content: center;padding-top: 10px;">
            <van-radio name="noBarcode" style="margin-right: 10px">不打印</van-radio>
            <van-radio name="actualUnit" style="margin-right: 10px">实际单位</van-radio>
            <van-radio name="smallUnit" style="margin-right: 10px">小单位</van-radio>
          </van-radio-group>
          <van-checkbox v-if="printBarcodeStyle === 'actualUnit' || printBarcodeStyle === 'smallUnit'" shape="square"
            v-model="printBarcodePic" icon-size="20px"
            class="print-barcode"
            style="margin-left: 50px; margin-top: 10px">打印条码图</van-checkbox>
        </template>
        <van-divider style="margin: 20px 0" @click="moreOptions = !moreOptions">
          <svg v-if="moreOptions" width="26px" height="26px" fill="#d8d8d8">
            <use xlink:href="#icon-double_arrow"></use>
          </svg>

          <svg
            style="-webkit-transform: rotate(180deg);"
            v-else
            width="26px"
            height="30px"
            fill="#d8d8d8"
          >
            <use xlink:href="#icon-double_arrow"></use>
          </svg>
        </van-divider>
        <template v-if="moreOptions">
          <div class="other_operate_content">
            <van-button type="default" :disabled="IsSubmiting || sheet.approve_time !== ''" style="border-radius:12px;"
              @click="onEmpty">清空</van-button>
            <van-button style="border-radius:12px;" type="default" @click="btnCopySheet_click">复制</van-button>
            <van-button type="default" style="border-radius:12px;" @click="btnOut">退出</van-button>
          </div>
        </template>
      </div>
    </van-popup>
  </div>
</template>

<script>
import {
  NavBar,
  Field,
  Icon,
  Picker,
  Toast,
  Button,
  Dialog,
  RadioGroup,
  Radio,
  Divider,
  Checkbox,
} from "vant";
import {
  GetBranchList,
  AppSheetStoreStockLoad,
  AppSheetStoreStockSubmit,
  AppSheetStoreStockRed,
  AppSheetStoreStockDelete,
  AppSheetStoreStockSave,
  AppSheetStoreStockCreate,
  AppSheetSaleOrderLoad,
  AppSheetStoreStockGetItemList,
  AppSheetStoreStockGetStockQtyList,
} from "../../api/api";
import StoreStockSheetRows from "./StoreStockSheetRows";
import ConcaveDottedCenter from "../components/ConcaveDottedCenter";
import StoreStockMixin from "./StoreStockMixin";
import mixins from "../SaleSheet/sheetMixin/mixin";
// import StoreStockSheetRowsTotalInformation from "./StoreStockSheetRowsTotalInformation";
import StoreStockEditSheetRows from "./StoreStockEditSheetRows";
import Printing from "../Printing/Printing";
import StoreStockMultiSelect from "./StoreStockMultiSelect";
import SelectCustomer from "../components/SelectCustomer";
import globalVars from "../../static/global-vars";
import RouterUtil from "../../util/RouterUtil";


export default {
  name: "StoreStockSheet",
  mixins: [StoreStockMixin, mixins],
  components: {
    StoreStockMultiSelect,
    StoreStockEditSheetRows,
    // StoreStockSheetRowsTotalInformation,
    StoreStockSheetRows,
    "van-nav-bar": NavBar,
    "van-icon": Icon,
    "van-button": Button,
    "van-picker": Picker,
    ConcaveDottedCenter,
    "van-radio-group": RadioGroup,
    "van-radio": Radio,
    "van-divider": Divider,
    "van-checkbox": Checkbox,
    SelectCustomer,
    "van-dialog": Dialog,
    "van-field": Field,
  },
  data() {
    return {
      sheet: {
        sheetType: "SS",
        sheet_no: "",
        sheet_id: "",
        approve_time: "",
        make_time: "",
        sheetRows: [],
        make_brief: "",
        branch_id_forsale:"",
        branch_name_forsale:"",
        sheetTypeCreate: "",
      },
      clientInfoObj:{},
      canChangeCustomer: true,
      bPopupClientSelectDialog: false,
      branchList: [],
      bPopupBranchPicker: false,
      IsSubmiting: false,
      searchStr: "",
      showUnsubmitedSheets: false,
      unsubmitedSheets: [],
      editSheetRowsInfo: [],
      popupEditSheetRows: false,
      showSubmitPopup: false,
      printBarcodeStyle: "noBarcode",
      moreOptions: false,
      printBarcodePic: false,
      isPrinting: false,
      popupAddStoreStockSheetRow: false,
      showDialog: false,
      sheetToOtherSheetAction: [
        { text: '销售单', sheetType: "X", right: 'sale.sheetSale.make' },
        { text: '销售订单', sheetType: "XD", right: 'sale.sheetSaleOrder.make' },
      ],
    };
  },
  mounted() {
    this.loadSheet();
    this.handleRegisterSayCode();
    this.loadBranches();
  },
  activated() {
    this.$refs.StoreStockSheetRows.handleUpdate();
    // this.$refs.StoreStockSheetRowsTotalInformation.handleUpdate();
  },
  beforeRouteLeave(to, from, next) {
    // if (to.name !== 'StoreStockSelectItems' && this.sheet.branch_name && this.sheet.sheet_no === '') {
    //  this.saveCurSheetToCache(this.sheet)
    // }
    next();
  },
  watch: {
    "sheet.sheetRows": {
      handler: function () {
        this.handleSelectedSheetRows('SS')
        if (this.sheet.branch_name && this.sheet.sheet_id === '') {
          this.saveCurSheetToCache(this.sheet)
        }
      },
      deep: true,
    },
    popupEditSheetRows: {
      handler: function(newVal) {
        if (newVal === false) {
          this.$refs.StoreStockSheetRows.handleUpdate();
          // this.$refs.StoreStockSheetRowsTotalInformation.handleUpdate();
        }
      },
      deep: true,
    },
  },
  computed: {
    canEdit() {
      if (this.sheet.approve_time) return false;
      if (!this.canApprove && this.sheet.sheet_id) return false;
      return true;
    },
    canRed() {
      return hasRight("stock.sheetStoreStock.red");
    },
    canMake() {
      return hasRight("stock.sheetStoreStock.make");
    },
    canDelete() {
      return hasRight("stock.sheetStoreStock.delete");
    },
    canPrint() {
      return hasRight("stock.sheetStoreStock.print");
    },
    canApprove() {
      return hasRight("stock.sheetStoreStock.approve");
    },
    allowPrintBeforeApprove() {
      return hasRight("delicacy.allowPrintBeforeApprove.value");
    },
    allowPrintBeforeSave() {
      return hasRight("delicacy.allowPrintBeforeSave.value");
    },
    sheetStatusForPrint() {
      return window.getRightValue("delicacy.moveSheetStatusForPrint.value");
    },
  },
  methods: {
    goback() {
      // eslint-disable-next-line no-undef
      myGoBack(this.$router);
    },
    async loadSheet() {
      const sheetID = this.$route.query.sheetID || "";
      
      if (sheetID) {
        this.showUnsubmitedSheets = false;
      }
      let params = {
        sheetID: sheetID,
      };
      await AppSheetStoreStockLoad(params)
        .then((res) => {
          if (res.result !== "OK") {
            // this.sheet.sheetType=sheetType
            // Toast.fail("请求失败")
            return;
          }
          if (!res.sheet) {
            Toast.fail("无表单数据");
            return;
          }
          this.sheet = res.sheet;
           this.sheet.sheetRows.forEach(row => {
            row.itemImages = row.item_images;
            row.itemImages = this.handleImage(row.itemImages);
          });
          this.handleSheetRowsForLoad();
          this.$store.commit("attrOptions", res.attrOptions);
          const sheetID = this.sheet.sheet_id;
          if (!sheetID) {
            this.showSheetsFromCache();
          }
        })
        .catch((err) => {
          console.log("???");
          Toast(err);
        });
         var branchValid = false;
            var sheetBranches = window.getCompanyStoreValue("c_sheetBranches", {})
            var cachedBranch = sheetBranches['X'] || {}
            //var cachedBranch = that.$store.state.sheetBranches['X']
            //if (!cachedBranch) cachedBranch = that.$store.state.salesBranch;

            var branchValid = window.hasBranchSheetRight(
              cachedBranch.branch_id,
              'X'
            );
            if (branchValid) {
              this.sheet.branch_id_forsale = cachedBranch.branch_id;
              this.sheet.branch_name_forsale  =
                cachedBranch.branch_name || "";
                console.log("branch_id_forsale"+this.sheet.branch_id_forsale)
                console.log("branch_name_forsale"+this.sheet.branch_name_forsale)
             }
    },
    // 弹出用户选择框
    onSelectClient() {
      console.log(111)
      if (!this.canChangeCustomer) {
        this.$toast.fail("强制拜访,无法切换客户")
        return
      }
      if (this.sheet.approve_time) return;
      this.bPopupClientSelectDialog = true;

    },
    // 获取选择用户信息
    onClientSelected(obj) {
      
      this.clientInfoObj=obj
       console.log(this.sheet)
      // 切换用户，将把旧客户的定货会商品转换为普通商品
      let isShowChangeDialog = false
      var that = this
      this.bPopupClientSelectDialog = false
      console.log('client:', { obj })
      // 添加统一收款信息 X,XD
      delete this.sheet.acct_cust_id
      delete this.sheet.acct_cust_name
      this.sheet.visit_cycle = obj.visit_cycle
      if (obj.acct_cust_id !== '') {
        this.sheet.acct_cust_id = obj.acct_cust_id
        this.sheet.acct_cust_name = obj.acct_cust_name
      }
      console.log(this.sheet.sheetRows.length)
      if (this.sheet.sheetRows.length > 0) {
        var bHaveOrderItem = false, bHaveDisplay = false
        for (let i = 0; i < this.sheet.sheetRows.length; i++) {
          var row = this.sheet.sheetRows[i]
          if (row.order_sub_id) bHaveOrderItem = true
          if (row.disp_flow_id) bHaveDisplay = true
        }
        this.bPopupAfterChangeClient = true
      }
      else {
        this.handleChangeSupcustInfo(obj)
        this.doChangeClient()

      }
      console.log("选择客户后",this.sheet)
      
    },
    handleChangeSupcustInfo(obj) {
        this.sheet.license_no = obj.license_no
        this.sheet.client_name = obj.titles;
        this.sheet.client_id = obj.ids;
        this.sheet.mobile = obj.mobile;
        this.sheet.sup_addr = obj.sup_addr;
        this.sheet.acct_type = obj.acct_type
      },
      doChangeClient() {
        if(this.canAppSelectSeller && this.clientInfoObj.charge_seller && this.clientInfoObj.charge_seller_name ){ 
          this.sheet.seller_id=this.clientInfoObj.charge_seller
          this.sheet.seller_name=this.clientInfoObj.charge_seller_name
        }
        if (this.sheet.client_id === 0) {
          this.handleAllowRetail()
          this.onSheetRowsChanged()

        } else {
          this.handleClientChange(true)
        }
        this.loadPromotions()

      },
    
    onSelectBranch() {
      if (this.sheet.approve_time) {
        return;
      }
      this.bPopupBranchPicker = true;
    },
    // 根据权限获取仓库列表
    loadBranches() {
      this.showCustomer = false;
      let params = {};
      GetBranchList(params).then((res) => {
        this.branchList = [];
        if (res.result === "OK") {
          for (let i = 0; i < res.data.length; i++) {
            let branch = res.data[i];
            let branchValid = window.hasBranchSheetRight(
              branch.branch_id,
              this.sheet.sheetType
            );
            // if (branchValid) {
            //   this.branchList.push(branch);
            // }
            if (branchValid) {
              let branchPosition = JSON.parse(branch.branch_position)
              let newBranchPosition = []
              branchPosition.forEach(e=>{
                if(e.branch_position !=="0"){
                  newBranchPosition.push(e)
                }
              })
              this.branchList.push({
                branch_id:branch.branch_id,
                branch_name:branch.branch_name,
                branch_position:newBranchPosition,
                branch_type:branch.branch_type
              })
            }
          }
          if(this.sheet.branch_id){
            this.branchList.some(b=>{
              if(b.branch_id.toString() == this.sheet.branch_id.toString()){
                this.$store.commit("setCurBranchPositionList",b.branch_position)
                return true
              }
            })
          }
          // this.$store.commit("setBranchList",this.branchList)
          return this.branchList;
        }
      });
    },
    // 确认仓库
    onConfirmBranch(value) {
      this.sheet.branch_id = value.branch_id;
      this.sheet.branch_name = value.branch_name;
      this.branchList.some(b=>{
        if(b.branch_id == value.branch_id){
          this.$store.commit("setCurBranchPositionList", b.branch_position)
          return true
        }
      })
      this.bPopupBranchPicker = false;
      // var sheetBranches=this.$store.state.sheetBranches;//[sheetType]
      // sheetBranches[this.sheet.sheetType]=
      //     {
      //       branch_id: this.sheet.branch_id,
      //       branch_name: this.sheet.branch_name
      //     }
      // this.$store.commit("sheetBranches", sheetBranches)
    },
    handleRegisterSayCode() {
      window.sayCode = (result) => {
        this.pageSayCode(result);
      };
    },
    pageSayCode(result) {
      this.searchStr = result;
      this.queryScan();
    },
    onSearchInputKeyDown(e) {
      if (e.keyCode === 13) {
        this.queryScan();
      }
    },
    queryScan() {
      /* if (!this.sheet.branch_id) {
        Toast.fail("请选择仓库"); return
      } */
      console.log(this.searchStr)
      this.$store.commit("currentSheet", this.sheet);
      let params = {
        searchStr: this.searchStr,
        showStockOnly: false,
        classID: "",
        branchID: this.sheet.branch_id,
        supcustID: this.sheet.client_id,
        client_name: this.sheet.client_name,
        pageSize: 20,
        startRow: 0,
        brandIDs: "",
      };
      AppSheetStoreStockGetItemList(params).then((res) => {
        if (res.result === "OK") {
          if (res.data.length === 0) {
            Toast("未找到对应商品");
            return;
          } else if (res.data.length === 1) {
            let item = res.data[0];
            item.item_images = this.handleImage(item.item_images);
            console.log('item',item);
            this.$store.commit("distinctStockFlag", false);
            this.$store.commit("shoppingCarFinish", false);
            let itemObj = JSON.parse(JSON.stringify(item));
            itemObj.isSelectFlag = false;
            itemObj.singleChoice = true;
            itemObj.distinctStockFlag = false;
            if (item.b_unit_no) itemObj.b_unit_qty = ""; // 录入数量
            if (item.m_unit_no) itemObj.m_unit_qty = ""; // 录入数量
            itemObj.s_unit_qty = ""; // 录入数量
            itemObj.remark = ""; // 录入数量
            if (itemObj.mum_attributes) {
              if (!itemObj.mum_attributes.forEach)
                itemObj.mum_attributes = JSON.parse(itemObj.mum_attributes);
              if (itemObj.mum_attributes.find((attr) => attr.distinctStock)) {
                this.$store.commit("distinctStockFlag", true);
                itemObj.distinctStockFlag = true;
              }
            }
            this.popupAddStoreStockSheetRow = true;
            this.$store.commit("multiSelectOpenFlag", true);
            this.$store.commit("attrShowFlag", false);
            setTimeout(() => {
              this.$refs.multiSelect.loadData([itemObj]);
            }, 310);
          } else if (res.data.length > 1) {
            this.btnClassView_click();
          }
        }
      });
    },

    async btnScanBarcode_click() {
      try {
        const result = await this.scanBarcodeNew({
          unit_type: 'store_stock'
        })

        if (!result.code) {
          return
        }

        this.searchStr = result.code
        this.btnClassView_click()
      } catch (error) {
        console.error('扫码失败:', error)
        this.$toast('扫码失败，请重试')
      }
    },
    btnClassView_click() {
      if (!this.sheet.client_id) {
        Toast.fail("请选择客户"); return
      }
      /* else if (!this.sheet.branch_id) {
        /* Toast.fail("请选择仓库"); 
      }  */
      else {
        let query = {
          searchStr: this.searchStr || "",
          sheet: this.sheet,
        };
        this.$store.commit("currentSheet", this.sheet);
        this.$router.push({ path: "/StoreStockSelectItems", query: query });
        this.searchStr = "";
      }
    },
    handleItemDelete(item, index) {
      this.sheet.sheetRows.splice(index, 1);
    },
    handelGetSheet() {
      //let tempTemp = {...this.sheet}
      this.sheet.IsFromWeb = false;
      // tempTemp.SheetRows = this.sheet.sheetRows
      this.sheet.cost_price_type = "3";
      if (this.$store.state.operInfo.setting)
        this.sheet.cost_price_type = this.$store.state.operInfo.setting.costPriceType;

      if (!this.sheet.seller_id)
        this.sheet.seller_id = this.$store.state.operInfo.oper_id;

      // this.sheet.seller_name = this.$store.state.operInfo.oper_name
      this.sheet.maker_name =
        this.sheet.maker_name === ""
          ? this.$store.state.operInfo.oper_name
          : this.sheet.maker_name;
        this.sheet.maker_id =this.$store.state.operInfo.oper_id;
      this.sheet.operKey = this.$store.state.operKey;
      this.sheet.sheetType = "SS";
      // this.sheet.sheet_id = this.sheet.sheet_id
      this.sheet.sheet_id_red_me = "";
      // this.sheet.sheet_no = ''
      this.sheet.sheet_type = "SHEET_STORE_STOCK";
      this.sheet.buy_amount = 0;
      this.sheet.cost_amount_avg = 0;
      this.sheet.wholesale_amount = 0;
      this.sheet.sheetRows.forEach((item) => {
        if (item.difference_qty === undefined) {
          item.difference_qty = item.real_quantity - item.stock_qty;
        }
        if (item.sys_quantity === "") item.sys_quantity = 0;
        if (item.real_quantity === "") item.real_quantity = 0;
        if (item.buy_quantity === "") item.buy_quantity = 0;
        if (item.client_stock_quantity === "") item.client_stock_quantity = 0;
        this.sheet.buy_amount +=
          Number(item.difference_qty) * Number(item.buy_price);
        this.sheet.cost_amount_avg +=
          Number(item.difference_qty) * Number(item.cost_price_avg);
        this.sheet.wholesale_amount +=
          Number(item.difference_qty) * Number(item.wholesale_price);
      });
      this.sheet.buy_amount = toMoney(this.sheet.buy_amount);
      this.sheet.cost_amount_avg = toMoney(this.sheet.cost_amount_avg);
      this.sheet.wholesale_amount = toMoney(this.sheet.wholesale_amount);
    },
    handleItemEdit(item) {
      if (!this.canEdit) return;
      this.editSheetRowsInfo = [item];
      this.popupEditSheetRows = true;
      setTimeout(() => {
        this.$refs.editSheetRows.loadData();
      }, 350);
    },
    closeEdit() {
      this.$refs.StoreStockSheetRows.handleUpdate();
      // this.$refs.StoreStockSheetRowsTotalInformation.handleUpdate();
      this.popupEditSheetRows = false;
      this.$refs.editSheetRows.clearState();
    },
    handleSubmitClick() {
      if (!this.sheet && this.sheet.sheetRows.length <= 0) {
        Toast.fail("请添加商品");
        return;
      }
      this.showSubmitPopup = true;
    },
    async handleSheetSave() {
      // 保存单据
      /* if (this.sheet.branch_id === "") {
        Toast.fail("请选择仓库");
        return;
      } */
      let message = await this.handleRefreshGetStockQtyList();
      console.log("message", message);
      if (message !== "") {
        this.handelGetSheet();
        Toast.fail(message);
        this.$refs.StoreStockSheetRows.handleUpdate();
        // this.$refs.StoreStockSheetRowsTotalInformation.handleUpdate();
        return;
      }
      this.handelGetSheet();
      if (this.sheet.sheetRows.length === 0) {
        Toast.fail("请选择商品");
      } else {
        this.IsSubmiting = true;
        console.log('this.sheet840',this.sheet);
        AppSheetStoreStockSave(this.sheet)
          .then((res) => {
            if (res.result === "OK") {
              this.sheet.sheet_no = res.sheet_no;
              this.sheet.sheet_id = res.sheet_id;
              this.sheet.make_time = res.currentTime;
              setTimeout(() => {
                this.removeCurSheetFromCache();
              }, 300);
              Toast.success("保存成功");
            } else {
              Toast.fail("保存失败");
            }
          })
          .catch((err) => {
            console.log(err);
          });
      }
    },
    async handleCreate(type) {
      // 生成销售订单或销售单
      /* if (this.sheet.branch_id === "") {
        Toast.fail("请选择仓库");
        return;
      } */
      this.showDialog = false; // 关闭对话框
      // 根据选择的类型进行处理
      let action = ""
      var notice=""
      if (type === 'SHEET_SALE_DD') {
        // 处理生成销售订单逻辑
        console.log("选择生成销售订单");
        action = this.sheetToOtherSheetAction[1]
        notice="无转销售订单权限"
      } else if (type === 'SHEET_SALE') {
        // 处理生成销售单逻辑
        console.log("选择生成销售单");
        action = this.sheetToOtherSheetAction[0]
        notice="无转销售单权限"
      }
      let message = await this.handleRefreshGetStockQtyList();
      console.log("message", message);
      if (message !== "") {
        this.handelGetSheet();
        Toast.fail(message);
        this.$refs.StoreStockSheetRows.handleUpdate();
        // this.$refs.StoreStockSheetRowsTotalInformation.handleUpdate();
        return;
      }
      this.handelGetSheet();
      if (this.sheet.sheetRows.length === 0) {
        Toast.fail("请选择商品");
      } else {
        this.IsSubmiting = true;
        this.sheet.sheet_type=type;
        console.log(this.sheet);
        // AppSheetSaleOrderLoad(this.sheet)
        // .then((res) => {
        //     if (res.result === "OK") {
        //       setTimeout(() => {
        //         this.removeCurSheetFromCache();
        //       }, 300);
        //       Toast.success("正在跳转……");
        //     } else {
        //       Toast.fail(res.message || "生成失败");
        //     }
        //   })
        //   .catch((err) => {
        //     Toast.fail("请求失败，请稍后重试");  // 错误处理反馈给用户
        //     console.log(err);
        //   })
          // 销售单权限
      
      if (!hasRight(action.right)) {
        Toast.fail(notice)
        return
      }
     
      // 仓库权限
      if (!this.$store.state.operInfo.branchRights) {
        Toast.fail("暂无仓库权限")
        return
      }
      // let to_branch_id = this.sheet.branch_id_forsale
      // let findBranch = this.$store.state.operInfo.branchRights.find(item => item.branch_id === to_branch_id)
      // if (!findBranch) {
      //   Toast.fail("仓库权限未设置")
      //   return
      // }
      // if (findBranch.sheet_x !== 'True') {
      //   Toast.fail("仓库无销售权限")
      //   return
      // }

      // 由于目前只有转销售，预留后期
      if (this.sheet.sheetRows.length === 0) {
        Toast.fail("请选择商品")
        return
      }

      this.sheetToOtherSheetPopover = false
      // let routerQuerySheetRows = this.sheet.sheetRows.map(item => {
      //   return { item_id: item.item_id, unit_type: "s", quantity: item.buy_quantity,b_unit_quantity }
      // })
      let routerQuerySheetRows = []
      this.sheet.sheetRows.forEach(item => {
        item.b_unit_qty && routerQuerySheetRows.push({ item_id: item.item_id, unit_type: "b", quantity: item.b_unit_qty })
        item.m_unit_qty && routerQuerySheetRows.push({ item_id: item.item_id, unit_type: "m", quantity: item.m_unit_qty })
        item.s_unit_qty && routerQuerySheetRows.push({ item_id: item.item_id, unit_type: "s", quantity: item.s_unit_qty })
      })
      

      // 库存问题
      setTimeout(() => {
        console.log(this.sheet.client_id)
        console.log(this.sheet.client_name)
        if (action.sheetType === 'X'||action.sheetType === 'XD') {
          RouterUtil.goSaleSheet(this, {
            sheetID: '',
            sheetType: action.sheetType,
            supcust_id:this.sheet.client_id,
            sup_name:this.sheet.client_name,
            sheetToOtherParams: {
              sourceSheet:"SS",
              branch_id: this.sheet.branch_id_forsale,
              branch_name: this.sheet.branch_name_forsale,
              sheetRows: routerQuerySheetRows
            }
          })
          console.log(routerQuerySheetRows)
        }
      }, 300)
      }
    },
    async handleSheetApprove() {
      let selectedOption = null;  // 用于保存用户选择的选项
      Dialog.confirm({
        title: "审核单据",
        message: "请确认是否审核?",
        width:"320px",
        }).then(async () => {
        let message = await this.handleRefreshGetStockQtyList();
        if (message) {
          this.handelGetSheet();
          Toast.fail(message);
          this.$refs.StoreStockSheetRows.handleUpdate();
          // this.$refs.StoreStockSheetRowsTotalInformation.handleUpdate();
          return;
        }
        this.handelGetSheet();
        if (!this.sheet.approver_name) {
          this.sheet.approver_name = this.$store.state.operInfo.oper_name;
        }
        this.IsSubmiting = true;
        console.log(this.sheet);
        AppSheetStoreStockSubmit(this.sheet).then((res) => {
          this.IsSubmiting = false;
          if (res.result === "OK") {
            this.sheet.sheet_no = res.sheet_no;
            this.sheet.sheet_id = res.sheet_id;
            this.sheet.make_time = res.currentTime;
            this.sheet.approve_time = res.approve_time;
            if (window.g_curSheetInList) {
              // window.g_curSheetInList.approve_time=res.approve_time
              window.g_curSheetInList.state = "approved";
            }
            this.removeCurSheetFromCache();
            Toast.success("审核成功");
          } else {
            Toast.fail(res.msg);
          }
        });
      });
    },
    handleSheetRed() {
      Dialog.confirm({
        title: "红冲单据",
        message: "请确认是否红冲",
        width:"320px"
      }).then(() => {
        let params = {
          operKey: this.$store.state.operKey,
          sheetID: this.sheet.sheet_id,
        };
        AppSheetStoreStockRed(params).then((res) => {
          if (res.result === "OK") {
            Toast.success("红冲成功,即将退出该页面");
            this.removeCurSheetFromCache();
            setTimeout(() => {
              this.btnOut();
            }, 1000);
          } else {
            Toast.fail("红冲失败:" + res.msg);
          }
        });
      });
    },
    handleSheetDelete() {
      Dialog.confirm({
        title: "删除单据",
        message: "请确认是否删除",
        width:"320px"
      }).then(() => {
        // var delFunc;
        // delFunc = SheetMoveDelete;
        let params = {
          operKey: this.$store.state.operKey,
          sheet_id: this.sheet.sheet_id,
        };
        AppSheetStoreStockDelete(params).then((res) => {
          if (res.result === "OK") {
            Toast.success("删除成功,即将退出该页面");
            this.removeCurSheetFromCache();
            setTimeout(() => {
              this.btnOut();
            }, 1000);
          } else {
            Toast.fail("删除失败:" + res.msg);
          }
        });
      });
    },
    handleSheetPrint() {
      this.isPrinting = true;
      // const showTotalSheetInfo = this.$refs.StoreStockSheetRowsTotalInformation
        // .showTotalSheetInfo;
      this.handelGetSheet();
      const defaultPrinter = window.getDefaultPrinter();
      if (defaultPrinter.type === "cloud") {
        Toast("云打印功能持续开发中，敬请期待，请选择蓝牙打印");
      } else {
        Printing.printStoreStockSheet(
          this.sheet,
          showTotalSheetInfo,
          this.printBarcodeStyle,
          this.printBarcodePic,
          null,
          (res) => {
            this.isPrinting = false;
            if (res.result === "OK") {
              Toast.success("打印成功");
            } else {
              Toast.fail(res.msg);
            }
          }
        );
      }
    },
    btnOut() {
      myGoBack(this.$router);
    },
    btnCopySheet_click() {
      logUserAction({
        tm: new Date().format("yyyy-MM-dd h:m"),
        act: "copyclick",
        sn: this.sheet.sheet_no,
      });
      Dialog.confirm({
        title: "复制单据",
        message: "请确认是否复制?",
        width:"320px"
      }).then(async () => {
        this.sheet.sheet_id = "";
        this.sheet.sheet_no = "";

        this.sheet.approve_time = "";
        this.sheet.approver_id = "";
        this.sheet.happen_time = "";
        this.sheet.make_time = "";
        this.sheet.maker_id = "";
        this.sheet.red_flag = "";
        this.sheet.red_sheet_id = "";
        this.sheet.print_count = "";
        this.sheet.submit_time = "";
        Toast("复制成功");
        let message = await this.handleRefreshGetStockQtyList();
        if (message !== "") {
          this.handelGetSheet();
          this.$refs.StoreStockSheetRows.handleUpdate();
          // this.$refs.StoreStockSheetRowsTotalInformation.handleUpdate();
          Toast("已成功更新当前库存");
          return;
        }
        logUserAction({
          tm: new Date().format("yyyy-MM-dd h:m"),
          act: "copyed",
          sn: this.sheet.sheet_type,
        });
      });
    },
    onEmpty() {
      Dialog.confirm({
        title: "清空单据",
        message: "请确认是否清空",
        width:"320px"
      }).then(() => {
        this.sheet.sheet_no = "";
        this.sheet.sheet_id = "";
        this.sheet.approve_time = "";
        this.sheet.make_time = "";
        this.sheet.sheetRows = [];
        this.sheet.buy_amount = "";
        this.sheet.cost_amount_avg = "";
        this.sheet.wholesale_amount = "";
      });
    },
    closePage(flag) {
      this.popupAddStoreStockSheetRow = false;
      this.$store.commit("multiSelectOpenFlag", false);
      this.$store.commit("shoppingCarFinish", false);
      this.$store.commit("shoppingCarObj", {
        sheetType: this.moveType,
        clearFlag: true,
      });
    },
    closeSingleChoicePage() {
      this.$store.commit("multiSelectOpenFlag", false);
      this.$store.commit("shoppingCarFinish", false);
      this.popupAddStoreStockSheetRow = false;
      this.$forceUpdate();
    },
    handleSheetRowsSort() {
      // let tempSheetRow = JSON.parse(JSON.stringify(this.sheet.sheetRows))
      this.sheet.sheetRows = this.sheet.sheetRows.sort((a, b) =>
        a.item_name.localeCompare(b.item_name, "zh")
      ); //a~z 排序
      // this.sheet.sheetRows = []
      //this.sheet.sheetRows = tempSheetRow
      this.$forceUpdate();
    },
    handleSheetRowsForLoad() {
      if (this.sheet.sheetRows.length > 0) {
        
        // 处理打开单据的时候,盘盈盘亏信息
        this.sheet.sheetRows.forEach((sheetRow) => {
          
          sheetRow.real_quantity =
            (sheetRow.b_unit_qty || 0) * (sheetRow.b_unit_factor || 0) +
            (sheetRow.m_unit_qty || 0) * (sheetRow.m_unit_factor || 0) +
            (sheetRow.s_unit_qty || 0) * 1;
          sheetRow.buy_quantity = 
            (sheetRow.b_unit_qty || 0) * (sheetRow.b_unit_factor || 0) +
            (sheetRow.m_unit_qty || 0) * (sheetRow.m_unit_factor || 0) +
            (sheetRow.s_unit_qty || 0) * 1;
          sheetRow.client_stock_quantity =
          (sheetRow.b_unit_qty_storestock || 0) * (sheetRow.b_unit_factor_storestock || 0) +
          (sheetRow.m_unit_qty_storestock || 0) * (sheetRow.m_unit_factor_storestock || 0) +
          (sheetRow.s_unit_qty_storestock || 0) * 1; 
          const difference_qty = sheetRow.real_quantity - sheetRow.stock_qty;
          sheetRow.difference_qty = difference_qty;
          let cost_price = sheetRow.buy_price;
          let costPriceType = sheetRow.cost_price_type;
          if (costPriceType === "2") {
            cost_price = sheetRow.cost_price_avg;
          }
          if (difference_qty > 0) {
            let profit_wholesale_amount = toMoney(
              difference_qty * sheetRow.wholesale_price
            );
            let profit_cost_amount = toMoney(difference_qty * cost_price);
            let profit_buy_amount = toMoney(
              difference_qty * sheetRow.buy_price
            );
            sheetRow.profit_wholesale_amount = profit_wholesale_amount;
            sheetRow.profit_cost_amount = profit_cost_amount;
            sheetRow.profit_buy_amount = profit_buy_amount;
            sheetRow.loss_qty = "";
            sheetRow.loss_wholesale_amount = "";
            sheetRow.loss_cost_amount = "";
            sheetRow.loss_buy_amount = "";
            sheetRow.b_loss_qty = 0;
            sheetRow.m_loss_qty = 0;
            sheetRow.s_loss_qty = 0;
            const res = this.getQtyUnit(
              difference_qty,
              sheetRow.b_unit_no,
              sheetRow.b_unit_factor,
              sheetRow.m_unit_no,
              sheetRow.m_unit_factor,
              sheetRow.s_unit_no
            );
            sheetRow.b_profit_qty = res.b_qty;
            sheetRow.m_profit_qty = res.m_qty;
            sheetRow.s_profit_qty = res.s_qty;
            sheetRow.profit_qty = res.qtyUnit;
          } else if (difference_qty < 0) {
            let loss_wholesale_amount = toMoney(
              difference_qty * sheetRow.wholesale_price
            );
            let loss_cost_amount = toMoney(difference_qty * cost_price);
            let loss_buy_amount = toMoney(difference_qty * sheetRow.buy_price);
            sheetRow.loss_wholesale_amount = loss_wholesale_amount;
            sheetRow.loss_cost_amount = loss_cost_amount;
            sheetRow.loss_buy_amount = loss_buy_amount;
            sheetRow.profit_qty = "";
            sheetRow.profit_wholesale_amount = "";
            sheetRow.profit_cost_amount = "";
            sheetRow.profit_buy_amount = "";
            sheetRow.b_profit_qty = 0;
            sheetRow.m_profit_qty = 0;
            sheetRow.s_profit_qty = 0;
            var res = this.getQtyUnit(
              difference_qty,
              sheetRow.b_unit_no,
              sheetRow.b_unit_factor,
              sheetRow.m_unit_no,
              sheetRow.m_unit_factor,
              sheetRow.s_unit_no
            );
            sheetRow.b_loss_qty = res.b_qty;
            sheetRow.m_loss_qty = res.m_qty;
            sheetRow.s_loss_qty = res.s_qty;
            sheetRow.loss_qty = res.qtyUnit;
          } else if (difference_qty === 0) {
            sheetRow.profit_qty = "";
            sheetRow.profit_wholesale_amount = "";
            sheetRow.profit_cost_amount = "";
            sheetRow.profit_buy_amount = "";
            sheetRow.loss_qty = "";
            sheetRow.loss_wholesale_amount = "";
            sheetRow.loss_cost_amount = "";
            sheetRow.loss_buy_amount = "";
            sheetRow.b_profit_qty = 0;
            sheetRow.m_profit_qty = 0;
            sheetRow.s_profit_qty = 0;
            sheetRow.b_loss_qty = 0;
            sheetRow.m_loss_qty = 0;
            sheetRow.s_loss_qty = 0;
          }
        });
      }
    },
    async handleRefreshGetStockQtyList() {
      let items_id = [];
      let message = "";
      this.sheet.sheetRows.forEach((sheetRow) => {
        items_id.push(sheetRow.item_id);
      });
      let params = {
        branch_id: this.sheet.branch_id,
        items_id: items_id.join(","),
        stockOnly: false,
        operKey: this.$store.state.operKey,
      };
      /*await AppSheetStoreStockGetStockQtyList(params, message).then(res => {
         if (res.result === "OK") {
           res.records.forEach(queryRow => {
             for (let i = 0; i < this.sheet.sheetRows.length; i++) {
               let sheetRow = this.sheet.sheetRows[i]
               if (queryRow.item_id === sheetRow.item_id && (sheetRow.stock_qty || 0) !== (queryRow.stock_qty||0)) {
                 this.sheet.sheetRows[i].current_qty = queryRow.current_qty;
                 this.sheet.sheetRows[i].stock_qty = queryRow.stock_qty;
                 this.sheet.sheetRows[i].sys_quantity= queryRow.stock_qty;
                 this.updateQueryItem(this.sheet.sheetRows[i])
                 message += this.sheet.sheetRows[i].item_name + ";"
               }
             }
           })
           console.log(message)
         }
       })
       .catch(err => {
         console.log(err);
         message=err
       })*/
      var res = await AppSheetStoreStockGetStockQtyList(params, message);
      //计算盈亏
      
      if (res.result === "OK") {
        // 到这里 b_unit_factor是undefined
        console.log('this.sheet.sheetRows',this.sheet.sheetRows);
        console.log('res.records',res.records);
        for (let j = 0; j < res.records.length; j++) {
          let queryRow = res.records[j];
          // let batch_level = queryRow.batch_level
          let produceDate = queryRow.produce_date?  queryRow.produce_date.slice(0, 10):""
          for (let i = 0; i < this.sheet.sheetRows.length; i++) {
            let sheetRow = this.sheet.sheetRows[i];
            if (
              queryRow.item_id === sheetRow.item_id &&
              sheetRow.produce_date === produceDate &&
                sheetRow.batch_no === queryRow.batch_no &&
                sheetRow.branch_position === queryRow.branch_position
            ) {
              if (
                (sheetRow.stock_qty.toString() || 0) !==
                (queryRow.stock_qty.toString() || 0)
              ) {
                this.sheet.sheetRows[i].current_qty = queryRow.current_qty;
                this.sheet.sheetRows[i].stock_qty = queryRow.stock_qty;
                this.sheet.sheetRows[i].sys_quantity = queryRow.stock_qty;
                this.updateQueryItem(this.sheet.sheetRows[i]);
                message += this.sheet.sheetRows[i].item_name + ";";
              }
            }
          }
          // if (batch_level !== "") {
          //   let produceDate = queryRow.produce_date === "" ? "" : queryRow.produce_date.slice(0, 10)
          //   for (let i = 0; i < this.sheet.sheetRows.length; i++) {
          //     let sheetRow = this.sheet.sheetRows[i]
          //     if (queryRow.item_id === sheetRow.item_id && (sheetRow.produce_date === produceDate && sheetRow.batch_no === queryRow.batch_no)) {
          //       console.log(queryRow)
          //       console.log(sheetRow)
          //       if ((sheetRow.stock_qty.toString() || 0) !== (queryRow.stock_qty.toString() || 0)) {
          //         this.sheet.sheetRows[i].current_qty = queryRow.current_qty;
          //         this.sheet.sheetRows[i].stock_qty = queryRow.stock_qty;
          //         this.sheet.sheetRows[i].sys_quantity = queryRow.stock_qty;
          //         this.updateQueryItem(this.sheet.sheetRows[i])
          //         message += this.sheet.sheetRows[i].item_name + ";"
          //       }
          //     }
          //   }
          // } else {
          //   for (let i = 0; i < this.sheet.sheetRows.length; i++) {
          //     let sheetRow = this.sheet.sheetRows[i]
          //     if (queryRow.item_id === sheetRow.item_id && (sheetRow.stock_qty || 0) !== (queryRow.stock_qty || 0)) {
          //       this.sheet.sheetRows[i].current_qty = queryRow.current_qty;
          //       this.sheet.sheetRows[i].stock_qty = queryRow.stock_qty;
          //       this.sheet.sheetRows[i].sys_quantity = queryRow.stock_qty;
          //       this.updateQueryItem(this.sheet.sheetRows[i])
          //       message += this.sheet.sheetRows[i].item_name + ";"
          //     }
          //   }
          // }
        }
      } else {
        return res.msg;
      }

      return message === "" ? "" : (message += "等库存已变化，请重新检查提交");
    },
    handleImage(itemImages) {
      let obj = {
        main: "",
        tiny: "",
        other: []
      }
      if (itemImages) {
        obj = JSON.parse(itemImages)
        obj.main = obj.main ? globalVars.obs_server_uri + '/' + obj.main : ''
        obj.tiny = obj.tiny ? globalVars.obs_server_uri + '/' + obj.tiny : ''
        for (let i = 0; i < obj.other.length; i++) {
          if (obj.other[i]) {
            obj.other[i] = globalVars.obs_server_uri + '/' + obj.other[i]
          }
        }
        obj.other.unshift(obj.main)
      }
      return obj
    },
    updateQueryItem(sheetRow) {
      let costPriceType = "";
      const setting = this.$store.state.operInfo.setting;
      if (setting && setting.costPriceType) {
        costPriceType = setting.costPriceType;
      }
      const difference_qty = sheetRow.real_quantity - sheetRow.stock_qty;
      sheetRow.difference_qty = difference_qty;
      sheetRow.sys_quantity = sheetRow.stock_qty;
      sheetRow.stockQty = sheetRow.stock_qty;
      let cost_price = sheetRow.buy_price;
      if (costPriceType === "2") {
        cost_price = sheetRow.cost_price_avg;
      }
      if (difference_qty > 0) {
        let profit_wholesale_amount = toMoney(
          difference_qty * sheetRow.wholesale_price
        );
        let profit_cost_amount = toMoney(difference_qty * cost_price);
        let profit_buy_amount = toMoney(difference_qty * sheetRow.buy_price);
        sheetRow.profit_wholesale_amount = profit_wholesale_amount;
        sheetRow.profit_cost_amount = profit_cost_amount;
        sheetRow.profit_buy_amount = profit_buy_amount;
        sheetRow.loss_qty = "";
        sheetRow.loss_wholesale_amount = "";
        sheetRow.loss_cost_amount = "";
        sheetRow.loss_buy_amount = "";
        sheetRow.b_loss_qty = 0;
        sheetRow.m_loss_qty = 0;
        sheetRow.s_loss_qty = 0;
        const res = getQtyUnit(
          difference_qty,
          sheetRow.b_unit_no,
          sheetRow.b_unit_factor,
          sheetRow.m_unit_no,
          sheetRow.m_unit_factor,
          sheetRow.s_unit_no
        );
        sheetRow.b_profit_qty = res.b_qty;
        sheetRow.m_profit_qty = res.m_qty;
        sheetRow.s_profit_qty = res.s_qty;
        sheetRow.profit_qty = res.qtyUnit;
        sheetRow.add_qty = res.qtyUnit;
      } else if (difference_qty < 0) {
        let loss_wholesale_amount = toMoney(
          difference_qty * sheetRow.wholesale_price
        );
        let loss_cost_amount = toMoney(difference_qty * cost_price);
        let loss_buy_amount = toMoney(difference_qty * sheetRow.buy_price);
        sheetRow.loss_wholesale_amount = loss_wholesale_amount;
        sheetRow.loss_cost_amount = loss_cost_amount;
        sheetRow.loss_buy_amount = loss_buy_amount;
        sheetRow.profit_qty = "";
        sheetRow.profit_wholesale_amount = "";
        sheetRow.profit_cost_amount = "";
        sheetRow.profit_buy_amount = "";
        sheetRow.b_profit_qty = 0;
        sheetRow.m_profit_qty = 0;
        sheetRow.s_profit_qty = 0;
        var res = getQtyUnit(
          difference_qty,
          sheetRow.b_unit_no,
          sheetRow.b_unit_factor,
          sheetRow.m_unit_no,
          sheetRow.m_unit_factor,
          sheetRow.s_unit_no
        );
        sheetRow.b_loss_qty = res.b_qty;
        sheetRow.m_loss_qty = res.m_qty;
        sheetRow.s_loss_qty = res.s_qty;
        sheetRow.loss_qty = res.qtyUnit;
        sheetRow.add_qty = res.qtyUnit;
      } else if (difference_qty === 0) {
        sheetRow.profit_qty = "";
        sheetRow.profit_wholesale_amount = "";
        sheetRow.profit_cost_amount = "";
        sheetRow.profit_buy_amount = "";
        sheetRow.loss_qty = "";
        sheetRow.loss_wholesale_amount = "";
        sheetRow.loss_cost_amount = "";
        sheetRow.loss_buy_amount = "";
        sheetRow.b_profit_qty = 0;
        sheetRow.m_profit_qty = 0;
        sheetRow.s_profit_qty = 0;
        sheetRow.b_loss_qty = 0;
        sheetRow.m_loss_qty = 0;
        sheetRow.s_loss_qty = 0;
        sheetRow.add_qty = "";
      }
      function getQtyUnit(
        qty,
        b_unit_no,
        b_unit_factor,
        m_unit_no,
        m_unit_factor,
        s_unit_no
      ) {
        let b_qty = 0,
          m_qty = 0,
          s_qty = 0;
        let leftQty = qty;
        let unitsQty = "";
        let absLeftQty = Math.abs(leftQty);
        let flag = leftQty < 0 ? -1 : 1;
        if (b_unit_factor) {
          b_qty = parseInt(absLeftQty / b_unit_factor);
          absLeftQty = absLeftQty % b_unit_factor;
          if (b_qty < 0.001) {
            b_qty = 0;
          }
          if (b_qty > 0) {
            b_qty *= flag;
            unitsQty += toMoney(b_qty) + b_unit_no;
          }
        }
        if (m_unit_factor) {
          m_qty = parseInt(absLeftQty / m_unit_factor);
          absLeftQty = absLeftQty % m_unit_factor;
          if (m_qty < 0.001) {
            m_qty = 0;
          }
          if (m_qty > 0) {
            m_qty *= flag;
            unitsQty += toMoney(m_qty) + m_unit_no;
          }
        }
        s_qty = absLeftQty;
        if (s_qty < 0.001) {
          s_qty = 0;
        }

        if (s_qty > 0) {
          s_qty *= flag;
          unitsQty += toMoney(s_qty) + s_unit_no;
        }
        return {
          qtyUnit: unitsQty,
          b_qty: b_qty,
          m_qty: m_qty,
          s_qty: s_qty,
        };
      }
    },
  },
};
</script>

<style lang="less" scoped>
@flex_w: {
  display: flex;
  flex-wrap: wrap;
}

;

@flex_a_j: {
  display: flex;
  align-items: center;
  justify-content: center;
}

;

@flex_a_bw: {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

;

.wrapper {
  width: 100%;
  height: 100%;
  box-sizing: border-box;
  background-color: #fff;
  display: flex;
  flex-direction: column;

  .content {
    width: 100%;
    box-sizing: border-box;
    background-color: #fff;
    overflow-y: auto;
    display: flex;
    flex-direction: column;
    // padding: 10px 10px 60px 10px;

    .content-query {
      box-sizing: border-box;
      width: 100%;
      display: flex;
      flex-direction: column;
      margin-bottom: 10px;
      // padding: 10px 15px 3px 15px;

      .content-query-item {
        height: 25px;
        line-height: 25px;
        display: flex;

        input {
          height: 100%;
          width: 100%;
          // padding: 0 10px 4px 0px;
          border: none;
          font-size: 15px;
          line-height: 35px;
          color: #333333;
          vertical-align: top;
          border-bottom: 1px solid #eee;
          text-align: right;
        }

        .van-icon {
          width: 30px;
          text-align: center;
          font-size: 22px;
          display: flex;
          align-items: center;
          justify-content: center;
          color: #aaa;
          background-color: #ffffff;
        }
      }
    }

    .public_query_title {
      background: #ffffff;
    }

    .public_query_title_t {
      height: 25px;
      line-height: 25px;
      font-size: 15px;
      color: #000000;
      // padding: 10px 10px 0;
      @flex_a_bw();
    }
  }

  .lowItem {
    width: 300px;
    height: auto;
    overflow: hidden;
    padding: 10px;

    h4 {
      height: 40px;
      font-size: 16px;
      font-weight: 500;
      color: #333333;
      @flex_a_j();
    }

    .lowItem_ul {
      height: auto;
      overflow: hidden;
      margin-bottom: 10px;

      li {
        height: auto;
        overflow: hidden;
        padding: 10px;
        font-size: 14px;
        @flex_a_bw();
        border-bottom: 1px solid #f2f2f2;
        .btn-delete{
          max-width: 70px; 
          line-height: 40px;
        }
      }

      li:last-child {
        border-bottom: none;
      }
    }
  }

  .footer {
    width: 100%;
    height: 50PX;
    position: absolute;
    bottom: 0;
    box-shadow: 0 1PX 5PX rgba(100, 100, 100, 0.2);
    background-color: #fff;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 10PX;
    box-sizing: border-box;

    .footer_input {
      // display: flex;
      // align-items: center;
      // justify-content: center;
      // width: 190px;
      // height: 50px;
      width: 190PX;
      font-size: 14PX;
      border-style: none;
      border-bottom: 1PX solid #eee;
      // background-color: #eee;
      height: 35PX;
      // padding-left: 3px;
    }

    .footer_iconBt {
      display: flex;
      // align-items: center;
      justify-content: center;
      background-color: transparent;
      border: none;
      // margin-left: 20px;
      align-items: end;
      text-align: right;
      width: 50PX;
      height: 50PX;
    }
  }

  .van_popup {
    height: 100%;
    overflow: hidden;
  }
  .print-barcode{
    font-size: 12px;
  }
  .other_operate {
    width: 100%;
    height: auto;

    .other_operate_content {
      height: 40px;
      vertical-align: top;
      margin-bottom: 15px;
      display: flex; /* 使用 flexbox 布局 */
      justify-content: space-between; /* 在元素之间平均分布空间 */
      align-items: center; /* 垂直方向对齐 */
      // @flex_a_j();

      button {
        width: 100px;
        height: 100%;
        vertical-align: top;
        margin: 0 15px;
        white-space: nowrap;
      }
    }
  }
  .custom-dialog-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.7);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 9999;
}

.custom-dialog {
  background:white;
  padding: 20px;
  width: 60%;
  border-radius: 14px;
  text-align: center;
}
  .public_query_wrapper {
      @flex_a_bw();

      // margin-top: 5px;
      .public_query_titleSrc_item {
        width: 50%;
        height: 100%;
        overflow: hidden;
        position: relative;
        vertical-align: top;
        border: none;
        padding: 10px 0 3px 0;

        div {
          height: 100%;
          width: calc(100% - 40px);
          padding: 0 30px 0 10px;
          border: none;
          font-size: 15px;
          line-height: 35px;
          color: #333333;
          text-align: left;
        }

        input,
        .selectOne {
          height: 100%;
          padding: 0 10px 4px 0px;
          border: none;
          font-size: 15px;
          line-height: 35px;
          color: #333333;
          vertical-align: top;
          border-bottom: 1px solid #eee;
          text-align: right;
        }

        .van-icon {
          position: absolute;
          left: 5px;
          top: 0;
          bottom: -10px;
          width: 30px;
          text-align: center;
          font-size: 22px;
          @flex_a_j();
          color: #aaa;
          background-color: #ffffff;
        }

        .query_icon {
          width: 22px;
          height: 22px;
          margin-left: 10px;
        }
      }
    }
}</style>
