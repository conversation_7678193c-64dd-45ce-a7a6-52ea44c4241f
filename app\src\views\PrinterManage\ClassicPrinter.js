import BluetoothPermissionHelper from './BluetoothPermissionHelper'

class ClassicPrinter{
  constructor(){
    this.unpairDeviceNameDic=[],
    this.unpairedDeviceList=[]
    this.permissionHelper = new BluetoothPermissionHelper()
  }
open_bluetooth() {
const promise= new Promise((resolve,reject)=>{
  bluetoothSerial.enable(
      //开启成功
    ()=> {resolve({ ret:1 })},
    //开启失败
    ()=> {reject({ ret:0})}
  )
})
return promise
}
discoverUnpairedDevices(){
  const promise=new Promise((resolve,reject)=>{
    // setTimeout(() => {
      console.log("搜索未配对设备")
      bluetoothSerial.discoverUnpaired((unpairedDeviceList)=>{
        // this.unpairedDeviceList=unpairedDeviceList
        // this.unpairDeviceNameDic=this.unpairedDeviceList.map(unpairedDevice=>{return unpairedDevice.name})
        // unpairedDeviceList.forEach(unpairedDevice=>{
        //   if (unpairedDevice.name&&this.unpairDeviceNameDic.indexOf(unpairedDevice.name)===-1) {
        //     unpairedDevice.type='classic'
        //     this.unpairedDeviceList.push(unpairedDevice)
        //     }
        // })
        unpairedDeviceList=unpairedDeviceList.filter(device=>device.name)
        unpairedDeviceList.map(device=>{
          device.type='classic'
        })
        resolve(unpairedDeviceList)
      })  
    // }, 3000);
  })
  return promise
}
deviceFilter(devices){
  const hideDeviceNameList=['Printer_EEB5','XP-P323B-93CD']
  //打印机定制化 class 列表
  //7936 QSPrinter 76mm针式打印机
  const specialDeviceClassList=[7936]
  let newDevices=[]
  // const newDevices=devices.filter(device=>
  //   device.name&&((Number(device.class)>=1536&&Number(device.class)<=1792)||specialDeviceClassList.indexOf(devices.class)!==-1)
  //   &&hideDeviceNameList.indexOf(device.name)===-1)
  devices.forEach(device => {
    if (device.name&&
      ((Number(device.class)>=1536&&Number(device.class)<=1792)||specialDeviceClassList.indexOf(devices.class)!==-1)
      &&hideDeviceNameList.indexOf(device.name)===-1 ) {
        newDevices.push(device)
    }
  });
return newDevices
}
async list(){
  const promise=new Promise((resolve,reject)=>{
    bluetoothSerial.list(devices=>{
          console.log("before devices:",devices)
          devices=this.deviceFilter(devices)
          console.log("after devices:",devices)

          resolve(devices)
      },(error)=>{
          // 使用权限助手处理错误
          if (this.permissionHelper.isPermissionError(error)) {
              console.log("经典蓝牙列表获取权限错误，返回空数组")
              resolve([]) // 权限错误时返回空数组而不是拒绝
          } else {
              reject(error)
          }
      })
  })
  return promise
}
}
export default ClassicPrinter