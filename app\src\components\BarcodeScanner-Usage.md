# BarcodeScanner 全局扫码服务使用说明

## 概述

`BarcodeScanner` 是一个全局扫码服务类，参考 `Position` 定位功能的设计模式，提供统一的扫码接口，支持参数化配置。

## 特性

- 🎯 **统一接口**：所有页面使用相同的扫码接口
- 🔧 **参数化配置**：支持灵活的格式配置，包括二维码开关
- 📱 **跨平台支持**：自动适配iOS和Android平台
- 🐛 **调试友好**：Web端支持模拟扫码结果
- 🔄 **向下兼容**：不影响现有代码

## 基本使用

### 1. 使用默认配置（推荐）

```javascript
// 使用默认配置，支持EAN13和Code128条码
async btnScanBarcode_click() {
  try {
    const result = await this.scanBarcodeNew()
    if (result.code) {
      console.log('扫码结果:', result.code)
      console.log('扫码格式:', result.format)
      // 处理扫码结果...
    }
  } catch (error) {
    console.error('扫码失败:', error)
  }
}
```

### 2. 启用二维码支持

```javascript
// 启用二维码支持
async btnScanBarcode_click() {
  const result = await this.scanBarcodeNew({
    barcodeFormats: {
      QRCode: true,   // 启用二维码
      EAN13: true,    // 启用EAN13
      Code128: true   // 启用Code128
    }
  })
  // 处理结果...
}
```

### 3. 完整参数配置

```javascript
async btnScanBarcode_click() {
  const result = await this.scanBarcodeNew({
    // 格式配置
    barcodeFormats: {
      QRCode: true,      // 启用二维码（默认false）
      EAN13: true,       // 启用EAN13（默认true）
      Code128: true,     // 启用Code128（默认true）
      EAN8: true,        // 启用EAN8（默认false）
      ITF: true,         // 启用ITF（默认false）
      DataMatrix: false  // 禁用DataMatrix
    },
    // 其他配置
    beepOnSuccess: true,      // 扫码成功蜂鸣
    vibrateOnSuccess: false,  // 扫码成功震动
    detectorSize: 0.6,        // 检测区域大小
    rotateCamera: false,      // 是否旋转摄像头
    unit_type: 'product'      // 业务类型标识
  })
  // 处理结果...
}
```

## 常用配置场景

### 场景1：默认商品条码扫描
```javascript
// 使用默认配置，只支持EAN13和Code128
const result = await this.scanBarcodeNew({
  unit_type: 'product'
})
```

### 场景2：商品档案页面（支持二维码）
```javascript
const result = await this.scanBarcodeNew({
  barcodeFormats: {
    QRCode: true,    // 启用二维码支持
    EAN13: true,     // 支持商品条码
    EAN8: true,
    Code128: true
  },
  unit_type: 'product'
})
```

### 场景3：更多条码格式支持
```javascript
const result = await this.scanBarcodeNew({
  barcodeFormats: {
    EAN13: true,
    EAN8: true,
    ITF: true,
    Code128: true,
    Code39: true     // 启用Code39
  },
  unit_type: 'sheet'
})
```

### 场景4：二维码专用扫描
```javascript
const result = await this.scanBarcodeNew({
  barcodeFormats: {
    QRCode: true,    // 只支持二维码
    EAN13: false,
    EAN8: false,
    Code128: false,
    // 其他格式都设为false...
  }
})
```

## 迁移指南

### 从旧的 scanBarcode 迁移

**旧代码：**
```javascript
async btnScanBarcode_click() {
  const result = await this.scanBarcode()
  // 处理结果...
}
```

**新代码：**
```javascript
async btnScanBarcode_click() {
  // 默认支持EAN13和Code128，需要二维码时显式启用
  const result = await this.scanBarcodeNew({
    barcodeFormats: {
      QRCode: true  // 需要时启用二维码支持
    }
  })
  // 处理结果...
}
```

### 从页面内 getScanBarResult 迁移

**旧代码：**
```javascript
async btnScanBarcode_click() {
  const result = await this.getScanBarResult()
  // 处理结果...
}
```

**新代码：**
```javascript
async btnScanBarcode_click() {
  const result = await this.scanBarcodeNew()
  // 处理结果...
}
```

## Web端调试

### 设置调试扫码结果
```javascript
// 在浏览器控制台中设置调试结果
BarcodeScanner.setDebugResult('6901028075831', 'EAN_13')  // 商品条码
BarcodeScanner.setDebugResult('https://www.example.com', 'QR_CODE')  // 二维码

// 清除调试结果
BarcodeScanner.clearDebugResult()
```

## 支持的格式

| 格式 | 说明 | 默认启用 |
|------|------|----------|
| EAN13 | 13位商品条码 | ✅ |
| Code128 | Code128条码 | ✅ |
| QRCode | 二维码 | ❌ |
| EAN8 | 8位商品条码 | ❌ |
| Code39 | Code39条码 | ❌ |
| Code93 | Code93条码 | ❌ |
| ITF | ITF条码 | ❌ |
| DataMatrix | 数据矩阵码 | ❌ |
| PDF417 | PDF417码 | ❌ |
| Aztec | Aztec码 | ❌ |
| UPCA | UPC-A条码 | ❌ |
| UPCE | UPC-E条码 | ❌ |
| CodaBar | CodaBar条码 | ❌ |

## 注意事项

1. **向下兼容**：原有的 `this.scanBarcode()` 方法仍然可用
2. **默认格式**：新服务默认只支持EAN13和Code128，需要其他格式时显式启用
3. **Web端调试**：在浏览器中会自动模拟扫码结果
4. **错误处理**：建议使用 try-catch 处理扫码异常

## 按需启用二维码

如果某些页面需要二维码支持，有两种方式：

### 方式1：使用新服务（推荐）
在需要二维码的页面使用 `this.scanBarcodeNew()` 并启用QRCode

```javascript
const result = await this.scanBarcodeNew({
  barcodeFormats: {
    QRCode: true  // 启用二维码
  }
})
```

### 方式2：修改旧服务
修改 `App.vue` 中的 `scanBarcode` 方法，将 `QRCode: false` 改为 `QRCode: true`
