ext.postBuildExtras = {
    android {
        compileOptions {
            sourceCompatibility JavaVersion.VERSION_1_8
            targetCompatibility JavaVersion.VERSION_1_8
        }
        allprojects {
            compileOptions {
                sourceCompatibility = JavaVersion.VERSION_1_8
                targetCompatibility = JavaVersion.VERSION_1_8
            }
        }
    }

    // 只排除旧的 Vision API，保留 ML Kit 需要的依赖
    configurations.all {
        exclude group: 'com.google.android.gms', module: 'play-services-vision'
        exclude group: 'com.google.android.gms', module: 'play-services-vision-common'
        // 不再排除 play-services-base 和 play-services-basement，因为 ML Kit 需要它们
         
        resolutionStrategy {
            force 'org.jetbrains.kotlin:kotlin-stdlib:1.8.22'
            eachDependency { details ->
                if (details.requested.group == 'org.jetbrains.kotlin') {
                    details.useVersion '1.8.22'
                }
            }
            force 'androidx.camera:camera-core:1.3.1'
            force 'androidx.camera:camera-camera2:1.3.1'
            force 'androidx.camera:camera-lifecycle:1.3.1'
            force 'androidx.camera:camera-view:1.3.1'
        }
        // 排除旧版本的 JDK 扩展
        exclude group: 'org.jetbrains.kotlin', module: 'kotlin-stdlib-jdk7'
        exclude group: 'org.jetbrains.kotlin', module: 'kotlin-stdlib-jdk8'
    }
}
