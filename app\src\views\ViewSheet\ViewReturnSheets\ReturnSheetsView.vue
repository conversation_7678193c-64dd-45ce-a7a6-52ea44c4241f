<template>
  <div class="sheet-wrapper">
    <div class="sheet-container">
      <div class="sheet-nar-bar" v-if="isPage">
        <van-nav-bar title="查退货单" left-arrow @click-left="myGoBack($router)" :placeholder="true">
          <template #right>
            <van-icon name="apps-o" @click="handleOriginShowFlag"/>
          </template>
        </van-nav-bar>
      </div>
      <div class="sheet-content" ref="sheetContentRef" id="ReturnSheetsViewSheetContentId">
        <van-popup
          v-model="queryInfoWrapperSimpleShowFlag"
          position="top"
          :overlay="false"
          :style="{ width: '100vw','margin-top': isPage ? '46px' : '0px', left: popupLeft + 'px' }">
          <div class="sheet-query-popup">
            <ReturnSheetsViewParams
              :isMountedQuery="true"
              :component-role="'simple'"
              :queryParams.sync="queryParams"
              @handleFinishSelect="handleFinishSelect"
              @handleClearItem="handleClearItem"
              @handleOriginShowFlag="handleOriginShowFlag"
              @handleClearAll="handleClearAll"/>
          </div>
          <div class="search_content" >
            <van-search id="txtSearch" v-model="querySearchStr" left-icon placeholder="单号/客户"
              @input="onSearchStrChange" @click="onSearchStrClick">
              <template #right-icon>
                <i class="iconfont">&#xe63c;</i>
              </template>
            </van-search>
          </div>
          <ConcaveDottedCenter />
        </van-popup>
        <div class="sheet-query-wrapper" >
          <!--占位高度使用-->
          <ReturnSheetsViewParams
            :component-role="'simple'"
            :isMountedQuery="false"
            :queryParams.sync="queryParams"
            @handleClearItem="handleClearItem"
            @handleFinishSelect="handleFinishSelect"
            @handleOriginShowFlag="handleOriginShowFlag"
            @handleClearAll="handleClearAll"/> 
          <van-search id="txtSearch" :style="{ opacity: 0, pointerEvents: 'none' }" v-model="querySearchStr" left-icon placeholder="单号/客户" 
            @input="onSearchStrChange" @click="onSearchStrClick">
            <template #right-icon>
              <i class="iconfont">&#xe63c;</i>
            </template>
          </van-search>
          <ConcaveDottedCenter />
        </div>
<!--        <van-sticky :offset-top="46" >-->
<!--          <div class="sheet-list-tip">-->
<!--            <div class="list-tip-item">客户</div>-->
<!--            <div class="list-tip-item">单号</div>-->
<!--            <div class="list-tip-item">入库</div>-->
<!--            <div class="list-tip-item">时间</div>-->
<!--            <div class="list-tip-item">额度</div>-->
<!--          </div>-->
<!--        </van-sticky>-->
        <div class="sheet-list-content">
          <van-pull-refresh v-model="refreshing" @refresh="onRefresh">
            <van-list
              v-model="loading"
              :finished="finished"
              finished-text="没有更多了"
              :immediate-check="false"
              @load="onLoad"
            >
              <ul class="list-item-wrapper">
                <li v-for="(item, index) in list" :key="index" @click="onSheetClick(item)">
                  <div class="item-sheet-no">{{ item.sheet_no }}</div>
                  <div class="item-sheet-row" style="display:flex;padding-top:5px;">
                      <div style="width:65%;text-align:left;">{{ item.supcust_id == '0' ? '散户' : item.sup_name  }}</div>
                      <div class="item-sheet-info">
                        <!-- <span v-if="item.state === 'approved'" style="">已审</span>-->
                        <span v-if="item.state === 'unapproved'" style="color: #ff976a">未审</span>
                        <span v-if="item.state === 'red'" style="color: #ee0a24">红冲</span>
                        <span v-if="item.state === 'reded'" style="color: #999999">被红冲</span>
                      </div>
                      <div style="width:35%;text-align:right;">
                        <div><span class="item-sheet-money" style="margin-right:3px;">￥</span>{{ item.real_pay_amount }}</div>
                        <div class="item-sheet-amount" v-if="item.now_disc_amount!=0||item.prepay_amount!=0||item.left_amount!=0">
                          <span>={{item.total_amount}}</span>
                          <span v-if="item.now_disc_amount!=0">-惠{{item.now_disc_amount}}</span>
                          <span v-if="item.prepay_amount!=0">-预{{item.prepay_amount}}</span>
                          <span v-if="item.left_amount!=0">-欠{{item.left_amount}}</span>
                        </div>
                      </div>

                    </div>
                  <div class="item-sheet-detail" style="display:flex;color:#999;">
                      <div style="width:45%;text-align:left;">
                        <div> {{ getShortTime(item.happen_time)}} {{ item.oper_name }}</div>
                        <div v-if="item.oper_name!=item.maker_name">制单:{{item.maker_name}}</div>
                       
                      </div>
                      <div style="width:10%;text-align:left;">
                  <span class="item-sheet-print" style="display:flex;" v-if="parseInt(item.print_count)">
                    <svg fill="#d88" style="margin-top:1px;">
                      <use xlink:href="#icon-printer"></use>
                    </svg>
                    <div style="margin-top:2px;color:#d88;">{{item.print_count}}</div>
                  </span>
                      </div>
                      <div class="need-notice" style="width:15%;color:#f77;">
                        <!-- <span v-if="item.visit_id">访</span> -->
                        <span v-if="item.has_return == 'true'">退</span>
                        <span v-if="item.cl == 'true'">陈</span>
                        <span v-if="item.dh == 'true'">定</span>
                        <span v-if="item.has_free == 'true'">赠</span>
                        <span v-if="item.has_arrears == 'true'">欠</span>
                        <span v-if="item.has_disc == 'true'">惠</span>
                        <span v-if="item.bj === 'true'">变</span>
                        <span v-if="item.j === 'true'">借</span>
                        <span v-if="item.h === 'true'">还</span>
                        <span v-if="item.hc === 'true'">换出</span>
                        <span v-if="item.hr === 'true'">换入</span>
                        <span v-if="item.ks === 'true'">客损</span>
                      </div>
                      <div style="width:30%;text-align:right;">{{ getQuantityText(item.total_quantity) }}</div>
                    </div>
                </li>
              </ul>
            </van-list>
          </van-pull-refresh>
        </div>
      </div>
      <div class="sheet-footer">
        <div class="sheet-list-basic">
          <div class="list-total-record">共 {{ total }} 条</div>
          <div class="list-total-amount">￥ {{ total_real_pay_amount }} 元</div>
        </div>
        <div class="sheet-list-detail" v-if="total_now_disc_amount || total_prepay_amount || total_left_amount !== 0">
        <div>={{total_total_amount}}</div>
        <div v-if="total_now_disc_amount !== 0">-惠{{total_now_disc_amount}}</div>
        <div v-if="total_prepay_amount">-预{{total_prepay_amount}}</div>
        <div v-if="total_left_amount">-欠{{total_left_amount}}</div>
      </div>
      </div>
    </div>
    <van-popup
      key="ReturnSheetsView"
      v-model="SaleOrderSheetsPopupShowFlag"
      position="bottom"
      get-container="body"
      @close="handlePopupClose"
      :style="{ width: '100%', height : '90%'}">
        <ReturnSheetsViewParams
          :isMountedQuery="false"
          :component-role="'original'"
          :queryParams.sync="queryParams"
          @handleClose="handleClose"
          @handleFinishSelect="handleFinishSelect"
          @handleClearAll="handleClearAll"
        />
    </van-popup>
  </div>
</template>

<script>
import {NavBar, Search, Icon, PullRefresh, List, Popup, Toast} from "vant";
import ReturnSheetsViewParams from "./ReturnSheetsViewParams.vue";
import {GetAllSaleSheets} from "../../../api/api";
import ConcaveDottedCenter from "../../components/ConcaveDottedCenter.vue";
export default {
  name: "ReturnSheetsView",
  components: {
    ReturnSheetsViewParams,
    ConcaveDottedCenter,
    "van-nav-bar": NavBar,
    "van-search": Search,
    "van-icon": Icon,
    "van-pull-refresh": PullRefresh,
    "van-list": List,
    "van-popup": Popup,
  },
  props: {
    isPage: {
      type: Boolean,
      default: true
    },
    popupLeft: {
      type: Number,
      default: 0
    },
    allQueryParams: {
      type: Object,
      default: () => {}
    }
  },
  computed: {
    operRegions() {
      let operRegions = this.$store.state.operInfo.operRegions;
      if (operRegions) {
        return JSON.stringify(operRegions)
      }
      return ''
    }
  },
  data() {
    return {
      list: [],
      loading: false,
      finished: false,
      refreshing: false,
      viewAllFlag: false,
      firstQueryData: true,
      querySearchStr:'',
      queryParams: {
        sheetType: 'T',
        pageSize: 50,
        startRow: 0,
        getTotal: true,
        regionsID: '',
        startDate: '',
        endDate: '',
        sellerInfo: [],
        senderInfo: [],
        customerInfo: [],
        branchInfo: [],
        // arrearInfo: [],
        approveStatus: [{ title: '所有', key: 'all' }],
        showRed: false,
        timeTypeInfo: this.$store.state.timeTypeInfo[0]?this.$store.state.timeTypeInfo:[{ title: '交易时间', id: 'happen_time' }]
        // timeTypeInfo: [{ title: '交易时间', id: 'happen_time' }]
      },
      queryInfoWrapperTop: 0, // 滚动相关
      queryInfoScroll: 0, // 滚动相关
      queryInfoWrapperSimpleShowFlag: true, // 筛选弹窗快捷
      SaleOrderSheetsPopupShowFlag: false,  // 全部筛选弹窗
      total: 0,
      amount: 0.00,
      total_total_amount: 0.00,
      total_real_pay_amount: 0.00,
      total_now_disc_amount: 0.00,
      total_prepay_amount: 0.00,
      total_left_amount: 0.00,
    };
  },
  mounted() {
    this.$refs.sheetContentRef.addEventListener('scroll', this.handleScroll)
    if (this.$route.query.viewAllFlag) {
      this.viewAllFlag = this.$route.query.viewAllFlag
    }
  },
  activated() {
    this.$nextTick(() => {
      setTimeout(() => {
        $("#ReturnSheetsViewSheetContentId").animate({ scrollTop: this.queryInfoScroll }, 0);
      }, 0)
    })

  },
  methods: {
    formatDate(date) {
      return `${date.getFullYear()}-${date.getMonth() + 1 < 10 ? '0' : '' }${date.getMonth() + 1}-${date.getDate() < 10 ? '0' : '' }${date.getDate()}`;
    },
    handleScroll() {
      this.queryInfoWrapperTop = this.$refs.sheetContentRef.scrollTop
      let scrollTemp = this.queryInfoWrapperTop - this.queryInfoScroll
      this.queryInfoScroll = this.queryInfoWrapperTop
      if (scrollTemp < 0) {
        this.queryInfoWrapperSimpleShowFlag = true
        return
      }
      if (this.queryInfoWrapperTop >= 50) {
        this.queryInfoWrapperSimpleShowFlag = false
      }
    },
    handleOriginShowFlag() {
      this.SaleOrderSheetsPopupShowFlag = true
    },
    handleClearItem(flag) {
      if (flag === 'sellerInfo') {
        this.queryParams.sellerInfo = []
      } else if (flag === 'senderInfo') {
        this.queryParams.senderInfo = []
      } else if (flag === 'customerInfo') {
        this.queryParams.customerInfo = []
      } else if (flag === 'branchInfo') {
        this.queryParams.branchInfo = []
      }
      // else if (flag === 'arrearInfo') {
      //   this.queryParams.arrearInfo = []
      // }
      else if (flag === 'approveStatus') {
        this.queryParams.approveStatus = [{ title: '所有', key: 'all' }]
      } else if (flag === 'showRed') {
        this.queryParams.showRed = false
      } else if (flag === 'timeTypeInfo') {
        this.queryParams.timeTypeInfo = [{ title: '交易时间', id: 'happen_time' }]
      }else if (flag === 'makeBrief') {
        this.queryParams.makeBrief = []
      } else if (flag === 'detailRemark') {
        this.queryParams.detailRemark = []
      } 
      this.handleFinishSelect()
    },
    handleClose() {
      this.SaleOrderSheetsPopupShowFlag = false
    },
    handleClearAll() {
      this.queryParams.sellerInfo = []
      this.queryParams.senderInfo = []
      this.queryParams.customerInfo = []
      this.queryParams.branchInfo = []
      // this.queryParams.arrearInfo = []
      this.queryParams.approveStatus = [{ title: '所有', key: 'all' }]
      this.queryParams.timeTypeInfo = [{ title: '交易时间', id: 'happen_time' }]
      this.queryParams.makeBrief = []
      this.queryParams.detailRemark = []
      this.queryParams.showRed = false
      this.handleFinishSelect()
    },
    handleFinishSelect() {
      this.handleClose()
      this.onRefresh()
    },
    async onLoad() {
      let params = {
        sheetType: this.queryParams.sheetType,
        pageSize: this.queryParams.pageSize,
        startRow: this.queryParams.startRow,
        getTotal: this.queryParams.getTotal,
        departID: '',
        regionsID: this.queryParams.regionsID,
        startDate: this.queryParams.startDate,
        endDate: this.queryParams.endDate,
        makeBrief: this.queryParams.makeBrief,
        detailRemark:this.queryParams.detailRemark,
        showRed: this.queryParams.showRed,
        operID: this.queryParams.sellerInfo.map(item => {return item.id}).join(","),
        senderID: this.queryParams.senderInfo.map(item => {return item.id}).join(","),
        supcustID: this.queryParams.customerInfo.map(item => {return item.ids}).join(","),
        branchID: this.queryParams.branchInfo.map(item => {return item.branch_id}).join(","),
        // arrears: this.queryParams.arrearInfo.map(item => {return item.ids}).join(","),
        approveStatus: this.queryParams.approveStatus.map(item => {return item.key}).join(","),
        timeType: this.queryParams.timeTypeInfo.map(item => {return item.id}).join(","),
        searchStr: this.querySearchStr,
      };
      var viewRange = window.getRightValue('delicacy.sheetViewRange.value')
      if( viewRange === 'self'){
        params.operID = this.$store.state.operInfo.oper_id;
      }else if(viewRange === "department"){
        params.departID = this.$store.state.operInfo.depart_id;
      } else {
        params.departID = ''
      }

       
      let notLimitViewRangeOnClient = window.getRightValue('delicacy.notLimitViewRangeOnClient.value')
      if(notLimitViewRangeOnClient == '' || notLimitViewRangeOnClient == 'true' ){
        params.notLimitViewRangeOnClient = true
      }else{
        params.notLimitViewRangeOnClient = false
      }
      
      if (this.viewAllFlag) { // 拜访快照进入(copy原始查单 -_- !!)
        params.operID = ''
        params.departID = ''
      }
      await GetAllSaleSheets(params).then((res) => {
        this.refreshing = false;
        if (res.result !== "OK") {
          Toast.fail(res.message);
          return
        }
        this.total = res.total;
        this.amount = toMoney(res.amount) || 0;
        this.total_real_pay_amount = toMoney(res.real_pay_amount) || 0
        this.total_now_disc_amount = toMoney(res.now_disc_amount) || 0
        this.total_total_amount = toMoney(res.total_amount) || 0
        this.total_prepay_amount = toMoney(res.prepay_amount) || 0
        this.total_left_amount = toMoney(res.left_amount) || 0
        res.data.map((item) => {
          this.list.push(item);
        });
        this.loading = false;
        this.queryParams.startRow = Number(this.queryParams.startRow) + this.queryParams.pageSize;
        if (this.list.length >= Number(res.total)) {
          this.finished = true;
        }
      }).catch(() => {
        Toast.fail('获取列表失败')
      })
    },
    onRefresh() {
      if (this.firstQueryData && this.allQueryParams) {
        Object.keys(this.allQueryParams).forEach((key) => {
          this.queryParams[key] = this.allQueryParams[key]
        })
        this.firstQueryData = false
      }
      // 清空列表数据
      this.finished = false;
      // 重新加载数据
      // 将 loading 设置为 true，表示处于加载状态
      this.loading = true;
      this.queryParams.startRow = 0;
      this.list = [];
      this.onLoad();
    },
    onSearchStrChange(value) {
      console.log(value); 
      var reg = new RegExp("'", "g");
      value = value.replace(reg, "");
      this.querySearchStr = value; 

      if (this.inputTimer) clearTimeout(this.inputTimer);
      this.inputTimer = 0;

      this.inputTimer = setTimeout(() => {
        this.inputTimer = 0;
        this.onRefresh();
      }, 500);
    },
    onSearchStrClick() {
      this.querySearchStr = ''; 
    },
    handlePopupClose() {
      this.handleFinishSelect()
    },
    onSheetClick(sheet) {
      window.g_curSheetInList = sheet;
      window.g_curSheetList = this.list;
      const routerObj = { path: "/SaleSheet", query: { sheetID: sheet.sheet_id, sheetType: this.queryParams.sheetType },}
      if (this.isPage) {
        this.$router.push(routerObj);
      } else {
        this.$emit("handleRoutePush", routerObj)
      }
    },
    getQuantityText(qty) {
      if (qty.indexOf("退") === -1) qty = qty.replace("销:", "")
      return qty
    },
  }
}
</script>

<style scoped lang="less">
.sheet-wrapper {
  width: 100vw;
  height: 100%;
  box-sizing: border-box;
  background-color: #fff;
  .sheet-container {
    width: 100%;
    height: 100%;
    box-sizing: border-box;
    display: flex;
    flex-direction: column;
    .sheet-nar-bar {
      width: 100%;
      height: 46px;
       .van-icon{
        font-size: 18px;
      }
    }
    .sheet-content {
      flex: 1;
      display: flex;
      flex-direction: column;
      box-sizing: border-box;
      overflow-y: auto;
      .sheet-query-popup {
      }
      .sheet-query-wrapper {
      }
      .van-search .van-search__content{
        background-color: transparent !important;
        border: none; 
        border-bottom: 1px solid #eeeeee;
      }
      .sheet-list-tip {
        padding: 0 10px;
        display: flex;
        width: 100%;
        height: 25px;
        background-color: #fff;
        box-sizing: border-box;
        justify-content: space-between;
        .list-tip-item {
          font-size: 14px;
          height: 25px;
        }
      }
      .sheet-list-content {
        flex: 1;
        padding: 0 10px;
        .list-item-wrapper {
          height: auto;
          overflow: hidden;
          background: #ffffff;
          li {
            height: auto;
            overflow: hidden;
            border-bottom: 1px solid #eeeeee;
            padding: 4px 0;
            box-sizing: border-box;
            display: flex;
            flex-direction: column;
            .item-sheet-row{
              min-height:30px;
            }
            .item-sheet-no {
              text-align: left;
              color: #AAAAAA;
            }
            .item-sheet-info{
              width:13%;
              font-size:13px;
            }
            .item-sheet-money {
              font-size: 15px;
              color: #dbdbdb;
            }
            .item-sheet-amount{
              font-size: 10px;
              text-align: right;
            }
            .item-sheet-detail{
              min-height:26px;
              .item-sheet-print{
                height:19px;
              }
              svg{
                width:24px; 
                height:18px;
              }
            }
          }
          li:last-child {
            border-bottom: none;
          }
        }
      }

    }
    .sheet-footer {
      width: 100%;
      min-height: 50px;
      box-sizing: border-box;
      border-top: 1px solid #ddd;
      display: flex;
      flex-direction: column;
      padding: 5px 10px;
      .sheet-list-basic {
        padding: 5px;
        font-size: 18px;
        width: 100%;
        display: flex;
        justify-content: space-between;
        .list-total-record {
          flex: 1;
          font-size: 18px;
          font-weight: bold;
          text-align: left;
        }
        .list-total-amount {
          flex: 1;
          font-size: 18px;
          font-weight: bold;
          text-align: right;
        }
      }
      .sheet-list-detail {
        width: 100%;
        display: flex;
        text-align: right;
        justify-content: flex-end;
        flex-wrap: wrap;
        align-items: center;
        div {
          height: 22px;
          line-height: 22px;
          padding: 0 2px;
        }

      }
    }
  }
}

</style>
