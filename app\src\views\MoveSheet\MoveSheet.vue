<template>
  <div ref="pages" class="pages">
    <van-nav-bar left-arrow safe-area-inset-top title="调拨单" @click-left="goback">
      <template #right>
        <div class="submitSalesSlip" :style="{ color: sheet.sheet_id ? '#cccccc' : '#000' }"
          @click="handlePopupFillFromSale">
          补货
        </div>
        <div class="submitSalesSlip" @click="btnSubmit_click">
          <svg style="margin:2px 2px 0 0" width="30px" height="30px" stroke-width="1.3" class="black">
            <use :xlink:href="'#icon-plane'"></use>
          </svg>
        </div>
      </template>
    </van-nav-bar>

    <div ref="public_box3" class="public_box3" :style="{ height: publicBox3Height }">
      <!-- <div class="public_header">
          <van-row gutter="10" v-if="sheet.sheet_no">
            <van-col span="12">
              <div class="public_header_l">
                <p>{{ sheet.sheet_no }}</p>
              </div>
            </van-col>
            <van-col span="12">
              <div class="public_header_r">
                <p>{{ sheet.make_time }}</p>
              </div>
            </van-col>
          </van-row>

          <van-row gutter="10">
            <van-col span="12">
              <van-field
                v-model="sheet.from_branch_name"
                label-width="40px"
                class="brand_style"
                placeholder="出仓"
                readonly
                @click="() => { if (!this.canEdit) return; popFromBranchList = true;}">
                <template #right-icon>
                  <i class="iconfont">&#xe6a2;</i>
                </template>
              </van-field>
            </van-col>
            <van-col span="12">
              <van-field
                v-model="sheet.to_branch_name"
                label-width="40px"
                class="brand_style"
                placeholder="入仓"
                readonly
                @click="() => { if (!this.canEdit) return; popToBranchList = true;}">
                <template #right-icon>
                  <i class="iconfont">&#xe66e;</i>
                </template>
              </van-field>
            </van-col>
          </van-row>

      </div> -->

      <div class="public_query">
        <div class="public_query_title" v-if="sheet.sheet_no" style="margin-bottom: 1px">
          <div class="public_query_title_t">
            <span>{{ sheet.sheet_no }}</span>
            <span>{{ sheet.approve_time }}</span>
          </div>
        </div>

        <WeChatShare ref="wechatshare" :sheet="sheet" @shareSheetSelected="shareSelected" />
        <div class="public_query_titleSrc" style="padding: 0px 15px 3px 15px;">
          <div class="public_query_wrapper">
            <div class="public_query_titleSrc_item" style="width:100%" @click="handleMoveSheetStoreMoveOut">
              <!-- <svg width="22px" height="22px" stroke-width="1.3" class="black">
                <use :xlink:href="'#icon-fromBranch'"></use>
              </svg> -->
              <div style="white-space: nowrap;">出仓</div>
              <input type="text" v-model="sheet.from_branch_name" placeholder="出仓" readonly />
            </div>
            <div class="public_query_titleSrc_item" style="width:100%" @click="handleMoveSheetStoreMoveIn">
              <div style="white-space: nowrap;">入仓</div>
              <!-- <svg width="22px" height="22px" stroke-width="1.3" class="black">
                <use :xlink:href="'#icon-toBranch'"></use>
              </svg> -->
              <input type="text" v-model="sheet.to_branch_name" placeholder="入仓" readonly />
            </div>
            <div v-if="canAppSelectSeller" class="public_query_titleSrc_item"
              style="width:100%;display:flex;align-items: center;padding-top: 0">
              <svg class="icon black" style="margin-left:10px;" width="22px" height="22px" stroke-width="1.3">
                <use :xlink:href="'#icon-walking'"></use>
              </svg>
              <select-one class="select-one" placeholder="业务员" :hasClearBtn="false" :target="'seller'" :formObj="sheet"
                :formFld="'seller_id'" :formNameFld="'seller_name'" />
            </div>
          </div>

        </div>
      </div>

      <div
        style="background-image: linear-gradient(to right,transparent 0%, #ccc 12%, #999 18%, transparent 30%);margin-left:20px; width:calc(100% - 40px); height: 1.5px; margin-top: 25px;margin-bottom:20px; background-size: 10px 40px; background-repeat: repeat-x;">
      </div>

      <div ref="sales_box_box" class="sales_box_box" :style="{ height: publicBox3Height }">
        <YJSheetRows
            ref="YJSheetRows"
            :priceLabel="priceLabel"
            :amountLabel="amountLabel"
            :sheet="sheet"
            :sheetRows="sheet.sheetRows"
            :keyProps="isContractSeller ? 'contract_price' : 'wholesale_price'"
            @handleRemoveRowClick="handleRemoveRowClick"
            @handleSheetRowsSort="handleSheetRowsSort"
            @handleItemRowEdit="handleItemRowEdit"
            @handleEndDrag="handleEndDrag"
            :canEdit="canEdit" />
      </div>
    </div>

    <div ref="sales_footer" class="sales_footer" style="padding: 10px;">
      <div ref="move_total" class="move_total">
        <span style="color:#555;">合: &nbsp;</span>
        <span class="sales_list_span"> {{ isContractSeller ? sheet.contract_amount : sheet.wholesale_amount }}</span>
        <span style="color:#555;">重量: &nbsp;</span>
        <span class="sales_list_span">{{ toMoney(sheet.total_weight) }}</span>
      </div>
      <div style="display: flex;">
        <input  ref="inputCodesRef" type="text" class="footer_input" v-model="transStr" @keydown="onSearchInputKeyDown($event)"
          style="padding-left: 3px;" left-icon="" placeholder="品名/条码" :disabled="!canEdit"
          :style="{ backgroundColor: sheet.approve_time ? '#f2f2f2' : '' }" />
        <van-button class="footer_iconBt" :disabled="!this.canEdit" @click="onAddClass"
          style="border: none">
          <svg width="35px" height="35px" fill="#F56C6C">
            <use xlink:href="#icon-add"></use>
          </svg>
        </van-button>
        <!-- <van-button class="van_search_btns" type="info" :disabled="sheet.approve_time?true:false" @click="onAddClass">添加</van-button> -->
        <van-button class="footer_iconBt" :disabled="!this.canEdit" @click="btnScanBarcode_click" style="border: none">
          <svg width="30px" height="30px" fill="#666">
            <use xlink:href="#icon-barcodeScan"></use>
          </svg>
        </van-button>
      </div>

    </div>

    <van-popup v-model="popFromBranchList" position="bottom" :duration="0.4">
      <div v-if="fromBranchList.length == 0" style="height:300px;padding-top:50px;">{{ fromBranchListEmptyPrompt }}</div>
      <van-picker v-if="fromBranchList.length > 0" :columns="fromBranchList" show-toolbar title="仓库选择"
        value-key="branch_name" @cancel="popFromBranchList = false" @confirm="onFromBranchSelected" />
    </van-popup>

    <van-popup v-model="popToBranchList" position="bottom" :duration="0.4">
      <div v-if="toBranchList.length == 0" style="height:300px;padding-top:50px;">{{ toBranchListEmptyPrompt }}</div>
      <van-picker v-if="toBranchList.length > 0" :columns="toBranchList" show-toolbar title="仓库选择" value-key="branch_name"
        @cancel="popToBranchList = false" @confirm="onToBranchSelected" />
    </van-popup>

    <van-popup v-model="m_bShowSubmitPopup" duration="0.4" :style="{ height: '100%', width: '80%' }" class="van_popup"
      position="right">
      <div style="height:30px;border-top:1px solid #ccc"></div>
      <div class="other_operate">
        <van-field v-model="sheet.make_brief" label="备注" style="white-space: nowrap;" label-width="40px" placeholder="备注"
          :disabled="sheet.approve_time ? true : false" />
        <div style="height:30px"></div>
        <div class="other_operate_content" style="justify-content: space-between;padding-left: 5px;padding-right:5px;">
          <button class="my-btn" v-if="canMake" plain type="info"
            :disabled="!canEdit||sheet.isRedAndChange" @click="btnConfirmSave_click"
          >
            保存
          </button>
 
          <button class="my-btn main-btn" v-if="canApprove" plain type="info" :disabled="sheet.approve_time!='' ||IsSubmiting"
            @click="btnConfirmApprove_click" >审核</button>
          <button class="my-btn" @click="btnGotoPrintView_click()" style="height: 45px;"
            :disabled="(sheetStatusForPrint == 'saved' && !sheet.sheet_id) || (sheetStatusForPrint == 'approved' && !sheet.approve_time) || isPrinting ">打印</button>
            <!-- <button class="my-btn" @click="btnShare_click" style="height: 45px;"
            :disabled="(sheetStatusForPrint == 'saved' && !sheet.sheet_id) || (sheetStatusForPrint == 'approved' && !sheet.approve_time) || isPrinting">分享</button> -->

        </div>

        <div v-if="!isInPrintView" class="other_operate_content">
          <button class="small-btn" v-if="canRed && sheet.approve_time" :disabled="sheet.red_flag !== ''"
            @click="btnRed_click">红冲</button>
          <button class="small-btn" v-if="canRed && sheet.approve_time" :disabled="sheet.red_flag !== ''"
            @click="btnRedAndModify_click">冲改</button>
          <button class="small-btn" v-if="sheet.sheet_id && !sheet.approve_time && canDelete"
            style="height: 45px;border-radius:12px" @click="btnDeleteSheet_click">删除</button>
          <button class="small-btn" type="default" :disabled="!(sheet.approve_time !== '' )" @click="shareWeChat">分享</button>
          <button class="small-btn" @click="btnCopySheet_click" style="height: 45px;border-radius:12px">复制</button>
        </div>

        <div class="other_operate_content" style="padding-top: 10px">
          <button class="small-btn" style="height: 45px;border-radius:12px" @click="handleSheetToOther">转销</button>
          <button class="small-btn" :disabled="IsSubmiting || sheet.approve_time !== ''" @click="onEmpty()">清空</button>
          <button class="small-btn" @click="btnOut">退出</button>
        </div>

        <!--          <div class="other_operate_content" style="padding-top: 0">-->
        <!--              <van-popover-->
        <!--                      v-model="sheetToOtherSheetPopover"-->
        <!--                      trigger="click"-->
        <!--                      :actions="sheetToOtherSheetAction"-->
        <!--                      @select="handleSheetToOther"-->
        <!--              >-->
        <!--                  <template #reference>-->
        <!--                      <button class="small-btn" style="height: 45px;border-radius:12px" @click="handleSheetToOther">转销售</button>-->
        <!--                      <button class="my-btn" style="height: 45px;color: #ff5500;"></button>-->
        <!--                  </template>-->
        <!--              </van-popover>-->
        <!--          </div>-->

        <!-- 打印详情界面 -->
      <transition name="hide-show">
        <div v-if="isInPrintView" class="sales_more">
          <div v-if="this.useTmp" class="print-template-wrapper" style="margin: 0px 0.55rem;">
            <div style="margin-top: 20px;margin-bottom: 10px;margin-left: 10px; text-align: left;">打印模板</div>
            <div class="radio-tmp-position">
              <!-- <div v-for="(item,i) in tmplist" :key="i" style="display: inline-block; margin: 5px 0; text-align: left; width: 50%;"> -->
              <div v-for="(item, i) in tmplist" :key="i" class="radio-tmp-style">
                <!-- //appearance: none; -->
                <input type="radio" name="template" :id="i" :value="i" v-model="selectedTmpId" style="position: relative; margin-left: 10px;" class="radio-tmp-type" />
                <label :for="i" class="radio-tmp-name">
                  {{ item.name }}
                </label>
              </div>
            </div>
          </div>
          <div v-else style="height:170px;" />
          <!-- 搞个选择打印机 -->
          <div class="select-printer">
            <van-cell-group inset style="width:100%">
              <van-cell is-link title="打印机" title-style="flex: inherit;" :value="defaultPrinter.name" @click="showPrinterSelectionPage" />
            </van-cell-group>
          </div>
          <div v-if="!this.useTmp && arrCompanyNamesToPrint && arrCompanyNamesToPrint.length" style="margin: 0px 0.55rem;">
            <div class="print-company-title" style="margin-top:10px;margin-bottom: 10px;margin-left: 10px; text-align: left;">公司名称</div>
            <div class="radio-tmp-position setHeight">
              <!-- <div v-for="(item,i) in tmplist" :key="i" style="display: inline-block; margin: 5px 0; text-align: left; width: 50%;"> -->
              <div v-for="(name, i) in arrCompanyNamesToPrint" :key="i" class="radio-tmp-style">
                <!-- //appearance: none; -->
                <input type="radio" name="companyName" :id="i" :value="name" v-model="companyNameToPrint" style="position: relative; margin-left: 10px;" class="radio-tmp-type" />
                <label :for="i" class="radio-tmp-name">
                  {{ name }}
                </label>
              </div>
            </div>
          </div>
          <div style="margin-top: 10px;">
            <van-checkbox-group v-model="printBarcodeStyle" style="display: flex;  margin: 20px;  margin-left: 1px; padding-top: 0px; ">
              <div style="margin-left: 8px;margin-right:20px; line-height: 30px;">条码</div>
              <van-checkbox name="actualUnit" style="margin-right: 10px;">实际单位</van-checkbox>
              <van-checkbox name="smallUnit" style="margin-right: 10px">小单位</van-checkbox>
            </van-checkbox-group>
            <van-checkbox shape="square" v-model="printBarcodePic" style="margin-left: 56px;">打印条码图</van-checkbox>
            <div class="print-count" v-if="defaultPrinter.type !== 'cloud'" style="margin-bottom: 15px;">
              <button style="font-size: 20px; background: white;color:#777; min-width: 40px;" @click="printCount = printCount < 5 ? printCount + 1 : 5">+</button>
              <div style="display: inline-block; margin-right: 5px; color: #8d8d8d;">{{ printCount }}次</div>
              <button style="font-size: 20px; background: white;color:#777; min-width: 40px;" @click="printCount = printCount > 1 ? printCount - 1 : 1">-</button>
            </div>
          </div>
          <div style="margin-top: 25px;">
            <van-button class="print-btns" style="flex:1; margin-right:5%;" type="default" @click="isInPrintView = false">返回</van-button>
            <van-button class="print-btns" style="flex:1;margin-left:5%;  background-color: #ffcccc;" type="default" @click="btnPrint_click" :disabled="isPrinting">确认打印</van-button>
          </div>
        </div>
      </transition>
      </div>
    </van-popup>
    <van-popup v-model="PopupPrintersSelect" position="bottom">
      <van-picker title="选择打印机" show-toolbar value-key="name" :columns="printers" :default-index="defaultPrinterIndex" @confirm="confirmPrinterSelectionChange" @cancel="hidePrinterSelectionPage" />
    </van-popup>
    <van-popup v-model="m_bPopupFillFromSale" duration="0.4" :style="{ height: '100%', width: '80%' }" class="van_popup"
      position="right">
      <div class="quick_move">
        <h5 class="custom_h5">
          补货
          <van-icon class="icon_h5" name="cross" @click="m_bPopupFillFromSale = false" />
        </h5>
        <div class="move-type">
          <van-radio-group v-model="moveWay" @change="handleTimeSelectShow()">
            <van-radio name="sale" style="margin: 10px">按销补货</van-radio>
            <van-radio name="return" style="margin: 10px">按退回库</van-radio>
            <van-radio name="stock" style="margin: 10px">按库存调拨</van-radio>
            <van-radio name="display" style="margin: 10px">按陈列兑付补货</van-radio>
          </van-radio-group>
        </div>
        <div class="bill-date" v-show="showBillDate">
          <van-radio-group v-model="timeRange" @change="handleTimeRange">
            <van-radio name="today" style="margin: 10px">今日单据</van-radio>
            <van-radio name="yesterday" style="margin: 10px">昨日单据</van-radio>
            <van-radio name="sinceLastMove" style="margin: 10px">上次调拨之后</van-radio>
            <van-radio name="other" style="margin: 10px">其他</van-radio>
          </van-radio-group>
          <div v-show="showOtherTime">
            <van-search class="van_search" v-model="times" left-icon="" placeholder="查询日期" right-icon="close" readonly
              @click="handleTimeShow" @click-right-icon.stop="clearDate" />
          </div>
        </div>
        <div class="branchPositionSelect" v-show="showBranchPositionSelect">
          <van-field readonly clickable placeholder="请选择出仓库位" :value="fromBranchPositionNames" @click="setBranchPositionSource('from')" />
          <van-field readonly clickable placeholder="请选择入仓库位" :value="toBranchPositionNames"  @click="setBranchPositionSource('to')" />
          <van-popup v-model="showFromBranchPositionPicker" round position="bottom" :style="{ height: '50%', width: '100%' }" @close="setBranchPositionlabel('from')">
            <van-checkbox-group v-model="fromBranchPositionList" class="branch-position-items">
              <van-checkbox class="branch-position-item" :name="item" v-for="item in fromBranchPositionSource" :key="item.branch_position">{{item.branch_position_name}}</van-checkbox>
            </van-checkbox-group>
          </van-popup>
          <van-popup v-model="showtoBranchPositionPicker" round position="bottom" :style="{ height: '50%', width: '100%' }" @close="setBranchPositionlabel('to')">
            <van-radio-group v-model="toBranchPosition" class="branch-position-items">
              <van-radio class="branch-position-item" :name="item" v-for="item in toBranchPositionSource" :key="item.branch_position">{{item.branch_position_name}}</van-radio>
            </van-radio-group>
          </van-popup>
        </div>
        <div class="btnForSale">
          <van-button type="default" :disabled="!canEdit" @click="btnForSale_submit" style="margin-top: 30px">确认</van-button>
        </div>
      </div>
    </van-popup>
    <div>
      <van-calendar :readonly="canSelectCalendar" :min-date="minDate" :max-date="maxDate" v-model="timeShow" type="range"
        @confirm="confirmDateDuring" :allow-same-day="true" />
    </div>

    <van-popup v-model="popupAddMoveSheetRow" duration="0.4" :lazy-render="false" position="bottom"
      :style="{ height: '90%' }">
      <h5 class="custom_h5">
        修改商品数量
        <van-icon name="cross" class="icon_h5" @click="popupAddMoveSheetRow = false" />
      </h5>
      <div class="class_add_goods">
        <AddMoveSheetRow ref="dlgEditRow" @onAddRow_OK="onAddRow_OK" @delIndexItem="delIndexItem" />
      </div>
    </van-popup>

    <!-- cloud print -->
    <van-popup round v-model="showTemplate" id="selectTmp" closeable :style="{ width: '65%', height: '50%' }">
      <br>请选择模板<br><br>
      <button v-for="(item, i) in tmplist" :key="i" plain id="templatePrint" style="height: 45px;border-radius:12px;"
        @click="btnPrint_ByTemplate(item.tmp)">{{ item.name }}</button>
    </van-popup>

    <van-popup v-model="showUnsubmitedSheets" round>
      <div class="lowItem" style="width: 320px;">
        <h4>以下是未提交的单据</h4>
        <ul class="lowItem_ul">
          <li v-for="(item, index) in unsubmitedSheets" :key="index">
            <div class="lowItem_ull" @click="onUnsubmitedSheetSelected(item)">
              {{ item.from_branch_name }}--{{ item.to_branch_name }}
            </div>
            <div class="lowItem_ulr" @click="onUnsubmitedSheetSelected(item)">
              {{ item.saveTime }}
            </div>
            <div style="width: 70px; line-height: 40px" class="btn-delete" @click="onUnsubmitedSheetDelete(index)">
              删除
            </div>
          </li>
        </ul>
        <van-button type="default" @click="showUnsubmitedSheets = false">新建单据</van-button>
      </div>
    </van-popup>

    <van-popup v-model="popupEditSheetRows" position="bottom" :style="{ minHeight:'264px' }">
      <EditSheetRows ref="editSheetRows" @closeEdit="closeEdit" :sheet="sheet" :editSheetRowsInfo="editSheetRowsInfo" :isContractSeller="isContractSeller" :fromBranchPositionSource="fromBranchPositionSource" :toBranchPositionSource="toBranchPositionSource" :fromBranchList="fromBranchList"/>
    </van-popup>
    <van-popup v-model="popupNewAddMoveSheetRow" :lazy-render="true" position="bottom"
      style="height:100vh;overflow:hidden">
      <MultiSelect v-if="popupNewAddMoveSheetRow" ref="multiSelect" :sheet="sheet" :moveType="moveType"
        :canSeeFromStock='canSeeFromStock' :canSeeToStock='canSeeToStock' @closePage="closePage"
        @closeSingleChoicePage="closeSingleChoicePage">
      </MultiSelect>
    </van-popup>
    <div v-if="loadingMsg"
      style="border-radius:20px; width:200px;height:80px;background:#555;color:#fff; position:fixed;top:calc(50vh - 40px);left:calc(50vw - 100px);z-index:99999999;display:flex;justify-content:center;align-items:center;">
      <van-loading size="24px" color="#fff">{{ loadingMsg }}...</van-loading>
    </div>
  </div>
</template>

<script>
import {
  Cell,
  CellGroup,
  NavBar,
  Button,
  Search,
  Toast,
  Popup,
  Field,
  Picker,
  Form,
  Col,
  Row,
  SwipeCell,
  Dialog,
  Divider,
  Icon,
  RadioGroup,
  Radio,
  Checkbox,
  Calendar,
  Loading,
  Popover,
 CheckboxGroup
} from "vant";

import {
  AppSheetMoveLoad,
  AppSheetMoveSubmit,
  ApiSheetMoveSave,
  GetBranchList,
  AppSheetMoveGetFillItemsFromSale,
  AppSheetMoveGetFillItemsFromStock,
  SheetMoveRed,
  SheetMoveDelete,
  AppCloudPrint_sheetTmp,
  AppCloudPrint_escCmd,
  AppSheetToEsc,
  AppSheetToImages,
  AppGetSheetToPrint,
  AppGetSheetToPrint_Post,
  AppGetTemplate,
  GetFromBranchItemList, AppSheetMoveGetItemList,
  ApiPrintMark,
  GetBranchPosition,
  ApiCheckVanStockOverLoad
} from "../../api/api";
import Printing from "../Printing/Printing";
import sheetCache from "../../util/sheetCache";
import EditSheetRows from "./EditSheetRows";
import AddMoveSheetRow from "./AddMoveSheetRow";
import WeChatShare from '../components/wechatComponents/WeChatShare.vue'
import mixins from '../SaleSheet/sheetMixin/mixin'
import YJSheetRows from '../components/sheetComponents/YJSheetRows'
import MultiSelect from './MultiSelect'
import html2canvas from 'html2canvas';
import sheetImages from "../../util/sheetImages";
import SelectOne from "../components/SelectOne";
import RouterUtil from "../../util/RouterUtil";
import globalVars from "../../static/global-vars";

// import SelectSeller from '../components/SelectSeller';
export default {
  name: "MoveSheet",
  mixins: [mixins],
  data() {
    return {
      selectedTmpId: 0, // 选择模板自动赋值
      companyNameToPrint: '',
      printCount: 1,
      //printBarcodeStyleForSale: [],
      arrCompanyNamesToPrint: [],
      printers: [],
      PopupPrintersSelect: false,
      tmplist: [],
      defaultPrinterIndex: 0,
      defaultPrinter: {},
      isPrinting: false,
      useTmp: false,
      isInPrintView: false,  // false审核界面 true打印详情
      showTemplate: false,
      tmplist: [],
      editingItemRowIndex: null,
      timeRange: "today", 
      popupAddMoveSheetRow: false,
      popupNewAddMoveSheetRow: false,
      transStr: "",
      warehouseSelect: "",
      warehouseSelectName: "",
      userId: "",
      onload_remarks: [],
      salesPopup: false,
      PaymentPopup: false,
      m_bShowSubmitPopup: false,
      m_bPopupFillFromSale: false,
      showUnsubmitedSheets: false,
      unsubmitedSheets: [],
      printBarcodeStyle: "noBarcode",
      printBarcodePic: false,
      PaymentList: [],
      paymentSelects: "",
      bindSalesDats: {
        discount: 0,
        arrears: 0,
      },
      sheet: {
        total_units_amount: [],
        sheet_no: "",
        sheet_id: "",
        approve_time: "",
        make_time: "",
        sheetRows: [],
        startTime: "",
        endTime: "",
        make_brief: "",
        seller_id: '',
        seller_name: ''
      },
      IsSubmiting: false,
      srcDatas: [],
      Form: {
        qk: "",
        yh: "",
        zf1: "",
        zf2: "",
      },
      columns: [],
      qk: "",
      yh: "",
      warehouseData: [],
      totalsDatas: {},
      popFromBranchList: false,
      popToBranchList: false,
      fromBranchList: [],
      toBranchList: [],
      fromBranchListEmptyPrompt: '',
      toBranchListEmptyPrompt: '',
      moveType: "",
      itemRows: [],
      minDate: new Date(2010, 0, 1),
      maxDate: new Date(),
      times: "",
      timeShow: false,
      canSelectCalendar: true,
      publicBox3Height: '',
      pageHeight: '',
      moreOptions: false,
      popupEditSheetRows: false,
      editSheetRowsInfo: [],
      loadingMsg: '',
      sheetToOtherSheetPopover: false,
      sheetToOtherSheetAction: [
        { text: '销售单', sheetType: "X", right: 'sale.sheetSale.make' },
      ],
      showFromBranchPositionPicker:false,
      showtoBranchPositionPicker:false,
      showBranchPositionSelect:false,
      fromBranchPositionSource:[],
      toBranchPositionSource:[],
      fromBranchPositionNames:'',
      toBranchPositionNames:'',
      fromBranchPositions:'',
      toBranchPositions:'',
      fromBranchPositionList:[],
      toBranchPosition:'',
      moveWay:"sale",
      showBillDate:false,
      showOtherTime:false,

      //控制保存后编辑时打印提示
      sheetHasChange:false,

      ignoreSheetChange:false, //加载单据， 设为true,区分是否是人为修改单据
    
     


      /*list: [],
      loading: false,
      finished: false,
      isSubmit: false,
      itemDataList:[],

      */
    };
  },
  beforeUpdate() {
    this.changeHeight();
  },
  watch: {
    printBarcodeStyle: {
      handler: function (val, oldVal) {
        this.$store.state.printBarcodeStyleForMove = val
      },
      deep: true

    },
    // "$route.query": function () {
    //   //监听vuex中userName变化而改变header里面的值
    //   var selectedItems = this.$route.query.selectedItems;
    //   console.log(typeof selectedItems)
    //   ;
    //   if (selectedItems) {
    //     this.addItems(selectedItems);
    //     this.updateTotal();
    //     sheetCache.saveCurSheetToCache(
    //       this,
    //       "unsubmitedSheets_DB" + this.moveType,
    //       3
    //     );
    //   }
    //   // let shoppingCarObj = this.$store.state.shoppingCarObj[this.moveType]
    //   // this.addItems(shoppingCarObj)
    // },
    timeRange: {
      handler: function (val, oldVal) {
        if (val === "other") {
          this.canSelectCalendar = false
        } else {
          this.canSelectCalendar = true
          this.clearDate()
        }
      },
      deep: true
    },
    'sheet.sheetRows': {
      handler(val, oldVal) {
        this.$refs.YJSheetRows.updatePage()
        this.updateTotal();
        this.handleSelectedSheetRows(this.moveType)
        this.$store.commit("currentSheet", this.sheet);
      },
      deep: true
    },
    sheet:{
      deep:true,
      handler(){
        //  let a= this.isChangeFromPrint
        if(this.sheet.sheet_no  && !this.sheet.approve_time && !this.ignoreSheetChange ){ 
            this.sheetHasChange = true
        }
     
      }
    }
  },
 async mounted() {
    this.ignoreSheetChange = true
    this.sheet.sheet_id = this.$route.query.sheetID;
    //var rights=this.$store.state.operRights
     
    //this.$refs.YJSheetRows.keyProps = 'contract_price'
    this.moveType = this.$route.query.moveType;
    await this.loadSheet();
    
    window.checkToUpdateApp(this.sheet.server_uri)

    this.pageHeight = this.$refs.pages.offsetHeight
    this.changeHeight();
    if (this.$store.state.printBarcodeStyleForMove) {
      this.printBarcodeStyle = this.$store.state.printBarcodeStyleForMove
    }
    if (this.$store.state.printBarcodePicForMove) {
      this.printBarcodePic = this.$store.state.printBarcodePicForMove
    }
    this.$forceUpdate()
    this.handleRegisterSayCode()

    if (!this.moveType) {
      this.moveType = '' // 's'+undefind will be 'sundefined'
    }
    setTimeout(() => {
      this.ignoreSheetChange = false
    }, 100);

    this.companyNameToPrint = this.$store.state.companyNameToPrint || ''
    var companyNames = window.getSettingValue('companyNamesToPrint')
    if (companyNames) {
      this.arrCompanyNamesToPrint = companyNames.split('\n')
    }
    //this.changOrderPayWay();
    this.printBarcodeStyle = this.$store.state.printBarcodeStyleForMove
    if (!this.printBarcodeStyle) {
      if (this.$store.state.printBarcodeStyle == 'noBarcode' || !this.$store.state.printBarcodeStyle) this.printBarcodeStyle = [];
      else this.printBarcodeStyle = [this.$store.state.printBarcodeStyle];
    }

    // 获取当前公司的打印机列表
    let printers = window.getCompanyStoreValue('c_printers')
    // 兼容旧版本
    if (!printers) {
      printers = []
      const printer = {
        id: this.$store.state.printerID ? this.$store.state.printerID : this.$store.state.printerID,
        name: this.$store.state.printer_name || '蓝牙打印机',
        type: this.$store.state.printerType || 'bluetooth',
        kind: this.$store.state.printer_kind || 'tiny',
        paperSize: this.$store.state.paperSize || '58',
        bluetoothID: this.$store.state.printerID,
        bluetoothType: 'classic',
        bluetoothDataStyle: this.$store.state.bluetoothSendDataStyle,
        useTemplate: this.$store.state.useTemplateToPrint === true || this.$store.state.printStyle == 'template',

        brand: this.$store.state.printer_brand || '',
        cloudID: this.$store.state.device_id || '',
        cloudCode: this.$store.state.check_code || '',

        isDefault: true
      }
      printers.push(printer)
    }
    this.printers = printers

    for (let _i = 0; _i < printers.length; _i++) {
      const _printer = printers[_i]
      if (_printer.isDefault) {
        this.defaultPrinter = _printer;
        this.defaultPrinterIndex = _i;
        break;
      }
    }
  },
  activated() {
    this.changeHeight();
  },
  components: {
    "van-cell": Cell,
    "van-cell-group": CellGroup,
    "van-nav-bar": NavBar,
    "van-button": Button,
    "van-search": Search,
    "van-popup": Popup,
    "van-field": Field,
    "van-picker": Picker,
    "van-form": Form,
    "van-row": Row,
    "van-col": Col,
    "van-divider": Divider,
    "van-icon": Icon,
    "van-swipe-cell": SwipeCell,
    "van-radio-group": RadioGroup,
    "van-radio": Radio,
    "van-popover": Popover,
    "van-checkbox": Checkbox,
    "van-calendar": Calendar,
    AddMoveSheetRow: AddMoveSheetRow,
    YJSheetRows,
    EditSheetRows,
    MultiSelect,
    WeChatShare,
    "select-one": SelectOne,
    "van-loading": Loading,
    "van-checkbox-group":CheckboxGroup
  },
  computed: {
    
    canAppSelectSeller() {
      var appSelectSeller = window.getRightValue("delicacy.appSelectSeller.value");
      return appSelectSeller == "true"
    }, 
    canMake() {
      return hasRight("stock.sheetMove.make")
    },
    canApprove() {
      return hasRight("stock.sheetMove.approve")
    },
    canRed() {
      return hasRight("stock.sheetMove.red")
    },
    canDelete() {
      return hasRight("stock.sheetMove.delete")
    },
    allowPrintBeforeApprove() {
      return hasRight("delicacy.allowPrintBeforeApprove.value");
    },
    allowPrintBeforeSave() {
      return hasRight("delicacy.allowPrintBeforeSave.value");
    },
    sheetStatusForPrint() {
      return window.getRightValue('delicacy.moveSheetStatusForPrint.value');
    },
    canEdit() { 
      if (this.sheet.approve_time|| this.IsSubmiting)
        return false
       
      var canMultiSave = hasRight("delicacy.moveShtMultiSave.value",this.canApprove?true:false)
 
      if(!canMultiSave && this.sheet.sheet_id) 
        return false
      return true
    },
    canSeeFromStock() {
      return window.hasBranchOperRight(this.sheet.from_branch_id, 'query_stock')
    },
    canSeeToStock() {
      return window.hasBranchOperRight(this.sheet.to_branch_id, 'query_stock')
    },
    forAssignVan() {
      return this.sheet.sheet_attribute.assignVan == 'assign'
    },
    forBackBranch() {
      return this.sheet.sheet_attribute.assignVan == 'back'
    },
    isContractSeller() {
      
      var v = window.getRightValue('delicacy.isContractSeller.value')
      if (v == "true") return true
      return false
    },
    priceLabel() {
      if (this.isContractSeller) return "承包价"
      else return "单价"
    },
    amountLabel() {
      if (this.isContractSeller) return "承包额"
      else return "金额"
    }
  },
  methods: {
    shareWeChat() {
      this.$refs.wechatshare.changeShowShare()
    },
    shareSelected(e){
      switch (e.type) {
        case "shareQRCode":
            this.popupQRCodeShare = true
            setTimeout(()=>{
                new QRcode(this.$refs.qrcode,{
				        text: e.webpageUrl, // 扫二维码后获得的信息
				        width: 180, // 图片宽90px，左右padding各4px，边框各1px， 100-8px-2px
				        height: 180, // 图片高90px，上下padding各4px，边框各1px， 100-8px-2px
			          })
            },200)
        break
        case "sharePicture":
            console.log(e.type)
            Toast.loading({
              message: "生成图片中..."
            })
            this.popupSubmitPannel = false

            setTimeout(async () => {
                const div = document.createElement('div');
                div.innerHTML = "<div ref='snipaste' class='snipaste' id='snipaste'></div>"
                document.body.append(...div.childNodes)
                const public_box3 = document.getElementsByClassName("public_box3")[0]
                const clone_public_box3 = public_box3.cloneNode(true)
                const sales_footer = document.getElementsByClassName("sales_footer")[0]
                const clone_sales_footer = sales_footer.cloneNode(true)
                const snipaste_element = document.getElementById("snipaste")
                snipaste_element.append(clone_public_box3)
                snipaste_element.append(clone_sales_footer)
                const canvas = await html2canvas(snipaste_element, {
                  backgroundColor: "#fff",
                  useCORS: true,
                  scale: 1.5,
                  height:  this.$refs.public_box3.scrollHeight + this.$refs.sales_box_box.scrollHeight + this.$refs.sales_footer.scrollHeight,
                  windowHeight:  this.$refs.public_box3.scrollHeight + this.$refs.sales_box_box.scrollHeight + this.$refs.sales_footer.scrollHeight
                })
                
                const url = canvas.toDataURL('image/png') //转图片链接,为图片的base64形式
                console.log(url)
                window.Wechat.share({
                  message: {
                    title: "X",
                    description: `来自【公司名称】`,
                    thumb: globalVars.wechatConf.imgUrl,
                    media: {
                      type: window.Wechat.Type.IMAGE,
                      image: url
                    }
                  },
                  scene: window.Wechat.Scene.SESSION
                }, function () {
                  Toast.success("分享成功")
                }, function (reason) {
                  Toast.fail("分享失败: " + reason)
                });
            }, 400);
    }
    },
    handleRegisterSayCode() {
      window.sayCode = result => {
        this.pageSayCode(result)
      }
    },
    pageSayCode(result) {
      this.transStr = result;
      this.queryScan()
    },
    queryScan() {
      
      // // 主动获取焦点
      // this.$nextTick(() => {
      //   this.$refs.searchInput.focus()
      // })

      console.log(this.transStr)
      this.$store.commit("currentSheet", this.sheet);
      let params = {
        fromBranchID: this.sheet.from_branch_id,
        toBranchID: this.sheet.to_branch_id,
        classID: '',
        searchStr: this.transStr,
        showStockOnly: false,
        pageSize: 50,
        startRow: 0,
        brandIDs: this.$store.state.operInfo.brands_id,
        isContractSeller: this.isContractSeller
      }
      AppSheetMoveGetItemList(params).then(res => {
        console.log(res)
        if (res.result === "OK") {
          if (res.data.length === 0) {
            Toast('未找到对应商品')
            return
          } else if (res.data.length === 1) {
            this.$refs.inputCodesRef.blur();
            let item = res.data[0]
            item.isActive = false;
            item.remark = "";
            item.remark_id = ""
            this.handleNewItemClik(item)
          } else {
            this.onAddClass()
          }
        }
      }).catch(err => {
        console.log(err);
      })

      // this.onAddClass();

    },
    handleNewItemClik(item) {
      this.$store.commit('distinctStockFlag', false)
      this.$store.commit("shoppingCarFinish", false)
      let itemObj = JSON.parse(JSON.stringify(item))
      itemObj.isSelectFlag = false
      itemObj.singleChoice = true
      itemObj.distinctStockFlag = false
      if (itemObj.mum_attributes) {
        if (!itemObj.mum_attributes.forEach) itemObj.mum_attributes = JSON.parse(itemObj.mum_attributes)
        if (itemObj.mum_attributes.find(attr => attr.distinctStock)) {
          this.$store.commit('distinctStockFlag', true)
          itemObj.distinctStockFlag = true
        }
      }
      this.popupNewAddMoveSheetRow = true
      this.$store.commit("multiSelectOpenFlag", true)
      this.$store.commit("attrShowFlag", false)
      setTimeout(() => {
        this.$refs.multiSelect.loadData([itemObj])
      }, 310);

      // if(!this.attrShowFlag) {
      //   this.$store.commit('activeSelectItem',activeSelectItem)
      // }
      // this.$refs.dlgEditRow.loadData(activeSelectItem)

    },
    /**
     * 页面加载数据
     */
   async loadSheet() {
      let params = {
        sheetID: this.sheet.sheet_id,
      };

      let branchlist = {};
      AppSheetMoveLoad(params).then(async (res) => {
        if (res.result === "OK") {
          // this.sheet.sheet_no = res.sheet.sheet_no;
          // this.sheet.make_time = res.sheet.happen_time;
          // this.sheet.red_flag = res.sheet.red_flag;
          // this.sheet.make_brief = res.sheet.make_brief;
          // this.sheet.sheetRows = res.sheet.sheetRows;
          this.sheet = res.sheet;
          this.sheet.sheetRows.forEach(item => {
            sheetImages.handleImage(item)
          })
          this.$store.commit("attrOptions", res.attrOptions)
          if (!this.sheet.approve_time && this.sheet.sheet_id) {
            await this.loadStockQty();
          }
          if (!this.sheet.sheet_id) {
          
            var fromBranch = {}, toBranch = {}
            if (this.moveType == "moveOut") {
              fromBranch = this.$store.state.moveOutFromBranch
              toBranch = this.$store.state.moveOutToBranch

              this.sheet.move_type = '装车单'
            } else if (this.moveType == "moveIn") {
              fromBranch = this.$store.state.moveInFromBranch
              toBranch = this.$store.state.moveInToBranch
              this.sheet.move_type = '回库单'
            }
            else {
              this.sheet.move_type == '调拨单'
            }
            
            if (this.$store.state.operInfo.companyID) {
              var sheetBranches = window.getCompanyStoreValue("c_sheetBranches", {})

              if (this.moveType == "moveOut") {

                fromBranch = sheetBranches['moveOutFrom'] || {}
                toBranch = sheetBranches['moveOutTo'] || {}

                this.sheet.move_type = '装车单'
              }
              else if (this.moveType == "moveIn") {
                fromBranch = sheetBranches['moveInFrom'] || {}
                toBranch = sheetBranches['moveInTo'] || {}
                this.sheet.move_type = '回库单'
              }
            }
            if (fromBranch?.branch_id) this.setSheetBranchByCache(fromBranch, true);
            if (toBranch?.branch_id) this.setSheetBranchByCache(toBranch, false);

            this.showSheetsFromCache();
          }

          this.sheet.moveType=this.moveType
          // 增加无产期初始化
          this.sheet.sheetRows.forEach((r) => {
          if (r.batch_level && !r.produce_date) {
              r.produce_date = '无产期'
            }
          })
        }
      });

      GetBranchList(branchlist).then((res) => {
        if (res.result === "OK") {
          this.branchList = [];
          for (var i = 0; i < res.data.length; i++) {
            var branch = res.data[i];
            var branchValid = window.hasBranchSheetRight(branch.branch_id, "dc");
            if (branchValid) {
              let branchPosition = JSON.parse(branch.branch_position)
              let newBranchPosition = []
              branchPosition.forEach(e=>{
                if(e.branch_position !=="0"){
                  newBranchPosition.push(e)
                }
              })
              this.fromBranchList.push({
                branch_id:branch.branch_id,
                branch_name:branch.branch_name,
                branch_position:newBranchPosition,
                branch_type:branch.branch_type,
                negative_stock_accordance:branch.negative_stock_accordance,
              })
            }
            branchValid = window.hasBranchSheetRight(branch.branch_id, "dr");
            // if (branchValid) this.toBranchList.push(branch);
            if (branchValid) {
              let branchPosition = JSON.parse(branch.branch_position)
              let newBranchPosition = []
              branchPosition.forEach(e=>{
                if(e.branch_position !=="0"){
                  newBranchPosition.push(e)
                }
              })
              this.toBranchList.push({
                branch_id:branch.branch_id,
                branch_name:branch.branch_name,
                branch_position:newBranchPosition,
                branch_type:branch.branch_type,
                negative_stock_accordance:branch.negative_stock_accordance,
              })
            }
          }
          
          if (this.moveType) {
            switch (this.moveType) {
              case 'moveOut':
                // for (var i = 0; i < res.data.length; i++) {
                //   var branch = res.data[i];
                //   var branchValid = window.hasBranchSheetRight(branch.branch_id, "dc");
                //   if (branchValid) this.fromBranchList.push(branch);
                //   branchValid = window.hasBranchSheetRight(branch.branch_id, "dr");
                //   if (branchValid) this.toBranchList.push(branch);
                // }
                // if (typeof branch.branch_type != 'undefined')
                this.fromBranchList = this.getStoreBranchList(this.fromBranchList)
                // if (typeof branch.branch_type != 'undefined')
                this.toBranchList = this.getTruckBranchList(this.toBranchList)
                this.fromBranchListEmptyPrompt = '仓库类型设置为仓库,并在员工档案里关联仓库的调出权限，这里才可见'
                this.toBranchListEmptyPrompt = '仓库类型设置为车辆,并在员工档案里关联仓库的调入权限，这里才可见'
                break;
              case 'moveIn':
                // for (var i = 0; i < res.data.length; i++) {
                //   var branch = res.data[i];
                //   var branchValid = window.hasBranchSheetRight(branch.branch_id, "dc");
                //   if (branchValid) this.fromBranchList.push(branch);
                //   branchValid = window.hasBranchSheetRight(branch.branch_id, "dr");
                //   if (branchValid) this.toBranchList.push(branch);
                // }
                // if (typeof branch.branch_type != 'undefined')
                this.fromBranchList = this.getTruckBranchList(this.fromBranchList)
                // if (typeof branch.branch_type != 'undefined')
                this.toBranchList = this.getStoreBranchList(this.toBranchList)
                this.toBranchListEmptyPrompt = '仓库类型设置为仓库,并在员工档案里关联仓库的调出权限，这里才可见'
                this.fromBranchListEmptyPrompt = '仓库类型设置为车辆,并在员工档案里关联仓库的调入权限，这里才可见'

                break;
            }
          }
              console.log("moveSheet onLoad")
              setTimeout(()=>{
                  if(this.sheet.from_branch_id){
                    console.log({fromBranchList:this.fromBranchList})
                    this.fromBranchList.some(b=>{
                      if(b.branch_id.toString() == this.sheet.from_branch_id.toString()){
                        if(b.branch_position.length != 0){
                          let branchPositionList = JSON.parse(JSON.stringify(b.branch_position))
                          branchPositionList.push({
                            branch_position:"0",
                            branch_position_name:'默认库位',
                            type_id:"",
                          })
                          this.fromBranchPositionSource = branchPositionList
                        }
                        this.$store.commit("setCurFromBranchPositionList",b.branch_position)
                        return true
                      }
                    })
                  }
                  if(this.sheet.to_branch_id){
                    this.toBranchList.some(b=>{
                      if(b.branch_id.toString() == this.sheet.to_branch_id.toString()){
                        if(b.branch_position.length!= 0){
                          let branchPositionList = JSON.parse(JSON.stringify(b.branch_position))
                          branchPositionList.push({
                            branch_position:"0",
                            branch_position_name:'默认库位',
                            type_id:"",
                          })
                          this.toBranchPositionSource = branchPositionList
                        }
                        this.$store.commit("setCurToBranchPositionList",b.branch_position)
                        return true
                      }
                    })
                  }
          },200)
          // 2024.03.27: 修正缓存会允许业务员使用被停用/删除仓库的错误
          // 张峻岭客户出现的问题
          if (this.canEdit && this.sheet.from_branch_id) {
            const check_has_branch = this.fromBranchList.some(b => 
              b.branch_id == this.sheet.from_branch_id
            )
            if (!check_has_branch) {
              this.sheet.from_branch_id = "";
              this.sheet.from_branch_name = "";
            }
          }
          if (this.canEdit && this.sheet.to_branch_id) {
            const check_has_branch = this.toBranchList.some(b => 
              b.branch_id == this.sheet.to_branch_id
            )
            if (!check_has_branch) {
              this.sheet.to_branch_id = "";
              this.sheet.to_branch_name = "";
            }
          }

          this.$store.commit("setFromBranchList",this.fromBranchList)
          this.$store.commit("setToBranchList",this.toBranchList)
        }
      });
    },

      setSheetBranchByCache(branch, bFromOrTo){
        var c = "dr";
        if (bFromOrTo) c = "dc";
        if (branch) {
          var branchValid = window.hasBranchSheetRight(
            branch.branch_id,
            c
          );
          if (branchValid) {
            if (bFromOrTo) {
              this.sheet.from_branch_id = branch.branch_id;
              this.sheet.from_branch_name = branch.branch_name;

              if(this.fromBranchList){
                let nowbranchinfo=this.fromBranchList.find(r=>r.branch_id==branch.branch_id);
                if(nowbranchinfo && nowbranchinfo.branch_name!=branch.branch_name){//如果仓库名改了，更新缓存中的仓库名，以便打印出来是新仓库名
                  this.sheet.from_branch_name=nowbranchinfo.branch_name;
                  this.onFromBranchSelected(nowbranchinfo);
                }
              }
            } else {
              this.sheet.to_branch_id = branch.branch_id;
              this.sheet.to_branch_name = branch.branch_name;

              if(this.toBranchList){
                let nowbranchinfo=this.toBranchList.find(r=>r.branch_id==branch.branch_id);
                if(nowbranchinfo && nowbranchinfo.branch_name!=branch.branch_name){//如果仓库名改了，更新缓存中的仓库名，以便打印出来是新仓库名
                  this.sheet.to_branch_name=nowbranchinfo.branch_name;
                  this.onToBranchSelected(nowbranchinfo);
                }
              }
            }
          }
        }
      },


    getTruckBranchList(branchList) {
      return branchList.filter(branch => branch.branch_type === 'truck' || branch.branch_type === '')
      // return branchList
    },
    getStoreBranchList(branchList) {
      // console.log(branchList.filter(branch => branch.branch_type === 'store' || branch.branch_type === ''))
      return branchList.filter(branch => branch.branch_type === 'store' || branch.branch_type === '')
      // return branchList
    },
    loadStockQty() {
      var itemIds = [];
      this.sheet.sheetRows.forEach(sheetRow => {
        itemIds.push(sheetRow.item_id)
      });
      itemIds = itemIds.join(",");
      let params = {
        // operKey: this.$store.state.operKey,
        itemIds: itemIds,
        branchId: this.sheet.from_branch_id,
      };
      GetFromBranchItemList(params).then((res) => {
        if (res.result === "OK") {
          res.data.forEach(item => {
            this.sheet.sheetRows.forEach(sheetRow => {
              if (sheetRow.item_id == item.item_id) {
                sheetRow.branchqty = item.branchidqty;
              }
            });
          });
          this.$refs.YJSheetRows.updatePage()

        }

      });

    },
    clearDate() {
      this.sheet.startDate = "";
      this.sheet.endDate = "";
      this.times = "";
      this.sheet.startTime = "";
      this.sheet.endTime = "";
    },
    confirmDateDuring(e) {
      this.timeShow = false;

      this.sheet.startTime = this.formatDate(e[0]);
      this.sheet.endTime = this.formatDate(e[1]);
      this.times = `${this.sheet.startTime} 至 ${this.sheet.endTime}`;
    },
    // btnScanBarcode_click() {
    //   var that = this;
    //   if (that.sheet.sheet_id !== "") return;
    //   scanBar(
    //     (res) => {
    //       that.transStr = res;
    //       that.onAddClass();
    //     },
    //     (err) => {
    //       console.log("barcode scan fail:", err);
    //     }
    //   );
    // },

    async btnScanBarcode_click() {
      try {
        const result = await this.scanBarcodeNew({
          unit_type: 'move_sheet'
        })

        if (!result.code) {
          return
        }

        this.transStr = result.code
        this.onAddClass()
      } catch (error) {
        console.error('扫码失败:', error)
        this.$toast('扫码失败，请重试')
      }
    },
    onUnsubmitedSheetSelected(obj) {
      this.sheet = obj;
      this.showUnsubmitedSheets = false;
    },
    onUnsubmitedSheetDelete(index) {
      var key = "unsubmitedSheets_DB" + this.moveType;
      this.unsubmitedSheets.splice(index, 1);
      this.$store.commit(key, this.unsubmitedSheets);
    },
    handleTimeSelectShow(){
      if(this.moveWay !== "stock"){
        this.showBillDate = true
        this.showBranchPositionSelect = false
      }else if(this.moveWay == "stock"){
         this.getBranchPosition()
        this.showBranchPositionSelect = true
        this.showBillDate = false
      }
    },
    handleTimeRange(){
      if(this.timeRange === "other"){
        this.showOtherTime = true
      }
    },
    btnForSale_submit(){
      if(this.moveWay == "stock"){
        this.btnFillByStock_click()
      }else if(this.moveWay == "sale"){
        if (this.handleCalendarToast()) {
          this.fillBySale(false);
        }
      }else if(this.moveWay == "return"){
        if (this.handleCalendarToast()) {
          this.fillBySale(true);
        }
      }else{
        if (this.handleCalendarToast()) {
          this.fillBySale(false,true);
        }
      }
      this.m_bPopupFillFromSale = false
    },
    /*btnForSale_submit() {
      this.m_bPopupFillFromSale = false;
      var params = {
     //  operKey: this.$store.state.operKey,
        timeRange: this.timeRange,
        fromBanchID: this.sheet.from_branch_id,
        toBranchID: this.sheet.to_branch_id,
        branchPositions: this.fromBranchPositions,
        isFromReturn: this.moveWay === "return",
        forDisplayItems: false,
        startTime: this.sheet.startTime,
        endTime: this.sheet.endTime,
        classes: "",
        brandsID: ""
      };
      this.loadingMsg = '正在加载数据'
      AppSheetMoveGetFillItemsFromSale(params).then(res => {
        this.loadingMsg = ''
        if (res.result === "OK") {
          if (res.data.length === 0) {
            Toast.fail("没有找到符合条件的商品");
            return;
          }
          this.sheet.sheetRows = [];
          res.data.forEach(row => {
            if (row.item_id) {
              var unitRow = {
                item_id: row.item_id,
                item_name: row.item_name,
                quantity: row.s_qty,
                unit_no: row.s_unit_no,
                unit_factor: row.s_unit_factor,
                wholesale_price: row.s_wholesale_price,
                contract_price: row.s_contract_price || 0,
                unit_weight: row.s_weight || 0,
                munit: row.m_unit_no,
                sfactor: row.s_unit_factor,
                sunit: row.s_unit_no,
                batch_id: row.batch_id,
                produce_date: row.produce_date,
                batch_no: row.batch_no,
                from_branch_position: row.from_branch_position,
                from_branch_position_name: row.from_branch_position_name,
                to_branch_position: row.to_branch_position,
                to_branch_position_name: row.to_branch_position_name,
              };
              this.sheet.sheetRows.push(unitRow);
            }
          });
          this.updateTotal();
        } else {
          Toast.fail(res.msg);
        }
      });
    },*/
    setBranchPositionlabel(flag){
      let branchPositions = ""
      let branchPositionNames = ""
      let branchPositionList = []
      if(flag == "from"){
        branchPositionList = this.fromBranchPositionList
        branchPositionList.forEach(e=>{
        if(!branchPositionNames) {
          branchPositionNames = e.branch_position_name
          branchPositions = e.branch_position
        }else{
          branchPositionNames += ","+ e.branch_position_name
          branchPositions +="," + e.branch_position
        }
      })
      }
      
      if(flag == "from"){
        this.fromBranchPositions = branchPositions
        this.fromBranchPositionNames = branchPositionNames
      }else{
        this.toBranchPositions = this.toBranchPosition.branch_position
        this.toBranchPositionNames = this.toBranchPosition.branch_position_name
      }
    },
    setBranchPositionSource(flag){
      if(flag === "from") {
        this.showFromBranchPositionPicker = true
        this.showtoBranchPositionPicker = false
      }
      else {
        this.showFromBranchPositionPicker = false
        this.showtoBranchPositionPicker = true
      }
    },
    getBranchPosition(){
      let branch_id = ""
      if(!this.fromBranchPositionSource.length){
        branch_id = this.sheet.from_branch_id
      }
      if(!this.toBranchPositionSource.length){
        if(branch_id == "") branch_id = this.sheet.to_branch_id
        else branch_id += "," + this.sheet.to_branch_id
      }
      if(branch_id == "") return
    },
    // 选择其他情况，日期提醒
    handleCalendarToast() {
      if (this.timeRange === "other" && (this.sheet.startTime === "" || this.sheet.startTime === undefined)) {
        Toast.fail("请选择日期")
        return false
      }
      return true
    },
    // btnFillBySale_click() {
    //   this.forReturn = formatTplSimple
    // },
    // btnFillByDisplayItems_click() {
    //   this.forReturn = false
    //   this.forDisplay = true
    // },

    // btnFillByReturn_click() {
    //   this.forReturn = true
    // },
    row_UnitsArray2Props(arrRow) {
      var propRow = {};
      propRow.item_id = arrRow.item_id;
      propRow.item_name = arrRow.item_name;
      propRow.bunit = arrRow.b_unit_no;
      propRow.bfactor = arrRow.b_unit_factor;
      propRow.bpprice = arrRow.b_wholesale_price;

      propRow.munit = arrRow.m_unit_no;
      propRow.mfactor = arrRow.m_unit_factor;
      propRow.mpprice = arrRow.m_wholesale_price;

      propRow.sunit = arrRow.s_unit_no;
      propRow.sfactor = 1;
      propRow.spprice = arrRow.s_wholesale_price;

      arrRow.rowUnits.forEach(function (unit) {
        if (unit.unit_type == "b") {
          propRow.bunit = unit.unit_no;
          propRow.bfactor = unit.unit_factor;
          propRow.bunitSum = unit.quantity;
          propRow.bpprice = unit.wholesale_price;
          propRow.bremark = unit.remark
        } else if (unit.unit_type == "m") {
          propRow.munit = unit.unit_no;
          propRow.mfactor = unit.unit_factor;
          propRow.munitSum = unit.quantity;
          propRow.mpprice = unit.wholesale_price;
          propRow.mremark = unit.remark
        } else if (unit.unit_type == "s") {
          propRow.sunit = unit.unit_no;
          propRow.sfactor = unit.unit_factor;
          propRow.sunitSum = unit.quantity;
          propRow.spprice = unit.wholesale_price;
          propRow.sremark = unit.remark
        }
      });
      return propRow;
    },
    row_UnitsProps2Array(propRow) {
      var arrRow = {};
      arrRow.item_id = propRow.item_id;
      arrRow.item_name = propRow.item_name;
      arrRow.b_unit_no = propRow.bunit;
      arrRow.b_unit_factor = propRow.bfactor;
      arrRow.b_wholesale_price = propRow.bpprice;
      arrRow.b_contract_price = propRow.b_contract_price;
      arrRow.m_unit_no = propRow.munit;
      arrRow.m_unit_factor = propRow.mfactor;
      arrRow.m_wholesale_price = propRow.mpprice;
      arrRow.m_contract_price = propRow.m_contract_price;
      arrRow.s_unit_no = propRow.sunit;
      arrRow.s_wholesale_price = propRow.spprice;
      arrRow.s_contract_price = propRow.s_contract_price;
      arrRow.m_barcode = propRow.m_barcode;
      arrRow.s_barcode = propRow.s_barcode;
      arrRow.b_barcode = propRow.b_barcode;
      arrRow.remarkAll = propRow.remarkAll;
      arrRow.item_total = 0;
      arrRow.rowUnits = [];
      if (propRow.bunit && propRow.bunitSum) {
        let unitRow = {
          item_id: propRow.item_id,
          item_name: propRow.item_name,
          unit_no: propRow.bunit,
          wholesale_price: propRow.bpprice,
          contract_price: propRow.b_contract_price,
          quantity: propRow.bunitSum,
          unit_factor: propRow.bfactor,
          unit_type: "b",
          remark: propRow.bremark
        }
        this.sheet.sheetRows.push(unitRow)
        // arrRow.rowUnits.push();
        // arrRow.item_total += this.fix(
        //   Number(propRow.bpprice || 0) * Number(propRow.bunitSum)
        // );
      }
      if (propRow.munit && propRow.munitSum) {
        //arrRow.rowUnits.push();
        this.sheet.sheetRows.push({
          item_id: propRow.item_id,
          item_name: propRow.item_name,
          unit_no: propRow.munit,
          unit_type: "m",
          unit_factor: propRow.mfactor,
          quantity: propRow.munitSum,
          wholesale_price: propRow.mpprice,
          contract_price: propRow.m_contract_price,
          remark: propRow.mremark
        })
      }
      if (propRow.sunit && propRow.sunitSum) {
        this.sheet.sheetRows.push({
          item_id: propRow.item_id,
          item_name: propRow.item_name,
          unit_no: propRow.sunit,
          unit_type: "s",
          unit_factor: propRow.sfactor,
          quantity: propRow.sunitSum,
          wholesale_price: propRow.spprice,
          contract_price: propRow.s_contract_price,
          remark: propRow.sremark
        });
        // arrRow.item_total += this.fix(
        //   Number(propRow.spprice || 0) * Number(propRow.sunitSum)
        // );
      }
      return arrRow;
    },
    handleItemRowEdit(row, index) {
      if (!this.canEdit) return;
      this.editSheetRowsInfo = [row]
      this.popupEditSheetRows = true
      this.fromBranchList.some(e=>{
        if(e.branch_id == this.sheet.from_branch_id){
          this.fromBranchPositionSource = e.branch_position
          return
        }
      })
      this.toBranchList.some(e=>{
        if(e.branch_id == this.sheet.to_branch_id){
          this.toBranchPositionSource = e.branch_position
          return
        }
      })
      setTimeout(() => {
        this.$refs.editSheetRows.loadData(index)
      }, 350);

      // var propRow = this.row_UnitsArray2Props(row);
      // this.popupAddMoveSheetRow = true;
      // this.editingItemRowIndex = index;
      // this.$refs.dlgEditRow.loadData([propRow],this.editingItemRowIndex);
    },
    closeEdit() {
      this.$refs.editSheetRows.clearState()
      this.popupEditSheetRows = false
    },
    onAddRow_OK(rowList) {
      
      var propRow = rowList[0];
      var arrRow = this.row_UnitsProps2Array(propRow);
      this.itemRows[this.editingItemRowIndex] = arrRow;
      this.popupAddMoveSheetRow = false;
      sheetCache.saveCurSheetToCache(
        this,
        "unsubmitedSheets_DB" + this.moveType
      );
      this.updateTotal();
    },
    saveCurSheetToCache() {
      
      var sheet = this.sheet
      if (sheet&&!sheet.sheet_id)
        sheetCache.saveCurSheetToCache(
          this,
          "unsubmitedSheets_DB" + this.moveType
        );
    },
    delIndexItem(delIndexItem) {
      this.itemRows.splice(delIndexItem, 1)
      this.popupAddMoveSheetRow = false;
      sheetCache.saveCurSheetToCache(
        this,
        "unsubmitedSheets_DB" + this.moveType
      );
      this.updateTotal();
    },
    fillBySale(isFromReturn, forDisplayItems) {
      var params = {
        timeRange: this.timeRange,
        fromBanchID: this.sheet.from_branch_id,
        toBranchID: this.sheet.to_branch_id,
        isFromReturn: isFromReturn,
        forDisplayItems: forDisplayItems,
        startTime: this.sheet.startTime,
        endTime: this.sheet.endTime,
        branchPositions:""
      };
      let day = new Date()
      if (params.timeRange == "today") {
        params.startTime = day.format("yyyy-MM-dd")
        params.endTime = day.format("yyyy-MM-dd")
      }

      if (params.timeRange == "yesterday") {
        params.timeRange = "other"
        day.setDate(day.getDate() - 1)
        params.startTime = day.format("yyyy-MM-dd")
        params.endTime = day.format("yyyy-MM-dd")
      }


      AppSheetMoveGetFillItemsFromSale(params).then((res) => {
        if (res.result === "OK") {
          if (res.data.length == 0) {
            Toast.fail("暂无数据");
            return;
          }
          this.sheet.sheetRows = []
        
          for (const row of res.data) {
            /*
            try {
              var branchAllowNegative = window.branchAllowNegative(this.sheet.from_branch_name, this.sheet.sheetType)
              const currentQty = Number(row.s_qty);
              const stockQty = Number(row.from_stock_qty);
              if (currentQty > stockQty && !branchAllowNegative) {
                Dialog.confirm({
                  title: "库存不足",
                  message: `商品【${row.item_name}】数量（${currentQty}）超过当前库存（${stockQty}），是否继续添加？`,
                  width: "320px"
                });
                row.s_qty = stockQty;
              }
            } catch {
              continue;
            }
            */
            var b_qty = 0, m_qty = 0, s_qty = 0;
            var b_wholesale_price;

            if (row.b_unit_no && row.b_unit_factor) {
              b_qty = parseInt(Number(row.s_qty) / Number(row.b_unit_factor))
              s_qty = Number(row.s_qty) % Number(row.b_unit_factor)
              if (row.m_unit_no && row.m_unit_factor) {
                m_qty = parseInt(Number(s_qty) / Number(row.m_unit_factor))
                s_qty = Number(s_qty) % Number(row.m_unit_factor)
              }
              if (row.b_wholesale_price)
                b_wholesale_price = Number(row.b_wholesale_price);
            }
            else {
              s_qty = Number(row.s_qty);
            }
            let moveRow = {
              item_id: row.item_id,
              b_unit_no: row.b_unit_no,
              b_unit_factor: row.b_unit_factor,
              b_wholesale_price: row.b_wholesale_price,
              b_contract_price: row.b_contract_price || 0,
              b_barcode: row.b_barcode,
              s_unit_no: row.s_unit_no,
              s_unit_factor: row.s_unit_factor,
              s_barcode: row.s_barcode,
              s_wholesale_price: row.s_wholesale_price,
              s_contract_price: row.s_contract_price || 0,
              m_unit_no: row.m_unit_no,
              m_unit_factor: row.m_unit_factor,
              m_wholesale_price: row.m_wholesale_price,
              m_contract_price: row.m_contract_price || 0,
              m_barcode: row.m_barcode,
              item_name: row.item_name,
              item_total: 0,
              remark: '',
              rowUnits: [],
              from_stock_qty : row.from_stock_qty,
              to_stock_qty : row.to_stock_qty,
              bfactor : row.b_unit_factor,
              bunit : row.b_unit_no,
              mfactor : row.m_unit_factor,
              munit : row.m_unit_no,
              sfactor : row.s_unit_factor,
              sunit : row.s_unit_no,
              batch_id: row.batch_id,
              produce_date: row.produce_date,
              batch_no: row.batch_no,
              from_branch_position: row.from_branch_position,
              from_branch_position_name: row.from_branch_position_name,
              to_branch_position: row.to_branch_position,
              to_branch_position_name: row.to_branch_position_name,
            };
            var unitRow;
            if (b_qty > 0) {
              unitRow = {
                item_id: row.item_id,
                classId: row.classid,
                item_name: row.item_name,
                b_unit_no: row.b_unit_no,
                b_unit_factor: row.b_unit_factor,
                b_wholesale_price: row.b_wholesale_price,
                b_contract_price: row.b_contract_price,
                b_barcode: row.b_barcode,
                s_unit_no: row.s_unit_no,
                s_unit_factor: row.s_unit_factor,
                s_barcode: row.s_barcode,
                s_wholesale_price: row.s_wholesale_price,
                m_unit_no: row.m_unit_no,
                m_unit_factor: row.m_unit_factor,
                m_wholesale_price: row.m_wholesale_price,
                m_contract_price: row.m_contract_price,
                m_barcode: row.m_barcode,
                unit_no: row.b_unit_no,
                unit_type: "b",
                wholesale_price: b_wholesale_price || 0,
                contract_price: Number(row.b_contract_price)||0,
                quantity: b_qty,
                unit_factor: row.b_unit_factor,
                remark: "",
                from_stock_qty : row.from_stock_qty,
                to_stock_qty : row.to_stock_qty,
                bfactor : row.b_unit_factor,
                bunit : row.b_unit_no,
                mfactor : row.m_unit_factor,
                munit : row.m_unit_no,
                sfactor : row.s_unit_factor,
                sunit : row.s_unit_no,
                batch_id: row.batch_id,
                produce_date: row.produce_date,
                batch_no: row.batch_no,
                from_branch_position: row.from_branch_position,
                from_branch_position_name: row.from_branch_position_name,
                to_branch_position: row.to_branch_position,
                to_branch_position_name: row.to_branch_position_name,
              };
              
              this.sheet.sheetRows.push(unitRow);

            }
            if (m_qty > 0) {
              unitRow = {
                item_id: row.item_id,
                classId: row.classid,
                item_name: row.item_name,
                b_unit_no: row.b_unit_no,
                b_unit_factor: row.b_unit_factor,
                b_wholesale_price: row.b_wholesale_price,
                b_contract_price: row.b_contract_price,
                b_barcode: row.b_barcode,
                s_unit_no: row.s_unit_no,
                s_unit_factor: row.s_unit_factor,
                s_barcode: row.s_barcode,
                s_wholesale_price: row.s_wholesale_price,
                s_contract_price: row.s_contract_price,
                m_unit_no: row.m_unit_no,
                m_unit_factor: row.m_unit_factor,
                m_wholesale_price: row.m_wholesale_price,
                m_contract_price: row.m_contract_price,
                m_barcode: row.m_barcode,
                unit_no: row.m_unit_no,
                unit_type: "m",
                wholesale_price: row.m_wholesale_price || 0,
                contract_price: Number(row.m_contract_price)||0,
                quantity: m_qty,
                unit_factor: row.m_unit_factor,
                remark: "",
                from_stock_qty : row.from_stock_qty,
                to_stock_qty : row.to_stock_qty,
                bfactor : row.b_unit_factor,
                bunit : row.b_unit_no,
                mfactor : row.m_unit_factor,
                munit : row.m_unit_no,
                sfactor : row.s_unit_factor,
                sunit : row.s_unit_no,
                batch_id: row.batch_id,
                produce_date: row.produce_date,
                batch_no: row.batch_no,
                from_branch_position: row.from_branch_position,
                from_branch_position_name: row.from_branch_position_name,
                to_branch_position: row.to_branch_position,
                to_branch_position_name: row.to_branch_position_name,
              };
              this.sheet.sheetRows.push(unitRow);
            }
            if (s_qty > 0) {
              unitRow = {
                item_id: row.item_id,
                classId: row.classid,
                item_name: row.item_name,
                b_unit_no: row.b_unit_no,
                b_unit_factor: row.b_unit_factor,
                b_wholesale_price: row.b_wholesale_price,
                b_contract_price: row.b_contract_price,
                b_barcode: row.b_barcode,
                s_unit_no: row.s_unit_no,
                s_unit_factor: row.s_unit_factor,
                s_barcode: row.s_barcode,
                s_wholesale_price: row.s_wholesale_price,
                s_contract_price: row.s_contract_price,
                m_unit_no: row.m_unit_no,
                m_unit_factor: row.m_unit_factor,
                m_wholesale_price: row.m_wholesale_price,
                m_contract_price: row.m_contract_price,
                m_barcode: row.m_barcode,
                unit_no: row.s_unit_no,
                wholesale_price: row.s_wholesale_price || 0,
                contract_price: Number(row.s_contract_price)||0,
                quantity: s_qty,
                unit_factor: 1,
                unit_type: "s",
                remark: "",
                from_stock_qty : row.from_stock_qty,
                to_stock_qty : row.to_stock_qty,
                bfactor : row.b_unit_factor,
                bunit : row.b_unit_no,
                mfactor : row.m_unit_factor,
                munit : row.m_unit_no,
                sfactor : row.s_unit_factor,
                sunit : row.s_unit_no,
                batch_id: row.batch_id,
                produce_date: row.produce_date,
                batch_no: row.batch_no,
                from_branch_position: row.from_branch_position,
                from_branch_position_name: row.from_branch_position_name,
                to_branch_position: row.to_branch_position,
                to_branch_position_name: row.to_branch_position_name,
              };
              this.sheet.sheetRows.push(unitRow);
            }

          };
          this.updateTotal();
        } else {
          Toast.fail(res.msg);
        }
      });
    },
    btnFillByStock_click() {
      if (this.handleCalendarToast()) {
        var params = {
          fromBanchID: this.sheet.from_branch_id,
          toBranchID: this.sheet.to_branch_id,
          fromBranchPosition:this.fromBranchPositions,
          from_branch_position_name:this.fromBranchPositionNames,
          toBranchPosition:this.toBranchPositions,
          to_branch_position_name:this.toBranchPositionNames,
        }

        AppSheetMoveGetFillItemsFromStock(params).then((res) => {
          if (res.result === "OK") {
            if (res.data.length == 0) {
              Toast.fail("暂无数据");
              return;
            }
            this.sheet.sheetRows = []
            res.data.forEach(row => {
              var b_qty = 0, m_qty = 0, s_qty = 0;
              var b_wholesale_price;
              if (row.b_unit_no && row.b_unit_factor) {
                b_qty = parseInt(Number(row.s_qty) / Number(row.b_unit_factor));
                // 23.4 % 3.9 js计算数据有问题，进行笨办法处理
                s_qty = Number(row.b_unit_factor) - (Number(row.s_qty) % Number(row.b_unit_factor)) <= 0.0001 
                        ? 0 
                        : (Number(row.s_qty) % Number(row.b_unit_factor))
                if (row.m_unit_no && row.m_unit_factor) {
                  m_qty = parseInt(Number(s_qty) / Number(row.m_unit_factor))
                  s_qty = Number(row.m_unit_factor) - (Number(s_qty) % Number(row.m_unit_factor)) <= 0.0001 
                        ? 0 
                        : (Number(row.s_qty) % Number(row.m_unit_factor))
                }
                if (row.b_wholesale_price)
                  b_wholesale_price = Number(row.b_wholesale_price);

              } else {
                s_qty = Number(row.s_qty);
              }
              var unitRow;
              if (b_qty > 0) {
                unitRow = {
                  item_id: row.item_id,
                  item_name: row.item_name,
                  b_unit_no: row.b_unit_no,
                  b_unit_factor: row.b_unit_factor,
                  b_wholesale_price: row.b_wholesale_price,
                  b_barcode: row.b_barcode,
                  s_unit_no: row.s_unit_no,
                  s_unit_factor: row.s_unit_factor,
                  s_barcode: row.s_barcode,
                  s_wholesale_price: row.s_wholesale_price,
                  m_unit_no: row.m_unit_no,
                  m_unit_factor: row.m_unit_factor,
                  m_wholesale_price: row.m_wholesale_price,
                  m_barcode: row.m_barcode,
                  unit_no: row.b_unit_no,
                  unit_type: "b",
                  wholesale_price: b_wholesale_price || 0,
                  quantity: b_qty,
                  unit_factor: row.b_unit_factor,
                  remark: "",
                  classId: row.classid,
                  from_stock_qty : row.from_stock_qty,
                  to_stock_qty : row.to_stock_qty,
                  bfactor : row.b_unit_factor,
                  bunit : row.b_unit_no,
                  mfactor : row.m_unit_factor,
                  munit : row.m_unit_no,
                  sfactor : row.s_unit_factor,
                  sunit : row.s_unit_no,
                  batch_id:row.batch_id,
                  produce_date:row.produce_date,
                  batch_no:row.batch_no,
                  from_branch_position:row.from_branch_position,
                  from_branch_position_name:row.from_branch_position_name,
                  to_branch_position:params.to_branch_position,
                  to_branch_position_name:params.to_branch_position_name,
                }
                
                this.sheet.sheetRows.push(unitRow);

              }
              if (m_qty > 0) {
                unitRow = {
                  item_id: row.item_id,
                  item_name: row.item_name,
                  b_unit_no: row.b_unit_no,
                  b_unit_factor: row.b_unit_factor,
                  b_wholesale_price: row.b_wholesale_price,
                  b_barcode: row.b_barcode,
                  s_unit_no: row.s_unit_no,
                  s_unit_factor: row.s_unit_factor,
                  s_barcode: row.s_barcode,
                  s_wholesale_price: row.s_wholesale_price,
                  m_unit_no: row.m_unit_no,
                  m_unit_factor: row.m_unit_factor,
                  m_wholesale_price: row.m_wholesale_price,
                  m_barcode: row.m_barcode,
                  unit_no: row.m_unit_no,
                  unit_type: "m",
                  wholesale_price: row.m_wholesale_price || 0,
                  quantity: m_qty,
                  unit_factor: row.m_unit_factor,
                  remark: "",
                  classId: row.classid,
                  from_stock_qty : row.from_stock_qty,
                  to_stock_qty : row.to_stock_qty,
                  bfactor : row.b_unit_factor,
                  bunit : row.b_unit_no,
                  mfactor : row.m_unit_factor,
                  munit : row.m_unit_no,
                  sfactor : row.s_unit_factor,
                  sunit : row.s_unit_no,
                  batch_id:row.batch_id,
                  produce_date:row.produce_date,
                  batch_no:row.batch_no,
                  from_branch_position:row.from_branch_position,
                  from_branch_position_name:row.from_branch_position_name,
                  to_branch_position:params.to_branch_position,
                  to_branch_position_name:params.to_branch_position_name,
                };
                this.sheet.sheetRows.push(unitRow);
              }

              if (s_qty > 0) {
                unitRow = {
                  item_id: row.item_id,
                  item_name: row.item_name,
                  unit_no: row.s_unit_no,
                  b_unit_no: row.b_unit_no,
                  b_unit_factor: row.b_unit_factor,
                  b_wholesale_price: row.b_wholesale_price,
                  b_barcode: row.b_barcode,
                  s_unit_no: row.s_unit_no,
                  s_unit_factor: row.s_unit_factor,
                  s_barcode: row.s_barcode,
                  s_wholesale_price: row.s_wholesale_price,
                  m_unit_no: row.m_unit_no,
                  m_unit_factor: row.m_unit_factor,
                  m_wholesale_price: row.m_wholesale_price,
                  m_barcode: row.m_barcode,
                  unit_type: "s",
                  wholesale_price: row.s_wholesale_price || 0,
                  quantity: s_qty,
                  unit_factor: 1,
                  remark: "",
                  classId: row.classid,
                  from_stock_qty : row.from_stock_qty,
                  to_stock_qty : row.to_stock_qty,
                  bfactor : row.b_unit_factor,
                  bunit : row.b_unit_no,
                  mfactor : row.m_unit_factor,
                  munit : row.m_unit_no,
                  sfactor : row.s_unit_factor,
                  sunit : row.s_unit_no,
                  batch_id:row.batch_id,
                  produce_date:row.produce_date,
                  batch_no:row.batch_no,
                  from_branch_position:row.from_branch_position,
                  from_branch_position_name:row.from_branch_position_name,
                  to_branch_position:params.to_branch_position,
                  to_branch_position_name:params.to_branch_position_name,
                };
                this.sheet.sheetRows.push(unitRow);

              }


            });

            this.getSheet()
          } else {
            Toast.fail(res.msg);
          }
        });
      }
    },
    handleRemoveRowClick(index) {
      Dialog.confirm({
        title: "删除数据",
        message: "请确认是否删除本条数据?",
        width:"320px"
      }).then(() => {
        this.sheet.sheetRows.splice(index, 1);
        sheetCache.saveCurSheetToCache(
          this,
          "unsubmitedSheets_DB" + this.moveType
        );
        this.updateTotal();

      })
    },
    updateTotalUnitObj(totalUnitObj, unit, quantity) {

      if (unit.unit_type === "s") {
        if (typeof totalUnitObj[unit.unit_no] === "undefined") {
          totalUnitObj[unit.unit_no] = quantity;
        } else {
          totalUnitObj[unit.unit_no] += quantity;
        }
      }
      if (unit.unit_type === "b") {


        if (typeof totalUnitObj[unit.unit_no] === "undefined") {
          totalUnitObj[unit.unit_no] = quantity;

        } else {
          totalUnitObj[unit.unit_no] += quantity;
        }
      }
      if (unit.unit_type === "m") {
        if (typeof totalUnitObj[unit.unit_no] === "undefined") {
          totalUnitObj[unit.unit_no] = quantity;
        } else {
          totalUnitObj[unit.unit_no] += quantity;
        }
      }
      return totalUnitObj;
    },
    transformTotalUnitObjectToList(totalUnitObj) {
      const unitList = [];
      Object.keys(totalUnitObj).forEach((key) => {
        unitList.push({
          key,
          value: totalUnitObj[key],
        });
      });
      return unitList;
    },
    calTotalUnitAmount() {
      let totalUnitObj = {};
      this.sheet.sheetRows.forEach((item) => {
        const quantity = Number(item.quantity);
        totalUnitObj = this.updateTotalUnitObj(totalUnitObj, item, quantity);

      });
      return this.transformTotalUnitObjectToList(totalUnitObj);
    },
    updateTotal() {
      var total = 0;
      var totalContract = 0
      var totalWeight = 0;
      this.sheet.sheetRows.forEach((item) => {
        total += Number(item.wholesale_price) * Number(item.quantity);
        if (!item.contract_price) item.contract_price = 0
        totalContract += Number(item.contract_price) * Number(item.quantity);
        totalWeight += Number(item.unit_weight) * Math.abs(Number(item.quantity));
      });
      this.sheet.total_units_amount = this.calTotalUnitAmount();
      this.sheet.total_amount = toMoney(total);
      this.sheet.wholesale_amount = toMoney(total);
      this.sheet.contract_amount = toMoney(totalContract || '0');
      this.sheet.total_weight = parseFloat(totalWeight || '0').toFixed(3)
    },
    onSearchInputKeyDown(e) {
      if (e.keyCode === 13) {
        this.queryScan()

      }
      //  console.log('keydown',e)
    },
    onAddClass() {
      if (this.sheet.from_branch_id && this.sheet.to_branch_id) {
        let obj = {
          searchStr: this.transStr,
          fromBranchID: this.sheet.from_branch_id,
          toBranchID: this.sheet.to_branch_id,
          sheet: this.sheet,
          moveType: this.moveType,
          fromBranchPositionSource: this.fromBranchPositionSource,
          toBranchPositionSource: this.toBranchPositionSource,
        };
        window.g_moveSheet = this
        this.$store.commit("currentSheet", this.sheet);
        this.$router.push({ path: "/SelectMoveItems", query: obj });
        this.transStr = "";
      } else {
        Toast.fail("请选择仓库");
      }
    },
    showSheetsFromCache() {
      if (
        this.$store.state["unsubmitedSheets_DB" + this.moveType] &&
        this.$store.state["unsubmitedSheets_DB" + this.moveType].length > 0
      ) {
        this.showUnsubmitedSheets = true;
        this.unsubmitedSheets = this.$store.state[
          "unsubmitedSheets_DB" + this.moveType
        ];

        for(let i=0;i<this.unsubmitedSheets.length;i++){
          if(this.fromBranchList){
            let nowbranchinfo=this.fromBranchList.find(r=>r.branch_id==this.unsubmitedSheets[i].from_branch_id);
            if(nowbranchinfo && nowbranchinfo.branch_name!=this.unsubmitedSheets[i].from_branch_name){//如果仓库名改了，更新缓存中的仓库名，以便打印出来是新仓库名
              this.unsubmitedSheets[i].from_branch_name=nowbranchinfo.branch_name;
            }
          }
          if(this.toBranchList){
            let nowbranchinfo=this.toBranchList.find(r=>r.branch_id==this.unsubmitedSheets[i].to_branch_id);
            if(nowbranchinfo && nowbranchinfo.branch_name!=this.unsubmitedSheets[i].to_branch_name){//如果仓库名改了，更新缓存中的仓库名，以便打印出来是新仓库名
              this.unsubmitedSheets[i].to_branch_name=nowbranchinfo.branch_name;
            }
          }
        }

      }
    },
    addItems(newRows) {
      newRows.forEach(item => {
        this.row_UnitsProps2Array(item);
      })
    },
    WarehouseSelectDatas(obj) {
      this.srcDatas = obj;
      this.warehouseSelect = obj[1].branch_id;
      this.branch_name = obj[1].branch_name;
    },
    onFromBranchSelected(value) {
      this.sheet.from_branch_name = value.branch_name;
      this.sheet.from_branch_id = value.branch_id;
      this.popFromBranchList = false;
      this.fromBranchList.some(b=>{
        if(b.branch_id== this.sheet.from_branch_id.toString()){
          if(b.branch_position.length){
            let branchPositionList = JSON.parse(JSON.stringify(b.branch_position))
            branchPositionList.push({
              branch_position:"0",
              branch_position_name:'默认库位',
              type_id:"",
            })
            this.fromBranchPositionSource = branchPositionList
            console.log({"onFromBranchSelected":this.fromBranchPositionSource})
          }
          this.$store.commit("setCurFromBranchPositionList",b.branch_position)
        }
      })
      if (this.$store.state.operInfo.companyID) {
        var sheetBranches = window.getCompanyStoreValue("c_sheetBranches", {})
        var moveType = this.moveType || 'move'
        sheetBranches[moveType + 'From'] = value
        window.setCompanyStoreValue("c_sheetBranches", sheetBranches)
      }
      else {
        if (this.moveType == "moveOut")
          this.$store.commit("moveOutFromBranch", value);
        else if (this.moveType == "moveIn")
          this.$store.commit("moveInFromBranch", value);
      }

    },
    onToBranchSelected(value) {
      this.sheet.to_branch_id = value.branch_id;
      this.sheet.to_branch_name = value.branch_name;
      this.popToBranchList = false;
      if (this.$store.state.operInfo.companyID) {
        var sheetBranches = window.getCompanyStoreValue("c_sheetBranches", {})
        var moveType = this.moveType || 'move'
        sheetBranches[moveType + 'To'] = value
        window.setCompanyStoreValue("c_sheetBranches", sheetBranches)
      }
      else {
        if (this.moveType == "moveOut")
          this.$store.commit("moveOutToBranch", value);
        else if (this.moveType == "moveIn")
          this.$store.commit("moveInToBranch", value);
      }
      this.toBranchList.some(b=>{
        if(b.branch_id.toString() == this.sheet.to_branch_id.toString()){
          if(b.branch_position.length){
            let branchPositionList = JSON.parse(JSON.stringify(b.branch_position))
            branchPositionList.push({
              branch_position:"0",
              branch_position_name:'默认库位',
              type_id:"",
            })
            this.toBranchPositionSource = branchPositionList
          }
          this.$store.commit("setCurToBranchPositionList",b.branch_position)
        }
      })

    },
    onPayment(obj) {
      this.paymentSelects = obj;
      this.PaymentPopup = true;
    },
    onConfirmPayment(value) {
      this.PaymentPopup = false;
      let objs = {
        ids: value.sub_id,
        titles: value.sub_name,
      };
      if (this.paymentSelects === "payment1") {
        if (this.payment1.ids !== value.sub_id) this.payment2 = objs;
      } else {
        if (this.payment2.ids !== value.sub_id) this.payment1 = objs;
      }
    },
    /*
    onRrears(value){
      this.qk = value;
      let objs = {
        discount:this.yh,
        arrears:this.qk
      }
      this.bindSalesDats = objs
    },
    onDiscount(value){
      this.yh = value;
      let objs = {
        discount:this.yh,
        arrears:this.qk
      }
      this.bindSalesDats = objs
    },*/
    btnCopySheet_click() {
      Dialog.confirm({
        title: "复制单据",
        message: "请确认是否复制?",
        width:"320px"
      }).then(() => {
        this.doCopySheet()
        Toast("复制成功");
      })

    },
    doCopySheet() {
      this.sheet.sheet_id = "";
      this.sheet.sheet_no = "";
      this.sheet.approve_time = "";
      this.sheet.approver_id = "";
      this.sheet.happen_time = "";
      this.sheet.make_time = "";
      this.sheet.maker_id = "";
      this.sheet.red_flag = "";
      sheetCache.saveCurSheetToCache(
        this,
        "unsubmitedSheets_DB" + this.moveType,
        3
      )
    },
    onEmpty() {
      Dialog.confirm({
        title: "清空单据",
        message: "请确认是否清空单据?",
        width:"320px"
      }).then(() => {
        this.$store.commit("itemList", []);
        this.$store.commit("productist", []);
        this.sheet.sheetRows = [];
        this.sheet.total_amount = 0;
        this.sheet.wholesale_amount = 0;
        this.sheet.total_weight = 0;
        sheetCache.removeCurSheetFromCache(
          this,
          "unsubmitedSheets_DB" + this.moveType
        );
        Toast.success("已清空");
        this.salesPopup = false;
      })

    },
    btnOut() {
      myGoBack(this.$router);
    },
    goback() {
      myGoBack(this.$router);
      this.$store.commit("classId", this.$store.state.AllItemClassId);
    },
    // bindtotalsUpdata(obj){
    //   let attrDatas = []
    //   obj.datas.map(item=>{
    //     let objs = {
    //       item_id: item.item_id,
    //       item_name: item.item_name,
    //       unit_no: item.unit_no,
    //       real_price: item.real_price? item.real_price:0,
    //       quantity: item.quantity,
    //       unit_factor:item.unit_factor,
    //       sub_amount: item.sub_amount,
    //       remark: item.remarkName,
    //     }
    //     attrDatas.push(objs)
    //   })
    //   this.totalsDatas.totles = obj.totles?obj.totles:0
    //   this.totalsDatas.datas = attrDatas
    // },
    getSheet() {
   
      var sheet = this.sheet;
      sheet.operKey = this.$store.state.operKey;
      sheet.sheetType = "DB";
      //sheet.sheetRows = sheetRows;
      if (!sheet.maker_name) {
        sheet.maker_name = this.$store.state.operInfo.oper_name;
      }
      if (!sheet.maker_id) {
        sheet.maker_id = this.$store.state.operInfo.oper_id;
      }
      this.updateTotal();
      return sheet;
    },
    btnSubmit_click() {
      if (!this.itemRows && this.itemRows.length <= 0) {
        Toast.fail("请添加商品");
        return;
      }
      this.m_bShowSubmitPopup = true;
    },
    btnConfirmSave_click() {
      this.doSave() 
    },
  async doSave(cbDone) {
      
      const message = this.handleCheckSheet()
      if (message) { 
        Toast.fail(message)
        return
      }
      var sheet = this.getSheet();
      if (sheet.from_branch_id === sheet.to_branch_id) { 
        Toast.fail("出仓与入仓相同,请重新选择");
        return;
      }

      if (!sheet.sheetRows.length) { 
        Toast.fail("请添加调拨商品");
        return;
      }
     
      var limitMaxVanStockOnMove = window.getRightValue('delicacy.limitMaxVanStockOnMove.value') === 'true';
      if(limitMaxVanStockOnMove && this.moveType == "moveOut")
      {
          const res =  await ApiCheckVanStockOverLoad(sheet)
          if (res.result != 'OK')
          {
             Toast.fail({ message: res.msg, duration: 6000})
             return
          }
      }

      this.IsSubmiting = true;
      let res = await ApiSheetMoveSave(sheet)
      this.IsSubmiting = false;
      if (res.result === "OK") { 
          sheet.sheet_no = res.sheet_no;
          sheet.sheet_id = res.sheet_id;
          sheet.make_time = res.currentTime;
          sheetCache.removeCurSheetFromCache(
            this,
            "unsubmitedSheets_DB" + this.moveType
          );
          Toast.success("提交成功");
          setTimeout(()=>{
            this.sheetHasChange=false
            if(cbDone) cbDone()
          },100)
       
      } else { 
        Toast.fail(res.msg);
      }
  
      
    },
     
  async btnConfirmApprove_click(){   
     
      Dialog.confirm({
        title: "审核单据",
        message: "确认审核吗?",
        width:"320px"
      }).then(async () => {
        
        const message = this.handleCheckSheet()
        if (message) { 
          Toast.fail(message)
          return
        }
        var sheet = this.getSheet();
        if (sheet.from_branch_id === sheet.to_branch_id) { 
          Toast.fail("出仓与入仓相同,请重新选择");
          return;
        }
        if (!sheet.approver_name) {
          sheet.approver_name = this.$store.state.operInfo.oper_name;
        }
        this.IsSubmiting = true

        var limitMaxVanStockOnMove = window.getRightValue('delicacy.limitMaxVanStockOnMove.value') === 'true';
        if(limitMaxVanStockOnMove && this.moveType == "moveOut")
        {
            const res =  await ApiCheckVanStockOverLoad(sheet)
            if (res.result != 'OK') 
            {
              this.IsSubmiting = false
              Toast.fail({ message: res.msg, duration: 5000})
              return
            }
        }

        AppSheetMoveSubmit(sheet).then((res) => {
          this.IsSubmiting = false;
          if (res.result === "OK") { 
            sheet.sheet_no = res.sheet_no;
            sheet.sheet_id = res.sheet_id;
            sheet.make_time = res.currentTime;
            this.sheet.approve_time = res.approve_time;
            if (window.g_curSheetInList) {
              // window.g_curSheetInList.approve_time=res.approve_time
              window.g_curSheetInList.state = "approved";
            }
            sheetCache.removeCurSheetFromCache( this,
              "unsubmitedSheets_DB" + this.moveType
            );

            
            Toast.success("审核成功");
            setTimeout(() => {//延时是为了等 watch sheet 被调用  
              this.sheetHasChange = false
            }, 100);
           
          } else {
           
            Toast.fail(res.msg);
          }
        });
      })

    },
    handleCheckSheet() {
      let message = ""
      for (let i = 0; i < this.sheet.sheetRows.length; i++) {
        let sheetRow = this.sheet.sheetRows[i]
        // 如果为空，那么会报错，进行处理
        sheetRow.from_stock_qty = Number(sheetRow.from_stock_qty)
        sheetRow.to_stock_qty = Number(sheetRow.to_stock_qty)
        if (sheetRow.quantity === "" || sheetRow.quantity === null || sheetRow.quantity === undefined) {
          message += `第${i + 1}行商品，数量未录入`
        }
        if (sheetRow.batch_level && !sheetRow.produce_date) {
          message += `第${i + 1}行商品，未指定生产日期`
        }
      }
      return message

    },
    // btnRedAndModify_click() {
    //   this.redBill(true)
    // },
     
     btnRedAndModify_click() {
      var action = '冲改'
      $('#red-brief').val('')
      var redSheetNoBrief = window.getRightValue('delicacy.redSheetNoBrief.value')
      redSheetNoBrief = redSheetNoBrief == 'true'
      if(!redSheetNoBrief)
      {
        Dialog.confirm({
          allowHtml: true,
          title: `输入${action}原因`,
          // message: '<van-field v-model="redBrief" type="tel" placeholder="请输入冲改原因" />',
          message: `<div><input id="red-brief" style="border-radius:4px;border:1px solid #ddd;line-height:40px;" v-model="redBrief" placeholder="${action}原因" /></div>`,
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          showCancelButton: true,
          width: '320px'
        }).then(() => {

          var redBrief = $('#red-brief').val()
          
          if (!redBrief) {
            Toast.fail(`请输入${action}原因`)
            return
          }
          redBrief = `${action}原因: ${redBrief}`
          this.doRedAndModify(redBrief)
        
          this.m_bShowSubmitPopup = false

        

        }).catch(() => {

        })
      }
      else{
        this.doRedAndModify('')
      }
         
    },
    doRedAndModify(redBrief){
         this.sheet.make_brief+=redBrief
         this.sheet.isRedAndChange = true
         this.sheet.old_sheet_id = this.sheet.sheet_id
         this.sheet.sheet_id = ""
 
         this.sheet.approve_time = ""
         this.sheet.approver_id = ""
         this.sheet.approver_name = ""

         this.sheet.make_time = ""
         this.sheet.maker_id = ""
         this.sheet.maker_name = ""
    },
    btnRed_click() {
      this.redBill()
    },
    redBill() {
      var action = '红冲'
     
      Dialog.confirm({
        allowHtml: true,
        title: `输入${action}原因`,
        message: `<div><input id="red-brief" style="border-radius:4px;border:1px solid #ddd;line-height:40px;" v-model="redBrief" placeholder="${action}原因" /></div>`,
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        showCancelButton: true,
        width:"320px"
      }).then(() => {

        var redBrief = $('#red-brief').val()
        if (!redBrief) {
          Toast.fail(`请输入${action}原因`)
          return
        }
        let params = {
          operKey: this.$store.state.operKey,
          sheetID: this.sheet.sheet_id,
        };
        SheetMoveRed(params).then((res) => {
          if (res.result === "OK") {
            this.sheet.red_flag = "1";
            
              if (this.sheet.make_brief) this.sheet.make_brief += ' '
              this.sheet.make_brief += redBrief
           
            if (window.g_curSheetList) {
              var redSheet = JSON.parse(JSON.stringify(this.sheet));
              redSheet.sheet_id = res.sheet_id;
              redSheet.happen_time = res.happen_time;
              redSheet.oper_name = this.$store.state.operInfo.oper_name;
              redSheet.f_branch = this.sheet.from_branch_name;
              redSheet.t_branch = this.sheet.to_branch_name;
              redSheet.wholesale_amount = this.sheet.wholesale_amount;
              redSheet.state = "red";
              window.g_curSheetList.unshift(redSheet);
              window.g_curSheetInList.state = "reded";
            }
          
              Toast.success("红冲成功")
          

          } else {
            Toast.fail("红冲失败:" + res.msg);
          }
        });
      })
    },
    showPrinterSelectionPage() {
      this.PopupPrintersSelect = true
    },
    hidePrinterSelectionPage() {
      this.PopupPrintersSelect = false
    },
    confirmPrinterSelectionChange(selectedPrinter) {
      this.useTmp = selectedPrinter.useTemplate
      this.printers.forEach((prt) => {
        if (prt.id === selectedPrinter.id) {
          prt.isDefault = true
        } else {
          prt.isDefault = false
        }
      })
      window.setCompanyStoreValue('c_printers', this.printers)
      // Toast.success('修改成功!')
      this.defaultPrinter = selectedPrinter
      for (let _i = 0; _i < this.printers.length; _i++) {
        const _printer = this.printers[_i]
        if (_printer.isDefault) {
          this.defaultPrinterIndex = _i;
          break;
        }
      }

      this.PopupPrintersSelect = false

      const printer_kind = selectedPrinter.kind
      if (this.useTmp) {
        if (this.templatesLoaded) {
          this.confirmTemplates(printer_kind)
        } else {
          this.loadPrintTemplates((data) => {
            this.confirmTemplates(printer_kind)
          })
        }
      }
    },
    /** 将模板加载到this.tmplist中,并执行验证 */
    confirmTemplates(printer_kind) {
      this.tmplist = printer_kind == 'tiny' ? this.printTemplatesTiny : this.printTemplates
      if (this.tmplist.length == 0) {
        var err = printer_kind == 'tiny' ? '没有可用的小票模板' : '没有可用的打印模板'
        Toast.fail(err); return
      }
    },
    loadPrintTemplates(successCb) {
      this.tmplist = []
      this.printTemplates = []
      this.printTemplatesTiny = []
      var params = {
        sheetType: this.sheet.sheet_type,
        clientID: this.sheet.supcust_id
      }
      AppGetTemplate(params).then(data => {
        var templateList = data.templateList
        for (let i = 0; i < templateList.length; i++) {
          const template = templateList[i]
          var inserttmp = {
            name: template.template_name,
            i: i,
            tmp: template
          }
          this.printTemplates.push(inserttmp)
          try {
            let tmp_tocheck = JSON.parse(template.template_content)
            if (tmp_tocheck.width <= 110) {
              this.printTemplatesTiny.push(inserttmp)
            }
          } catch {
            console.error('在解析模板的宽度时发生错误,inserttmp:', inserttmp)
          }
        }
        this.templatesLoaded = true
        if (successCb) {
          successCb(data)
        }
      })
    },
    btnPrint_ByTemplate(tmp,printCount) {
      console.log("打印按模板打印")
      if (this.sheet.sheet_id == "") {
        Toast.fail("请先保存单据");
        return;
      }

      const printErrorHandler = (error) => {
        handlePrintComplete()
        Toast.fail(error)
      }
      const handlePrintComplete = () => {
        this.loadingMsg = ''
        this.isPrinting = false
      }
      const defaultPrinter = window.getDefaultPrinter()
       // 为打印机类型赋默认值(如果未定义)
       var printer_kind = defaultPrinter.kind
      if (!printer_kind) {
        if (defaultPrinter.type == "cloud")
          printer_kind = '24pin'
        else
          printer_kind = 'tiny'
      }
      // 组装单据校验的请求参数
      this.isPrinting = true

      var sTmp = tmp.template_content
      tmp = JSON.parse(tmp.template_content);
      var printTemplate = []

      if (sTmp.indexOf('"prepay_balance"')) printTemplate.push({ name: "prepay_balance" })
      if (sTmp.indexOf('"arrears_balance"')) printTemplate.push({ name: "arrears_balance" })
      if (sTmp.indexOf('"print_count"')) printTemplate.push({ name: "print_count" })

      var smallUnitBarcode = this.printBarcodeStyle === 'smallUnit'

      //get sheet
      var params = {
        sheetType: this.sheet.sheetType,
        sheet_type: this.sheet.sheet_type,

        //operKey: this.$store.state.operKey,
        sheet_id: this.sheet.sheet_id,
        smallUnitBarcode: smallUnitBarcode,
        printTemplate: JSON.stringify(printTemplate)
      }
      // 使用POST方法避免printTemplate参数过长导致的问题
      AppGetSheetToPrint_Post(params).then(data => {
        if (data.result != 'OK') {
          Toast.fail(data.msg)
        }
        else {
          var sheet = data.sheet

          // start print
         
          if (defaultPrinter.type === "cloud") {
            var params = {
              operKey: this.$store.state.operKey,
              tmp: tmp,
              sheet: sheet,
              printer_brand: defaultPrinter.brand,
              device_id: defaultPrinter.cloudID,
              check_code: defaultPrinter.cloudCode,
              cus_orderid: this.sheet.sheet_no,
              copies: "1"
            }
            AppCloudPrint_sheetTmp(params).then(res => {
              console.log(res);
              if (res.result == "OK") {
                Toast.success("打印成功,请等待");
              }
              else if (res.return_msg == "打印成功" || (!res.printer_state && !res.return_msg)) {//兼容写法，适应老版本，要去掉
                Toast.success("打印成功,请等待");
              }
              else {
                if (res.msg) {
                  Toast.fail(res.msg);
                }
                else {
                  var result = res.printer_state ? res.printer_state : res.return_msg
                  Toast.fail(result);
                }

              }
            })
          }
          else 
          {

           
            // 蓝牙打印

            let must24Pin = false // 必须使用针式打印机的图片打印指令
            let ignorePaperFeedCmd = false // 跳过换行、走纸、换页命令
            const pname = defaultPrinter.name
            // 2023.02.13 兼容ZICOX-CC4打印机
            if (pname.match('CC4_')) { // CC4_1656L
              ignorePaperFeedCmd = true
              must24Pin = true
            }

            var params = {
              operKey: this.$store.state.operKey,
              tmp: tmp,
              sheet: sheet,
              ignorePaperFeedCmd: ignorePaperFeedCmd
            }



            Printing.initPrinter()
            if (Printing.cmdMode === '_cpcl') {
              console.warn('正在使用CPCL打印机 | 蓝牙打印')
            }

            // 蓝牙打印需要区分小票打印机和标准(针式)打印机。
            // >> 小票打印机需要后端传回图片->前端处理成ESC并打印
            // >> 针式打印机需要后端传回ESC指令->前端执行打印
            if (must24Pin || ignorePaperFeedCmd ||
              (printer_kind === '24pin' && Printing.cmdMode !== '_cpcl')
            ) {
              // * 从后端获取打印指令
              var blocks = []
              AppSheetToEsc(params).then(res => {
                var imageBytes = this.base64ToUint8Array(res)
                blocks.push({ imageBytes: imageBytes })
                Printing.printSheetOrInfo(blocks, function c(e) {
                  console.log(e)
                  if (e.result == 'Error') { Toast.fail(e.msg) }
                  else { Toast.success(e.msg); that.MarkPrint() }
                  handlePrintComplete()
                })
              }).catch(err => { printErrorHandler(err); })
            } 
            else 
            {
              // * 在前端生成打印指令
              AppSheetToImages(params).then(async res => {
                var blocks = []
                var jr = []
                jr = res

                /** 处理图片转译的函数(须为async) */
                let imgFunc = Printing.base64ToPrintBytesPromise
                /** 图片指令是否应添加至content, 而非imageBytes */
                let addToContent = false
                if (Printing.cmdMode === '_cpcl') {
                  imgFunc = Printing.base64ToPrintBytesPromise_Cpcl
                  addToContent = true
                }

                for (var i = 0; i < jr.length; i++) {
                  let imgb64 = `data:image/jpg;base64,${jr[i]}` // 重要! base64ToPrintBytesPromise须使用合法的src作为图片源并处理
                  var imageBytes = await imgFunc(imgb64)
                  var s = Printing.ab2hex(imageBytes)
                  addToContent ? blocks.push({ content: imageBytes }) : blocks.push({ imageBytes: imageBytes })
                }

                Printing.printSheetOrInfo(blocks, (e) => {
                  if (e.result == 'Error') {
                    Toast.fail(e.msg)
                  } else {
                    Toast.success(e.msg)
                    this.sheet.print_count = Number(this.sheet.print_count || 1) + 1
                    ApiPrintMark(
                      {
                        operKey: this.$store.state.operKey,
                        sheetType: sheet.sheetType,
                        sheetIDs: sheet.sheet_id,
                        printEach: true,
                        printSum: false
                      }).then(() => { }).catch(() => { })

                  }
                  handlePrintComplete()
                })
              }).catch(err => { printErrorHandler(err); })
            }
          }
         
        }
      })
    },
    // base64 to uint8array (byC)
    base64ToUint8Array(base64) {
      var raw = atob(base64);
      var rawLength = raw.length;
      var array = new Uint8Array(new ArrayBuffer(rawLength));
      for (var i = 0; i < rawLength; i++) {
        array[i] = raw.charCodeAt(i);
      }
      return array;
    },
    // 跳转打印详情 判断是否使用模板与打印+渲染模板radio
    btnGotoPrintView_click() {
      this.isInPrintView = true
      // 获取默认打印机
      const defaultPrinter = window.getDefaultPrinter()
      // 判断是否为云打印
      var printer_kind = defaultPrinter.kind
      if (!printer_kind) {
        if (defaultPrinter.type == "cloud")
          printer_kind = '24pin'
        else
          printer_kind = 'tiny'
      }
      this.useTmp = defaultPrinter.useTemplate
      //获取模板并展示
      if (this.useTmp) {
        if (this.templatesLoaded) {
          this.confirmTemplates(printer_kind)
        } else {
          this.loadPrintTemplates((data) => {
            this.confirmTemplates(printer_kind)
          })
        }
      }
      this.isPrinting = false
    },
    btnPrint_click(){
      console.log("点击了确认打印的按钮")
      if(this.sheet.sheet_no && this.sheetHasChange && !this.sheet.approve_time){
        this.doSave(()=>{
          //this.doPrint()
          this.confirmPrint()
        })     
      }
      else{ 
        //this.doPrint()
        this.confirmPrint()
      }
    },
    // 最终打印方法
    async confirmPrint() {
      
      if (!this.useTmp) {
        this.$store.commit('companyNameToPrint', this.companyNameToPrint)
        this.print_noTemplate(this.printCount)
      } else {
        if (!this.tmplist || this.tmplist.length == 0) {
          Toast.fail("没有可用的打印模板")
        } else {
          var tmp
          try {
            tmp = this.tmplist[this.selectedTmpId].tmp
          }
          catch (e) {
            Toast.fail('打印错误' + e.message)
            return
          }
          console.log("选择的模板是：" + tmp.template_name)
          this.btnPrint_ByTemplate(tmp, this.printCount)
        }
      }
       setTimeout(()=>{
          this.sheetHasChange = false
      },100) 
    },
    //打印  不按照模板
    async print_noTemplate(printCount) {
      console.log("打印不按照模板")
      
      this.isPrinting = true
      var that = this
      var sheet_id = this.sheet.sheet_id
      var sheet = this.getSheet()

      const defaultPrinter = window.getDefaultPrinter()
      const printerType = defaultPrinter.type

      // 判断是否为云打印
      var printer_kind = defaultPrinter.kind
      if (!printer_kind) {
        if (printerType == "cloud")
          printer_kind = '24pin'
        else
          printer_kind = 'tiny'
      }
      console.log("打印种类是：",printer_kind)
      // 不使用模板打印(小票打印)的场合，直接发送打印请求
      if (printerType == "cloud") { // 云打印机 不使用次数
        var that = this
        var imageBase64 = "";
        if (that.$store.state.company_cachet)
          imageBase64 = "data:image/png;base64," + that.$store.state.company_cachet;
        sheet = JSON.parse(JSON.stringify(sheet))
        var errMsg = that.checkAttrQtyValid()
        if (errMsg) {
          Toast.fail(errMsg)
          return
        }

        let b64Esc_tinyTicket = await Printing._sheetToB64Esc(sheet, that.printBarcodeStyle, that.printBarcodePic)

        var params = {
          operKey: this.$store.state.operKey,
          tmp: { width: '80', height: '100' }, // 后端接收directPrint的ESC指令时只会读取模板宽高 所以随意指定一个值
          sheet: sheet,
          printer_brand: defaultPrinter.brand,
          device_id: defaultPrinter.cloudID,
          check_code: defaultPrinter.cloudCode,
          cus_orderid: this.sheet.sheet_no,
          copies: "1",  // 云打印暂不启用
          //directPrint:true,
          escCommand: b64Esc_tinyTicket,
        }
        AppCloudPrint_escCmd(params).then(res => {
          console.log(res);
          this.showCloudPrintResult(res)
        })
        this.isPrinting = false
      }
      else { // 蓝牙打印机 启用多次打印
        console.log("进入蓝牙打印分支")
        Printing.printMoveSheet(sheet, this.printBarcodeStyle, this.printBarcodePic, imageBase64, res => {
          this.isPrinting = false
          if (res.result == "OK") {
            this.sheet.print_count = Number(this.sheet.print_count || 1) + 1
            ApiPrintMark(
              {
                operKey: this.$store.state.operKey,
                sheetType: 'DB',
                sheetIDs: sheet.sheet_id,
                printEach: true,
                printSum: false
              }).then(() => {

              }).catch(() => {

              })
            this.loadingMsg = ''
            Toast.success("打印成功");
          } else {
            this.loadingMsg=''
            Toast.fail(res.msg);
          }
        });
      }
    },

    doPrint() {
      console.log("这里是doPrint方法")
      this.$store.state.printBarcodeStyleForMove = this.printBarcodeStyle
      this.$store.state.printBarcodePicForMove = this.printBarcodePic
      this.isPrinting = true
      var sheet = this.getSheet();
      var imageBase64 = null;
      // if (this.$store.state.company_cachet)
      //   imageBase64 = "data:image/png;base64," + this.$store.state.company_cachet;
      const defaultPrinter = window.getDefaultPrinter()
      if (defaultPrinter.type === "cloud") {
        // get tmp
        this.tmplist = []
        var params = {
          sheetType: this.sheet.sheet_type,
          clientID: this.sheet.supcust_id
        }
        AppGetTemplate(params).then(data => {
          if (data.templateList.length == 0) {
            Toast.fail("没有可用的打印模板")
          }
          else {
            this.showTemplate = true;
            var templateList = data.templateList;
            console.log(document.getElementById('selectTmp'))
            for (let i = 0; i < templateList.length; i++) {
              var inserttmp = {
                name: templateList[i].template_name,
                i: i,
                tmp: templateList[i]
              }
              this.tmplist.push(inserttmp)
            }
          }
        })
        this.isPrinting = false
      }
      else {
        this.loadingMsg = '正在打印' // 显示打印信息
        console.log("调拨单原生打印方法")
        Printing.printMoveSheet(sheet, this.printBarcodeStyle, this.printBarcodePic, imageBase64, res => {
          this.isPrinting = false
          if (res.result == "OK") {
            this.sheet.print_count = Number(this.sheet.print_count || 1) + 1
            ApiPrintMark(
              {
                operKey: this.$store.state.operKey,
                sheetType: 'DB',
                sheetIDs: sheet.sheet_id,
                printEach: true,
                printSum: false
              }).then(() => {

              }).catch(() => {

              })
            this.loadingMsg = ''
            Toast.success("打印成功");
          } else {
            this.loadingMsg=''
            Toast.fail(res.msg);
          }
        });
      }

    },
    fix(num, n = 2) {
      var pn = Math.pow(10, n);
      return Math.round(Number(num) * pn) / pn;
    },
    // 删除未审核单据
    btnDeleteSheet_click() {
      //sheet.sheet_id
      var that = this;
      Dialog.confirm({
        title: "删除单据",
        message: "请确认是否删除",
        width:"320px"
      }).then(() => {
        var delFunc;
        delFunc = SheetMoveDelete;
        let params = {
          operKey: this.$store.state.operKey,
          sheet_id: this.sheet.sheet_id,
        };
        delFunc(params).then((res) => {
          if (res.result === "OK") {
            Toast.success("删除成功,即将退出该页面");
            setTimeout(function () {
              that.btnOut();
            }, 1000);
          } else {
            Toast.fail("删除失败:" + res.msg);
          }
        });
      })
    },
    handleTimeShow() {
      if (this.canSelectCalendar) {
        Toast.fail("请点击其他，再进行日期选择")
      } else {
        this.timeShow = true
      }

    },
    handlePopupFillFromSale() {
      if (this.sheet.sheet_no) return;
      if (this.sheet.from_branch_id === this.sheet.to_branch_id && this.sheet.from_branch_id !== "" && this.sheet.to_branch_id !== "") {
        Toast.fail("出仓与入仓相同,请重新选择");
        return;
      }
      if (this.sheet.from_branch_id === "") {
        Toast.fail("请选择")
        return
      }
      if (this.sheet.to_branch_id === "") {
        Toast.fail("请选择")
        return
      }
      this.m_bPopupFillFromSale = true;
      this.handleTimeSelectShow()
    },
    changeHeight() {

      // .sales_no_submit {
      //   height: calc(100% - 44px - 25px - 29px);
      // }
      // .sales_box_list {
      //   height: calc(100% - 44px - 25px);
      // }
      this.publicBox3Height = Number(this.pageHeight) - 120 - Number(this.$refs.move_total.offsetHeight)
      if (this.sheet.sheet_no) {
        this.publicBox3Height -= 29
      }
      this.publicBox3Height += 'px'
    },
    handleMoveSheetStoreMoveOut() {
      if (!this.canEdit) return;
      this.popFromBranchList = true;
    },
    handleMoveSheetStoreMoveIn() {
      if (!this.canEdit) return;
      this.popToBranchList = true;
    },
    closePage() {
      this.transStr = ''
      this.popupNewAddMoveSheetRow = false
      this.$store.commit("multiSelectOpenFlag", false)
      this.$store.commit("shoppingCarFinish", false)
      this.$store.commit('shoppingCarObj', {
        sheetType: this.moveType,
        clearFlag: true
      })
    },
    closeSingleChoicePage() {
      this.transStr = ''
      this.$store.commit("multiSelectOpenFlag", false)
      this.$store.commit("shoppingCarFinish", false)
      this.popupNewAddMoveSheetRow = false
      this.$forceUpdate()
    },
    handleSheetRowsSort() {
      // let tempSheetRow = JSON.parse(JSON.stringify(this.sheet.sheetRows))
      this.sheet.sheetRows = this.sheet.sheetRows.sort((a, b) => a.item_name.localeCompare(b.item_name, 'zh')); //a~z 排序
      // this.sheet.sheetRows = []
      //this.sheet.sheetRows = tempSheetRow
      this.$forceUpdate()

    },
    // 转单
    handleSheetToOther() {
      // 销售单权限
      let action = this.sheetToOtherSheetAction[0]
      if (!hasRight(action.right)) {
        Toast.fail("无转销售单权限")
        return
      }
      // 仓库权限
      if (!this.$store.state.operInfo.branchRights) {
        Toast.fail("暂无仓库权限")
        return
      }
      let to_branch_id = this.sheet.to_branch_id
      let findBranch = this.$store.state.operInfo.branchRights.find(item => item.branch_id === to_branch_id)
      if (!findBranch) {
        Toast.fail("仓库权限未设置")
        return
      }
      if (findBranch.sheet_x !== 'True') {
        Toast.fail("仓库无销售权限")
        return
      }

      // 由于目前只有转销售，预留后期
      if (this.sheet.sheetRows.length === 0) {
        Toast.fail("请选择商品")
        return
      }
      this.sheetToOtherSheetPopover = false
      let routerQuerySheetRows = this.sheet.sheetRows.map(item => {
        return { item_id: item.item_id, unit_type: item.unit_type, quantity: item.quantity }
      })
      // 库存问题
      setTimeout(() => {
        if (action.sheetType === 'X') {
          RouterUtil.goSaleSheet(this, {
            sheetID: '',
            sheetType: action.sheetType,
            sheetToOtherParams: {
              branch_id: this.sheet.to_branch_id,
              branch_name: this.sheet.to_branch_name,
              sheetRows: routerQuerySheetRows
            }
          })
          console.log(routerQuerySheetRows)
        }
      }, 300)

    },
    handleEndDrag(arr){
      this.sheet.sheetRows = arr
    }
  },
};
</script>

<style lang="less" scoped>
.sales_more {
  background-color: #fff;
  position: absolute;
  top: 0px;
  width: 100%;
  height: 100%;
  left: 0px;

  .print-template-wrapper {
    height: 250px;
  }

  .select-printer {
    display: flex;
    width: 100%;
    min-height: 45px;

    /deep/.van-cell-group.van-cell-group--inset.van-hairline--top-bottom {
      margin: 0px;
    }
  }

  /deep/.van-checkbox-group {
    font-size: 16px;
  }

  .print-count {
    display: flex;
    justify-content: flex-end;
    align-items: center;
  }

  .print-btns {
    height: 45px;
    border-radius: 12px;
    width: calc(45% - 5px);
  }

  .other_payway {
    // margin: 15px 0;
    .payway-minHeight {
      min-height: 40px;
    }

    .payway {
      height: 30px;

      font-size: 15px;
      @flex_a_j();
      margin: 5px;

      .van-col {
        height: inherit;
        @flex_a_j();
        color: #000000;
      }

      input {
        width: 90px;
        height: 100%;
        border: none;
        outline: none;
        border-bottom: 0.5px solid #eee;
        text-align: center;
      }

      .payway_add {
        font-size: 20px;
        color: #ccc;
        margin-left: 10px;
        margin-top: 10px;
      }

      .arrow {
        line-height: 17px;
      }
    }

    .payway::after {
      position: absolute;
      content: "";
      border-bottom: 1px solid #444;
    }
  }

  /deep/.van-field {
    height: calc(100% - 46px);
    color: #000000;
  }

  /deep/.van-radio-group {
    margin-top: 5px;
  }

  /deep/.van-radio {
    margin: 0 0 10px 0;
  }

  /deep/.van-divider {
    margin: 10px 0 3px;
    color: #ddd;
  }

  h4 {
    font-size: 16px;
    text-align: left;
    height: 35px;
    line-height: 35px;
    padding-left: 10px;
    color: #333333;
    font-weight: normal;
  }
}
.radio-tmp-position {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  height: 210px;
  overflow-y: auto;
}
.setHeight {
  height: 60px;
}
.radio-tmp-style {
  display: inline-block;
  position: relative;
  left: 0;
  margin: 5px 0;
  width: 50%;
  text-align: left;
}
// 模板radio样式
.radio-tmp-type {
  width: 20px;
  height: 20px;
  appearance: none;
  position: relative;
}

.radio-tmp-type:before {
  content: "";
  width: 20px;
  height: 20px;
  border: 1px solid #8d8d8d;
  display: inline-block;
  border-radius: 50%;
  vertical-align: middle;
}

.radio-tmp-type:checked:before {
  content: "";
  width: 20px;
  height: 20px;
  border: 1px solid #8d8d8d;
  background: #8d8d8d;
  display: inline-block;
  border-radius: 50%;
  vertical-align: middle;
}

.radio-tmp-type:checked:after {
  content: "";
  width: 10px;
  height: 5px;
  border: 0.065rem solid white;
  border-top: transparent;
  border-right: transparent;
  text-align: center;
  display: block;
  position: absolute;
  top: 6px;
  left: 5px;
  vertical-align: middle;
  transform: rotate(-45deg);
}
.myslide-right-enter-active,
.myslide-right-leave-active,
.myslide-left-enter-active,
.myslide-left-leave-active {
  transition: all 0.4s ease;
}
.hide-show-div-enter-active {
  transition: opacity 0.5s;
}

.hide-show-div-enter {
  opacity: 0;
}

.hide-show-div-leave-active {
  transition: opacity 0.5s;
}

.hide-show-div-leave-to {
  opacity: 0;
}
.black {
  fill: currentColor;
  // color: #757575;
  color: #8d8d8d;
}

@flex_w: {
  display: flex;
  flex-wrap: wrap;
}

;

@flex_a_j: {
  display: flex;
  align-items: center;
  justify-content: center;
}

;

@flex_a_bw: {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

;

@posAblot: {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
}

;

.iconfont {
  color: #b197c2;
}

.brand_style {
  font-size: 15px;
}

.clear_bill {
  margin-top: 30px;
}

.sales_no_submit {
  height: calc(100% - 44px - 25px - 29px);
}

.sales_box_list {
  height: calc(100% - 44px - 25px);
}

.public_box3 {
  overflow-y: auto;
}

.public_query {
  padding: 0 10px;

  .public_query_title {
    background: #ffffff;
  }

  .public_query_title_t {
    height: 25px;
    line-height: 25px;
    font-size: 15px;
    color: #000000;
    padding: 10px 10px 0px;
    @flex_a_bw();

    i {
      height: 18px;
      border: 1px solid red;
      display: inline-block;
      font-size: 12px;
      font-style: normal;
      line-height: 20px;
      padding: 0 5px;
      border-radius: 4px;
      margin-left: 10px;
    }
  }

  .top_line {
    background-image: linear-gradient(to right,
        transparent 0%,
        #ccc 12%,
        #999 18%,
        transparent 30%);
    width: 100%;
    height: 2px;
    margin: 10px 0 15px 0;
    background-size: 10px 40px;
    background-repeat: repeat-x;
  }

  .public_query_titleSrc {
    // padding: 0px 15px 3px 15px;

    display: flex;
    flex-direction: column;
    background: #ffffff;

    .public_query_wrapper {
      .public_query_titleSrc_item {
        width: 50%;
        height: 100%;
        overflow: hidden;
        position: relative;
        vertical-align: top;
        border: none;
        padding: 10px 0 3px 0;
        display: flex;
        flex-direction: row;
        align-items: flex-end;

        .select-one {
          flex: 1;
        }

        div {
          height: 100%;
          width: 40px;
          padding-right: 20px;
          border: none;

          font-size: 15px;
          line-height: 35px;
          color: #333333;
          text-align: left;
        }

        input {
          height: 100%;
          width: 100%;
          padding: 0 10px 4px 0px;
          border: none;
          font-size: 15px;
          line-height: 35px;
          color: #333333;
          vertical-align: top;
          border-bottom: 1px solid #eee;
          text-align: right;
        }

        .van-icon {
          position: absolute;
          left: 5px;
          top: 0;
          bottom: -10px;
          width: 30px;
          text-align: center;
          font-size: 22px;
          @flex_a_j();
          color: #aaa;
          background-color: #ffffff;
        }
      }
    }
  }

  .public_list_title {
    height: 40px;
    @flex_a_bw();
    margin-top: 5px;
    padding: 0 5px;

    div {
      height: 40px;
      line-height: 40px;
      font-size: 15px;
      text-align: center;
      width: calc(25% - 10px);
      padding: 0 5px;
      font-weight: 500;
      color: #333333;
    }

    div:first-child {
      width: calc(50% - 10px);
      text-align: left;
    }

    div:last-child {
      width: calc(25% - 10px);
      text-align: right;
    }
  }
}

.sales_box_box {
  width: 100%;
  height: calc(100% - 2px);

  .sales_box_ul {
    height: auto;
    overflow: hidden;
    padding: 10px 10px 0 10px;

    li {
      width: 100%;
      border-radius: 4px;
      margin: 0 auto 10px auto;
      background: #ffffff;

      h5 {
        height: 40px;
        @flex_a_bw();
        padding: 0 15px;
        font-size: 16px;
        border-bottom: 1px solid #f2f2f2;
        color: #333333;

        span {
          text-align: left;
        }
      }

      .van-col:first-child {
        p {
          text-align: left;
        }
      }

      .van-col:last-child {
        p {
          text-align: right;
        }
      }

      .van-col {
        p {
          font-size: 15px;
          color: #333333;
          padding: 0 5px;
          margin: 0;
        }
      }

      .sales_box_ul_title {
        height: 30px;
        line-height: 30px;
        padding: 0 7.5px;
        font-size: 15px;
        font-weight: 500;
        color: #333333;
      }

      .sales_box_ul_item {
        padding: 5px 7.5px;
        color: #666666;
      }
    }

    .removeRow {
      height: inherit;
    }

    .sheet_state {
      z-index: 999;
      position: fixed;
    }

    .approved {
      width: 105px;
      height: 60px;
      top: 95px;
      left: 125px;
    }

    .reded {
      width: 86px;
      height: 75px;
      top: 75px;
      left: 125px;
    }
  }
}

.unit-amount-container {
  display: flex;
  flex-direction: row;
  float: left;
}

.total-amount-container {
  right: 0;
}

.move_total {
  display: flex;
  background: #fff;
  font-size: 16px;
  padding: 5px;
  text-align: center;
  flex-direction: row;
  align-items: center;

  // border-top: 1px solid #f2f2f2;
  // border-bottom: 1px solid #f2f2f2;
  // box-shadow: 0 2px 5px #f2f6fc;
  .sales_list_span {
    font-family: numFont;
    //margin-top: 4px;
    padding-top: 4px;
    margin-right: 15px;
  }
}

.sales_footer {
  @posAblot();
  background: #ffffff;
  border-top: 1px solid #f2f2f2;
  // padding: 10px;
  box-sizing: border-box;

  :last-child {
    @flex_a_bw();
  }

  .footer_input {
    flex: 6;
    width: 290PX;
    font-size: 14PX;
    border-style: none;
    border-bottom: 1PX solid #eee;
    // background-color: #eee; 
    height: 35PX;
  }

  .footer_iconBt {
    flex: 1;
    justify-content: center;
    width: 50PX;
    height: 50PX;
    @flex_a_j();
  }

}

.van_popup {
  height: 100%;
  overflow: hidden;
}
.quick_move{
  display:flex;
  flex-direction:column;
  height:100%;
  width:100%;
  position:relative;
}
.move-type,.bill-date{
  margin-bottom:8px;
  padding:8px;
  border-bottom:1px solid #eee;
}
.bill-date{
 .van-radio-group{
  display:flex;
  flex-wrap:wrap;
 }
}
.custom_h5 {
  height: 46px;
  line-height: 50px;
  font-size: 16px;
  color: #333333;
  border-bottom: 1px solid #cccccc;
  box-shadow: 1px 1px 1px #f2f2f2;
  color: steelblue;
  position: relative;

  .icon_h5 {
    position: absolute;
    height: 46px;
    width: 46px;
    right: 0;
    top: 0;
    display: block;
    @flex_a_j();
    font-size: 20px;
  }
}

// .showBillDate {
//   margin: 30px 0;
//   height: auto;
//   font-size: 16px;
//   @flex_a_j();
// }

.replenish {
  display:flex;
  flex-direction:column;
  align-items:center;
  justify-content:center;
  .replenish_btn{
    width:100%;
    display: flex;
    justify-content: space-around;
  }

}
.branchPositionSelect{
  margin-top:8px;
  padding:8px;
  .van-cell--clickable{
    margin-bottom:8px;
  }
  .van-field{
    border:1px solid #eee;
    /deep/ .van-field__label{
      color:#000;
    }
    /deep/ .van-field__value{
      display:flex;
      flex-wrap:nowrap;
    }
    /deep/.van-field__control{
      color:#969799;
    }
  }
  .van-checkbox-group{
    padding:8px;
    display:flex;
    flex-direction:column;
    .van-checkbox{
      padding:8px;
    }
  }
  .van-radio-group{
    padding:8px;
    display:flex;
    flex-direction:column;
    .van-radio{
      padding:8px;
    }
  }
 
}

.btnForSale{
  position:fixed;
  margin-left:-20px;
  bottom:250px;
  left:50%;
}
/deep/.inputZf {
  .van-field__control {
    text-align: right;
  }
}

.van_search_style {
  display: contents;
  border-top: 1px solid #f2f2f2;
}

.van_search_btns {
  margin-left: 10px;
}

.submitSalesSlip {
  color: #000;
  font-size: 16px;
  margin: 0 15px 0 0;
  font-weight: 600;
}

.submitSalesSlip:last-child {
  margin: 0;
}

.lowItem {
  width: 300px;
  height: auto;
  overflow: hidden;
  padding: 10px;

  h4 {
    height: 40px;
    font-size: 16px;
    font-weight: 500;
    color: #333333;
    @flex_a_j();
  }

  .lowItem_ul {
    height: auto;
    overflow: hidden;
    margin-bottom: 10px;

    li {
      height: auto;
      overflow: hidden;
      padding: 10px;
      font-size: 14px;
      @flex_a_bw();
      border-bottom: 1px solid #f2f2f2;
    }

    li:last-child {
      border-bottom: none;
    }
  }
}

.other_operate {
  width: 100%;
  height: auto;

  .other_operate_content {
    padding-top: 30px;
    height: 40px;
    vertical-align: top;
    margin-bottom: 15px;
    @flex_a_j();

    button {
      // width: 100px;
      height: 100%;
      vertical-align: top;
      // margin: 0 15px;
    }

    .my-btn {
      width: calc(30% - 5px);
    }

    .small-btn {
      margin-bottom: 20px;
      padding: 0;
      background-color: #fff;
      color: #5577bb;
      height: 30px;
      margin-left:30px;
    }

    .small-btn:disabled {
      color: #ddd;
    }
  }
}

input::-webkit-input-placeholder {
  /* WebKit browsers，webkit内核浏览器 */
  color: #a0a0a0;
}

input:-moz-placeholder {
  /* Mozilla Firefox 4 to 18 */
  color: #a0a0a0;
}

input::-moz-placeholder {
  /* Mozilla Firefox 19+ */
  color: #a0a0a0;
}

input:-ms-input-placeholder {
  /* Internet Explorer 10+ */
  color: #a0a0a0;
}</style>
