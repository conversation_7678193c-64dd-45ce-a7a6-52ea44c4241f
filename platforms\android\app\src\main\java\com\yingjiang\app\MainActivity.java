/*
       Licensed to the Apache Software Foundation (ASF) under one
       or more contributor license agreements.  See the NOTICE file
       distributed with this work for additional information
       regarding copyright ownership.  The ASF licenses this file
       to you under the Apache License, Version 2.0 (the
       "License"); you may not use this file except in compliance
       with the License.  You may obtain a copy of the License at

         http://www.apache.org/licenses/LICENSE-2.0

       Unless required by applicable law or agreed to in writing,
       software distributed under the License is distributed on an
       "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
       KIND, either express or implied.  See the License for the
       specific language governing permissions and limitations
       under the License.
 */

package com.yingjiang.app;

import android.graphics.Color;
import android.os.Build;
import android.os.Bundle;
import android.view.View;
import android.view.Window;
import android.view.WindowManager;
import android.util.Log;

import org.apache.cordova.*;
import org.apache.cordova.engine.SystemWebViewEngine;

public class MainActivity extends CordovaActivity
{
    private String selectedWebViewEngine;
    @Override
    public void onCreate(Bundle savedInstanceState)
    {
        Log.i("MainActivity", "MainActivity onCreate 开始");

        // 动态选择 WebView 引擎
        selectedWebViewEngine = SmartWebViewSelector.getRecommendedWebViewEngine(this);
        Log.i("MainActivity", "选择的 WebView 引擎: " + selectedWebViewEngine);

        // 设置 WebView 引擎偏好
        if (selectedWebViewEngine.equals("org.apache.cordova.engine.SystemWebViewEngine")) {
            Log.i("MainActivity", "设置使用 SystemWebView");
        } else {
            Log.i("MainActivity", "设置使用 X5WebView，开始初始化 X5");
            try {
                X5InitHelper.initX5(this);
            } catch (Exception e) {
                Log.e("MainActivity", "X5 初始化失败，回退到 SystemWebView", e);
                selectedWebViewEngine = "org.apache.cordova.engine.SystemWebViewEngine";
            }
        }

        Log.i("MainActivity", "准备调用 super.onCreate");
        super.onCreate(savedInstanceState);
        Log.i("MainActivity", "super.onCreate 完成");

        // 设置Activity背景为黑色，与splash保持一致
        getWindow().setBackgroundDrawableResource(android.R.color.black);
        // 设置DecorView背景也为黑色
        getWindow().getDecorView().setBackgroundColor(Color.BLACK);
        Log.i("MainActivity", "设置Activity和DecorView背景为黑色");


      /*   Window window= getWindow();
        //确保不被状态栏覆盖 (即使没这行代码应该也是不被覆盖的,这里为了保险，就写上了，SYSTEM_UI_FLAG_LAYOUT_FULLSCREEN 是覆盖)
      //  window.getDecorView().setSystemUiVisibility(View.SYSTEM_UI_FLAG_LAYOUT_STABLE | View.SYSTEM_UI_FLAG_VISIBLE);
        //设置状态栏背景色
       getWindow().setStatusBarColor(Color.parseColor("#ffffff"));
       //设置状态栏前景色是深色的
        int uiOptions = window.getDecorView().getSystemUiVisibility();
        window.getDecorView().setSystemUiVisibility(uiOptions | View.SYSTEM_UI_FLAG_LIGHT_STATUS_BAR);
     */


        //  enable Cordova apps to be started in the background
        int a=1;
        Bundle extras = getIntent().getExtras();
        if (extras != null && extras.getBoolean("cdvStartInBackground", false)) {
            moveTaskToBack(true);
        }

        // Set by <content src="index.html" /> in config.xml
       // return;
        Log.i("MainActivity", "准备调用 loadUrl");
        loadUrl(launchUrl);
        Log.i("MainActivity", "loadUrl 完成");

        // 确保WebView背景开始是黑色
        if (appView != null && appView.getView() != null) {
            appView.getView().setBackgroundColor(Color.BLACK);
            Log.i("MainActivity", "设置WebView背景为黑色");
        }

        // 延迟再次确保WebView背景为黑色
        new android.os.Handler().postDelayed(new Runnable() {
            @Override
            public void run() {
                if (appView != null && appView.getView() != null) {
                    appView.getView().setBackgroundColor(Color.BLACK);
                    Log.i("MainActivity", "延迟设置WebView背景为黑色");
                }
            }
        }, 100);
    }

    @Override
    protected CordovaWebViewEngine makeWebViewEngine() {
        Log.i("MainActivity", "makeWebViewEngine 被调用，使用引擎: " + selectedWebViewEngine);

        if (selectedWebViewEngine != null && selectedWebViewEngine.equals("org.apache.cordova.engine.SystemWebViewEngine")) {
            Log.i("MainActivity", "创建 SystemWebViewEngine");
            return new SystemWebViewEngine(this, preferences);
        } else {
            Log.i("MainActivity", "使用默认引擎创建方式");
            return super.makeWebViewEngine();
        }
    }

}
