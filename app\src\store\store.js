import Vue from "vue"
import Vuex from "vuex"
import createPersistedState from "vuex-persistedstate"
Vue.use(Vuex)
/**
 * clearCreatePersistedState
 * 在此方法添加用于退出用户进行清空缓存操作
 */
const store = new Vuex.Store({
  state: {
    classId: "",
    motherId: "",
    AllItemClassId: "",
    itemList: [],
    positionWeather: false,
    positionToCustom: false,
    positionVisit: false,
    positionToCustomCurrent: false,
    positionJourney: false,
    positionAttendance: false,
    positionRadar: false,
    positionTrail: false,
    positionRights: false,
    unsubmitedSheets_X: [],
    unsubmitedSheets_T: [],
    unsubmitedSheets_XD: [],
    unsubmitedSheets_TD: [],
    unsubmitedSheets_DB: [],
    unsubmitedSheets_DBmoveOut: [],
    unsubmitedSheets_DBmoveIn: [],
    unsubmitedSheets_DH: [],
    unsubmitedSheets_CG: [],
    unsubmitedSheets_TG: [],
    unsubmitedSheets_CT: [],
    unsubmitedSheets_PD: [],
    unsubmitedSheets_SS: [],
    unsubmitedSheets_BS: [],
    unsubmitedSheets_CX: [],
    unsubmitedSheets_TJSP: [],
    unsubmitedSheets_BQ: [],
    lastPosition: {},
    operKey: "",
    operInfo: { operRights: "", oper_name: "", mobile: "" },
    account: {},
    accountList: [],
    companyList: [],
    noticeStr: "",
    // workList: [],
    inPrivateMode: false,
    productist: [],
    openBluetooth: false,
    useTemplateToPrint: false,
    c_printers: {},
    printStyle: "", // 'template' or 'text', template as "true".
    printerType: "",
    printer_kind: "",
    cloudPrinterSetted: false,
    printer_brand: "",
    printer_name: "",
    device_id: "",
    printer_id: "",
    check_code: "",
    device_code: "",
    bluetoothSendDataStyle: "auto",
    paperSize: "",
    printerID: "",
    printerName: "",
    printerUUID: {},
    bluetoothType: "",
    radarSupType: "",
    printing: {},
    server_uri: "",
    currentSheet: {},
    sheetChangeTime: "",
    visitRecord: {},
    cachedViews: [],
    cachePageName: "", //缓存组件名称
    catchList: [],
    salesBranch: {},
    sheetBranches: {},
    c_sheetBranches: {},
    salesSender: {},
    salesOrderAccount: {},
    moveOutFromBranch: {},
    moveOutToBranch: {},
    moveInFromBranch: {},
    moveInToBranch: {},
    saleShowStockOnly: "",
    printBarcodeStyle: "noBarcode",
    printBarcodeStyleForSale: null,
    printBarcodeStyleForMove: null,
    printBarcodePic: false,
    printBarcodePicForMove: false,
    companyNameToPrint: "",
    company_cachet: "",
    sheet: { noRed: "", sheetType: "" },
    activeItemClass: [],
    activeAddress: {},
    checkedSheetsCount: 0,
    stock: {},
    releaseType: "production", //app当前模式 test dev
    activeNavBar: 0,
    curStock: {},
    weatherCurrentCity: "",
    showStockOnly: false,
    stockBranchInfo: { branchID: "", branchName: "" },
    costsOrwholesaleFlag: null,
    showStockAmount: null,
    HRItem: {},
    trade_type: "",
    onloadPayways: null,
    itemDispRecList: null,
    btnSaveClick: false,
    ksPayWayObj: null,
    AllItemClass: null,
    ItemClassObj: null,
    selectedRegion: {},
    infoRegionObj: [],
    shoppingCarObj: {}, // key：sheetType, value: MultiSelectItemObjArray
    selectedItemsObj: {},
    shoppingCarFinish: false,
    attrShowFlag: false, // true 商品属性列表打开
    activeSelectItem: {}, // 当前点击的商品信息
    activeSelectItemAttrList: {}, // 包含点击商品信息Attr列表
    distinctStockFlag: true, // 判断当前商品是否区分库存true说明区分库存，和普通商品进行对待即可
    noItemIdSheetRows: [], // 如果当前商品没有
    selectedSheetRows: {}, // sheetRows 封装对象
    multiSelectOpenFlag: false,
    acceptPrivacy: false,
    attrOptions: [], // 保存商品属性
    branches: [],
    printCheckAccountDialogResult: [], // 交账单打印选项
    visitDayIndex: "",
    visitShowMode: "visitUser",
    visitPlanIndex: "",
    saleSheetAddSheetRowState: "X",
    visitDay_sortByDistanceFlag: false,
    selectItemOrderResult: [],
    userActions: [],
    unSubmitVisitTemplate: [],
    unSubmitInfoVisitTemplateMappingObj: {},
    unSubmitDisplayMaintainAction: [],
    unSubmitDisplayMaintainActionForMonth: [],
    salePromotionCombines: [], // 促销活动-组合
    salePromotionSecKills: [], // 促销活动-秒杀
    salePromotionFullDiscs: [], // 促销活动-满减
    salePromotionFullGifts: [], // 促销活动-满赠
    salePromotionCashPrizes: [], // 促销活动-兑奖
    yjSelectCalendarCacheStore: {}, // 日历组件缓存上次时间差天数
    yjCalendarSettingOptionsStore: [], // 日历组件自定义按钮
    timeTypeInfo(state, payload) {
      // 假设你想要记忆 item 和单据类型
      state.timeTypeInfo = payload.item; // 保存 item
      state.documentType = payload.documentType; // 保存单据类型
    },
    selectCustomerOrderBy: "",
    setFontSize: 1,
    showZeroStock:false,
    showPositionStock:false,
    showBatchStock:false,
    showExpiredItems:true,
    showAvailItems:true,
    queryItemSortType:"default",
    stockQtyType:"default",
    isShowAllClientForClientLossWarning:false,//客户流失预警
    orderQtyType:"default",
    operSetting: {
      fontSize: 1,
    },
    checkAccountDateType: '', // 交账单查询的时间缓存
    checkAccountDateSpecDateNum: 0, // 交账单查询的时间缓存指定时间
    branchList:[],
    curBranchPositionList:[],
    fromBranchList:[],
    curFromBranchPositionList:[],
    toBranchList:[],
    curToBranchPositionList:[],
    customerPageOption: null, //用于客户信息页面进入下一个详情页，从详情页返回上个页面标记， see create update
    itemArchivePageOption: null, //用于客户信息页面进入下一个详情页，从详情页返回上个页面标记， see create update
  },
  mutations: {
    keepAlive(state, component) {
      !state.catchList.includes(component) && state.catchList.push(component)
    },
    noKeepAlive(state) {
      state.catchList = []
    },
    resetcachePageName(state, res) {
      state.cachePageName = res
    },
    visitDay_sortByDistanceFlag(state, obj) {
      state.visitDay_sortByDistanceFlag = obj
    },
    //添加缓存组件名称
    addcachePageName(state, res) {
      if (state.cachePageName == "") {
        state.cachePageName = res
      } else {
        let arr = state.cachePageName.split(",")
        if (res && typeof res === "string") {
          let i = arr.indexOf(res)
          if (i <= -1) {
            state.cachePageName = state.cachePageName + "," + res
          }
        }
      }
    },
    //删除缓存组件名称
    delcachePageName(state, res) {
      let arr = state.cachePageName.split(",")
      if (res && typeof res === "string") {
        let i = arr.indexOf(res)
        if (i > -1) {
          arr.splice(i, 1)
          state.cachePageName = arr.join()
        }
      }
    },
    classId(state, obj) {
      state.classId = obj
    },
    motherId(state, obj) {
      state.motherId = obj
    },
    AllItemClassId(state, obj) {
      state.AllItemClassId = obj
    },
    itemList(state, obj) {
      state.itemList = obj
    },
    positionWeather(state, obj){
      state.positionWeather = obj
    },
    positionToCustom(state, obj){
      state.positionToCustom = obj
    },
    positionVisit(state, obj){
      state.positionVisit = obj
    },
    positionToCustomCurrent(state, obj){
      state.positionToCustomCurrent = obj
    },
    positionJourney(state, obj){
      state.positionJourney = obj
    },
    positionAttendance(state, obj){
      state.positionAttendance = obj
    },
    positionRadar(state, obj){
      state.positionRadar = obj
    },
    positionTrail(state, obj){
      state.positionTrail = obj
    },
    positionRights(state, obj){
      state.positionRights = obj
    },
    unsubmitedSheets_X(state, obj) {
      state.unsubmitedSheets_X = obj
    },
    unsubmitedSheets_T(state, obj) {
      state.unsubmitedSheets_T = obj
    },
    selectedRegion(state, obj) {
      state.selectedRegion = obj
    },
    unsubmitedSheets_XD(state, obj) {
      state.unsubmitedSheets_XD = obj
    },
    unsubmitedSheets_TD(state, obj) {
      state.unsubmitedSheets_TD = obj
    },
    unsubmitedSheets_DH(state, obj) {
      state.unsubmitedSheets_DH = obj
    },
    unsubmitedSheets_DB(state, obj) {
      state.unsubmitedSheets_DB = obj
    },
    unsubmitedSheets_DBmoveOut(state, obj) {
      state.unsubmitedSheets_DBmoveOut = obj
    },
    unsubmitedSheets_DBmoveIn(state, obj) {
      state.unsubmitedSheets_DBmoveIn = obj
    },

    unsubmitedSheets_CG(state, obj) {
      state.unsubmitedSheets_CG = obj
    },
    unsubmitedSheets_TG(state, obj) {
      state.unsubmitedSheets_TG = obj
    },
    unsubmitedSheets_CT(state, obj) {
      state.unsubmitedSheets_CT = obj
    },
    unsubmitedSheets_PD(state, obj) {
      state.unsubmitedSheets_PD = obj
    },
    unsubmitedSheets_SS(state, obj) {
      state.unsubmitedSheets_SS = obj
    },
    unsubmitedSheets_TJSP(state, obj) {
      state.unsubmitedSheets_TJSP = obj
    },
    unsubmitedSheets_CX(state, obj) {
      state.unsubmitedSheets_CX = obj
    },
    unsubmitedSheets_BS(state, obj) {
      state.unsubmitedSheets_BS = obj
    },
    unsubmitedSheets_BQ(state, obj) {
      state.unsubmitedSheets_BQ = obj
    },
    bluetoothType(state, obj) {
      state.bluetoothType = obj
    },
    operKey(state, obj) {
      state.operKey = obj
    },
    operInfo(state, obj) {
      state.operInfo = obj
    },
    printBarcodeStyle(state, obj) {
      state.printBarcodeStyle = obj
    },
    printBarcodeStyleForSale(state, obj) {
      state.printBarcodeStyleForSale = obj
    },
    printBarcodeStyleForMove(state, obj) {
      state.printBarcodeStyleForMove = obj
    },
    printBarcodePic(state, obj) {
      state.printBarcodePic = obj
    },
    printBarcodePicForMove(state, obj) {
      state.printBarcodePicForMove = obj
    },
    companyNameToPrint(state, obj) {
      state.companyNameToPrint = obj
    },
    showStockOnly(state, obj) {
      state.showStockOnly = obj
    },
    company_cachet(state, obj) {
      state.company_cachet = obj
    },
    noticeStr(state, obj) {
      state.noticeStr = obj
    },
    account(state, obj) {
      state.account = obj
    },
    companyList(state, obj) {
      state.companyList = obj
    },
    userActions(state, obj) {
      state.userActions = obj
    },

    // workList(state, obj) {
    //     state.workList = obj;
    // },
    productist(state, obj) {
      state.productist = obj
    },
    inPrivateMode(state, obj) {
      state.inPrivateMode = obj
    },
    printing(state, obj) {
      state.printing = obj
    },
    openBluetooth(state, obj) {
      state.openBluetooth = obj
    },
    useTemplateToPrint(state, obj) {
      state.useTemplateToPrint = obj
    },
    printStyle(state, obj) {
      state.printStyle = obj
    },
    printerType(state, obj) {
      state.printerType = obj
    },
    printer_kind(state, obj) {
      state.printer_kind = obj
    },
    cloudPrinterSetted(state, obj) {
      state.cloudPrinterSetted = obj
    },
    device_id(state, obj) {
      state.device_id = obj
    },
    printer_id(state, obj) {
      state.printer_id = obj
    },
    printer_name(state, obj) {
      state.printer_name = obj
    },
    printer_brand(state, obj) {
      state.printer_brand = obj
    },
    check_code(state, obj) {
      state.check_code = obj
    },
    device_code(state, obj) {
      state.device_code = obj
    },
    bluetoothSendDataStyle(state, obj) {
      state.bluetoothSendDataStyle = obj
    },
    paperSize(state, obj) {
      state.paperSize = obj
    },
    printerID(state, obj) {
      state.printerID = obj
    },
    printerUUID(state, obj) {
      state.printerUUID = obj
    },
    printerName(state, obj) {
      state.printerName = obj
    },
    server_uri(state, obj) {
      state.server_uri = obj
    },
    currentSheet(state, obj) {
      state.currentSheet = obj
    },
    sheetChangeTime(state, obj) {
      state.sheetChangeTime = obj
    },
    visitRecord(state, obj) {
      state.visitRecord = obj
    },
    cachedViews(state, obj) {
      state.cachedViews = obj
    },
    radarSupType(state, obj) {
      state.radarSupType = obj
    },
    /*,
        catchList(state, obj) {
            state.catchList = obj;
        }*/
    salesBranch(state, obj) {
      state.salesBranch = obj
    },
    sheetBranches(state, obj) {
      state.sheetBranches = obj
    },
    c_sheetBranches(state, obj) {
      state.c_sheetBranches = obj
    },
    c_printers(state, obj) {
      state.c_printers = obj
    },
    salesSender(state, obj) {
      state.salesSender = obj
    },
    salesOrderAccount(state, obj) {
      state.salesOrderAccount = obj
    },
    moveOutFromBranch(state, obj) {
      state.moveOutFromBranch = obj
    },
    moveOutToBranch(state, obj) {
      state.moveOutToBranch = obj
    },
    moveInFromBranch(state, obj) {
      state.moveInFromBranch = obj
    },
    moveInToBranch(state, obj) {
      state.moveInToBranch = obj
    },
    saleShowStockOnly(state, obj) {
      state.saleShowStockOnly = obj
    },
    sheet(state, obj) {
      state.sheet = obj
    },
    activeItemClass(state, obj) {
      state.activeItemClass = obj
    },
    activeAddress(state, obj) {
      state.activeAddress = obj
    },
    checkedSheetsCount(state, obj) {
      state.checkedSheetsCount += Number(obj)
    },
    stock(state, obj) {
      state.stock = obj
    },
    releaseType(state, obj) {
      state.releaseType = obj
    }, //production test  ,for test purpose
    activeNavBar(state, obj) {
      state.activeNavBar = obj
    },
    curStock(state, obj) {
      state.curStock = obj
    },
    stockBranchInfo(state, obj) {
      state.stockBranchInfo = obj
    },
    costsOrwholesaleFlag(state, obj) {
      state.costsOrwholesaleFlag = obj
    },
    showStockAmount(state, obj) {
      state.showStockAmount = obj
    },
    HRItem(state, obj) {
      state.HRItem = obj
    },
    trade_type(state, obj) {
      state.trade_type = obj
    },
    onloadPayways(state, obj) {
      state.onloadPayways = obj
    },
    ksPayWayObj(state, obj) {
      state.ksPayWayObj = obj
    },
    itemDispRecList(state, obj) {
      state.itemDispRecList = obj
    },
    btnSaveClick(state, obj) {
      state.btnSaveClick = obj
    },
    AllItemClass(state, obj) {
      state.AllItemClass = obj
    },
    infoRegionObj(state, obj) {
      state.infoRegionObj = obj
    },
    ItemClassObj(state, obj) {
      state.ItemClassObj = obj
    },
    shoppingCarObj(state, obj) {
      if (obj.clearFlag) {
        state.shoppingCarObj[obj.sheetType] = []
      } else if (obj.updateFlag) {
        state.shoppingCarObj[obj.sheetType] = obj.val
      } else {
        if (state.shoppingCarObj.hasOwnProperty(obj.sheetType) && state.shoppingCarObj[obj.sheetType]) {
          state.shoppingCarObj[obj.sheetType].unshift(obj.item)
        } else {
          state.shoppingCarObj[obj.sheetType] = []
          state.shoppingCarObj[obj.sheetType].unshift(obj.item)
        }
      }
    },
    updateShoppingCar(state, obj) {
      if(state.shoppingCarObj[obj.sheetType]) {
        state.shoppingCarObj[obj.sheetType] = obj.data
      } else {
        state.shoppingCarObj[obj.sheetType] = []
        state.shoppingCarObj[obj.sheetType] = obj.data
      }
    },
    clearShoppingCarObj(state, obj) {
      state.shoppingCarObj = obj
    },
    selectedItemsObj(state, obj) {
      state.selectedItemsObj[obj.sheetType] = obj.val
    },
    clearSelectedItemsObj(state, obj) {
      state.selectedItemsObj = obj
    },
    shoppingCarFinish(state, obj) {
      state.shoppingCarFinish = obj
    },
    accountList(state, obj) {
      state.accountList = obj
    },

    attrShowFlag(state, obj) {
      state.attrShowFlag = obj
    },
    activeSelectItem(state, obj) {
      state.activeSelectItem = obj
    },
    activeSelectItemAttrList(state, obj) {
      state.activeSelectItemAttrList = obj
    },
    distinctStockFlag(state, obj) {
      state.distinctStockFlag = obj
    },
    noItemIdSheetRows(state, obj) {
      state.noItemIdSheetRows = obj
    },
    selectedSheetRows(state, obj) {
      state.selectedSheetRows = obj
    },
    multiSelectOpenFlag(state, obj) {
      state.multiSelectOpenFlag = obj
    },
    attrOptions(state, obj) {
      state.attrOptions = obj
    },
    branches(state, obj) {
      state.branches = obj
    },
    printCheckAccountDialogResult(state, obj) {
      state.printCheckAccountDialogResult = obj
    },
    visitDayIndex(state, obj) {
      state.visitDayIndex = obj
    },
    visitShowMode(state, obj) {
      state.visitShowMode = obj
    },
    visitPlanIndex(state, obj) {
      state.visitPlanIndex = obj
    },
    saleSheetAddSheetRowState(state, obj) {
      state.saleSheetAddSheetRowState = obj
    },
    selectItemOrderResult(state, obj) {
      state.selectItemOrderResult = obj
    },
    unSubmitVisitTemplate(state, obj) {
      state.unSubmitVisitTemplate = obj
    },
    unSubmitInfoVisitTemplateMappingObj(state, obj) {
      state.unSubmitInfoVisitTemplateMappingObj = obj
    },
    unSubmitDisplayMaintainAction(state, obj) {
      state.unSubmitDisplayMaintainAction = obj
    },
    unSubmitDisplayMaintainActionForMonth(state, obj) {
      state.unSubmitDisplayMaintainActionForMonth = obj
    },
    salePromotionCombines(state, obj) {
      state.salePromotionCombines = obj
    },
    salePromotionSecKills(state, obj) {
      state.salePromotionSecKills = obj
    },
    salePromotionFullDiscs(state, obj) {
      state.salePromotionFullDiscs = obj
    },
    salePromotionFullGifts(state, obj) {
      state.salePromotionFullGifts = obj
    },
    salePromotionCashPrizes(state, obj) {
      state.salePromotionCashPrizes = obj
    },
    yjSelectCalendarCacheStore(state, obj) {
      if (state.yjSelectCalendarCacheStore[obj.key]) {
        state.yjSelectCalendarCacheStore[obj.key] = obj.value
      } else {
        state.yjSelectCalendarCacheStore[obj.key] = ""
        state.yjSelectCalendarCacheStore[obj.key] = obj.value
      }
    },
    yjCalendarSettingOptionsStore(state, obj) {
      state.yjCalendarSettingOptionsStore = obj
    },
    timeTypeInfo(state, obj) {
      state.timeTypeInfo = obj
    },
    selectCustomerOrderBy(state, obj) {
      state.selectCustomerOrderBy = obj
    },
    setFontSize(state, obj) {
      state.setFontSize = obj
    },
    showZeroStock(state, obj){
      state.showZeroStock = obj
    },
    showPositionStock(state, obj){
      state.showPositionStock = obj
    },
    showBatchStock(state, obj){
      state.showBatchStock = obj
    },
    showExpiredItems(state, obj){
      state.showExpiredItems = obj
    },
    showAvailItems(state,obj){
      state.showAvailItems = obj
    },
    checkAccountDateType(state, obj) {
      state.checkAccountDateType = obj
    },
    checkAccountDateSpecDateNum(state, obj) {
      state.checkAccountDateSpecDateNum = obj
    },
    queryItemSortType(state, obj){
      state.queryItemSortType = obj
    },
    stockQtyType(state, obj){
      state.stockQtyType = obj
    },
    isShowAllClientForClientLossWarning(state, obj){
      state.isShowAllClientForClientLossWarning = obj
    },
    orderQtyType(state, obj){
      state.orderQtyType = obj
    },
    customerPageOption(state, obj){
      state.customerPageOption = obj
    },
    itemArchivePageOption(state, obj){
      state.itemArchivePageOption = obj
    },
    operSetting(state, obj) {
      // state.personalSetting=obj
      if (obj.propFlag) {
        if (state.operSetting[obj.key]) {
          state.operSetting[obj.key] = obj.value
        } else {
          state.operSetting[obj.key] = ""
          state.operSetting[obj.key] = obj.value
        }
      } else {
        state.operSetting = obj
      }
    },
    setBranchList(state, obj){
      state.branchList = obj
    },
    setFromBranchList(state, obj){
      state.fromBranchList = obj
    },
    setToBranchList(state, obj){
      state.toBranchList = obj
    },
    setCurBranchPositionList(state, obj){
      state.curBranchPositionList = obj
    },
    setCurFromBranchPositionList(state, obj){
      state.curFromBranchPositionList = obj
    },
    setCurToBranchPositionList(state, obj){
      state.curToBranchPositionList = obj
    },
    // 部分数据的还原
    clearPartOfSheetState(state, obj) {
      state.trade_type = ""
      state.attrShowFlag = false
      state.shoppingCarFinish = false
      state.multiSelectOpenFlag = false
    },
    // 清空本地缓存（保留重要数据的版本）
    clearCreatePersistedState(state, obj) {
      // 只在完全退出登录时才清理这些重要数据
      if (obj && obj.fullLogout) {
        state.operKey = ""
        state.operInfo = { operRights: "", oper_name: "", mobile: "" }
        state.unsubmitedSheets_X = []
        state.unsubmitedSheets_DH = []
        state.unsubmitedSheets_T = []
        state.unsubmitedSheets_XD = []
        state.unsubmitedSheets_TD = []
        state.unsubmitedSheets_DB = []
        state.unsubmitedSheets_DBmoveOut = []
        state.unsubmitedSheets_DBmoveIn = []
        state.unsubmitedSheets_CG = []
        state.unsubmitedSheets_TG = []
        state.unsubmitedSheets_CT = []
        state.unsubmitedSheets_PD = []
        state.unsubmitedSheets_SS = []
        state.unsubmitedSheets_TJSP = []
        state.unsubmitedSheets_CX = []
        state.unsubmitedSheets_BS = []
        state.unsubmitedSheets_BQ = []
      }

      // 清理临时数据和缓存（保留用户设置和打印设置）
      state.classId = ""
      state.motherId = ""
      state.productist = []
      state.currentSheet = {}
      state.sheet = { noRed: "", sheetType: "" }
      state.activeItemClass = []
      state.activeAddress = {}
      state.stock = {}
      state.activeNavBar = 0
      state.curStock = {}
      state.AllItemClassId = ""
      state.itemList = []
      // 保留打印设置
      // state.printing = {} // 不清理
      // state.printBarcodeStyle = "noBarcode" // 不清理
      // state.printBarcodeStyleForSale = null // 不清理
      // state.printBarcodeStyleForMove = null // 不清理
      state.noticeStr = ""
      // 保留用户偏好设置
      // state.queryItemSortType = 'default' // 不清理
      // state.stockQtyType = 'default' // 不清理
      // state.orderQtyType = 'default' // 不清理
      state.isShowAllClientForClientLossWarning = false
      state.printBarcodePic = false
      state.printBarcodePicForMove = false
      state.companyNameToPrint = false
      state.showStockOnly = false
      state.account = {}
      state.companyList = []
      state.userActions = []
      state.selectedRegion = {}
      state.visitDay_sortByDistanceFlag = false
      //state.openBluetooth = false
      //state.paperSize = ''
      //state.printerID = ''
      //state.printerName = ''
      //state.printerUUID = {}
      //state.bluetoothType = ''
      //state.server_uri = ''
      //state.releaseType = 'production'
      state.visitRecord = {}
      state.salesBranch = {}
      state.salesSender = {}
      state.salesOrderAccount = {}
      state.moveOutFromBranch = {}
      state.moveOutToBranch = {}
      state.moveInFromBranch = {}
      state.moveInToBranch = {}
      state.saleShowStockOnly = ""
      state.stockBranchInfo = { branchID: "", branchName: "" }
      state.costsOrwholesaleFlag = null
      state.showStockAmount = null
      state.HRItem = {}
      state.trade_type = ""
      state.ksPayWayObj = null
      state.onloadPayways = null
      state.itemDispRecList = null
      state.btnSaveClick = false
      state.AllItemClass = null
      state.ItemClassObj = null
      state.infoRegionObj = []
      state.clearSelectedItemsObj = {}
      state.shoppingCarFinish = false
      state.clearShoppingCarObj = {}
      state.shoppingCarObj = {}

      state.attrShowFlag = false
      state.activeSelectItem = {}
      state.activeSelectItemAttrList = {}
      state.distinctStockFlag = false
      state.noItemIdSheetRows = []
      state.selectedSheetRows = {}
      state.multiSelectOpenFlag = false
      state.branches = []
      state.printCheckAccountDialogResult = []
      state.selectItemOrderResult = []

      state.visitDayIndex = ""
      state.visitShowMode = "visitUser"
      state.visitPlanIndex = ""
      state.saleSheetAddSheetRowState = "X"
      state.unSubmitVisitTemplate = []
      state.unSubmitDisplayMaintainAction = []
      state.unSubmitDisplayMaintainActionForMonth = []
      state.unSubmitInfoVisitTemplateMappingObj = ""

      state.salePromotionCombines = []
      state.salePromotionSecKills = []
      state.salePromotionFullDiscs = []
      state.salePromotionFullGifts = []
      state.salePromotionCashPrizes = []

      state.yjSelectCalendarCacheStore = {}
      state.yjCalendarSettingOptionsStore = []
      state.timeTypeInfo = []
      state.setFontSize = 1
      state.showZeroStock = false
      state.showPositionStock = false
      state.showBatchStock = false
      state.showExpiredItems = true
      state.showAvailItems = true
      state.operSetting = {
        fontSize: 1,
      }

      state.selectCustomerOrderBy = ""
      state.checkAccountDateType = ""
      state.checkAccountDateSpecDateNum = 0
      state.branchList = []
      state.curBranchPositionList = []
      state.fromBranchList = []
      state.curFromBranchPositionList = []
      state.toBranchList = []
      state.curToBranchPositionList = []
      state.customerPageOption = null
      state.itemArchivePageOption = null
    },
    acceptPrivacy(state, obj) {
      state.acceptPrivacy = obj
    },
    // 手动从 localStorage 重新加载所有持久化数据
    reloadPersistedData(state) {
      try {
        const vuexData = JSON.parse(localStorage.getItem('vuex') || '{}')
        
        // 重新加载所有持久化的数据
        if (vuexData.companyNameToPrint !== undefined) {
          state.companyNameToPrint = vuexData.companyNameToPrint
        }
        if (vuexData.operKey !== undefined) {
          state.operKey = vuexData.operKey
        }
        if (vuexData.operInfo !== undefined) {
          state.operInfo = vuexData.operInfo
        }
        if (vuexData.printBarcodeStyle !== undefined) {
          state.printBarcodeStyle = vuexData.printBarcodeStyle
        }
        // ... 恢复其他所有持久化字段
        
        console.log('已从 localStorage 重新加载所有持久化数据')
        
      } catch (e) {
        console.error('重新加载持久化数据失败:', e)
      }
    }
  },
  plugins: [
    createPersistedState({
      storage: window.sessionStorage,
      reducer(val) {
        return {
          classId: val.classId,
          motherId: val.motherId,
          productist: val.productist,
          // selectedSheetRows: val.selectedSheetRows,
         // currentSheet: val.currentSheet,
          sheet: val.sheet,
          activeItemClass: val.activeItemClass,
          activeAddress: val.activeAddress,
          stock: val.stock,
          activeNavBar: val.activeNavBar,
          curStock: val.curStock,
        }
      },
    }),
    createPersistedState({
      reducer(val) {
        // 🎯 精准缓存策略：只保留真正需要的数据
        return {
          // ✅ 用户身份和基础设置（必须保留）
          acceptPrivacy: val.acceptPrivacy,
          operKey: val.operKey,
          operInfo: val.operInfo,
          account: val.account,  // 👈 添加账号信息持久化

          // ✅ 用户偏好设置（需要保留）
          queryItemSortType: val.queryItemSortType,
          stockQtyType: val.stockQtyType,
          orderQtyType: val.orderQtyType,
          showStockOnly: val.showStockOnly,
          inPrivateMode: val.inPrivateMode,

          // ✅ 位置权限设置（需要保留）
          positionWeather: val.positionWeather,
          positionToCustom: val.positionToCustom,
          positionVisit: val.positionVisit,
          positionToCustomCurrent: val.positionToCustomCurrent,
          positionJourney: val.positionJourney,
          positionAttendance: val.positionAttendance,
          positionRadar: val.positionRadar,
          positionTrail: val.positionTrail,
          positionRights: val.positionRights,

          // ✅ 业务数据持久化（新增）
          visitRecord: val.visitRecord,
          c_printers: val.c_printers,
          salesBranch: val.salesBranch,
          c_sheetBranches: val.c_sheetBranches,
          salesSender: val.salesSender,
          salesOrderAccount: val.salesOrderAccount,
          moveOutFromBranch: val.moveOutFromBranch,
          moveOutToBranch: val.moveOutToBranch,
          moveInFromBranch: val.moveInFromBranch,
          moveInToBranch: val.moveInToBranch,

          // ✅ 单据缓存（限制数量，每种最多3个）
          unsubmitedSheets_X: val.unsubmitedSheets_X?.slice(0, 3),
          unsubmitedSheets_DH: val.unsubmitedSheets_DH?.slice(0, 3),
          unsubmitedSheets_T: val.unsubmitedSheets_T?.slice(0, 3),
          unsubmitedSheets_XD: val.unsubmitedSheets_XD?.slice(0, 3),
          unsubmitedSheets_TD: val.unsubmitedSheets_TD?.slice(0, 3),
          unsubmitedSheets_DB: val.unsubmitedSheets_DB?.slice(0, 3),
          unsubmitedSheets_DBmoveOut: val.unsubmitedSheets_DBmoveOut?.slice(0, 3),
          unsubmitedSheets_DBmoveIn: val.unsubmitedSheets_DBmoveIn?.slice(0, 3),
          unsubmitedSheets_CG: val.unsubmitedSheets_CG?.slice(0, 3),
          unsubmitedSheets_TG: val.unsubmitedSheets_TG?.slice(0, 3),
          unsubmitedSheets_CT: val.unsubmitedSheets_CT?.slice(0, 3),
          unsubmitedSheets_PD: val.unsubmitedSheets_PD?.slice(0, 3),
          unsubmitedSheets_SS: val.unsubmitedSheets_SS?.slice(0, 3),
          unsubmitedSheets_TJSP: val.unsubmitedSheets_TJSP?.slice(0, 3),
          unsubmitedSheets_CX: val.unsubmitedSheets_CX?.slice(0, 3),
          unsubmitedSheets_BS: val.unsubmitedSheets_BS?.slice(0, 3),
          unsubmitedSheets_BQ: val.unsubmitedSheets_BQ?.slice(0, 3),

          // ✅ 其他必要设置
          isShowAllClientForClientLossWarning: val.isShowAllClientForClientLossWarning,

          // ✅ 打印设置（用户配置，需要保留）
          printBarcodeStyle: val.printBarcodeStyle,
          printBarcodeStyleForSale: val.printBarcodeStyleForSale,
          printBarcodeStyleForMove: val.printBarcodeStyleForMove,
          printBarcodePic: val.printBarcodePic,
          printBarcodePicForMove: val.printBarcodePicForMove,
          companyNameToPrint: val.companyNameToPrint,

          // ✅ 蓝牙和打印设备配置（需要保留）
          openBluetooth: val.openBluetooth,
          useTemplateToPrint: val.useTemplateToPrint,
          printStyle: val.printStyle,
          printerType: val.printerType,
          printer_kind: val.printer_kind,
          cloudPrinterSetted: val.cloudPrinterSetted,
          printer_name: val.printer_name,
          device_id: val.device_id,
          printer_id: val.printer_id,
          printer_brand: val.printer_brand,
          check_code: val.check_code,
          device_code: val.device_code,
          bluetoothSendDataStyle: val.bluetoothSendDataStyle,
          paperSize: val.paperSize,
          printerID: val.printerID,
          printerName: val.printerName,
          printerUUID: val.printerUUID,
          bluetoothType: val.bluetoothType,
          server_uri: val.server_uri,

          // ✅ 业务配置（需要保留）
          visitDay_sortByDistanceFlag: val.visitDay_sortByDistanceFlag,
          saleShowStockOnly: val.saleShowStockOnly,
          releaseType: val.releaseType,
          costsOrwholesaleFlag: val.costsOrwholesaleFlag,
          showStockAmount: val.showStockAmount,
          trade_type: val.trade_type,

          // ❌ 移除大数据和临时状态（不再持久化）
          // visitRecord: val.visitRecord,           // 访问记录，临时数据
          // salesBranch: val.salesBranch,           // 销售分支，可重新获取
          // sheetBranches: val.sheetBranches,       // 单据分支，可重新获取
          // c_sheetBranches: val.c_sheetBranches,   // 分支数据，可重新获取
          // c_printers: val.c_printers,             // 打印机列表，可重新获取
          // salesSender: val.salesSender,           // 销售发送者，临时数据
          // salesOrderAccount: val.salesOrderAccount, // 销售订单账户，临时数据
          // moveOutFromBranch: val.moveOutFromBranch, // 调拨分支，临时数据
          // moveOutToBranch: val.moveOutToBranch,     // 调拨分支，临时数据
          // moveInFromBranch: val.moveInFromBranch,   // 调拨分支，临时数据
          // moveInToBranch: val.moveInToBranch,       // 调拨分支，临时数据
          // stockBranchInfo: val.stockBranchInfo,     // 库存分支，临时数据
          // HRItem: val.HRItem,                       // HR项目，临时数据

          // ✅ 支付和业务配置（需要保留）
          onloadPayways: val.onloadPayways,
          ksPayWayObj: val.ksPayWayObj,

          // ❌ 移除大数据和临时状态（不再持久化）
          // itemDispRecList: val.itemDispRecList,     // 商品分发记录，临时数据
          // btnSaveClick: val.btnSaveClick,            // 按钮点击状态，UI状态
          // AllItemClass: val.AllItemClass,            // 商品分类，大数据，可重新获取
          // ItemClassObj: val.ItemClassObj,            // 商品分类对象，大数据，可重新获取
          // selectedRegion: val.selectedRegion,        // 选中区域，临时状态
          // infoRegionObj: val.infoRegionObj,          // 区域信息，可重新获取
          // shoppingCarObj: val.shoppingCarObj,        // 购物车，大数据，临时状态
          // selectedItemsObj: val.selectedItemsObj,    // 选中商品，临时状态
          // shoppingCarFinish: val.shoppingCarFinish,  // 购物车完成状态，UI状态
         accountList: val.accountList,              // 账户列表，可重新获取
          // attrShowFlag: val.attrShowFlag,            // 属性显示标志，UI状态
          // distinctStockFlag: val.distinctStockFlag,  // 库存区分标志，UI状态
          // noItemIdSheetRows: val.noItemIdSheetRows,  // 无商品ID行，临时数据

          // ❌ 移除更多临时数据和大对象（不再持久化）
          // multiSelectOpenFlag: val.multiSelectOpenFlag,    // 多选开关，UI状态
          // attrOptions: val.attrOptions,                    // 属性选项，可重新获取
          // branches: val.branches,                          // 分支列表，可重新获取
          // printCheckAccountDialogResult: val.printCheckAccountDialogResult, // 打印对话框结果，临时状态
          // visitDayIndex: val.visitDayIndex,                // 访问日索引，临时状态
          // visitShowMode: val.visitShowMode,                // 访问显示模式，UI状态
          // visitPlanIndex: val.visitPlanIndex,              // 访问计划索引，临时状态
          // selectItemOrderResult: val.selectItemOrderResult, // 商品排序结果，临时状态

          // ✅ 拜访模板相关状态（需要持久化以支持APP重启后恢复）
          unSubmitVisitTemplate: val.unSubmitVisitTemplate, // 未提交访问模板数据
          unSubmitInfoVisitTemplateMappingObj: val.unSubmitInfoVisitTemplateMappingObj, // 模板映射对象
          unSubmitDisplayMaintainAction: val.unSubmitDisplayMaintainAction, // 维护操作数据
          unSubmitDisplayMaintainActionForMonth: val.unSubmitDisplayMaintainActionForMonth, // 月度维护数据

          // salePromotionCombines: val.salePromotionCombines, // 促销组合，可重新获取
          // salePromotionSecKills: val.salePromotionSecKills, // 促销秒杀，可重新获取

          // ✅ 显示设置（用户偏好，需要保留）
          showZeroStock: val.showZeroStock,
          showPositionStock: val.showPositionStock,
          showBatchStock: val.showBatchStock,
          showExpiredItems: val.showExpiredItems,
          showAvailItems: val.showAvailItems,

          // ✅ 日历缓存（用户选择记忆，需要保留）
          yjSelectCalendarCacheStore: val.yjSelectCalendarCacheStore,
          yjCalendarSettingOptionsStore: val.yjCalendarSettingOptionsStore,

          // ❌ 移除最后的大数据（不再持久化）
          // salePromotionFullDiscs: val.salePromotionFullDiscs, // 促销满减，可重新获取
          // salePromotionFullGifts: val.salePromotionFullGifts, // 促销满赠，可重新获取
          // salePromotionCashPrizes: val.salePromotionCashPrizes, // 促销兑奖，可重新获取
          // branchList: val.branchList,                         // 分支列表，可重新获取
          // curBranchPositionList: val.curBranchPositionList,   // 当前分支位置，可重新获取
          // fromBranchList: val.fromBranchList,                 // 来源分支，可重新获取
          // curFromBranchPositionList: val.curFromBranchPositionList, // 来源分支位置，可重新获取
          // toBranchList: val.toBranchList,                     // 目标分支，可重新获取
          // curToBranchPositionList: val.curToBranchPositionList, // 目标分支位置，可重新获取
          // timeTypeInfo: val.timeTypeInfo,                     // 时间类型信息，可重新获取

          // ✅ 最后的用户设置（需要保留）
          selectCustomerOrderBy: val.selectCustomerOrderBy,
          setFontSize: val.setFontSize,
          operSetting: val.operSetting,
          checkAccountDateType: val.checkAccountDateType,
          checkAccountDateSpecDateNum: val.checkAccountDateSpecDateNum,

          // ❌ UI状态不需要持久化
          // saleSheetAddSheetRowState: val.saleSheetAddSheetRowState, // UI状态
        }
      },
    }),
  ],
})
export default store
