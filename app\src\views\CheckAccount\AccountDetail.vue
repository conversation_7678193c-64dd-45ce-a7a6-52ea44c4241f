<template>
  <div class="public_box4">
    <div class="public_box4_m">
      <div class="public_box4_t">
        <div class="title_wapper">
          <van-checkbox v-model="isChecked" @click="selectAll()" @change="AllChecked" checked-color="#ff99aa"
            icon-size="18px" shape="square">
            客户名
          </van-checkbox>

          <div style="margin-left: -40px;">单据类</div>
          <div>金额</div>
        </div>

        <div class="receive_boxs">
          <ul class="receive">
            <li v-for="(sheet,index) in allSheets" :class="sheet.isChecked?'':'unchecked-sheet'" :key="sheet.sheet_no">
              <template v-if="sheet.type_sheet === 'sale'">
                <div @click="onSaleSheetNoClick($event,sheet,index)">
                  <div class="receive_item">
                    <template>
                      <span style="width:125px;" @click.stop="onViewDetails(sheet)">
                        <van-checkbox v-model="sheet.isChecked" @change="checkChange(sheet)" checked-color="#ff99aa"
                          icon-size="18px" shape="square">
                          {{index+1}}.{{sheet.sup_name}}
                        </van-checkbox>
                      </span>
                    </template>
                    <div style="display:flex;flex-direction:column;align-items:center">
                      <span>{{sheet.sheetType === 'X' ? '销售单' : sheet.sheetType === 'T' ? '退货单':'未知'}}</span>
                      <span style="font-size:16px;;color:#f77;margin-top:4px;"
                        v-if="checkAttribute(sheet,'returnAmt')">退</span>
                      <span style="font-size:16px;;color:#f77;margin-top:4px;" v-if="checkAttribute(sheet,'cl')">陈</span>
                      <span style="font-size:16px;;color:#f77;margin-top:4px;" v-if="checkAttribute(sheet,'dh')">定</span>
                      <span style="font-size:16px;;color:#f77;margin-top:4px;"
                        v-if="checkAttribute(sheet,'free')">赠</span>
                      <span style="font-size:16px;;color:#f77;margin-top:4px;"
                        v-if="toMoney(Math.abs(sheet.total_amount - sheet.now_disc_amount - sheet.now_pay_amount)) > 0">欠</span>
                      <span style="font-size:16px;;color:#f77;margin-top:4px;" v-if="sheet.now_disc_amount > 0">惠</span>
                      <span style="font-size:16px;;color:#f77;margin-top:4px;" v-if="checkAttribute(sheet,'bj')">变</span>
                      <span style="font-size:16px;;color:#f77;margin-top:4px;" v-if="checkAttribute(sheet,'j')">借</span>
                      <span style="font-size:16px;;color:#f77;margin-top:4px;" v-if="checkAttribute(sheet,'h')">还</span>
                      <span style="font-size:16px;;color:#f77;margin-top:4px;" v-if="checkAttribute(sheet,'hc')">换出</span>
                      <span style="font-size:16px;;color:#f77;margin-top:4px;" v-if="checkAttribute(sheet,'hr')">换入</span>
                      <span style="font-size:16px;;color:#f77;margin-top:4px;" v-if="checkAttribute(sheet,'ks')">客损</span>
                    </div>
                    <div>{{toMoney(sheet.real_get_amount - handleGetSheetFeeOut(sheet)) }}</div>
                  </div>
                  <div class="receive_son_bottom">
                    <div class="receive_son_bottom_flex">
                      <span
                        v-if="sheet.payway1_type == 'QT' && sheet.payway1_name !== '' && Number(sheet.payway1_amount) !== 0">{{sheet.payway1_name}}</span>
                      <span
                        v-if="sheet.payway2_type == 'QT' && sheet.payway2_name !== '' && Number(sheet.payway2_amount) !== 0">
                        <template
                          v-if="sheet.payway1_type == 'QT' && sheet.payway1_name !== '' && Number(sheet.payway1_amount) !== 0">/{{sheet.payway2_name}}</template>
                        <template v-else>{{sheet.payway2_name}}</template>
                      </span>
                      <span
                        v-if="sheet.payway3_type == 'QT' && sheet.payway3_name !== '' && Number(sheet.payway3_amount) !== 0">
                        <template
                          v-if="(sheet.payway1_type == 'QT' && sheet.payway1_name !== '' && Number(sheet.payway1_amount) !== 0)||(sheet.payway2_type == 'QT' && sheet.payway2_name !== '' && Number(sheet.payway2_amount) !== 0)">/{{sheet.payway3_name}}</template>
                        <template v-else>{{sheet.payway3_name}}</template>
                      </span>
                    </div>
                    <div class="receive_son_bottom_flex">
                      <!-- 修复退货单显示逻辑 -->
                      <span v-if="sheet.sheetType === 'T'">销:0</span>
                      <span v-else>销:{{toMoney(sheet.total_amount + (sheet.return_amount || 0))}}</span>

                      <!-- 退货金额显示 -->
                      <span v-if="sheet.sheetType === 'T' && Number(sheet.total_amount)"> - 退:{{toMoney(sheet.total_amount)}}</span>
                      <span v-else-if="Number(sheet.return_amount)"> - 退:{{toMoney(sheet.return_amount)}}</span>
                      <span v-if="Number(sheet.left_amount)"> - </span>
                      <span v-if="Number(sheet.left_amount)" style="color:red;">欠:{{processReturnAmount(sheet.left_amount)}}</span>
                      <span v-if="Number(sheet.prepay_amount)"> - 预:{{toMoney(sheet.prepay_amount)}}</span>
                      <span v-if="Number(sheet.now_disc_amount)"> - 惠:{{toMoney(sheet.now_disc_amount)}}</span>
                      <span v-if="Number(handleGetSheetFeeOut(sheet)) && sheet.sheetType === 'X'"> -
                        支:{{handleGetSheetFeeOut(sheet)}}</span>
                    </div>
                  </div>
                  <div class="receive_item_c">
                    <span class="receive_item_underline"
                      :class="{activeSheetNo: isSheetActive.includes(sheet.sheet_id) }">{{sheet.sheet_no}}</span>
                    <span>{{sheet.happen_time}}</span>
                  </div>
                  <div class="receive_item_c" v-if="sheet.make_brief" style="padding:0">
                    <span>{{sheet.make_brief}}</span>
                  </div>
                </div>
              </template>

              <template v-if="sheet.type_sheet === 'borrow'">
                <div @click="onBorrowSheetNoClick($event,sheet,index)">
                  <div class="receive_item">
                    <template>
                      <span style="width:125px;" @click.stop="onViewDetails(sheet)">
                        <van-checkbox v-model="sheet.isChecked" @change="checkChange(sheet)" checked-color="#ff99aa"
                          icon-size="18px" shape="square">
                          {{index+1}}.{{sheet.sup_name}}
                        </van-checkbox>
                      </span>
                    </template>
                    <div style="display:flex;flex-direction:column;align-items:center">
                      <span>{{sheet.sheetType === 'JH' ? '借货单' : sheet.sheetType === 'HH' ? '还货单':'未知'}}</span> 
          
                    </div>
                    <div></div>
                  </div>
             
                  <div class="receive_item_c">
                    <span class="receive_item_underline"
                      :class="{activeSheetNo: isSheetActive.includes(sheet.sheet_id) }">{{sheet.sheet_no}}</span>
                    <span>{{sheet.happen_time}}</span>
                  </div>
                  <div class="receive_item_c" v-if="sheet.make_brief" style="padding:0">
                    <span>{{sheet.make_brief}}</span>
                  </div>
                </div>
              </template>

              <template v-else-if="sheet.type_sheet === 'preget'">
                <div @click="onPregetSheetNoClick($event, sheet, index)">
                  <div class="receive_item">
                    <template>
                      <span style="width:125px;" @click.stop="onViewDetails(sheet)">
                        <van-checkbox v-model="sheet.isChecked" @change="checkChange(sheet)" checked-color="#ff99aa"
                          icon-size="18px" shape="square">
                          {{index+1}}.{{ sheet.sup_name }}
                        </van-checkbox>
                      </span>
                    </template>
                    <div>
                      <span>{{ sheet.sheet_no.substr(0, 2) === "DH" ? '定货单' : '预收款单' }}</span>
                    </div>
                    <div>{{ Number(sheet.real_get_amount) - Number(sheet.disc_amount) - Number(sheet.left_amount) }}</div>
                  </div>
                  <div class="receive_son_bottom">
                    <div class="receive_son_bottom_flex">
                      <span
                        v-if="sheet.payway1_type == 'QT' && sheet.payway1_name !== '' && Number(sheet.payway1_amount) !== 0">{{ sheet.payway1_name }}</span>
                      <span
                        v-if="sheet.payway2_type == 'QT' && sheet.payway2_name !== '' && Number(sheet.payway2_amount) !== 0">
                        <template
                          v-if="sheet.payway1_type == 'QT' && sheet.payway1_name !== '' && Number(sheet.payway1_amount) !== 0">/{{ sheet.payway2_name }}</template>
                        <template v-else>{{ sheet.payway2_name }}</template>
                      </span>
                    </div>
                    <div class="receive_son_bottom_flex" v-if="Number(sheet.disc_amount) || Number(sheet.left_amount)">
                      <span
                        v-if="Number(sheet.disc_amount) || Number(sheet.left_amount)">=预收:{{ Number(sheet.real_get_amount) }}
                        - 欠:{{ Number(sheet.left_amount) }} - 惠:{{ Number(sheet.disc_amount) }} </span>
                    </div>
                  </div>
                  <div class="receive_item_c">
                    <span class="receive_item_underline"
                      :class="{ activeSheetNo: isSheetActive.includes(sheet.sheet_id) }">{{ sheet.sheet_no }}</span>
                    <span>{{ sheet.happen_time }}</span>
                  </div>
                  <div class="receive_item_c" v-if="sheet.make_brief" style="padding:0">
                    <span>{{ sheet.make_brief }}</span>
                  </div>
                </div>
              </template>

              <template v-else-if="sheet.type_sheet === 'prepay'">
                <div @click="onprepaySheetNoClick($event,sheet,index)">
                  <div class="receive_item">
                  <template>
                    <span style="width:125px;" @click.stop="onViewDetails(sheet)">
                      <van-checkbox v-model="sheet.isChecked" @change="checkChange(sheet)" checked-color="#ff99aa"
                        icon-size="18px" shape="square">
                        {{index+1}}.{{sheet.sup_name}}
                      </van-checkbox>
                    </span>
                  </template>
                  <div>
                    <span>预付款单</span>
                  </div>
                  <div>{{Number(sheet.real_get_amount) - Number(sheet.disc_amount) - Number(sheet.left_amount)}}</div>
                  </div>
                  <div class="receive_son_bottom">
                    <div class="receive_son_bottom_flex">
                      <span
                        v-if="sheet.payway1_type == 'QT' && sheet.payway1_name !== '' && Number(sheet.payway1_amount) !== 0">{{sheet.payway1_name}}</span>
                      <span
                        v-if="sheet.payway2_type == 'QT' && sheet.payway2_name !== '' && Number(sheet.payway2_amount) !== 0">
                        <template
                          v-if="sheet.payway1_type == 'QT' && sheet.payway1_name !== '' && Number(sheet.payway1_amount) !== 0">/{{sheet.payway2_name}}</template>
                        <template v-else>{{sheet.payway2_name}}</template>
                      </span>
                    </div>
                    <div class="receive_son_bottom_flex"
                      v-if="Number(sheet.disc_amount) || Number(sheet.left_amount) || sheet.qtsr_amount">
                      <!--                  <span v-if="Number(sheet.disc_amount) || Number(sheet.left_amount)">=预付:{{Number(sheet.real_get_amount)}} </span>-->

                      <span>= 合:{{Number(sheet.real_get_amount ) +Number(sheet.disc_amount) +Number(sheet.left_amount)
                        +Number(sheet.qtsr_amount)}} - 欠:{{Number(sheet.left_amount)}} - 惠:{{Number(sheet.disc_amount)}} {{
                        sheet.qtsr_amount ? `- 其他收入:${sheet.qtsr_amount}` : '' }} </span>
                    </div>
                  </div>
                  <div class="receive_item_c">
                    <span class="receive_item_underline"
                      :class="{activeSheetNo: isSheetActive.includes(sheet.sheet_id) }">{{sheet.sheet_no}}</span>
                    <span>{{sheet.happen_time}}</span>
                  </div>
                  <div class="receive_item_c" v-if="sheet.make_brief" style="padding:0">
                    <span>{{sheet.make_brief}}</span>
                  </div>
                </div>
              </template>

              <template v-else-if="sheet.type_sheet === 'getArrear'">
                <div @click="onGetArrearsSheetNoClick($event,sheet,index)">
                  <div class="receive_item">
                  <template>
                    <span style="width:125px;" @click.stop="onViewDetails(sheet)">
                      <van-checkbox v-model="sheet.isChecked" @change="checkChange(sheet)" checked-color="#ff99aa"
                        icon-size="18px" shape="square">
                        {{index+1}}.{{sheet.sup_name}}
                      </van-checkbox>
                    </span>
                  </template>
                  <div>
                    <span>{{sheet.sheet_no.substr(0,2) === "SK" ? '收款单' : '付款单'}}</span>
                  </div>
                  <div>{{sheet.real_get_amount}}</div>
                  </div>
                  <div class="receive_son_bottom">
                    <div class="receive_son_bottom_flex">
                      <span v-if="sheet.payway1_type == 'QT' && sheet.payway1_name !== '' && Number(sheet.payway1_amount) !== 0">{{sheet.payway1_name}}</span>
                      <span v-if="sheet.payway2_type == 'QT' && sheet.payway2_name !== '' && Number(sheet.payway2_amount) !== 0">
                        <template v-if="sheet.payway1_type == 'QT' && sheet.payway1_name !== '' && Number(sheet.payway1_amount) !== 0">/{{sheet.payway2_name}}</template>
                        <template v-else>{{sheet.payway2_name}}</template>
                      </span>
                    </div>
                    <div class="receive_son_bottom_flex">
                      <span>= 合:{{Number(sheet.real_get_amount ) + Number(sheet.a_now_disc) + Number(sheet.prepay_amount) + Number(sheet.zc_amount) +  Number(sheet.qtsr_amount)}} - 惠:{{sheet.a_now_disc}} - 预:{{sheet.prepay_amount}}   {{ sheet.zc_amount ? `- 支:${sheet.zc_amount}` : ''   }}   {{ sheet.qtsr_amount ? `- 其他收入:${sheet.qtsr_amount}` : ''   }} </span>
                    </div>
                  </div>
                  <div class="receive_item_c">
                    <span class="receive_item_underline" :class="{activeSheetNo: isSheetActive.includes(sheet.sheet_id) }">{{sheet.sheet_no}}</span>
                    <span>{{sheet.happen_time}}</span>
                  </div>
                  <div class="receive_item_c" v-if="sheet.make_brief" style="padding:0">
                    <span>{{sheet.make_brief}}</span>
                  </div>
                </div>
              </template>

              <template v-else-if="sheet.type_sheet === 'feeOut'">
                <div @click="onFeeOutSheetNoClick($event,sheet,index)">
                  <div class="receive_item">
                  <template>
                    <span style="width:125px;" @click.stop="onViewDetails(sheet)">
                      <van-checkbox v-model="sheet.isChecked" @change="checkChange(sheet)" checked-color="#ff99aa" icon-size="18px" shape="square">
                        {{index+1}}.{{sheet.sup_name}}
                      </van-checkbox>
                    </span>
                  </template>
                  <!--                  <div></div>-->
                  <div>
                    <span>支出单</span>
                  </div>
                  <div>{{Number(sheet.real_get_amount)}}</div>
                  </div>
                  <div class="receive_son_bottom">
                    <div class="receive_son_bottom_flex">
                      <span v-if="sheet.payway1_type == 'QT' && sheet.payway1_name !== '' && Number(sheet.payway1_amount) !== 0">{{sheet.payway1_name}}</span>
                      <span v-if="sheet.payway2_type == 'QT' && sheet.payway2_name !== '' && Number(sheet.payway2_amount) !== 0">
                        <template v-if="sheet.payway1_type == 'QT' && sheet.payway1_name !== '' && Number(sheet.payway1_amount) !== 0">/{{sheet.payway2_name}}</template>
                        <template v-else>{{sheet.payway2_name}}</template>
                      </span>
                    </div>
                    <div class="receive_son_bottom_flex">
                      <span>{{sheet.fee_sub_name}}</span>
                    </div>
                  </div>
                  <div class="receive_item_c">
                    <span class="receive_item_underline" :class="{activeSheetNo: isSheetActive.includes(sheet.sheet_id) }">{{sheet.sheet_no}}</span>
                    <span>{{sheet.happen_time}}</span>
                  </div>
                  <div class="receive_item_c" v-if="sheet.make_brief" style="padding:0">
                    <span>{{sheet.make_brief}}</span>
                  </div>
                </div>
              </template>

              <template v-else-if="sheet.type_sheet === 'income'">
                <div>
                  <div class="receive_item">
                  <template>
                    <span style="width:125px;" @click.stop="onViewDetails(sheet)">
                      <van-checkbox v-model="sheet.isChecked" @change="checkChange(sheet)" checked-color="#ff99aa" icon-size="18px" shape="square">
                        <span v-if="sheet.sup_name">{{index+1}}.{{sheet.sup_name}}</span>
                      </van-checkbox>
                    </span>
                  </template>

                  <div>
                    <span>其他收入单</span>
                  </div>
                  <div>{{Number(sheet.real_get_amount)}}</div>
                  </div>
                  <div class="receive_son_bottom">
                    <div class="receive_son_bottom_flex">
                      <span v-if="sheet.payway1_type == 'QT' && sheet.payway1_name !== '' && Number(sheet.payway1_amount) !== 0">{{sheet.payway1_name}}</span>
                      <span v-if="sheet.payway2_type == 'QT' && sheet.payway2_name !== '' && Number(sheet.payway2_amount) !== 0">
                        <template v-if="sheet.payway1_type == 'QT' && sheet.payway1_name !== '' && Number(sheet.payway1_amount) !== 0">/{{sheet.payway2_name}}</template>
                        <template v-else>{{sheet.payway2_name}}</template>
                      </span>
                    </div>
                    <div class="receive_son_bottom_flex">
                      <span>{{sheet.fee_sub_name}}</span>
                    </div>
                  </div>
                  <div class="receive_item_c">
                    <span>{{sheet.sheet_no}}</span>
                    <span>{{sheet.happen_time}}</span>
                  </div>
                  <div class="receive_item_c" v-if="sheet.make_brief" style="padding:0">
                    <span>{{sheet.make_brief}}</span>
                  </div>
                </div>
              </template>
            </li>

            <li style="background: #fff">
              <div></div>
              <div></div>
              <div style="display:flex;justify-content:flex-end;flex-wrap: wrap">
                <div class="amout-style" v-if="Number(saleSum.sale_amount) !== 0">销售:{{saleSum.sale_amount}}&nbsp;</div>
                <div class="amout-style" v-if="Number(saleSum.return_amount) !== 0">退货:{{saleSum.return_amount}}&nbsp;</div>
                <div class="amout-style" v-if="Number(saleSum.feeout_amount) !== 0">销售内支出:{{toMoney(saleSum.feeout_amount)}}</div>
                <div class="amout-style" v-if="(Number(saleSum.sale_amount) - Number(saleSum.return_amount)) !== 0">销售净额: {{toMoney(Number(saleSum.sale_amount) - Number(saleSum.return_amount))}}&nbsp;</div>
                <div class="amout-style" v-if="Number(pregetSheetSum.real_get_amount_Pre) !== 0">预收款{{pregetSheetSum.real_get_amount_Pre}}&nbsp;</div>
                <div class="amout-style" v-if="Number(pregetSheetSum.real_get_amount_DH) !== 0">定货款{{pregetSheetSum.real_get_amount_DH}}&nbsp;</div>
                <div class="amout-style" v-if="Number(prepaySheetSum.real_get_amount) !== 0">预付款{{prepaySheetSum.real_get_amount}}&nbsp;</div>
                <div class="amout-style" v-if="Number(getArrearSheetSum.real_get_amount_SK) !== 0">收欠款{{getArrearSheetSum.real_get_amount_SK}}&nbsp;</div>
                <div class="amout-style" v-if="Number(getArrearSheetSum.real_get_amount_FK) !== 0">付欠款{{getArrearSheetSum.real_get_amount_FK}}</div>
                <div class="amout-style" v-if="Number(feeOutSheetSum.real_get_amount)!== 0">费用支出{{feeOutSheetSum.real_get_amount}}&nbsp;</div>
                <div class="amout-style" v-if="Number(incomeSheetSum.real_get_amount) !== 0">其他收入{{incomeSheetSum.real_get_amount}}&nbsp;</div>
              </div>
            </li>
          </ul>
        </div>
      </div>

      <v-check-payway-sum ref="checkPayWaySum" :sheets="sheets">
      </v-check-payway-sum>
    </div>
  </div>
</template>
<script>
import CheckPayWaySum from "./CheckPayWaySum"
import Printing from "../Printing/Printing"
import { Toast, Checkbox } from "vant"
export default {
  name: "AccountDetail",
  data() {
    return {
      listData: [],
      total: [],
      selectSales: false,
      selectReturn: false,
      selectOwe: false,
      selectPrepay: false,
      selectFeeout: false,
      detailedList: [],
      detailedfeeOut: [],
      arrearsList: {},
      nowDisc: 0,
      Totals: 0,
      arreaesSum: [],
      isChecked: true,
      isSheetActive: [],
      isSortBySheetType: false,//是否按照单据类型排序 这里预留变量之后可能做按钮
      sheet: {},
    };
  },
  props: {
    bindOnloadDatas: Object,
    sheets: Object
  },
  watch: {
    'bindOnloadDatas': {
      immediate: true,
      handler(obj) {
        this.onloadData(obj);
      },
    }/*,
    'sheets':{
      immediate: true,
      handler(obj){
        this.sheets=obj;
      },
    }*/
  },
  computed: {
    pregetStartRow() {
      return this.sheets.saleSheets ? this.sheets.saleSheets.length : 0
    },
    prepayStartRow() {
      return (this.sheets.saleSheets ? this.sheets.saleSheets.length : 0) + (this.sheets.pregetSheets ? this.sheets.pregetSheets.length : 0)
    },
    getArrearsStartRow() {
      let num = (this.sheets.saleSheets ? this.sheets.saleSheets.length : 0) + (this.sheets.pregetSheets ? this.sheets.pregetSheets.length : 0) + (this.sheets.prepaySheets ? this.sheets.prepaySheets.length : 0)
      return num
    },
    feeOutStartRow() {
      let num = (this.sheets.saleSheets ? this.sheets.saleSheets.length : 0) + (this.sheets.pregetSheets ? this.sheets.pregetSheets.length : 0) + (this.sheets.getArrearSheets ? this.sheets.getArrearSheets.length : 0) + (this.sheets.prepaySheets ? this.sheets.prepaySheets.length : 0)
      return num
    },
    incomeStartRow() {
      let num = (this.sheets.saleSheets ? this.sheets.saleSheets.length : 0) + (this.sheets.pregetSheets ? this.sheets.pregetSheets.length : 0) + (this.sheets.prepaySheets ? this.sheets.prepaySheets.length : 0) + (this.sheets.getArrearSheets ? this.sheets.getArrearSheets.length : 0) + (this.sheets.feeOutSheets ? this.sheets.feeOutSheets.length : 0)
      return num
    },
    saleSum() {
      var saleSum = {
        real_get_amount: 0, sale_amount: 0, return_amount: 0, prepay_amount: 0,
        left_amount: 0, disc_amount: 0, total_count: 0, feeout_amount: 0
      }
      if (this.sheets.saleSheets) {
        var that = this
        this.sheets.saleSheets.forEach(sheet => {
          if (!that.$store.state.checkedSheetsCount || sheet.isChecked) {
            if (sheet.sheetType == 'X') {
              saleSum.total_count++
              saleSum.disc_amount += Number(sheet.now_disc_amount)
              saleSum.left_amount += Number(sheet.left_amount)
              saleSum.prepay_amount += Number(sheet.prepay_amount)
              if (sheet.payway1_type === 'ZC') { saleSum.feeout_amount += Number(sheet.payway1_amount) }
              if (sheet.payway2_type === 'ZC') { saleSum.feeout_amount += Number(sheet.payway2_amount) }
              if (sheet.payway3_type === 'ZC') { saleSum.feeout_amount += Number(sheet.payway3_amount) }

              sheet.sheetRows.forEach(item => {
                if (Number(item.sub_amount) > 0 && item.trade_type !== 'KS') {
                  saleSum.sale_amount += Number(item.sub_amount)
                }
                if (Number(item.sub_amount) < 0) {
                  saleSum.return_amount -= Number(item.sub_amount)
                }
                if (item.trade_type !== 'KS') {
                  saleSum.real_get_amount += Number(item.sub_amount)
                }
              })
            } else if (sheet.sheetType == 'T') {
              saleSum.total_count++
              saleSum.disc_amount += Number(sheet.now_disc_amount)
              saleSum.left_amount += Number(sheet.left_amount)
              saleSum.prepay_amount += Number(sheet.prepay_amount)

              if (sheet.payway1_type === 'ZC') { saleSum.feeout_amount -= Number(sheet.payway1_amount)*-1 }
              if (sheet.payway2_type === 'ZC') { saleSum.feeout_amount -= Number(sheet.payway2_amount)*-1 }
              if (sheet.payway3_type === 'ZC') { saleSum.feeout_amount -= Number(sheet.payway3_amount)*-1 }

              sheet.sheetRows.forEach(item => {
                if (Number(item.sub_amount) > 0) {
                  saleSum.return_amount += Number(item.sub_amount)
                  saleSum.real_get_amount -= Number(item.sub_amount)
                }
              })
            }
          }
        })
        saleSum.real_get_amount -= Number(saleSum.disc_amount) + Number(saleSum.left_amount) + Number(saleSum.prepay_amount) + Number(saleSum.feeout_amount)
      }
      for (let property in saleSum) {
        if (property != 'total_count') {
          saleSum[property] = toMoney(saleSum[property])
        }
      }
      return saleSum
    },
    pregetSheetSum() {
      //real_get_amount_DH:0,real_get_amount_Pre:0 展示打印用
      var sum = { sheet_title: '预收款单', preget_amount: 0, real_get_amount: 0, left_amount: 0, count: 0, disc_amount: 0, real_get_amount_DH: 0, real_get_amount_Pre: 0 }
      if (this.sheets.pregetSheets) {
        var that = this
        this.sheets.pregetSheets.forEach(function (sheet) {
          if (!that.$store.state.checkedSheetsCount || sheet.isChecked) {
            sum.count++
            sum.real_get_amount += Number(sheet.real_get_amount)
            sum.disc_amount += Number(sheet.disc_amount)
            sum.left_amount += Number(sheet.left_amount)
            sum.preget_amount = Number(sum.real_get_amount)
            sum.real_get_amount_DH += Number(sheet.sheet_no.substr(0, 2) === "DH" ? Number(sheet.real_get_amount) : 0)
            sum.real_get_amount_Pre += Number(sheet.sheet_no.substr(0, 2) !== "DH" ? Number(sheet.real_get_amount) : 0)
          }
        })
        sum.real_get_amount -= Number(sum.disc_amount) + Number(sum.left_amount)
        sum.real_get_amount_DH = toMoney(sum.real_get_amount_DH)
        sum.real_get_amount_Pre = toMoney(sum.real_get_amount_Pre)
      }
      sum.real_get_amount = toMoney(sum.real_get_amount)
      console.log("this.sheets.sum", sum)
      return sum
    },
    prepaySheetSum() {
      //real_get_amount_DH:0,real_get_amount_Pre:0 展示打印用
      var sum = { sheet_title: '预付款单', prepay_amount: 0, real_get_amount: 0, left_amount: 0, count: 0, disc_amount: 0, real_get_amount_DH: 0, real_get_amount_Pre: 0 }
      if (this.sheets.prepaySheets) {
        var that = this
        this.sheets.prepaySheets.forEach(function (sheet) {
          if (!that.$store.state.checkedSheetsCount || sheet.isChecked) {
            sum.count++
            sum.real_get_amount += Number(sheet.real_get_amount)
            sum.disc_amount += Number(sheet.disc_amount)
            sum.left_amount += Number(sheet.left_amount)
            sum.prepay_amount = Number(sum.real_get_amount)
          }
        })
        //sum.real_get_amount -= Number(sum.disc_amount) + Number(sum.left_amount)
      }
      sum.real_get_amount = toMoney(sum.real_get_amount)
      return sum
    },
    getArrearSheetSum() {
      var sum = { sheet_title: '收款单', real_get_amount: 0, count: 0, disc_amount: 0, real_get_amount_FK: 0, real_get_amount_SK: 0 }
      if (this.sheets.getArrearSheets) {
        var that = this
        this.sheets.getArrearSheets.forEach(function (sheet) {
          if (!that.$store.state.checkedSheetsCount || sheet.isChecked) {
            sum.count++
            sum.real_get_amount += Number(sheet.real_get_amount)
            sum.disc_amount += Number(sheet.a_now_disc)
            sum.real_get_amount_FK += Number(sheet.sheet_no.substr(0, 2) === "FK" ? Number(sheet.real_get_amount) : 0)
            sum.real_get_amount_SK += Number(sheet.sheet_no.substr(0, 2) == "SK" ? Number(sheet.real_get_amount) : 0)
          }
        })
      }
      sum.real_get_amount = toMoney(sum.real_get_amount)
      sum.disc_amount = toMoney(sum.disc_amount)
      return sum
    },
    feeOutSheetSum() {
      var sum = { sheet_title: '费用支出单', real_get_amount: 0, count: 0 }
      if (this.sheets.feeOutSheets) {
        var that = this
        this.sheets.feeOutSheets.forEach(function (sheet) {
          if (!that.$store.state.checkedSheetsCount || sheet.isChecked) {
            sum.count++
            sum.real_get_amount += Number(sheet.real_get_amount)
          }
        })
      }
      sum.real_get_amount = toMoney(sum.real_get_amount)
      return sum
    },
    incomeSheetSum() {
      var sum = { sheet_title: '其他收入单', real_get_amount: 0, count: 0 }
      if (this.sheets.feeOutSheets) {
        var that = this
        this.sheets.incomeSheets.forEach(function (sheet) {
          if (!that.$store.state.checkedSheetsCount || sheet.isChecked) {
            sum.count++
            sum.real_get_amount += Number(sheet.real_get_amount)
          }
        })
      }
      sum.real_get_amount = toMoney(sum.real_get_amount)
      return sum
    },
    totalAmount () {
      return
    },
    allSheets(){
      //合并单据并进行排序
      let allSheets = [
      ...this.sheets.saleSheets.map(sheet => ({ ...sheet, type_sheet: 'sale' })),
      ...this.sheets.borrowSheets.map(sheet => ({ ...sheet, type_sheet: 'borrow' })),
      ...this.sheets.pregetSheets.map(sheet => ({ ...sheet, type_sheet: 'preget' })),
      ...this.sheets.prepaySheets.map(sheet => ({ ...sheet, type_sheet: 'prepay' })),
      ...this.sheets.getArrearSheets.map(sheet => ({ ...sheet, type_sheet: 'getArrear' })),
      ...this.sheets.feeOutSheets.map(sheet => ({ ...sheet, type_sheet: 'feeOut' })),
      ...this.sheets.incomeSheets.map(sheet => ({ ...sheet, type_sheet: 'income' }))
      ];
      if(!this.isSortBySheetType){
        allSheets= allSheets.slice().sort((a, b) => {
          //按时间排序
          return b.happen_time.localeCompare(a.happen_time);
        });
      }
      return allSheets;
    },

    //处理退款tomoney
    processedReturnAmount() {
            const amount = this.sheet.return_amount;
            if (amount === undefined || amount === "" || isNaN(amount)) {
                return amount;
            }
            const num = parseFloat(amount);
            const parts = num.toString().split('.');
            if (parts.length === 1) {
                return num;
            } else if (parts[1].length === 1) {
                return num;
            } else {
                return window.toMoney(num, 2);
            }
        },

  },
  mounted() {
    debugger
    console.log('表单:', this.sheets)
    this.computeSaleSheets()
  },
  components: {
    'v-check-payway-sum': CheckPayWaySum,
    "van-checkbox": Checkbox
  },
  methods: {

    
    // isbj(sheet) {
    //   if (!sheet.sheet_attribute || sheet.sheet_attribute == '{}') {
    //     return false
    //   }
    //   const sheet_attribute = JSON.parse(sheet.sheet_attribute)
    //   if (typeof sheet_attribute.bj == undefined) {
    //     return false
    //   }
    //   return Boolean(sheet_attribute.bj)
    // },
      checkAttribute(sheet, attribute) {
      if (!sheet.sheet_attribute || sheet.sheet_attribute == '{}') {
        return false;
      }
      const sheet_attribute = JSON.parse(sheet.sheet_attribute);
      if (typeof sheet_attribute[attribute] === 'undefined') {
        return false;
      }
      return Boolean(sheet_attribute[attribute]);
    },
    fix(num, n = 2) {
      var pn = Math.pow(10, n);
      if (num) {
        return Number(Math.round(num * pn) / pn);
      } else {
        return 0;
      }
    },
    onloadData(obj) {
      /*
      if (obj) {
        if (obj.sheets&&obj.sheets.length>0) {
          let Sum = 0;
          let SumLen = 0;
          let nowdisc = 0;
          let total = 0;
          this.arreaesSum = []
          obj.sheets.map(item=>{
            item.isChecked = true;
            this.arreaesSum.push(item.sheet_id)
            nowdisc+= Number(item.x_now_disc? item.x_now_disc:0)
            total+= Number(item.x_total? item.x_total:0)
            if(item.sheet_type === "SK"){
              SumLen+=Number(1)
              Sum+=Number(item.x_amount? item.x_amount:0)
            }
          })
          let objs ={
                amount:Sum,
                len:SumLen
          }
          this.detailedList = obj.sheets;
          this.Totals = total;
          this.arrearsList = objs;
          this.nowDisc = nowdisc;
        }
        if (obj.feeOut&&obj.feeOut.length>0) this.detailedfeeOut = obj.feeOut[0];
      }*/
    },
    // 补充退货款和销售金额计算
    computeSaleSheets() {
      let saleSheets = this.sheets.saleSheets
      if (saleSheets) {
        saleSheets.forEach(sheet => {
          if (sheet.sheetType == 'X') {
            // 销售单：计算退货金额
            let return_amount = 0
            sheet.sheetRows.forEach(row => {
              if (Number(row.sub_amount) < 0) {
                return_amount -= Number(row.sub_amount)
              }
            })
            sheet.return_amount = return_amount
            sheet.total_amount = Number(sheet.total_amount)
          } else if (sheet.sheetType == 'T') {
            // 退货单：total_amount就是退货金额，销售金额为0
            sheet.return_amount = 0  // 退货单本身不产生新的退货
            sheet.total_amount = Number(sheet.total_amount)  // 这是退货的金额
          }
        });
      }
    },
    onSaleSheetNoClick(e, sheet, index) {
      e.preventDefault()
      e.stopPropagation()
      this.isSheetActive.push(sheet.sheet_id)

      // 设置全局变量，用于销售单页面处理返回时的数据更新
      window.g_curSheetInList = sheet
      window.g_curSheetList = this.sheets[`${sheet.type_sheet}Sheets`] || []

      this.$router.push({
        path: '/SaleSheet',
        query: {
          sheetID: sheet.sheet_id,
          sheetType: sheet.sheetType,
          sheets: this.sheets[`${sheet.type_sheet}Sheets`] || []
        }
      })
    },
    onBorrowSheetNoClick(e, sheet, index) {
      e.preventDefault()
      e.stopPropagation()
      this.isSheetActive.push(sheet.sheet_id)
      this.$router.push({ path: '/BorrowItemSheet', query: { sheetID: sheet.sheet_id, sheetType: sheet.sheetType } })
    },
    onPregetSheetNoClick(e, sheet, index) {
      e.preventDefault()
      e.stopPropagation()
      this.isSheetActive.push(sheet.sheet_id)
      if (sheet.sheet_no.substr(0, 2) === "DH") {
        this.$router.push({ path: '/SaleSheet', query: { sheetID: sheet.sheet_id, sheetType: "DH" } })
      } else {
        this.$router.push({ path: "/PrepaySheet", query: { sheetID: sheet.sheet_id } })
      }
    },
    onprepaySheetNoClick(e, sheet, index) {
      e.preventDefault()
      e.stopPropagation()
      this.isSheetActive.push(sheet.sheet_id)
      this.$router.push({ path: "/PrepaySheet", query: { sheetID: sheet.sheet_id, sheetType: sheet.sheetType } })
    },
    onGetArrearsSheetNoClick(e, sheet, index) {
      e.preventDefault()
      e.stopPropagation()
      this.isSheetActive.push(sheet.sheet_id)
      this.$router.push({ path: "/GetAndPayArrearsSheet?sheetType=SK", query: { sheetID: sheet.sheet_id } })
    },
    onFeeOutSheetNoClick(e, sheet, index) {
      e.preventDefault()
      e.stopPropagation()
      this.isSheetActive.push(sheet.sheet_id)
      this.$router.push({ path: "/FeeOut", query: { sheetID: sheet.sheet_id } })
    },
    onViewDetails(sheet) {
      //做了合并展示 这里同步checkbox状态
      let sheetsArray = this.sheets[`${sheet.type_sheet}Sheets`];
      let originalSheet = sheetsArray.find(s => s.sheet_no === sheet.sheet_no);
      if (originalSheet) {
        originalSheet.isChecked = !originalSheet.isChecked;
      }
    },
    checkChange(sheet) {
      //console.log("点击checkbox",sheet)
      if (!this.sheets.sheet_id) {
        if (sheet.isChecked) {
          this.$store.commit('checkedSheetsCount', -1)
          // this.checkedSheetsCount++;
        } else {
          this.$store.commit('checkedSheetsCount', 1)
          // this.checkedSheetsCount--;
        }
      }
      /**
       *
       * sheets.saleSheets
       *  sheets.prepaySheets
       * sheets.getArrearSheets
       * sheets.feeOutSheets
       * sheets.incomeSheets
       */
      const sheetNum = this.sheets.saleSheets.length +  this.sheets.borrowSheets.length + this.sheets.prepaySheets.length + this.sheets.pregetSheets.length + this.sheets.getArrearSheets.length + this.sheets.feeOutSheets.length + this.sheets.incomeSheets.length
      let saleSheets = this.sheets.saleSheets.filter(sheet => { return sheet.isChecked }).length
      let borrowSheets = this.sheets.borrowSheets.filter(sheet => { return sheet.isChecked }).length
 
      let getArrearSheets = this.sheets.getArrearSheets.filter(sheet => { return sheet.isChecked }).length
      let pregetSheets = this.sheets.pregetSheets.filter(sheet => { return sheet.isChecked }).length
      let prepaySheets = this.sheets.prepaySheets.filter(sheet => { return sheet.isChecked }).length
      let feeOutSheets = this.sheets.feeOutSheets.filter(sheet => { return sheet.isChecked }).length
      let incomeSheets = this.sheets.incomeSheets.filter(sheet => { return sheet.isChecked }).length
      const checkedOptions = saleSheets + borrowSheets + getArrearSheets + pregetSheets + prepaySheets + feeOutSheets + incomeSheets
      this.isChecked = (checkedOptions === sheetNum)

    },
    dateFormat(fmt, date) {
      let ret
      const opt = {
        "Y+": date.getFullYear().toString(),        // 年
        "m+": (date.getMonth() + 1).toString(),     // 月
        "d+": date.getDate().toString(),            // 日
        "H+": date.getHours().toString(),           // 时
        "M+": date.getMinutes().toString(),         // 分
        "S+": date.getSeconds().toString()          // 秒
        // 有其他格式化字符需求可以继续添加，必须转化成字符串
      }
      for (let k in opt) {
        ret = new RegExp("(" + k + ")").exec(fmt)
        if (ret) {
          fmt = fmt.replace(ret[1], (ret[1].length == 1) ? (opt[k]) : (opt[k].padStart(ret[1].length, "0")))
        }
      }
      return fmt
    },
    getPrintInfo(sellerName) {
      return {
        saleSheets: this.sheets.saleSheets.filter(sheet => sheet.isChecked == true),
        borrowSheets: this.sheets.borrowSheets.filter(sheet => sheet.isChecked == true),
 
        getArrearSheets: this.sheets.getArrearSheets.filter(sheet => sheet.isChecked == true),
        pregetSheets: this.sheets.pregetSheets.filter(sheet => sheet.isChecked == true),
        prepaySheets: this.sheets.prepaySheets.filter(sheet => sheet.isChecked == true),
        feeOutSheets: this.sheets.feeOutSheets.filter(sheet => sheet.isChecked == true),
        incomeSheets: this.sheets.incomeSheets.filter(sheet => sheet.isChecked == true),
        sellerName: sellerName,
        happen_time: this.sheets.happen_time || this.dateFormat("YYYY-mm-dd HH:MM", new Date()),
        paywaySum: this.$refs.checkPayWaySum.paywaySum,
        saleSum: this.saleSum,
        pregetSheetSum: this.pregetSheetSum,
        prepaySheetSum: this.prepaySheetSum,
        getArrearSheetSum: this.getArrearSheetSum,
        feeOutSheetSum: this.feeOutSheetSum,
        incomeSheetSum: this.incomeSheetSum
      }
    },
    selectAll() {
      this.sheets.saleSheets.forEach(saleSheet => {
        saleSheet.isChecked = this.isChecked
      })
      this.sheets.borrowSheets.forEach(sheet => {
        sheet.isChecked = this.isChecked
      })
      
      this.sheets.pregetSheets.forEach(pregetSheet => {
        pregetSheet.isChecked = this.isChecked
      })
      this.sheets.prepaySheets.forEach(prepaySheets => {
        prepaySheets.isChecked = this.isChecked
      })
      this.sheets.getArrearSheets.forEach(getArrearSheet => {
        getArrearSheet.isChecked = this.isChecked
      })
      this.sheets.feeOutSheets.forEach(feeOutSheet => {
        feeOutSheet.isChecked = this.isChecked
      })

      this.sheets.incomeSheets.forEach(incomeSheet => {
        incomeSheet.isChecked = this.isChecked
      })

    },
    AllChecked(isChecked) {
      console.log("checked", isChecked)
      if (isChecked) {
        this.$store.commit('checkedSheetsCount', -1)
      } else {
        this.$store.commit('checkedSheetsCount', 1)
      }
    },
    print(sellerName, printStartAndEndTime) {
      var sheet = this.getPrintInfo(sellerName)
      sheet.printStartAndEndTime = printStartAndEndTime
      Printing.printCheckAccountDetail(sheet, res => {
        if (res.result == "OK") {
          Toast.success('打印成功');
        }
        else {
          Toast.fail(res.msg)
        }
      })
    },
    handleGetSheetFeeOut(sheet) {
      let feeOutTotal = 0;
      if (sheet.payway1_type === 'ZC') { feeOutTotal += Number(sheet.payway1_amount) }
      if (sheet.payway2_type === 'ZC') { feeOutTotal += Number(sheet.payway2_amount) }
      if (sheet.payway3_type === 'ZC') { feeOutTotal += Number(sheet.payway3_amount) }
      return this.fix(feeOutTotal)
    },

    //处理退款tomoney
    processReturnAmount(amount) {
            if (amount === undefined || amount === "" || isNaN(amount)) {
                return amount;
            }
            const num = parseFloat(amount);
            const parts = num.toString().split('.');
            if (parts.length === 1) {
                return num;
            } else if (parts[1].length === 1) {
                return num;
            } else {
                return window.toMoney(num, 2);
            }
        },
  }
}
</script>
<style lang="less" scoped>
@flex_acent_jbw: {
  display: flex;
  align-items: center;
  justify-content: space-between;
};
@flex_acent_jend: {
  display: flex;
  align-items: center;
  justify-content: flex-end;
};
@flex_a_flex: {
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
};
.checked-sheet {
}
.unchecked-sheet {
  background-color: #f6f6f6;
}
.li_selected {
  background-color: #a11e3a;
  color: #ffffff;
}
.public_box4 {
  height: 96.5%;
  overflow-y: auto;
  overflow-x: hidden;
  .public_box4_t {
    margin-bottom: 5px;
  }
  .public_box4_m {
    height: auto;
    overflow: hidden;
  }
}
.receive_boxs {
  height: calc(100% - 42px);
  background-color: #ffffff;
  padding: 0 5px;
  font-size: 15px;
  .receive {
    height: auto;
    clear: both;
    li {
      height: auto;
      clear: both;
      padding: 5px;
      border-bottom: 1px solid #f2f2f2;
      .receive_item {
        height: auto;
        @flex_acent_jbw();
        div {
          flex: 1;
          text-align: center;
        }
        div:first-child {
          text-align: left;
        }
        div:last-child {
          text-align: right;
        }
      }
      .receive_item_c {
        font-size: 13px;
        color: gray;
        @flex_acent_jbw();
        padding: 5px 0;
      }
      .receive_son_bottom {
        font-size: 13px;
        color: gray;
        text-align: right;
        display: flex;
        justify-content: space-between;
        .receive_son_bottom_flex {
          display: flex;
          flex-wrap: wrap;
        }
      }
      .receive_item_underline {
        //text-decoration: underline
        border-bottom: 1px dashed gray;
      }

      position: relative;
      .detaileActive {
        position: absolute;
        top: 0;
        right: -5px;
        color: #ee0a24;
        font-size: 20px;
      }
    }
  }
}
.title_wapper {
  display: flex;
  color: #000;
  justify-content: space-between;
  box-sizing: border-box;
  padding: 0 10px;
}
.pay_ul {
  padding: 0 5px;
  li {
    height: auto;
    overflow-x: hidden;
    padding: 10px 0;
  }
}
.pay_ul_t_arrears {
  font-size: 13px;
  color: #999999;
}
.amout-style {
  margin: 0 10px;
}
/deep/.van-checkbox__label {
  text-align: left;
}
.activeSheetNo {
  color: #ff99aa;
}
</style>
