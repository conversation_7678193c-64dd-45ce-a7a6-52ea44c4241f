<?xml version="1.0" encoding="utf-8"?>
<plugin
    xmlns="http://www.phonegap.com/ns/plugins/1.0"
    xmlns:android="http://schemas.android.com/apk/res/android"
    id="cordova-plugin-bluetooth-serial"
    version="0.4.7">

    <name>Bluetooth Serial</name>
    <description>Bluetooth Serial Communication Plugin</description>
    <license>Apache 2.0</license>
    <keywords>bluetooth, BLE, arduino</keywords>
    <repo>https://github.com/don/BluetoothSerial.git</repo>
    <issue>https://github.com/don/BluetoothSerial/issues</issue>

    <js-module src="www/bluetoothSerial.js" name="bluetoothSerial">
        <clobbers target="window.bluetoothSerial" />
    </js-module>

    <platform name="android">

        <config-file target="res/xml/config.xml" parent="/widget">
            <feature name="BluetoothSerial">
                <param name="android-package" value="com.megster.cordova.BluetoothSerial"/>
            </feature>
        </config-file>

        <source-file src="src/android/com/megster/cordova/BluetoothSerial.java"
            target-dir="src/com/megster/cordova"/>
        <source-file src="src/android/com/megster/cordova/BluetoothSerialService.java"
            target-dir="src/com/megster/cordova"/>

        <config-file target="AndroidManifest.xml" parent="/manifest">
            <uses-permission android:name="android.permission.BLUETOOTH" />
            <uses-permission android:name="android.permission.BLUETOOTH_ADMIN" />
            <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />
        </config-file>

    </platform>

    <platform name="ios">

        <config-file target="config.xml" parent="/widget">
            <feature name="BluetoothSerial">
                <param name="ios-package" value="MEGBluetoothSerial" onload="true"/>
            </feature>
        </config-file>

        <header-file src="src/ios/MEGBluetoothSerial.h" target-dir="BluetoothSerial" />
        <source-file src="src/ios/MEGBluetoothSerial.m" target-dir="BluetoothSerial" />
        <header-file src="src/ios/CBPeripheral+BTSExtensions.h" target-dir="BluetoothSerial" />
        <source-file src="src/ios/CBPeripheral+BTSExtensions.m" target-dir="BluetoothSerial" />

        <!-- BLEMini see http://redbearlab.com -->
        <header-file src="src/ios/BLE.h" target-dir="BluetoothSerial" />
        <header-file src="src/ios/BLEDefines.h" target-dir="BluetoothSerial" />
        <source-file src="src/ios/BLE.m" target-dir="BluetoothSerial" />

        <!-- frameworks -->
        <framework src="CoreBluetooth.framework" />

    </platform>

    <platform name="wp8">
        <config-file target="config.xml" parent="/*">
             <feature name="BluetoothSerial">
                 <param name="wp-package" value="BluetoothSerial"/>
             </feature>
         </config-file>


         <config-file target="Properties/WMAppManifest.xml" parent="/Deployment/App/Capabilities">
             <Capability Name="ID_CAP_PROXIMITY" />
         </config-file>

         <source-file src="src/wp/BluetoothSerial.cs" />
         <source-file src="src/wp/BluetoothConnectionManager.cs" />

    </platform>

    <platform name="browser">
        <js-module src="src/browser/bluetoothSerial.js" name="BluetoothSerial_browser">
            <clobbers target="window.bluetoothSerial" />
        </js-module>

    </platform>
</plugin>
