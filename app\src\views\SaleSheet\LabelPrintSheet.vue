<template>
  <div ref="pages" class="pages" id="pages">
    <my-preview-image v-if="showImagePreviewFlag" @closePreviewEvent="showImagePreviewFlag = false"
      :images="[previewItemImageUrl]"></my-preview-image>

    <!-- 标题栏 -->
    <div ref="publicBox2" @click='title_click'>
      <van-nav-bar left-arrow safe-area-inset-top title="标签打印单" @click-left.stop="stayOrLeave">
        <template #right>
          <div>
            <yj-dot-menu @menuGo="menuGo"
              :menuVals="[{ name: '历史单据', url: '/ViewSheetAll?queryParams=' + JSON.stringify({ startDate: calcThreeMonthAgo, endDate: calcNowDate, customerInfo: [{ ids: sheet.supcust_id, titles: sheet.sup_name }], }) + '&&sheetTabType=' + getSheetTypeTab() + '&timestamp=' + Date.now() }]"></yj-dot-menu>
          </div>
          <svg style="margin:2px 2px 0 20px " @click.stop="submitLabelPrintSheet" width="30px" height="30px"
            stroke-width="1.3" class="black">
            <use :xlink:href="'#icon-plane'"></use>
          </svg>
        </template>
      </van-nav-bar>
    </div>

    <!-- 中间内容 -->
    <div ref="publicBox3" class="public_box3">
      <!-- 单据头部信息 -->
      <div class="public_query">
        <div class="public_query_title" v-if="sheet.sheet_no">
          <div style="padding-top: 5px;padding-left: 25px;" class="public_query_title_t sheet_info">
            <span>{{ sheet.sheet_no }}</span>
          </div>
          <div style="padding-top: 5px;padding-left: 25px;text-align: left;" v-if="sheet.make_time"
            class="public_query_title_t sheet_info">
            <span>制单:{{ sheet.maker_name }} {{ sheet.make_time }}</span>
          </div>
        </div>
        <div class="public_query_titleSrc">
          <div class="public_query_wrapper">
            <div class="public_query_titleSrc_item" style="width:100%">
              <van-icon name="wap-home-o" @click="onSelectDefaultUnit" />
              <input type="text" style=" width: calc(100% - 60px); text-align: center;
              " v-model="sheet.default_unit_name" placeholder="默认单位" readonly @click="onSelectDefaultUnit" />
            </div>
            <div class="public_query_titleSrc_item" style="width:100%">
              <van-icon name="wap-home-o" />
              <input type="text" style=" width: calc(100% - 60px); text-align: center;
              " v-model="sheet.make_brief" placeholder="备注" />
            </div>
          </div>
        </div>
      </div>

      <ConcaveDottedCenter />
      <div class="approved_reded">
        <div class="sheet_state approved" v-if="sheet.approve_time && (sheet.red_flag == '' || sheet.red_flag == '0')">
          <img src="../../assets/images/approved.png" />
        </div>
        <div class="sheet_state reded" v-if="sheet.red_flag == '1'">
          <img src="../../assets/images/reded.png" />
        </div>
      </div>

      <!-- 商品列表 -->
      <div style="padding:0 10px">
        <!-- 标题 -->
        <div class="title" style="margin-top: 6px; color: #aaa">
          <van-row>
            <van-col style="text-align: left;display:flex;justify-content:space-between;" span="12">
              <div>品名</div>
              <div>
                <svg width="24px" height="18px" fill="#aaa" style="margin-top:1px;" @click="onSortByItemName">
                  <use xlink:href="#icon-sort"></use>
                </svg>
              </div>
            </van-col>
            <van-col style="text-align: right;" span="12">数量</van-col>
          </van-row>
        </div>

        <div :class="sheet.sheet_no ? 'sales_box_list_big' : 'sales_box_list'">
          <div class="sales_list">
            <div class="sales_list_boxs">
              <ul class="sales_ul">
                <SlickList axis="y" v-model="sheet.sheetRows" :pressDelay="500" helperClass="helper-class-slick-item"
                  :disabled="sheet.approve_time">
                  <SlickItem v-for="(item, index) in sheet.sheetRows" :key="index" :index="index">
                    <van-swipe-cell>
                      <div style="display:flex;flex-direction:row;">

                        <!-- 商品图片处理 -->
                        <div v-if="!isNoImagesInAllItems" class="sales_img_wrapper" style="width: 63px;height: 63px;">
                          <img @click="() => {
                            showImagePreviewFlag = true
                            previewItemImageUrl = (item.showImages && item.showImages.main !== '') ? item.showImages.main : require('@/assets/images/default_good_img.png')
                          }" width="50" height="50" class="sales_img_wrapper"
                            :src="(item.showImages && item.showImages.tiny !== '') ? item.showImages.tiny : require('@/assets/images/default_good_img.png')"
                            fit="resize" />
                        </div>

                        <!-- 商品信息展示 -->
                        <div @click="onRowEdit(item, index)" style="display: flex;align-items: center; width: 100%">
                          <div class="sales_ul_li"
                            style="border-bottom-style: solid;display: flex;flex-direction: column;justify-content: center;height: fix-content;">

                            <van-row style="width: 100%;">
                              <van-col span="24" style="justify-content:flex-start;height:100%;">
                                <span>{{ index + 1 }}、</span>
                                <span>{{ item.item_name }}
                                  <template v-if="item.attr_qty">
                                    {{ HandleNoStockAttrSplitShow(item.attr_qty) }}
                                  </template>
                                </span>
                              </van-col>
                            </van-row>

                            <!-- 条码显示 -->
                            <div style="display:flex;width:100%;flex-wrap:wrap">
                              <div v-if="appSheetShowItemSpec && item.item_spec" class="other-info">{{ item.item_spec }}
                              </div>
                              <div class="other-info"
                                v-if="item.s_barcode && (appShowBarcode == 'sbarcode' || (appShowBarcode == 'ubarcode' && item.unit_no == item.s_unit_no))">
                                {{ "小:" + item.s_barcode }}
                              </div>
                              <div class="other-info"
                                v-else-if="item.b_barcode && (appShowBarcode == 'bbarcode' || (appShowBarcode == 'ubarcode' && item.unit_no == item.b_unit_no))">
                                {{ "大:" + item.b_barcode }}
                              </div>
                              <div class="other-info"
                                v-else-if="item.m_barcode && (appShowBarcode == 'mbarcode' || (appShowBarcode == 'ubarcode' && item.unit_no == item.m_unit_no))">
                                {{ "中:" + item.m_barcode }}
                              </div>
                            </div>

                            <!-- 商品数量、备注显示 -->
                            <van-row style="width: 100%;flex-wrap: wrap;">
                              <van-col span="12" style="text-align:right;">
                                <span v-if="item.remark" style="color:#f88;">{{ item.remark }}</span>
                              </van-col>
                              <van-col span="12" style="display:flex; text-align: right;">{{ item.quantity }}
                                <span style="width:1px"></span>{{ item.unit_no }}
                              </van-col>
                            </van-row>

                            <!-- 商品数量展示 -->
                            <div v-if="item.attr_qty && !item.item_id.startsWith('nanoid')"
                              class="item_attr_qty_wrapper">
                              <div v-for="(attr, attrIndex) in handleToJSON(item)" :key="attrIndex"
                                class="item_attr_qty_content">
                                {{ handleAttrNameShow(attr) }}:
                                <span style="font-family: numfont;">{{ attr.qty }}</span>{{ item.unit_no }}
                              </div>
                            </div>

                          </div>
                        </div>
                      </div>
                      <template #right>
                        <van-button v-if="!sheet.approve_time" class="delSheetRow" square type="danger" text="删除"
                          @click="btnRemoveRow_click(index)" />
                      </template>
                    </van-swipe-cell>
                  </SlickItem>
                </SlickList>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 底部合计 -->
    <div ref="publicBox4" class="total_money">
      <footer ref="salesListFooter" class="sales_list_footer">
        <div>
          <span style="color:#555;padding-left:15px">合计: </span><span class="sales_list_span">{{ sheetRowCount }}
            行</span>
          <span v-if="sheetRowCount > 0" style="color:#555;padding-left:15px">{{ sumQuantityUnitConv }}</span>
        </div>
      </footer>
      <div class="sales_footer" style="padding: 0 15px 10px 10px;">
        <!-- 底部搜索框 -->
        <div class="footer_input">
          <input ref="inputCodesRef" id="codes" type="text" style="padding-left: 3px;" v-model="searchStr"
            enterkeyhint="go" @keydown="onSearchInputKeyDown($event)" placeholder="名称/简拼/条码/货号"
            :disabled="sheet.approve_time ? true : false"
            :style="{ width: getSearchInputWidth(), backgroundColor: sheet.approve_time ? '#f2f2f2' : '' }" />
        </div>
        <!-- 添加商品按钮 -->
        <div class="footer_iconBt" :disabled="sheet.approve_time !== '' || isSubmiting" @click="btnClassView_click">
          <svg width="35px" height="35px" fill="#F56C6C">
            <use xlink:href="#icon-add"></use>
          </svg>
        </div>
        <!-- 扫码按钮 -->
        <div class="footer_iconBt" type="info" @click="btnScanBarcode_click">
          <svg width="30px" height="30px" fill="#555">
            <use xlink:href="#icon-barcodeScan"></use>
          </svg>
        </div>
      </div>
    </div>

    <!-- 选择默认单位弹窗 -->
    <van-popup v-model="popupDefaultUnitPicker" round position="bottom">
      <van-picker show-toolbar title="选择默认单位" :columns="defaultUnitList" value-key="unit_name"
        @cancel="popupDefaultUnitPicker = false" @confirm="onConfirmDefaultUnitPicker" />
    </van-popup>

    <!-- 添加商品弹窗 -->
    <van-popup v-if="!multiSelectItemShow" class="addSheetRowOne" v-model="popupAddSheetRow" duration="0.12"
      position="bottom" @close="addSheetRowClose">
      <div class="add_goods_wrapper">
        <div class="goods_attr_wrapper">
          <transition name="myslide-left">
            <div v-show="attrShowFlag" class="goods_attr" key="attrListAdd">
              <AttrSelect ref="arrtMainToAddSheetRow" v-show="attrShowFlag" @handleAttrItemClick="handleItemClik"
                @handleAutoBtnOkClick="handleAutoBtnOkClick" key="attrlist_list" />
            </div>
          </transition>
          <transition name="myslide-right">
            <div v-show="!attrShowFlag" class="layout_content" @click="popupAddSheetRow = false" key="noAttrList"></div>
          </transition>
        </div>
        <div class="goods_add_wrapper" id="goodsAddWrapper">
          <div class="class_add_goods">
            <AddSheetRow ref="addSheetRow" :sheet="sheet" :multiSelectFlag="multiSelectFlag" @handleHC="handleHC"
              @onItemClick="handleItemClik" @onSheetRowAdd_Attr="onSheetRowAdd_Attr"
              @popupAddSheetRowFalse="popupAddSheetRowFalse" @handleAttrShowFlagChange="handleAttrShowFlagChange"
              @onSheetRowAdd_OK="onSheetRowAdd_OK" />
          </div>
        </div>
      </div>
    </van-popup>

    <!-- 提交弹窗 -->
    <van-popup v-model="popupSubmitPannel" :style="{ height: '100%', width: '80%', overflowY: 'auto' }"
      class="van_popup" position="right">
      <div style="height:30px;border-top:1px solid #ccc"></div>

      <!-- <van-number-keyboard v-model="keyboardText" extra-key="." :title="keyboardText ? `${keyboardText}折` : ''"
        close-button-text="完成" :show="needKeyboardField != ''" @close="onKeyboardInputFinished"
        @blur="needKeyboardField = ''" /> -->

      <!-- 审核界面 -->
      <div class="sales_more">
        <!-- <div
          v-if="canSeePrice && ',X,T,CG,CT,DH,XD,TD,'.indexOf(',' + sheet.sheetType + ',') >= 0 && !(sheet.prepay_sub_id == '-1' && sheet.sheetType == 'DH')">
          <van-field v-model="sheet.make_brief" label="备注" :disabled="sheet.approve_time ? true : false">
            <template #button
              v-if="(sheet.approve_time ? true : false) && (sheet.sheetType == 'X' || sheet.sheetType == 'XD' || sheet.sheetType == 'CG')">
              <van-button style="border:0" icon="edit" size="mini" type="default" @click="handleAppendBrief" />
            </template>
          </van-field>
        </div> -->

        <div>
          <div class="other_operate">
            <van-divider style="margin-top: 10px;">附件</van-divider>
            <div class="other_operate_content">
              <div class="appendixphoto-container" v-for="item, index in sheet.appendixPhotos" :key="index"
                style="justify-content:center;">
                <img @click="preview_photo(index)" class="photo" :src="item">
                <div v-if="sheet.approve_time == '' && !isSubmiting" class="remove-icon" @click="remove_photo(index)">
                  <van-icon name="close" />
                </div>
              </div>
              <div class="iconfont" v-if="sheet.approve_time == '' && !isSubmiting" @click="showImageSelect = true">
                &#xe62e;
              </div>
            </div>
          </div>


          <div class="other_operate">
            <van-divider style="margin: 20px 0px 40px;"></van-divider>
            <div class="other_operate_content" style="margin-bottom: 80px;flex-wrap: nowrap;">
              <button plain type="info" style="height: 45px; margin-top:10px;max-width: 105px;" class="color_fff"
                v-if="canMake" :disabled="sheet.approve_time !== '' || isSubmiting" @click="btnSave_click">保存
              </button>

              <!-- <button plain type="info" class="color_fff"
                style="height: 45px; margin-top:10px;max-width: 105px; background-color: #ffcccc;" v-if="canApprove"
                :disabled="sheet.approve_time !== '' || isSubmiting" 
                @click="btnApprove_click">审核
              </button>

              <button plain type="info" style="height: 45px;max-width: 105px; margin-top:10px;" class="color_ffcccc"
                v-if="canReview" :disabled="sheet.sheet_id && (sheet.review_time || isSubmiting)"
                @click="btnReview_click">复核
              </button> -->

              <button v-if="canPrint" plain type="info" class="color_fff"
                style="height: 45px;  margin-top:10px;max-width: 105px;" @click="btnGotoPrintView_click()"
                :disabled="!sheet.sheet_id || isPrinting">打印
              </button>

              <!-- <button plain type="info" class="color_fff"
                style="height: 45px; margin-top:10px; background-color: #ffcccc;max-width: 105px;" v-if="sheet.approve_time !== '' &&
                  (sheet.payway1_channel || sheet.payway2_channel || sheet.payway3_channel)
                  && (sheet.payway1_channel != 2 && sheet.payway2_channel != 2 && sheet.payway3_channel != 2)
                  && sheet.sheetType === 'X'
                " @click="btn_pay_OR_unpay_click">
                {{ isBillPaid ? '退款' : '付款' }}
              </button> -->

            </div>
            <div v-if="!isInPrintView" class="other_operate_content"
              style="justify-content: space-around; margin-top:20px;">

              <!-- <button class="small-btn" plain type="danger" :style="{ 'border-radius': '9px' }"
                v-if="canRed && sheet.approve_time" :disabled="!sheet.sheet_id || sheet.red_flag != ''"
                @click="btnRed_click">红冲
              </button>

              <button class="small-btn" plain type="danger" :style="{ 'border-radius': '9px' }"
                v-if="canRed && sheet.approve_time && sheet.sheetType != 'DH'"
                :disabled="!sheet.sheet_id || sheet.red_flag != ''" @click="btnRedAndModify_click">冲改
              </button> -->

              <button class="small-btn" plain type="danger" v-if="canDelete && sheet.sheet_id && !sheet.approve_time"
                @click="btnDeleteSheet_click">删除
              </button>

              <button class="small-btn" style="padding: 0; width: 50px; height: 30px;" type="default"
                @click="btnCopySheet_click">复制
              </button>

              <button class="small-btn" type="default" :disabled="isSubmiting || sheet.approve_time !== ''"
                @click="onEmpty()">清空
              </button>

              <!-- <button class="small-btn" type="default" :disabled="!(sheet.sheet_id)" @click="shareWeChat">分享</button> -->

              <!-- <button class="small-btn" v-if="sheet.sheetType === 'X' ||
                sheet.sheetType === 'T' ||
                sheet.sheetType === 'XD' ||
                sheet.sheetType === 'TD' ||
                sheet.sheetType === 'DH'" type="default" @click="handleInvite">邀请
              </button> -->

              <button class="small-btn" v-if="canPrint" type="default" @click="confirmPreviewOutside"
                :disabled="!sheet.sheet_id || isPrinting">预览
              </button>

              <button class="small-btn" type="default" @click="btnOut">退出</button>
            </div>
          </div>
        </div>
      </div>

      <!-- 打印详情界面 -->
      <transition name="hide-show">
        <div v-if="isInPrintView" class="sales_more">

          <div v-if="this.useTmp" class="print-template-wrapper" style="margin: 0px 0.55rem;">
            <div style="margin-top: 20px;margin-bottom: 10px;margin-left: 10px; text-align: left;">打印模板</div>
            <div class="radio-tmp-position">
              <div v-for="(item, i) in tmplist" :key="i" class="radio-tmp-style">
                <input type="radio" name="template" :id="i" :value="i" v-model="selectedTmpId"
                  style="position: relative; margin-left: 10px;" class="radio-tmp-type" />
                <label :for="i" class="radio-tmp-name">
                  {{ item.name }}
                </label>
              </div>
            </div>
          </div>

          <div v-else style="height:170px;" />
          <!-- 搞个选择打印机 -->
          <div class="select-printer">
            <van-cell-group inset style="width:100%">
              <van-cell is-link title="打印机" title-style="flex: inherit;" :value="defaultPrinter.name"
                @click="showPrinterSelectionPage" />
            </van-cell-group>
          </div>

          <div v-if="!this.useTmp && arrCompanyNamesToPrint && arrCompanyNamesToPrint.length"
            style="margin: 0px 0.55rem;">
            <div class="print-company-title"
              style="margin-top:10px;margin-bottom: 10px;margin-left: 10px; text-align: left;">公司名称
            </div>

            <div class="radio-tmp-position setHeight">
              <div v-for="(name, i) in arrCompanyNamesToPrint" :key="i" class="radio-tmp-style">
                <input type="radio" name="companyName" :id="i" :value="name" v-model="companyNameToPrint"
                  style="position: relative; margin-left: 10px;" class="radio-tmp-type" />
                <label :for="i" class="radio-tmp-name">{{ name }}</label>
              </div>
            </div>

          </div>
          <div style="margin-top: 10px;">

            <van-checkbox-group v-model="printBarcodeStyleForSale"
              style="display: flex;  margin: 20px;  margin-left: 1px; padding-top: 0px; ">
              <div style="margin-left: 8px;margin-right:20px; line-height: 30px;">条码</div>
              <van-checkbox name="actualUnit" style="margin-right: 10px;">实际单位</van-checkbox>
              <van-checkbox name="smallUnit" style="margin-right: 10px">小单位</van-checkbox>
            </van-checkbox-group>

            <van-checkbox shape="square" v-model="printBarcodePic" style="margin-left: 56px;">打印条码图</van-checkbox>

            <div class="print-count" v-if="defaultPrinter.type !== 'cloud'" style="margin-bottom: 15px;">
              <button style="font-size: 20px; background: white;color:#777; min-width: 40px;"
                @click="printCount = printCount < 5 ? printCount + 1 : 5">+</button>
              <div style="display: inline-block; margin-right: 5px; color: #8d8d8d;">{{ printCount }}次</div>
              <button style="font-size: 20px; background: white;color:#777; min-width: 40px;"
                @click="printCount = printCount > 1 ? printCount - 1 : 1">-</button>
            </div>
          </div>
          <div
            style="margin-top: 25px; display: flex; justify-content: space-between; align-items: center; gap: 10px; flex-wrap: nowrap;">
            <van-button class="print-btns"
              style="flex: 1; min-width: 70px; max-width: 90px; padding: 1px 2px;margin-left:10px" type="default"
              @click="isInPrintView = false">返回
            </van-button>

            <van-button class="print-btns"
              style="flex: 1; min-width: 70px; max-width: 90px; background-color: #ffcccc; padding: 1px 2px;"
              type="default" @click="btnPrint_click" :disabled="isPrinting">确认打印
            </van-button>

            <van-button class="print-btns"
              style="flex: 1; min-width: 70px; max-width: 90px; background-color: #f0f0f0; padding: 1px 2px;margin-right:10px"
              type="default" @click="confirmPreview">打印预览</van-button>
          </div>
        </div>
      </transition>
    </van-popup>

    <!-- 图片预览 -->
    <van-image-preview v-model="previewShow" :images="previewImageUrls" :start-position="previewIdx"
      @change="previewOnChange">
      <template v-slot:previewIdx>第{{ previewIdx + 1 }}页</template>
    </van-image-preview>
    <div>
      <!-- 传递 previewImageUrls 给 WeChatShare 组件 -->
      <!-- <WeChatShare :previewImageUrls="previewImageUrls" /> -->
    </div>

    <!-- 预览界面下方的返回、确认打印、分享按钮 -->
    <div v-if="previewShow" class="preview-footer"
      style="position: fixed; bottom: 20px; left: 50%; transform: translateX(-50%); z-index: 3000; display: flex; gap: 10px;">
      <van-button type="default" @click="previewShow = false" style="width: 100px;">
        返回
      </van-button>
      <van-button type="default" @click="btnPrint_click" :disabled="isPrinting"
        style="background-color: #ffcccc; width: 100px;">
        确认打印
      </van-button>
      <!-- <div>
        在分享之前先把previewImageUrls图片传给wechatShare组件
        <WeChatShare ref="wechatShare" :previewImageUrls="previewImageUrls" />
        <van-button type="default" @click="callShareWeChatPrintPreview" style="width: 100px;">
          分享
        </van-button>
      </div> -->
    </div>

    <!-- 选择打印机弹窗 -->
    <van-popup v-model="PopupPrintersSelect" position="bottom">
      <van-picker title="选择打印机" show-toolbar value-key="name" :columns="printers" :default-index="defaultPrinterIndex"
        @confirm="confirmPrinterSelectionChange" @cancel="hidePrinterSelectionPage" />
    </van-popup>

    <!-- 未保存单据弹窗 -->
    <van-popup v-model="showUnsubmitedSheets" round>
      <div class="lowItem" style="width: 300px;">
        <!-- 关闭按钮，外面包裹一个小圈圈 -->
        <div class="close-circle" @click="showUnsubmitedSheets = false"
          style="position: absolute; top: 10px; right: 10px; cursor: pointer;">
          <van-icon name="cross" class="close-icon" />
        </div>
        <h4>有未保存的单据</h4>
        <ul class="lowItem_ul">
          <li v-for="(item, index) in unsubmitedSheets" :key="index">
            <div class="lowItem_ull" @click="onUnsubmitedSheetSelected(item)">
              {{ item.sup_name }}
            </div>
            <div class="lowItem_ulr" @click="onUnsubmitedSheetSelected(item)">
              {{ item.saveTime }}
            </div>
            <div class="btn-delete" @click="onUnsubmitedSheetDelete(index)">
              删除
            </div>
          </li>
        </ul>
        <!-- 按钮区域 -->
        <div class="button-row">
          <van-button type="default" @click="showUnsubmitedSheets = false"
            style="height: 40px;border-radius:12px;">新建单据</van-button>
        </div>
      </div>
    </van-popup>

    <!-- 商品编辑弹窗 -->
    <van-popup v-if="popupEditSheetRowPannel" v-model="popupEditSheetRowPannel" position="bottom"
      :style="{ height: '100%' }">
      <EditSheetRow @closeEditItem='() => {
        popupEditSheetRowPannel = false
      }' :sheet="sheet" :editingRow="editingItem" @onRowEditDone="onRowEditDone" @delIndexItem="delIndexItem"
        @handlePopupEditSheetRowPannelFalse="handlePopupEditSheetRowPannelFalse" :branchList="branchList" />
    </van-popup>

  </div>
</template>

<script>
import {
  NavBar,
  Field,
  Col,
  Row,
  Popup,
  Button,
  Icon,
  Toast,
  Dialog,
  SwipeCell,
  Image,
  Picker,
  Tag,
  Divider,
  Cell,
  CellGroup,
  Checkbox,
  CheckboxGroup,
  NumberKeyboard,
} from "vant";

import {
  //标签打印单
  AppSheetLabelPrintLoad,
  AppSheetLabelPrintSave,
  AppSheetLabelPrintDelete,

  //打印
  AppGetSheetToPrint_Post,
  AppGetTemplate,
  AppSheetToEsc,
  AppSheetToImages,
  AppCloudPrint_sheetTmp,
  AppCloudPrint_escCmd,
  ApiPrintMark,

  //商品查询
  AppSheetSaleGetItemList,
  AppSheetBuyGetItemList,
  AppSheetOrderItemGetItemList,
} from "../../api/api";

import Printing from "../Printing/Printing";
import LabelPrinter from "../Printing/LabelPrinter";
import EditSheetRow from "./EditSheetRow";
import AddSheetRow from "./AddSheetRow";
import SelectItems from "./SelectItems";
import SaleSheetMultiSelect from "./SaleSheetMultiSelect";
import mixins from './sheetMixin/mixin';
import ConcaveDottedCenter from "../components/ConcaveDottedCenter";
import YJDotMenu from '../components/YJDotMenu.vue';
import MyPreviewImage from '../VisitRecord/MyPreviewImage.vue';
import { SlickList, SlickItem } from 'vue-slicksort';

export default {
  name: "LabelPrintSheet",
  mixins: [mixins],
  components: {
    SlickItem,
    SlickList,
    ConcaveDottedCenter,
    "my-preview-image": MyPreviewImage,
    "van-nav-bar": NavBar,
    "van-button": Button,
    "van-icon": Icon,
    "van-tag": Tag,
    "van-popup": Popup,
    "van-field": Field,
    "van-swipe-cell": SwipeCell,
    "van-image": Image,
    "van-divider": Divider,
    "van-picker": Picker,
    "van-cell": Cell,
    "van-cell-group": CellGroup,
    "van-checkbox": Checkbox,
    "van-checkbox-group": CheckboxGroup,
    "van-number-keyboard": NumberKeyboard,
    EditSheetRow: EditSheetRow,
    "van-row": Row,
    "van-col": Col,
    [Dialog.Component.name]: Dialog.Component,
    AddSheetRow,
    SelectItems,
    SaleSheetMultiSelect,
    "yj-dot-menu": YJDotMenu
  },
  data() {
    return {
      defaultUnitList: [{ unit_type: 'b', unit_name: '大' }, { unit_type: 'm', unit_name: '中' }, { unit_type: 's', unit_name: '小' }],
      // 单据数据
      sheet: {},

      //单据状态相关
      sheetHasChange: false,


      // 商品选择弹窗
      popupAddSheetRow: false,
      multiSelectItemShow: false,
      attrShowFlag: false,
      popupDefaultUnitPicker: false,
      multiSelectFlag: false,
      multiSelectItemShow: false,

      // 商品编辑弹窗
      popupEditSheetRowPannel: false,
      editingItem: {},
      branchList: [],
      editSheetRowIndex: "",

      // 提交弹窗
      isSubmiting: false,
      popupSubmitPannel: false,

      // 搜索相关
      searchStr: '',
      queryCondition: {},
      onload_remarks: [],

      // 状态标志
      IsSaving: false,
      IsDeleting: false,


      // 图片预览
      showImagePreviewFlag: false,
      previewImageUrls: [],
      previewItemImageUrl: '',
      previewShow: false,    // 控制预览显示
      previewIdx: 0,         // 当前预览的图片索引

      //打印相关
      arrCompanyNamesToPrint: [],
      companyNameToPrint: '',
      defaultPrinter: {},
      defaultPrinterIndex: 0,
      isPrinting: false,
      isInPrintView: false,
      isTmpPriting: true, // 判断是否有可用模板
      loadingMsg: '',
      PopupPrintersSelect: false,
      printCount: 1,
      printers: [],
      printBarcodeStyleForSale: [],
      printBarcodePic: false,
      previewImageUrls: [],
      printTemplates: [],
      printTemplatesTiny: [],
      tmplist: [],
      templatesLoaded: false,
      selectedTmpId: 0, // 选择模板自动赋值
      useTmp: false,

      //添加附件相关
      showImageSelect: false,
      // 其他
      isChangingSheetByCode: false,
      showUnsubmitedSheets: false,
      unsubmitedSheets: [],
      bluetoothDeviceInputFlag: false,
    };
  },

  props: {
    isPopup: {
      type: Boolean,
      default: false
    },
    saleSheetShowFlag: { // 历史单据打开，阻止已选信息
      type: Boolean,
      default: false
    }
  },

  computed: {
    // 计算总行数
    sheetRowCount() {
      return this.sheet.sheetRows ? this.sheet.sheetRows.length : 0;
    },

    // 计算总数量
    sumQuantityUnitConv() {
      if (!this.sheet.sheetRows || this.sheet.sheetRows.length === 0) {
        return '';
      }

      let totalQty = 0;
      this.sheet.sheetRows.forEach(row => {
        totalQty += parseFloat(row.quantity || 0);
      });

      return `总数量: ${this.toMoney(totalQty, 0)}`;
    },

    // 计算三个月前日期
    calcThreeMonthAgo() {
      const date = new Date();
      date.setMonth(date.getMonth() - 3);
      return date.toISOString().split('T')[0];
    },

    // 计算当前日期
    calcNowDate() {
      return new Date().toISOString().split('T')[0];
    },

    // 判断是否所有商品都没有图片
    isNoImagesInAllItems() {
      if (Array.isArray(this.sheet.sheetRows)) {
        return this.sheet.sheetRows.every(item => {
          return item.showImages && item.showImages.tiny === '';
        });
      }
      return false;
    },

    // 应用设置
    appSheetShowItemSpec() {
      return window.getSettingValue('appSheetShowItemSpec') === 'true';
    },

    appShowBarcode() {
      return window.getSettingValue('appShowBarcode') || 'sbarcode';
    },

    inPrivateMode() {
      return this.$store.state.inPrivateMode
    },
    //权限相关
    canRed() {
      return hasRight("sale.sheetLabelPrint.red")
    },
    canMake() {
      return hasRight("sale.sheetLabelPrint.make")
    },
    canDelete() {
      return hasRight("sale.sheetLabelPrint.delete")
    },
    canApprove() {
      return hasRight("sale.sheetLabelPrint.approve")
    },
    canReview() {
      return hasRight("sale.sheetLabelPrint.review")
    },
    canPrint() {
      return hasRight("sale.sheetLabelPrint.print")
    }
  },

  watch: {
    "sheet.sheetRows": {
      handler: function () {
        this.handleSelectedSheetRows('LP')
        // 当单据行数据发生变化时，自动保存到缓存
        if (this.sheet.sheet_id === '' && this.sheet.sheetRows.length > 0) {
          // this.saveCurSheetToCache()
        }
      },
      deep: true
    },
  },

  beforeRouteLeave(to, from, next) {
    // 在离开页面前，如果是新建单据且有商品，保存到缓存
    if (this.sheet.sheet_id === '' && this.sheet.sheetRows.length > 0) {
      this.saveCurSheetToCache()
    }
    next();
  },

  mounted() {
    console.log('entering LabelPrintSheet....................||||||||||||||||||||||.........................')

    this.templatesLoaded = false
    var sheetType = ''
    var sheetID = ''
    sheetType = this.$route.query.sheetType;
    sheetID = this.$route.query.sheetID || "";

    if (sheetType) { // 历史单据
      console.log('sheetID is ' + sheetID)
      if (sheetID) {
        this.showUnsubmitedSheets = false;
      }
      else{
        // 延迟检查未保存单据，确保页面初始化完成
        setTimeout(() => {
          this.showSheetsFromCache();
        }, 100);
      }
      this.initSheet();
    }

    // 获取当前公司的打印机列表，只选择使用模板打印的打印机
    let allPrinters = window.getCompanyStoreValue('c_printers')
    let printers = []

    // 兼容旧版本
    if (!allPrinters) {
      allPrinters = []
      const printer = {
        id: this.$store.state.printerID ? this.$store.state.printerID : this.$store.state.printerID,
        name: this.$store.state.printer_name || '蓝牙打印机',
        type: this.$store.state.printerType || 'bluetooth',
        kind: this.$store.state.printer_kind || 'tiny',
        paperSize: this.$store.state.paperSize || '58',
        bluetoothID: this.$store.state.printerID,
        bluetoothType: 'classic',
        bluetoothDataStyle: this.$store.state.bluetoothSendDataStyle,
        useTemplate: this.$store.state.useTemplateToPrint === true || this.$store.state.printStyle == 'template',

        brand: this.$store.state.printer_brand || '',
        cloudID: this.$store.state.device_id || '',
        cloudCode: this.$store.state.check_code || '',

        isDefault: true
      }
      allPrinters.push(printer)
    }

    // 标签打印只使用支持模板打印的打印机
    printers = allPrinters.filter(printer => printer.useTemplate === true)

    this.printers = printers

    for (let _i = 0; _i < printers.length; _i++) {
      const _printer = printers[_i]
      if (_printer.isDefault) {
        this.defaultPrinter = _printer;
        this.defaultPrinterIndex = _i;
        break;
      }
    }
    this.is_sender = this.$store.state.operInfo.is_sender == 'True' ? true : false
    // 初始化进入单据，清空该单据购物车，防止缓存上个客户购物车，切换到其他客户情况
    this.$store.commit("shoppingCarObj", {
      clearFlag: true,
      sheetType: this.sheet.sheetType
    })
  },

  methods: {
    getSheetTypeTab() {
      return this.sheet.sheetType == 'X' ? 'sheetSale' :
        this.sheet.sheetType == 'T' ? 'sheetReturn' :
          this.sheet.sheetType == 'XD' ? 'sheetSaleOrder' :
            this.sheet.sheetType == 'TD' ? 'sheetReturnOrder' :
              this.sheet.sheetType == 'CG' ? 'sheetBuy' :
                this.sheet.sheetType == 'CT' ? 'sheetReturnBuy' :
                  this.sheet.sheetType == 'BQ' ? 'sheetLabelPrint' :
                    ''
    },
    delIndexItem() {
      const targetRow = this.sheet.sheetRows[this.EditSheetRowIndex]
      if (targetRow?.deleteLinkId) {
        // ! 促销活动 - 组合/兑奖, 须连锁删除
        this.sheet.sheetRows = this.sheet.sheetRows.filter((_row) =>
          !_row?.deleteLinkId || _row.deleteLinkId !== targetRow.deleteLinkId
        )
      } else {
        this.sheet.sheetRows.splice(this.EditSheetRowIndex, 1)
      }
      this.popupEditSheetRowPannel = false
      this.handleSelectedSheetRows()
    },
    handlePopupEditSheetRowPannelFalse() {
      this.popupEditSheetRowPannel = false
    },
    // 初始化单据
    async initSheet() {
      try {
        const sheetID = this.$route.query.sheetID;
        if (sheetID && sheetID !== 'new') {
          // 加载现有单据
          await this.loadSheet(sheetID);
        } else {
          // 创建新单据
          await this.createNewSheet();
        }
      } catch (error) {
        console.error('初始化单据失败:', error);
        Toast('初始化单据失败');
      }
    },

    // 加载现有单据
    async loadSheet(sheetID) {
      try {
        const res = await AppSheetLabelPrintLoad({ sheetID: sheetID });
        if (res.result === 'OK') {
          this.sheet = res.sheet;
          this.processSheetRows();
        } else {
          Toast(res.msg || '加载单据失败');
        }
      } catch (error) {
        console.error('加载单据失败:', error);
        Toast('加载单据失败');
      }
    },

    // 创建新单据
    async createNewSheet() {
      try {
        const res = await AppSheetLabelPrintLoad({ sheet_id: 'new' });
        if (res.result === 'OK') {
          this.sheet = {
            ...res.sheet,
            sheetType: 'BQ',
            sheetRows: [],
            default_unit_type: ''
          };
        } else {
          Toast(res.msg || '创建新单据失败');
        }
      } catch (error) {
        console.error('创建新单据失败:', error);
        Toast('创建新单据失败');
      }
    },

    // 处理单据行数据
    processSheetRows() {
      if (this.sheet.sheetRows && this.sheet.sheetRows.length > 0) {
        this.sheet.sheetRows.forEach(row => {
          // 处理图片显示
          if (row.item_images) {
            try {
              row.showImages = typeof row.item_images === 'string'
                ? JSON.parse(row.item_images)
                : row.item_images;
            } catch (e) {
              row.showImages = { tiny: '', main: '' };
            }
          } else {
            row.showImages = { tiny: '', main: '' };
          }
        });
      }
    },

    // 提交标签打印单
    submitLabelPrintSheet() {
      this.popupSubmitPannel = !this.popupSubmitPannel;
      this.isChangingSheetByCode = true
      setTimeout(() => {
        this.isChangingSheetByCode = false
      }, 100)
    },


    // 保存按钮点击
    btnSave_click() {
      this.handleSheetSave()
    },

    // 保存单据
    async handleSheetSave() {
      logUserAction({ tm: new Date().format("yyyy-MM-dd h:m"), act: 'saveClick', sn: this.sheet.sheet_no })
      var sheet = JSON.parse(JSON.stringify(this.getSheet()));

      sheet.sheetRows.forEach(sheetRow => {
        if (sheetRow.attr_qty) {
          sheetRow.attr_qty = typeof sheetRow.attr_qty == 'string' ? sheetRow.attr_qty : JSON.stringify(sheetRow.attr_qty)
        }
        if (!sheetRow.output_tax_rate) sheetRow.output_tax_rate = 0;
        if (!sheetRow.input_tax_rate) sheetRow.input_tax_rate = 0;
        delete sheetRow.mum_attributes
      })
      // 单据有效性检查
      var err = this.checkSheetValid();
      if (err) {
        Toast.fail(err);
        return;
      }
      this.isSubmiting = true;
      var submitFunc;
      submitFunc = AppSheetLabelPrintSave

      var happenTimeOnSave = window.getSettingValue('happenTimeOnSave')
      if (happenTimeOnSave.toLowerCase() == 'true') {
        //如果设置了保存单据 保存单据时自动把交易日期弄成当下时间
        sheet.TempHappenTime = false
      }

      try {
        let res = await submitFunc(sheet)
        if (res.result === "OK") {
          Toast.success("保存成功", 3000);

          logUserAction({ tm: new Date().format("yyyy-MM-dd h:m"), act: 'saved', sn: res.sheet_no })
          this.removeCurSheetFromCache();
          this.isSubmiting = false;
          this.sheet.make_time = res.make_time;
          this.sheet.sheet_no = res.sheet_no;
          this.sheet.sheet_id = res.sheet_id;

          setTimeout(() => {
            this.sheetHasChange = false  //终结其他操作变化
          }, 100)

        } else {
          this.isSubmiting = false;
          Toast.fail(res.msg);
        }
      } catch (error) {
        this.isSubmiting = false;
        Toast.fail(error);
      }
    },

    // 检查单据是否有效
    checkSheetValid() {
      if (this.sheet.sheetRows.length <= 0) {
        return "请添加商品"
      }
      var errMsg = that.checkAttrQtyValid()
      return errMsg;
    },

    // 获取单据信息
    getSheet() {
      if (this.sheet.length <= 0) {
        Toast.fail("请添加商品");
      } else {
        let dataSum = this.sheet.sheetRows;
        dataSum.map((item) => {
          if (item.bfactor) item.b_unit_factor = item.bfactor
          if (item.mfactor) item.m_unit_factor = item.mfactor
        })
        this.sheet.operKey = this.$store.state.operKey;
        this.sheet.total_quantity = this.sumQuantityUnitConv
        return this.sheet;
      }
    },


    removeCurSheetFromCache() {
      var key = "unsubmitedSheets_" + this.sheet.sheetType;
      var unsubmitedSheets = this.$store.state[key];
      if (!unsubmitedSheets) unsubmitedSheets = [];
      var sheet = this.getSheet();

      Date.prototype.format = function (fmt) {
        var o = {
          "M+": this.getMonth() + 1, //月份
          "d+": this.getDate(), //日
          "h+": this.getHours(), //小时
          "m+": this.getMinutes(), //分
          "s+": this.getSeconds(), //秒
          "q+": Math.floor((this.getMonth() + 3) / 3), //季度
          S: this.getMilliseconds(), //毫秒
        };
        if (/(y+)/.test(fmt))
          fmt = fmt.replace(
            RegExp.$1,
            (this.getFullYear() + "").substr(4 - RegExp.$1.length)
          );
        for (var k in o)
          if (new RegExp("(" + k + ")").test(fmt))
            fmt = fmt.replace(
              RegExp.$1,
              RegExp.$1.length == 1
                ? o[k]
                : ("00" + o[k]).substr(("" + o[k]).length)
            );
        return fmt;
      };
      sheet.saveTime = new Date().format("yyyy-MM-dd h:m");
      for (var i = unsubmitedSheets.length - 1; i >= 0; i--) {
        var sht = unsubmitedSheets[i];
        if (sht.sup_name == sheet.sup_name) {
          unsubmitedSheets.splice(i, 1);
        }
      }
      this.$store.commit(key, unsubmitedSheets);
    },

    addSheetRowClose() {
      if (!this.$store.state.btnSaveClick && this.$refs.addSheetRow.trade_type == "HC") {
        Toast('未确认换出商品，将删除已换入商品')
        this.sheet.sheetRows.splice(-1, 2);
      }
    },

    handleItemClik(item) {
      var sheetItem = {
        itemUnitsInfo: item,
        supcust_id: this.sheet.supcust_id,
        sup_name: this.sheet.sup_name,
        branch_id: this.sheet.branch_id,
        branch_name: this.sheet.branch_name,
        onload_remarks: this.onloadBrief,
        stock: item.stock,
        sheet: this.sheet,
        selectDistinctStockFlag: ''
      }
      Promise.resolve().then(() => {
        if (!this.attrShowFlag) {
          this.$store.commit("activeSelectItem", item)
          let distinctStock = false
          if (item.mum_attributes) {
            if (!item.mum_attributes.forEach) item.mum_attributes = JSON.parse(item.mum_attributes)
            if (item.mum_attributes.find(attr => attr.distinctStock)) {
              distinctStock = true
            }
          }
          this.$store.commit("distinctStockFlag", distinctStock)
        }
        this.popupAddSheetRow = true
      }).then(() => {
        this.searchStr = ''
        this.$refs.addSheetRow.loadData(sheetItem)
        this.$store.commit("HRItem", sheetItem)
      })
    },

    onSheetRowAdd_Attr(obj) {
      // 属性下一个输入
      let trade_type = this.$store.state.trade_type
      if (trade_type !== "HR") {
        this.$refs.arrtMainToAddSheetRow.nextItemClick(obj)
      }
    },


    // 删除单据行
    btnRemoveRow_click(index) {
      if (index >= 0 && index < this.sheet.sheetRows.length) {
        this.sheet.sheetRows.splice(index, 1);
      }
    },

    // 商品编辑行点击
    onRowEdit(item, index) {
      if (this.sheet.approve_time) return; // 已审核不能编辑

      var readonly = false;
      let readonly_type = '';
      if (item.promotion_type) {
        readonly = true; readonly_type = 'promotion';
      }
      if (item.trade_type === 'H') {
        readonly = true; readonly_type = 'trade_h';
      }
      if (this.sheet.approve_time) {
        readonly = true; readonly_type = 'approved'; // * 已审的提醒优先级高于其他
      }
      this.popupEditSheetRowPannel = true;
      this.EditSheetRowIndex = index;
      let editingItem = {
        datas: item,
        onloadBrief: this.onloadBrief,
        readonly: readonly,
        readonly_type: readonly_type
      };
      console.log({ editingItem })
      this.editingItem = editingItem;
    },

    // 排序商品
    onSortByItemName() {
      this.sheet.sheetRows = this.sheet.sheetRows.sort((a, b) => a.item_name.localeCompare(b.item_name, 'zh')); //a~z 排序
      this.$forceUpdate()
    },

    //选择默认单位
    onSelectDefaultUnit() {
      if (this.sheet.approve_time) return;
      this.popupDefaultUnitPicker = true;
    },

    //选择默认单位确认
    onConfirmDefaultUnitPicker(value) {
      this.sheet.default_unit_type = value.unit_type;
      this.sheet.default_unit_name = value.unit_name;
      this.popupDefaultUnitPicker = false;
    },

    // 搜索输入键盘事件
    onSearchInputKeyDown(event) {
      if (event.key === 'Enter') {
        this.searchItems();
      }
    },

    // 搜索商品
    searchItems() {
      if (!this.searchStr.trim()) {
        Toast('请输入搜索内容');
        return;
      }

      this.queryCondition = {
        searchStr: this.searchStr.trim()
      };

      this.popupSelectItems = true;
    },

    // 添加商品
    btnClassView_click() {
      if (this.sheet.approve_time) {
        return
      }
      let query = {
        searchStr: this.searchStr || "",
      };
      this.$store.commit("currentSheet", this.sheet);
      this.$router.push({
        name: "SelectItems",
        query: query,
        params: {
          data: {},
          sheet: this.sheet,
          onload_remarks: this.onloadBrief
        }
      });
    },

    // 扫码按钮点击
    async btnScanBarcode_click() {
      if (this.sheet.approve_time) return; // 已审核不能扫码

      try {
        const result = await this.getScanBarResult();
        this.pageSayCode(result.code)
      } catch (error) {
        console.error('扫码失败:', error);
      }
    },

    // 获取扫码结果
    getScanBarResult(unit_type = '') {
      return new Promise((resolve, reject) => {
        const supportFormat = {
          Code128: true,
          Code39: true,
          Code93: true,
          CodaBar: true,
          DataMatrix: true,
          EAN13: true,
          EAN8: true,
          ITF: true,
          QRCode: false,
          UPCA: true,
          UPCE: true,
          PDF417: true,
          Aztec: true,
        }
        const androidconfig = {
          barcodeFormats: supportFormat,
          beepOnSuccess: true,
          vibrateOnSuccess: false,
          detectorSize: .6,
          rotateCamera: false,
          // preferFrontCamera: false, // iOS and Android
          // showFlipCameraButton: true, // iOS and Android
          // showTorchButton: true, // iOS and Android
          // torchOn: false, // Android, launch with the torch switched on (if available)
          // saveHistory: true, // Android, save scan history (default false)
          // prompt: "Place a barcode inside the scan area", // Android
          // resultDisplayDuration: 1000, // Android, display scanned text for X ms. 0 suppresses it entirely, default 1500
          // // formats: "", // default: all but PDF_417 and RSS_EXPANDED
          // orientation: "portrait", // Android only (portrait|landscape), default unset so it rotates with the device
          // disableAnimations: true, // iOS
          // disableSuccessBeep: false // iOS and Android
        }
        const iosconfig = {
          preferFrontCamera: false, // iOS and Android
          showFlipCameraButton: false, // iOS and Android
          showTorchButton: true, // iOS and Android
          torchOn: false, // Android, launch with the torch switched on (if available)
          disableAnimations: false, // iOS
          disableSuccessBeep: false // iOS and Android
        }
        let barcodeScannerAndroidConfig = {
          preferFrontCamera: false, // iOS and Android
          showFlipCameraButton: true, // iOS and Android
          showTorchButton: true, // iOS and Android
          torchOn: false, // Android, launch with the torch switched on (if available)
          saveHistory: true, // Android, save scan history (default false)
          prompt: "Place a barcode inside the scan area", // Android
          resultDisplayDuration: 1000, // Android, display scanned text for X ms. 0 suppresses it entirely, default 1500
          // formats: "", // default: all but PDF_417 and RSS_EXPANDED
          orientation: "portrait", // Android only (portrait|landscape), default unset so it rotates with the device
          disableAnimations: true, // iOS
          disableSuccessBeep: false, // iOS and Android
          barcodeFormats: supportFormat
        }


        const config = isiOS ? iosconfig : (typeof cordova.plugins.mlkit?.barcodeScanner == 'undefined' ? androidconfig : barcodeScannerAndroidConfig)
        const plugin = isiOS || typeof cordova.plugins.mlkit?.barcodeScanner == 'undefined' ? cordova.plugins.barcodeScanner : cordova.plugins.mlkit.barcodeScanner
        if (isiOS) {
          plugin.scan(
            async (result) => {
              const res = { unit_type, code: result.text, format: result.format }
              resolve(res)
            },
            async (res) => {
              reject(res)
            },
            config
          );

        } else {
          const useOldPlugin = typeof cordova.plugins.mlkit?.barcodeScanner == 'undefined'
          console.log(useOldPlugin)
          if (useOldPlugin) {
            plugin.scan(

              async (result) => {
                const res = { unit_type, code: result.text, format: result.format }
                resolve(res)
              },
              async (res) => {
                reject(res)
              },
              config
            );
          } else {
            plugin.scan(
              config,
              async (result) => {
                const res = { unit_type, code: result.text, format: result.format }
                resolve(res)
              },
              async (res) => {
                reject(res)
              }
            );
          }
        }
      })
    },

    pageSayCode(result) {
      this.searchStr = result;
      this.queryScan()
    },

    queryScan() {
      const sortType = this.$store.state.queryItemSortType ?? "default"
      this.$store.commit("currentSheet", this.sheet);
      let params = {
        sheetType: this.sheet.sheetType,
        searchStr: this.searchStr,
        branchID: this.sheet.branch_id,
        classID: '',
        showStockOnly: false,
        branch_name: this.sheet.branch_name,
        supcustID: this.sheet.supcust_id,
        sup_name: this.sheet.sup_name,
        querySearchStr: '',
        pageSize: 20,
        startRow: 0,
        noStockAttrSplitShow: this.noStockAttrSplitShow
      }
      let funcGetItemList = AppSheetSaleGetItemList
      if (this.sheet.sheetType === 'CG' || this.sheet.sheetType === 'CT') {
        funcGetItemList = AppSheetBuyGetItemList
      } else if (this.sheet.sheetType === 'DH') {
        funcGetItemList = AppSheetOrderItemGetItemList
      }
      let that = this
      funcGetItemList(params).then(res => {
        if (res.result === "OK") {
          if (res.data.length === 0) {
            Toast('未找到对应商品')
            return
          }
          if (res.data.length === 1) {

            this.$refs.inputCodesRef.blur();   // 失焦 ios测试
            let item = this.handleItemClickToKeyboard(res.data[0])
            if (this.popupAddSheetRow) {
              let el = this.$refs.addSheetRow
              let event = document.createEvent('Events');
              event.initEvent('touchstart', true, true);
              // 是否区分库存
              if (el.distinctStock) {
                if (this.attrShowFlag) {
                  el.btnOK_clicked(event)
                } else {
                  this.popupAddSheetRowFalse()
                }
              } else {
                el.btnOK_clicked(event)
                if (this.attrShowFlag) {
                  this.popupAddSheetRowFalse()
                  this.attrShowFlag = false
                }
              }
            }
            setTimeout(() => {
              let existItem = this.sheet.sheetRows.find(sheetRow => {
                return sheetRow.item_id === item.item_id
              })
              if (existItem) {
                Dialog.confirm({
                  title: '继续录入',
                  message: '该商品已存在，继续录入，数量将累加',
                  width: "320px"
                })
                  .then(() => {
                    if (this.searchStr.length >= 13) {
                      item.scanBarcode = this.searchStr
                    }
                    this.handleItemClik(item)
                  })

              } else { // 商品不存在
                if (this.searchStr.length >= 13) {
                  item.scanBarcode = this.searchStr
                }
                if (!this.isLoadVirtualProduceDate) {
                  item.virtual_produce_date = ""//cw虚拟产期是produce_date,branch_position虚拟产期是virtual_produce_date
                }
                this.handleItemClik(item)
              }
            }, 300)
          } else if (res.data.length > 1) {
            that.btnClassView_click();
          }

        }
      }).catch(err => {
        console.log(err)
      })
    },

    // 弹窗关闭事件
    popupAddSheetRowFalse() {
      this.popupAddSheetRow = false
      this.multiSelectItemShow = false
      this.$store.commit("clearPartOfSheetState", '')
      //this.handleSelectedSheetRows()
    },

    // 标题点击切换隐私/正常模式
    title_click(e) {
      if (!(e.target.className && e.target.className.indexOf && e.target.className.indexOf('van-nav-bar__title') >= 0)) return
      if (this.lastClickTime) {
        if (new Date() - this.lastClickTime < 1000) {
          this.$store.commit('inPrivateMode', !this.inPrivateMode)
          if (this.inPrivateMode) {
            this.showPrivateModePrompt()
          } else {
            Toast('进入正常模式')
          }
        }
      }
      this.lastClickTime = new Date()
    },

    //隐私模式模式提示
    showPrivateModePrompt() {
      if (this.inPrivateMode) {
        Toast.success({ message: '进入隐私模式, 双击顶部标题返回', duration: 3000 })
      }
    },

    // 离开页面确认
    stayOrLeave() {
      this.goback()
    },

    goback() {
      if (!this.isPopup) {
        this.$store.commit("sheet", { noRed: true, sheetType: "" });
        this.$store.commit("classId", this.$store.state.AllItemClassId);
        this.myGoBack(this.$router)
      } else {
        this.$emit("closePage")
      }
    },

    // 添加页面返回回调方法，用于刷新列表页面
    onBackToThisPage(fromPageName, params) {
      console.log('返回到标签打印单页面，来自:', fromPageName, '参数:', params);
      // 如果需要刷新数据，可以在这里添加刷新逻辑
      if (params && params.needRefresh) {
        this.initSheet();
      }
    },

    // 页面关闭时的回调方法
    onThisPageClose(params) {
      // 传递当前单据信息给返回的页面
      if (params) {
        params.sheet_id = this.sheet.sheet_id;
        params.sheetType = 'BQ'; // 标签打印单类型
      }
    },

    handleHC() {
      this.popupAddSheetRow = true;
      let data = JSON.parse(JSON.stringify(this.$store.state.HRItem));
      data.itemUnitsInfo.bReturn = false;
      data.itemUnitsInfo.unitPriceRows.forEach(item => {
        if (Math.abs(item.quantity) > 0) {
          item.quantity = Math.abs(item.quantity);
          item.sub_amount = Math.abs(item.sub_amount);
          item.remark = "换出";
          item.remarkid = "";
        }
      })
      this.$refs.addSheetRow.loadData(data);
    },

    handleAttrShowFlagChange() {
      if (!this.attrShowFlag) {
        this.$refs.arrtMainToAddSheetRow.initData(() => { })
        this.attrShowFlag = !this.attrShowFlag
        this.$store.commit("attrShowFlag", this.attrShowFlag)
      } else {
        this.attrShowFlag = !this.attrShowFlag
        if (!this.multiSelectFlag) {
          this.popupAddSheetRowFalse()
        } else {
          this.handleItemClik(this.$store.state.activeSelectItem)
        }
      }
    },

    onSheetRowAdd_OK() {
      console.log('onSheetRowAdd_OK')
      this.popupAddSheetRow = false
      // 如果是蓝牙设备就进行聚焦输入框
      if (this.bluetoothDeviceInputFlag) {
        this.$refs.inputCodesRef.focus();   // 聚焦
      }
    },

    handleAutoBtnOkClick() {
      if (this.attrShowFlag) {
        this.$refs.addSheetRow.btnOK_clicked(null);
      }
    },

    // 菜单跳转
    menuGo({ name, url }) {
      console.warn('Route url:', url)
      this.$router.push(url)
    },

    // 获取搜索输入框宽度
    getSearchInputWidth() {
      return 'calc(100% - 40px)';
    },

    // 处理商品数量显示
    HandleNoStockAttrSplitShow(attr_qty) {
      attr_qty = typeof attr_qty == 'string' ? JSON.parse(attr_qty) : attr_qty
      const attr = attr_qty.length > 0 ? attr_qty[0] : 0
      if (this.noStockAttrSplitShow) {
        if (attr === '') {
          return ''
        }
        let keyName = 'optName_'
        let tempName = []
        let temp = typeof attr == 'string' ? JSON.parse(attr) : attr
        Object.keys(temp).forEach(item => {
          if (item.substr(0, keyName.length) == keyName) {
            tempName.push(temp[item])
          }
        })
        return '(' + tempName.sort().join('_') + ')'
      }
      return ''
    },

    // 处理JSON数据
    handleToJSON(item) {
      console.log('attr_qty', item)
      return typeof item.attr_qty == 'string' ? JSON.parse(item.attr_qty) : item.attr_qty
    },

    // 处理属性名称显示
    handleAttrNameShow(attr) {
      if (attr === '') {
        return ''
      }
      let keyName = 'optName_'
      let tempName = []
      let temp = typeof attr == 'string' ? JSON.parse(attr) : attr
      Object.keys(temp).forEach(item => {
        if (item.substr(0, keyName.length) == keyName) {
          tempName.push(temp[item])
        }
      })
      return tempName.sort().join('_')
    },

    // 金额格式化（从mixin继承，这里重新定义以确保可用）
    toMoney(value, precision = 2) {
      if (value === null || value === undefined || value === '') {
        return '0.00';
      }

      const num = parseFloat(value);
      if (isNaN(num)) {
        return '0.00';
      }

      return num.toFixed(precision);
    },

    // 图片预览
    preview_photo(idx) {
      ImagePreview({
        images: this.sheet.appendixPhotos,
        startPosition: idx,
        closeable: true,
      });
    },

    // 删除图片
    remove_photo(idx) {
      console.log({ idx })
      this.sheet.appendixPhotos.splice(idx, 1)
    },

    // 打印相关
    // 确认打印模版
    confirmTemplates(printer_kind) {
      this.tmplist = printer_kind == 'tiny' ? this.printTemplatesTiny : this.printTemplates
      if (this.tmplist.length == 0) {
        var err = printer_kind == 'tiny' ? '没有可用的小票模板' : '没有可用的打印模板'
        Toast.fail(err); return
      }
    },

    showPrinterSelectionPage() {
      this.PopupPrintersSelect = true
    },
    hidePrinterSelectionPage() {
      this.PopupPrintersSelect = false
    },

    //确认打印机选择 - 标签打印只允许选择支持模板打印的打印机
    confirmPrinterSelectionChange(selectedPrinter) {
      // 检查选择的打印机是否支持模板打印
      if (!selectedPrinter.useTemplate) {
        Toast.fail('标签打印只能使用支持模板打印的打印机')
        return
      }

      // 标签打印强制使用模板
      this.useTmp = true

      this.printers.forEach((prt) => {
        if (prt.id === selectedPrinter.id) {
          prt.isDefault = true
        } else {
          prt.isDefault = false
        }
      })

      // 更新所有打印机列表（包括不支持模板的）
      let allPrinters = window.getCompanyStoreValue('c_printers') || []
      allPrinters.forEach((prt) => {
        if (prt.id === selectedPrinter.id) {
          prt.isDefault = true
        } else {
          prt.isDefault = false
        }
      })
      window.setCompanyStoreValue('c_printers', allPrinters)

      this.defaultPrinter = selectedPrinter
      for (let _i = 0; _i < this.printers.length; _i++) {
        const _printer = this.printers[_i]
        if (_printer.isDefault) {
          this.defaultPrinterIndex = _i;
          break;
        }
      }

      this.PopupPrintersSelect = false
      Toast.success('已选择支持模板打印的打印机')

      const printer_kind = selectedPrinter.kind
      // 标签打印强制使用模板
      if (this.templatesLoaded) {
        this.confirmTemplates(printer_kind)
      } else {
        this.loadPrintTemplates((data) => {
          this.confirmTemplates(printer_kind)
        })
      }
    },

    //加载打印模版
    loadPrintTemplates(successCb) {
      this.tmplist = []
      this.printTemplates = []
      this.printTemplatesTiny = []
      debugger
      var params = {
        sheetType: this.sheet.sheetType,
        clientID: this.sheet.supcust_id
      }
      AppGetTemplate(params).then(data => {
        var templateList = data.templateList
        for (let i = 0; i < templateList.length; i++) {
          const template = templateList[i]
          var inserttmp = {
            name: template.template_name,
            i: i,
            tmp: template
          }
          this.printTemplates.push(inserttmp)
          try {
            let tmp_tocheck = JSON.parse(template.template_content)
            if (tmp_tocheck.width <= 110) {
              this.printTemplatesTiny.push(inserttmp)
            }
          } catch {
            console.error('在解析模板的宽度时发生错误,inserttmp:', inserttmp)
          }
        }
        this.templatesLoaded = true
        if (successCb) {
          successCb(data)
        }
      })
    },

    // 跳转打印详情
    btnGotoPrintView_click() {
      this.isInPrintView = true
      // 获取默认打印机
      const defaultPrinter = window.getDefaultPrinter()

      // 检查默认打印机是否支持模板打印
      if (!defaultPrinter || !defaultPrinter.useTemplate) {
        Toast.fail('没有可用的模板打印机')
        this.isInPrintView = false
        return
      }

      // 判断是否为云打印
      var printer_kind = defaultPrinter.kind
      if (!printer_kind) {
        if (defaultPrinter.type == "cloud")
          printer_kind = '24pin'
        else
          printer_kind = 'tiny'
      }

      // 标签打印强制使用模板
      this.useTmp = true

      //获取模板并展示
      if (this.templatesLoaded) {
        this.confirmTemplates(printer_kind)
      } else {
        this.loadPrintTemplates((data) => {
          this.confirmTemplates(printer_kind)
        })
      }
      this.isPrinting = false
    },

    // 打印按钮点击处理函数
    async btnPrint_click() {
      if (this.sheet.sheet_id && !this.sheet.approve_time && this.sheetHasChange) {
        this.doSave(() => {
          this.confirmPrint()
        })
      }
      else {
        this.confirmPrint()
      }
    },

    // 打印预览方法 外层 
    async confirmPreviewOutside() {
      if (!this.sheet.sheet_id) {
        Toast.fail("请先保存单据");
        return;
      }

      // 标签打印直接使用第一个可用的标签打印模板进行预览
      if (this.templatesLoaded) {
        this.handleLabelPreviewOutside()
      } else {
        this.loadPrintTemplates((data) => {
          this.handleLabelPreviewOutside()
        })
      }
      return
    },

    
    /**生成标签打印预览图片
     * 参数:template 标签打印模版对象
     * 作用:将生成的base64图片组添加到previewImageUrls数组
     * 无返回值
     */
    async generateLabelPreviewImages(template) {
      if (!this.sheet.sheetRows || this.sheet.sheetRows.length === 0) {
        throw new Error('没有商品数据')
      }
      try {
        window._PRINTER_IMG_DPI = 8
        // 为每个商品行生成标签预览图片
        for (const row of this.sheet.sheetRows) {
          const printCount = parseInt(row.quantity) || 1
          for (let i = 0; i < printCount; i++) {
            const itemData = { ...row }
            // 条码字段映射 - 将商品数据中的条码字段映射到模板期望的字段名
            if (itemData.b_barcode && !itemData.barcode) {
              itemData.barcode = itemData.b_barcode  // 大包装条码
            }
            if (itemData.s_barcode && !itemData.barcode) {
              itemData.barcode = itemData.s_barcode  // 小包装条码
            }
            const labelImageB64 = await LabelPrinter.printToImage(itemData, template)
            this.previewImageUrls.push(labelImageB64)
          }
        }
      } catch (error) {
        console.error('生成标签预览时出错:', error)
        throw error
      }
    },

    /** 处理标签打印的外层预览
     */
    handleLabelPreviewOutside() {
      // 获取可用的标签打印模板
      const availableTemplates = this.printTemplatesTiny.length > 0 ? this.printTemplatesTiny : this.printTemplates
      if (!availableTemplates || availableTemplates.length === 0) {
        Toast.fail("没有可用的标签打印模板")
        return
      }
      // 使用第一个可用模板进行预览
      const tmp = availableTemplates[0].tmp
      this.btnPreview_ByTemplate(tmp)
      this.previewShow = true
    },

    // 打印预览方法 - 标签打印强制使用模板
    async confirmPreview() {
      this.isInPrintView = true
      // 获取默认打印机
      const defaultPrinter = window.getDefaultPrinter()

      // 检查默认打印机是否支持模板打印
      if (!defaultPrinter || !defaultPrinter.useTemplate) {
        Toast.fail('当前默认打印机不支持模板打印，请选择支持模板打印的打印机')
        return
      }

      // 判断是否为云打印
      var printer_kind = defaultPrinter.kind
      if (!printer_kind) {
        if (defaultPrinter.type == "cloud")
          printer_kind = '24pin'
        else
          printer_kind = 'tiny'
      }

      // 标签打印强制使用模板
      this.useTmp = true

      //获取模板并展示
      if (this.templatesLoaded) {
        this.confirmTemplates(printer_kind)
      } else {
        this.loadPrintTemplates((data) => {
          this.confirmTemplates(printer_kind)
        })
      }

      // 标签打印只使用模板打印
      if (!this.tmplist || this.tmplist.length == 0) {
        Toast.fail("没有可用的打印模板")
        return
      }

      var tmp
      try {
        tmp = this.tmplist[this.selectedTmpId].tmp
      }
      catch (e) {
        Toast.fail('打印错误' + e.message)
        return
      }

      // 先对是否保存单据进行判断
      if (!this.sheet.sheet_id) {
        Toast.fail("请先保存单据");
        return;
      }

      console.log('标签打印预览模板:', tmp)
      this.btnPreview_ByTemplate(tmp)
      this.previewShow = true;

      setTimeout(() => {
        this.sheetHasChange = false
      }, 100)
    },

    btnPreview_ByTemplate(tmp) {//模板预览
      // 为了避免重复点击打印预览之后 previewImageUrls数组被重复添加值，初始化一个空数组
      this.previewImageUrls = []
      // 检查单据状态
      if (!this.sheet.sheet_id) {
        Toast.fail("请先保存单据");
        return;
      }
      const that = this
      const printErrorHandler = (error) => {
        handlePrintComplete()
        Toast.fail(error)
      }
      const handlePrintComplete = () => {
        this.loadingMsg = ''
        this.isPrinting = false
      }
      console.log('Print Template:', tmp)

      this.loadingMsg = '正在生成标签预览'
      this.isPrinting = true

      try {
        // 解析模板内容
        var parsedTmp = JSON.parse(tmp.template_content);

        // 使用LabelPrinter生成预览图片
        this.generateLabelPreviewImages(parsedTmp).then(() => {
          handlePrintComplete()
        }).catch(err => {
          console.error('标签预览生成失败:', err)
          printErrorHandler('标签预览生成失败: ' + err.message)
        })
      } catch (err) {
        console.error('模板解析失败:', err)
        printErrorHandler('模板解析失败: ' + err.message)
      }
      return
    },


    // 最终打印方法 - 标签打印只使用模板打印
    async confirmPrint() {
      // 标签打印强制使用模板打印
      if (!this.tmplist || this.tmplist.length == 0) {
        Toast.fail("没有可用的打印模板")
        return
      }

      var tmp
      try {
        tmp = this.tmplist[this.selectedTmpId].tmp
      }
      catch (e) {
        Toast.fail('打印错误' + e.message)
        return
      }

      console.log('标签打印使用模板:', tmp)
      this.btnPrint_ByTemplate(tmp, this.printCount)

      setTimeout(() => {
        this.sheetHasChange = false
      }, 100)
    },

    //模板打印
    btnPrint_ByTemplate(tmp, printCount) {
      const that = this
      const printErrorHandler = (error) => {
        handlePrintComplete()
        Toast.fail(error)
      }
      const handlePrintComplete = () => {
        this.loadingMsg = ''
        this.isPrinting = false
      }
      console.log('Print Template:', tmp)
      // 检查单据状态
      if (!this.sheet.sheet_id) {
        Toast.fail("请先保存单据");
        return;
      }

      const defaultPrinter = window.getDefaultPrinter()
      this.loadingMsg = '正在打印' // 显示打印信息

      // 为打印机类型赋默认值(如果未定义)
      var printer_kind = defaultPrinter.kind
      if (!printer_kind) {
        if (defaultPrinter.type == "cloud")
          printer_kind = '24pin'
        else
          printer_kind = 'tiny'
      }
      // 组装单据校验的请求参数
      this.isPrinting = true
      var sTmp = tmp.template_content
      tmp = JSON.parse(tmp.template_content);
      var printTemplate = []
      const isCloud = defaultPrinter.type == 'cloud'
      if (sTmp.indexOf('"prepay_balance"') !== -1) printTemplate.push({ name: "prepay_balance" })
      if (sTmp.indexOf('"arrears_balance"') !== -1) printTemplate.push({ name: "arrears_balance" })
      if (sTmp.indexOf('"print_count"') !== -1) printTemplate.push({ name: "print_count" })
      if (sTmp.indexOf('"name":"give_qty_unit"') >= 0) printTemplate.push({ name: "give_qty_unit" })

      var smallUnitBarcode = false
      if (this.printBarcodeStyleForSale?.length) {
        if (this.printBarcodeStyleForSale.indexOf('smallUnit') !== -1) {
          smallUnitBarcode = true
        }
      }
      console.warn('smallUnitBarcode:', smallUnitBarcode)

      // 与后端通信校验单据内容
      var params = {
        sheetType: this.sheet.sheetType,
        sheet_type: this.sheet.sheet_type,
        sheet_id: this.sheet.sheet_id,
        smallUnitBarcode: smallUnitBarcode,
        printTemplate: JSON.stringify(printTemplate),
        copies: "1"
      }
      // 使用POST方法避免printTemplate参数过长导致的问题
      AppGetSheetToPrint_Post(params).then(data => {
        if (data.result != 'OK') {
          printErrorHandler(data.msg)
        }
        else {
          var sheet = data.sheet

          // 处理云打印的打印逻辑
          if (isCloud) {
            let default_params = {
              operKey: this.$store.state.operKey,
              tmp: tmp,
              sheet: sheet,
              printer_brand: defaultPrinter.brand,
              device_id: defaultPrinter.cloudID,
              check_code: defaultPrinter.cloudCode,
              cus_orderid: this.sheet.sheet_no,
              copies: "1",
              directPrint: false,
              escCommand: null,
            }
            // 当要用小票云打印机打印时，ESC指令应本地生成。
            if (printer_kind == 'tiny') {
              var comm = []
              var params = {
                operKey: this.$store.state.operKey,
                tmp: tmp,
                sheet: sheet
              }
              AppSheetToImages(params).then(async res => {
                var escCommand = ''
                var jr = []
                jr = res//JSON.parse(res);
                console.log(jr);
                for (var i = 0; i < jr.length; i++) {
                  let imgb64 = `data:image/jpg;base64,${jr[i]}` // 重要! base64ToPrintBytesPromise须使用合法的src作为图片源并处理
                  var imageBytes = await Printing.base64ToPrintBytesPromise(imgb64)
                  var s = Printing.ab2hex(imageBytes)
                  comm.push(imageBytes)
                }
                var temparr = [27, 64] // 1b 40 用来调整偏移
                for (var i = 0; i < comm.length; i++) {
                  let uint8array = comm[i]
                  for (var u = 0; u < uint8array.length; u++) {
                    temparr.push(uint8array[u])
                  }
                  // escCommand += btoa(this.uint8ArrayToBase64(comm[i]))
                  // BASE64加密不能拼接：Base64将每3个字节转换为4个字符。如果输入数据不是3字节的倍数，则编码将添加1或2个零字节作为填充。然后在流的末尾用一个或两个'='字符表示。
                  // 如果尝试解码多个串联的代码块，则流中很可能会有'='字符，这是非法的。
                }
                var finalUint8Array = Uint8Array.from(temparr)
                escCommand = btoa(this.uint8ArrayToBase64(finalUint8Array))
                //default_params.directPrint = true;
                default_params.escCommand = escCommand;
                AppCloudPrint_escCmd(default_params).then(res => {
                  console.log(res)
                  handlePrintComplete()
                  this.showCloudPrintResult(res)
                }).catch(err => { printErrorHandler(err); })
              }).catch(err => { printErrorHandler(err); })
            } else {
              // 若为针式云打印机，将变量发往后端，剩余逻辑均在后端处理。
              AppCloudPrint_sheetTmp(default_params).then(res => {
                console.log(res)
                handlePrintComplete()
                this.showCloudPrintResult(res)
              }).catch(err => { printErrorHandler(err); })
            }
          }
          else {
            // 蓝牙打印
            let must24Pin = false // 必须使用针式打印机的图片打印指令
            let ignorePaperFeedCmd = false // 跳过换行、走纸、换页命令
            const pname = defaultPrinter.name
            // 2023.02.13 兼容ZICOX-CC4打印机
            if (pname.match('CC4_')) { // CC4_1656L
              ignorePaperFeedCmd = true
              must24Pin = true
            }

            var params = {
              operKey: this.$store.state.operKey,
              tmp: tmp,
              sheet: sheet,
              ignorePaperFeedCmd: ignorePaperFeedCmd
            }

            Printing.initPrinter()
            if (Printing.cmdMode === '_cpcl') {
              console.warn('正在使用CPCL打印机 | 蓝牙打印')
            }
            // 标签打印机在前端生成TSPL指令并直接打印
            Printing.initPrinter()
            window._PRINTER_IMG_DPI = 8

            // 解析模板内容并生成打印指令
            Promise.resolve().then(async () => {
              var parsedTmp = tmp
              return await LabelPrinter.getPrintCommands(sheet, parsedTmp)
            }).then(async res => {
              var blocks = []
              var jr = []
              jr = res // 标签打印指令数组

              /* 处理标签指令转译的函数(须为async) */
              let cmdFunc = async (commandArray) => {
                return new Uint8Array(commandArray)
              }

              /* 标签打印指令处理 - TSPL格式始终使用imageBytes */
              for (var i = 0; i < jr.length; i++) {
                const commandGroup = jr[i] // 标签打印指令组
                var imageBytes = await cmdFunc(commandGroup)
                var s = Printing.ab2hex(imageBytes) // 保持与普通打印一致的调试信息
                blocks.push({ imageBytes: imageBytes })
              }

              Printing.printSheetOrInfo(blocks, (e) => {
                if (e.result == 'Error') {
                  Toast.fail(e.msg)
                } else {
                  Toast.success(e.msg)
                  this.sheet.print_count = Number(this.sheet.print_count || 1) + 1
                  ApiPrintMark(
                    {
                      operKey: this.$store.state.operKey,
                      sheetType: sheet.sheetType,
                      sheetIDs: sheet.sheet_id,
                      printEach: true,
                      printSum: false
                    }).then(() => { }).catch(() => { })
                }
                handlePrintComplete()
              })
            }).catch(err => { printErrorHandler(err); })
          }
        }
      }).catch(err => { printErrorHandler(err); })
    },

    /** 记录打印次数 */
    MarkPrint() {
      this.sheet.print_count = Number(this.sheet.print_count || 1) + 1
      ApiPrintMark({
        operKey: this.$store.state.operKey,
        sheetType: this.sheet.sheetType,
        sheetIDs: this.sheet.sheet_id,
        printEach: true,
        printSum: false
      }).then(() => { }).catch(() => { })
    },

    showCloudPrintResult(res) {
      if (res.result == "OK") {
        var duration = 3000
        var msg = '打印成功'
        if (res.msg) msg = res.msg
        if (res.printSuccess == false) duration = 6000
        this.isInPrintView = false
        Toast.success({ message: msg, duration: duration })
      }
      else {
        Toast.fail({ message: res.msg, duration: 8000 })
      }
    },

    // 点击图片时，触发预览函数
    previewImage(index) {
      this.previewIdx = index; // 设置当前预览的索引
      this.previewShow = true; // 显示图片预览
    },

    // 监听预览变化
    previewOnChange(index) {
      this.previewIdx = index; // 更新当前显示的图片索引
    },

    clearShoppingCarCache() {
      this.$store.commit('clearPartOfSheetState', {})
      this.$store.commit('shoppingCarObj', {
        sheetType: this.sheet.sheetType,
        clearFlag: true
      })
    },

    onEmpty() {
      Dialog.confirm({
        title: "清空单据",
        message: "请确认是否清空单据？",
        width: '320px'
      }).then(() => {
        this.sheet.sheetRows = []
        this.popupSubmitPannel = false
        this.clearShoppingCarCache()
        Toast.success("已清空")
      })

    },

    //商品编辑页面
    onRowEditDone(sheetRow) {
      this.sheet.sheetRows[this.EditSheetRowIndex] = sheetRow;
      this.popupEditSheetRowPannel = false;
      this.onSheetRowsChanged()
    },

    onSheetRowsChanged(isLoadingSheet) {
      this.handleSelectedSheetRows()
      this.handleActionsWorkContent()
    },

    handleActionsWorkContent() {
      console.log('handleActionsWorkContent')
      // this.sheet.displayGiveProofs = []
      // 判断是什么类型
      const typeAction = this.displayGiveProofsType.key
      if (this.sheet.sheetRows.length === 0) {
        this.sheet.displayGiveProofs = []
      }
      // 添加
      this.sheet.sheetRows.forEach(sheetRow => {
        if (sheetRow.disp_template_id) {
          const findItem = this.sheet.displayGiveProofs.find(item => item.disp_sheet_id === sheetRow.disp_sheet_id)
          if (!findItem && sheetRow[typeAction + '_actions'] && sheetRow[typeAction + '_actions'].length > 0) {
            const actionItem = {
              disp_sheet_id: sheetRow.disp_sheet_id,
              disp_sheet_no: sheetRow.disp_sheet_no,
              disp_template_name: sheetRow.disp_template_name,
              disp_template_id: sheetRow.disp_template_id,
              action: sheetRow[typeAction + '_actions'],
              work_content: [],
              need_review: sheetRow[typeAction + '_need_review'],
              reviewer: '',
              review_time: '',
              review_comment: '',
              review_refused: '',
            }
            actionItem.action.forEach(workContentItem => {
              let pushObj = {
                action: workContentItem,
                work_content: ''
              }
              if (workContentItem.type === 'photo') {
                pushObj.work_content = {
                  mandatory: [],  // 必选列表
                  mandatoryName: [],
                  optional: [],   // 可选列表
                  optionalName: [],
                }
                let num = pushObj.action.items.length === 0 ? Number(pushObj.action.minNum) : pushObj.action.items.length
                for (let i = 0; i < num; i++) {
                  pushObj.work_content.mandatory[i] = ''
                }
              } else {
                pushObj.work_content = ''
              }
              actionItem.work_content.push(pushObj)
            })
            this.sheet.displayGiveProofs.push(actionItem)
          }
        }
      })
      // 如果sheet没有,就需要删除掉
      if (this.sheet.displayGiveProofs) {
        for (let i = 0; i < this.sheet.displayGiveProofs.length; i++) {
          let displayGiveProof = this.sheet.displayGiveProofs[i]
          const findItem = this.sheet.sheetRows.find(item => item.disp_template_id === displayGiveProof.disp_template_id)
          if (!findItem) {
            this.sheet.displayGiveProofs.splice(i--, 1)
          }
        }
      }

    },

    btnOut() {
      this.$store.commit("sheet", { noRed: true, sheetType: "" });
      myGoBack(this.$router);
    },

    btnCopySheet_click() {
      logUserAction({ tm: new Date().format("yyyy-MM-dd h:m"), act: "copyclick", sn: this.sheet.sheet_no })
      Dialog.confirm({
        title: "复制单据",
        message: "请确认是否复制?",
        width: '320px'
      }).then(() => {
        this.doCopySheet()
        Toast("复制成功");
        logUserAction({ tm: new Date().format("yyyy-MM-dd h:m"), act: "copyed", sn: this.sheet.sheet_no })
      })
    },

    doCopySheet() {
      this.sheet.sheet_id = "";
      this.sheet.sheet_no = "";
      this.sheet.approve_time = "";
      this.sheet.approver_id = "";
      this.sheet.happen_time = "";
      this.sheet.make_time = "";
      this.sheet.maker_id = "";
      window.g_SaleSheet = this
    },

    // 删除未审核单据
    btnDeleteSheet_click() {
      var that = this
      Dialog.confirm({
        title: '删除单据',
        message: '请确认是否删除',
        width: '320px'
      }).then(() => {

        var delFunc = AppSheetLabelPrintDelete;
        let params = {
          operKey: this.$store.state.operKey,
          sheet_id: this.sheet.sheet_id,
          sheetType: this.sheet.sheetType
        };
        delFunc(params).then((res) => {
          if (res.result === "OK") {
            Toast.success("删除成功,即将退出该页面");
            setTimeout(function () {
              that.btnOut()
            }, 1000);
          } else {
            Toast.fail("删除失败:" + res.msg);
          }
        });

      })
        .catch(() => {
          Toast("取消删除");
        });
    },

    // 显示缓存的未保存单据
    showSheetsFromCache() {
      var key = "unsubmitedSheets_" + this.sheet.sheetType;
      if (this.$store.state[key] && this.$store.state[key].length > 0) {
        if (this.$route.query.showUnsubmitedSheetsQureyFlag !== undefined) {
          let cacheSheetsFlag = this.$route.query.showUnsubmitedSheetsQureyFlag == 'false' ? false : true
          if (!cacheSheetsFlag) { // 拜访门店进入，只显示自己的缓存
            this.unsubmitedSheets = this.$store.state[key].filter(item => item.supcust_id == this.$route.query.supcust_id)
          }
        } else {
          this.unsubmitedSheets = this.$store.state[key];
        }
        // 只有在新建单据且有缓存单据时才显示弹窗
        if (this.unsubmitedSheets.length > 0 && !this.$route.query.sheetID) {
          this.showUnsubmitedSheets = true;
        } else {
          this.showUnsubmitedSheets = false;
        }
      }
    },

    // 保存当前单据到缓存
    saveCurSheetToCache() {
      var key = "unsubmitedSheets_" + this.sheet.sheetType;
      var unsubmitedSheets = this.$store.state[key];
      if (!unsubmitedSheets) unsubmitedSheets = [];
      var sheet = this.getSheet();
      Date.prototype.format = function (fmt) {
        var o = {
          "M+": this.getMonth() + 1, //月份
          "d+": this.getDate(), //日
          "h+": this.getHours(), //小时
          "m+": this.getMinutes(), //分
          "s+": this.getSeconds(), //秒
          "q+": Math.floor((this.getMonth() + 3) / 3), //季度
          S: this.getMilliseconds(), //毫秒
        };
        if (/(y+)/.test(fmt))
          fmt = fmt.replace(
            RegExp.$1,
            (this.getFullYear() + "").substr(4 - RegExp.$1.length)
          );
        for (var k in o)
          if (new RegExp("(" + k + ")").test(fmt))
            fmt = fmt.replace(
              RegExp.$1,
              RegExp.$1.length == 1
                ? o[k]
                : ("00" + o[k]).substr(("" + o[k]).length)
            );
        return fmt;
      };
      sheet.saveTime = new Date().format("yyyy-MM-dd h:m");
      // for (var i = unsubmitedSheets.length - 1; i >= 0; i--) {
      //   var sht = unsubmitedSheets[i];
      //   if (sht.saveTime == sheet.saveTime) {
      //     unsubmitedSheets.splice(i, 1);
      //   }
      // }
      unsubmitedSheets.unshift(sheet);
      if (unsubmitedSheets.length > 5)
        unsubmitedSheets.splice(5, unsubmitedSheets.length - 5);
      this.$store.commit(key, unsubmitedSheets);
    },

    // 选择未保存的单据
    onUnsubmitedSheetSelected(obj) {
      this.copyFlag = true
      this.sheet = obj;
      this.showUnsubmitedSheets = false;
      this.processSheetRows(); // 处理单据行数据
      this.showPrivateModePrompt()
    },

    // 删除未保存的单据
    onUnsubmitedSheetDelete(index) {
      var key = "unsubmitedSheets_" + this.sheet.sheetType;
      this.unsubmitedSheets.splice(index, 1);
      this.$store.commit(key, this.unsubmitedSheets);
    },

    // 检查属性数量有效性
    checkAttrQtyValid() {
      for (let i = 0; i < this.sheet.sheetRows.length; i++) {
        let sheetRow = this.sheet.sheetRows[i]
        if (sheetRow.attr_qty && sheetRow.attr_qty !== "[]" && !sheetRow.sale_print_combine_attr) {
          let attr_qty = typeof sheetRow.attr_qty == 'string' ? JSON.parse(sheetRow.attr_qty) : sheetRow.attr_qty
          var qtyInAttr = 0
          attr_qty.forEach(attr => {
            qtyInAttr += parseFloat(attr.qty)
          })
          if (qtyInAttr != sheetRow.quantity) {
            return `${sheetRow.item_name}的分口味数量和总数不一致，请删除该行重新录入`
          }
        }
      }
      return ''
    },

    // uint8Array转base64
    uint8ArrayToBase64(array) {
      let u8a = array
      var CHUNK_SZ = 0x8000
      var c = []
      for (var i = 0; i < u8a.length; i += CHUNK_SZ) {
        c.push(String.fromCharCode.apply(null, u8a.subarray(i, i + CHUNK_SZ)))
      }
      return c.join("")
    },

    // base64转uint8Array
    base64ToUint8Array(base64) {
      var raw = atob(base64);
      var rawLength = raw.length;
      var array = new Uint8Array(new ArrayBuffer(rawLength));
      for (var i = 0; i < rawLength; i++) {
        array[i] = raw.charCodeAt(i);
      }
      return array;
    },
  }
};
</script>

<style lang="less" scoped>
/deep/ .van-overlay.van-image-preview__overlay {
  //打印预览的时候底部透明度不要那么高
  background-color: rgba(0, 0, 0, 0.8) !important;
}

/deep/ .mx-calendar-content .cell:hover {
  background-color: #fff;
}

/deep/ .mx-calendar-content .cell.active {
  border-radius: 100px;
  color: #fff;
  background-color: rgb(255, 153, 153);
}

/deep/ .mx-table-date .today {
  color: rgb(255, 153, 153);
}

/deep/ .mx-datepicker {
  width: 100%;
  height: 100%;
  box-sizing: border-box;

  .mx-input-wrapper {
    height: 40px;
    box-sizing: border-box;
    display: flex;

    .mx-input {
      outline: none;
      border: none;
      height: 100%;
      box-shadow: none;
      border-radius: 0;
    }
  }

  .mx-datepicker-main {
    border: none;
    border-top: 1px solid #e8e8e8;
  }

  .mx-datepicker-content,
  .mx-calendar,
  .mx-datepicker-body,
  .mx-calendar {
    height: 100%;
    width: 100%;
    border: none;
    box-sizing: border-box;
  }

  .mx-calendar-header {
    height: 1.5rem;
    line-height: 1.5rem;
  }

  .mx-calendar-content {
    height: calc(100% - 1.5rem);
  }

  button {
    min-width: 0;
  }

  .mx-btn-icon-left,
  .mx-btn-icon-double-left,
  .mx-btn-icon-right,
  .mx-btn-icon-double-right {
    padding: 0 0.3rem;
  }
}

// 在线支付UI相关
.payb-status-text {
  color: green;
  font-weight: 600;
  padding-left: 15px;
  margin-right: 15px;
}

.close-circle {
  width: 30px;
  height: 30px;
  border-radius: 50%;
  background-color: #f2f2f2;
  /* 圈圈背景色 */
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  /* 给圈圈加阴影，增加立体感 */
}

.close-icon {
  font-size: 16px;
  color: #555;
  /* 图标颜色 */
  font-weight: bold;
  text-shadow: 1px 1px 2 #555, -1px -1px 2 #555;
  /* 模拟图标的粗体效果 */
}

//审核界面删除分享按钮样式
.approved_reded {
  position: absolute;
  left: 25px;
  top: 165px;
  margin-top: -40px;
  z-index: 100;
}

.approved {
  width: 105px;
  height: 60px;
  // top: 110px;
  // left: 50px;
}

.reded {
  width: 105px;
  height: 60px;
  // top: 110px;
  // left: 50px;
}

.btn_CRUD_type {
  border-radius: 12px;
  height: 40px;
}

.radio-tmp-position {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  height: 210px;
  overflow-y: auto;
}

.setHeight {
  height: 60px;
}

.radio-tmp-style {
  display: inline-block;
  position: relative;
  left: 0;
  margin: 5px 0;
  width: 50%;
  text-align: left;
}

.radio-tmp-name {
  color: #9999b8;
}

// 模板radio样式
.radio-tmp-type {
  width: 20px;
  height: 20px;
  appearance: none;
  position: relative;
}

.radio-tmp-type:before {
  content: "";
  width: 20px;
  height: 20px;
  border: 1px solid #8d8d8d;
  display: inline-block;
  border-radius: 50%;
  vertical-align: middle;
}

.radio-tmp-type:checked:before {
  content: "";
  width: 20px;
  height: 20px;
  border: 1px solid #8d8d8d;
  background: #8d8d8d;
  display: inline-block;
  border-radius: 50%;
  vertical-align: middle;
}

.radio-tmp-type:checked:after {
  content: "";
  width: 10px;
  height: 5px;
  border: 0.065rem solid white;
  border-top: transparent;
  border-right: transparent;
  text-align: center;
  display: block;
  position: absolute;
  top: 6px;
  left: 5px;
  vertical-align: middle;
  transform: rotate(-45deg);
}

/deep/ .van-overlay {
  position: fixed;
  top: 0;
  left: 0;
  z-index: 1;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.4) !important;
}

/deep/ .van-cell__title.van-field__label {
  width: 40px;
}

.black {
  fill: currentColor;
  // color: #757575;
  color: #8d8d8d;
}

@flex_w: {
  display: flex;
  flex-wrap: wrap;
}

;

@flex_a_j: {
  display: flex;
  align-items: center;
  justify-content: center;
}

;

@flex_a_bw: {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

;

@posAblot: {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
}

;

// @scorllHeight: this.$refs.salesListFooter.offsetHeight + 'px';
.van-divider {
  margin-bottom: 90px;
}

.van-divider--hairline {
  margin-bottom: 90px;
}

/deep/.inputZf {
  .van-field__control {
    text-align: right;
  }
}

/deep/.van_search.van-cell.van-field {
  padding: 0px;
  border: 0px;
}

/deep/input.van-field__control.van-field__control--right {
  padding: 0px 10px 4px 0px;
}

/deep/.van-toast {
  z-index: 99999 !important;
}

/deep/.van-swipe-cell__right {
  .van-button {
    height: 100%;
  }
}

/deep/.price-col {
  display: flex;
  flex-direction: column;
  justify-content: center;
}

/deep/.price-col>div {}

/deep/.price-col>div>del,
/deep/.price-col>div>span {
  margin-right: 2px;
}

.sys_price {
  color: #f88;
  font-size: 12px;
}

.color_fff {
  border-radius: 12px;
  border: 1px solid #ccc;
  background-color: #fff;
}

.color_ffcccc {
  border-radius: 12px;
}

.style_checkButton {
  border-radius: 12px;
  // width: 80px;
  height: 45px;
}

.button-row {
  display: flex;
  justify-content: center;
  /* 中间对齐 */
  align-items: center;
  position: relative;
  /* 保证按钮内部定位 */
  margin-top: 20px;
  /* 和内容保持一定距离 */
}

.btn-new {
  width: 150px;
  /* 固定宽度 */
  height: 40px;
  border-radius: 12px;
}



// 重新修改样式
.pages {
  width: 100%;
  height: 100%;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;

  .public_box3 {
    width: 100%;
    flex: 1;
    overflow-y: auto;
    overflow-x: hidden;
    display: flex;
    flex-direction: column;
  }

  .total_money {

    //@posAblot();
    .sales_list_footer {
      border-top: 1px solid #f2f2f2;
      border-bottom: 0px solid #f2f2f2;
      min-height: 30px;
      line-height: 30px;
      font-size: 14px;
      text-align: left;
      background: #fff;
      box-shadow: 0 0px 0px #f2f2f2;
      padding-top: 6px;

      div {
        padding: 0 5px;
      }

      .sales_list_span {
        font-family: numFont;
        margin-right: 15px;
      }
    }

    .sales_footer {
      height: 55px;
      background: #ffffff;
      border-top: 0px solid #f2f2f2;

      @flex_a_bw();

      .footer_input {
        @flex_a_j();
        flex: 6;
        // width: 250px;
        height: 50px;
        text-align: left;

        input[type="text"] {
          width: 100%;
          font-size: 14px;
          border-style: none;
          border-bottom: 1px solid #eee;
          background-color: #eee;
          height: 35px;
        }
      }

      .footer_iconBt {
        @flex_a_j();
        flex: 1;
        background-color: transparent;
        border: none;
        // margin-left: 20px;
        width: 50px;
        height: 50px;
      }
    }
  }
}

.amount-row {
  display: flex;
  flex-direction: row;
  align-items: center;
  margin-left: 16px;
  margin-top: 10px;
  width: 100%;
}

.discount-btn {
  position: absolute;
  right: 30px;
  margin-top: 10px;
  height: 30px;
  width: 30px;
  border-radius: 6px;
  background-color: #ffcccc;
  line-height: 30px;
  color: #222;
  margin: 10px;
}

.sales_box_list_big {
  height: calc(100% - 40px - 37px - 30px);
}

.sales_box_list {
  height: calc(100% - 40px - 37px);
}

.van_popup {
  overflow: hidden;
}

.van_search_style {
  display: contents;
  font-size: 15px;
}

.van_search_btns {
  margin-left: 10px;
}

.btn-delete {
  position: absolute;
  right: 10px;
  width: 50px;
  line-height: 30px;
  border: 1px solid #f2f2f2;
  /* 添加黑色边框 */
  border-radius: 10px;
  /* 设置圆角 */
  text-align: center;
}

.btn-delete:active {
  background-color: #ffcccc;
}

.delSheetRow {
  vertical-align: top;

  button {
    height: 100%;
    vertical-align: top;
  }
}

.appendix-container {
  display: flex;
  flex-direction: column;
  margin-bottom: 10px;
}

.appendix_tip {
  font-size: 12px;
  color: #666;
}

.other_operate_content_left {
  display: flex;
  // margin: 30px 0;
  width: 70%;
  left: 0px;
  flex-direction: row;
  overflow-x: scroll;
  position: absolute;
}

.other_operate_content_left::-webkit-scrollbar {
  display: none;
}

.other_operate_content_right {
  width: 80px;
  position: fixed;

  right: 0px;
}

.appendixphoto-container {
  margin-left: -10px;
  border-radius: 50%;
  width: 60px;
  position: relative;
  margin-left: 10px;
  height: 60px;
  position: relative;

  .remove-icon {
    position: absolute;
    top: -7px;
    margin-left: 51px;
    color: #f31010;
    font-size: 16px;
    float: right;
  }
}

.clientInfo {
  margin-top: 10px;
  display: flex;
  // font-family: font;
  font-size: 14px;
  color: #f56c6c;
  padding: 8px 5px;

  span {
    // font-family: font;
    font-size: 14px;
  }

  .prepayInfoSpan {
    text-align: left;
    // font-family: font;
    font-size: 14px;
  }

  .arrearsInfoSpan {
    border-bottom: 0.5px solid rgb(248, 198, 198);
    margin-right: 20px;
    // font-family: font;
    font-size: 14px;
  }

  .acct-cust-name {
    margin-right: 20px;
  }
}

.sales_more {
  background-color: #fff;
  position: absolute;
  top: 0px;
  width: 100%;
  height: 100%;
  left: 0px;

  .print-template-wrapper {
    height: 250px;
  }

  .select-printer {
    display: flex;
    width: 100%;
    min-height: 45px;

    /deep/.van-cell-group.van-cell-group--inset.van-hairline--top-bottom {
      margin: 0px;
    }
  }

  /deep/.van-checkbox-group {
    font-size: 16px;
  }

  .print-count {
    display: flex;
    justify-content: flex-end;
    align-items: center;
  }

  .print-btns {
    height: 45px;
    border-radius: 12px;
    width: calc(45% - 5px);
  }

  .other_payway {

    // margin: 15px 0;
    .payway-minHeight {
      min-height: 40px;
    }

    .payway {
      height: 30px;

      font-size: 15px;
      @flex_a_j();
      margin: 5px;

      .van-col {
        height: inherit;
        @flex_a_j();
        color: #000000;
      }

      input {
        width: 90px;
        height: 100%;
        border: none;
        outline: none;
        border-bottom: 0.5px solid #eee;
        text-align: center;
      }

      .payway_add {
        font-size: 20px;
        color: #ccc;
        margin-left: 10px;
        margin-top: 10px;
      }

      .arrow {
        line-height: 17px;
      }
    }

    .payway::after {
      position: absolute;
      content: "";
      border-bottom: 1px solid #444;
    }
  }

  /deep/.van-field {
    height: calc(100% - 46px);
    color: #000000;
  }

  /deep/.van-radio-group {
    margin-top: 5px;
  }

  /deep/.van-radio {
    margin: 0 0 10px 0;
  }

  /deep/.van-divider {
    margin: 10px 0 3px;
    color: #ddd;
  }

  h4 {
    font-size: 16px;
    text-align: left;
    height: 35px;
    line-height: 35px;
    padding-left: 10px;
    color: #333333;
    font-weight: normal;
  }
}

.red {
  color: red;
}

.title {
  font-size: 16px;
  background: #ffffff;
  border-bottom: 1px solid #f2f2f2;
  // padding: 5px 15px;

  svg {
    width: 24px;
    height: 18px;
    margin-top: 1px;
  }

  position:relative .van-row {
    height: 100%;

    .van-col {
      height: 100%;
      @flex_a_j();
    }

    .van-col:first-child {
      justify-content: flex-start;
    }

    .van-col:last-child {
      justify-content: flex-end;
    }
  }
}

.sales_list {
  height: 100%;

  .sales_list_boxs {
    height: calc(100% - 40px);
    // overflow-y: auto;
    // overflow-x: hidden;
  }

  .sales_list_other {
    height: calc(100% - 40px);
    overflow-y: auto;
    overflow-x: hidden;
  }
}

.sales_ul {
  height: auto;
  overflow: hidden;
  color: #000;
  background: #ffffff;
  padding-left: 8px;

  // .sales_img {
  //   width: 50px;
  //   height: 50px;
  // }

  // .sales_img_wrapper {
  //   width: 63px;
  //   height: 63px;
  // }

  .sales_ul_li {
    width: 100%;
    min-height: 30px;
    height: auto;
    font-size: 15px;
    // padding: 10px 15px 10px 5px;
    box-sizing: border-box;
    border-style: none;
    border-bottom-style: solid;
    border-width: 1px;
    border-color: #f2f2f2;

    .van-row {
      height: auto;

      .van-col {
        height: 25px;
        line-height: 25px;
        @flex_a_j();
      }

      .van-col:first-child {
        justify-content: flex-start;
        text-align: left;
      }

      .van-col:last-child {
        justify-content: flex-end;
      }
    }
  }

  .total_msg {
    margin-top: 15px;
    //min-height: 80px;
    text-align: left;
    font-weight: normal;
    padding: 5px;
    color: #999;

    :first-child {
      margin-right: 5px;
    }

    .sheet-mark-brief-wrapper {
      margin-top: 15px;
      word-wrap: break-word;
      word-break: break-all;
    }
  }
}

.lowItem {
  height: auto;
  overflow: hidden;
  padding: 10px;

  h4 {
    height: 40px;
    font-size: 16px;
    font-weight: 500;
    color: #333333;
    @flex_a_j();
  }

  .lowItem_ul {
    height: auto;
    overflow: hidden;
    margin-bottom: 10px;

    li {
      height: auto;
      overflow: hidden;
      padding: 10px;
      padding-right: 80px;
      font-size: 14px;
      @flex_a_bw();
      border-bottom: 1px solid #f2f2f2;
    }

    li:last-child {
      border-bottom: none;
    }
  }
}

.other_operate {
  // display: flex;
  width: 100%;
  height: auto;

  .other_operate_content {
    vertical-align: top;
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
    height: 40px;
    width: 100%;
    @flex_a_j();

    .small-btn {
      margin-bottom: 20px;
      padding: 0;
      background-color: #fff;
      color: #5577bb;
      height: 30px;
      border: none;
    }

    .small-btn:disabled {
      color: #ddd;
    }

    button {
      padding: 0;
      width: 50px;
      height: 30px;
      vertical-align: top;
      margin: 0 15px;
      flex: 1;
      // height: 100%;
      // color: #eee;
    }
  }

  // .other_operate_content::after{
  //   content: "";
  //   flex: auto;
  //   width: 31px;
  // }

  // van-button {
  //   width: 100px;
  // }
}

.loading-msg {
  border-radius: 20px;
  width: 200px;
  height: 80px;
  background: #555;
  color: #fff;
  position: fixed;
  top: calc(50vh - 40px);
  left: calc(50vw - 100px);
  z-index: 99999999;
  display: flex;
  justify-content: center;
  align-items: center;

  /deep/.van-loading {
    font-size: 24px;
  }
}

.custom_h5 {
  height: 46px;
  line-height: 46px;
  font-size: 16px;
  color: steelblue;
  background: #f2f2f2;
  position: relative;

  .icon_h5 {
    position: absolute;
    height: 46px;
    width: 46px;
    right: 0;
    top: 0;
    display: block;
    @flex_a_j();
    font-size: 20px;
  }
}

.class_add_goods {
  height: calc(100% - 46px);
  background: #f2f2f2;
  overflow: auto;
}

.class_add_goods_m {
  height: 100%;
}

.submitSaleSheet {
  color: #000;
  font-size: 15px;
}

.van-radio-group {
  font-size: 12px;
  margin-left: 100px;
  margin-top: 20px;
}

.van-radio {
  margin-top: 10px;
}

.selectSheetRowStyle {
  border-width: 1px;
  border-style: solid;
  min-width: 20px;
  min-height: 20px;
  text-align: center;
  color: rgba(245, 108, 108, 0.8);
  font-size: 14px;
  line-height: 20px;
  font-weight: 500;
  border-radius: 6px;
  margin-right: 2px;
  margin-top: 2px;
}

//head
@flex_w: {
  display: flex;
  flex-wrap: wrap;
}

;

@flex_a_j: {
  display: flex;
  align-items: center;
  justify-content: center;
}

;

@flex_a_bw: {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

;

.photo {
  margin-right: 6px;
  border-radius: 6px;
  width: 60px;
  height: 60px;
}

.iconfont {
  //margin-top: 20px;
  padding: 10px;
  width: 40px;
  height: 40px;
  font-size: 0.53333rem;
  color: #ccc;
  // border: 1px solid #ccc;
}

.public_query {

  //padding: 0 10px;
  .public_query_title {
    background: #ffffff;
    margin-bottom: 1px;

    .sheet_info {
      color: #888;
      // padding-top: 5px;
      // padding-left: 25px;
    }
  }

  .public_query_title_t {
    padding: 0 10px 5px;
    height: 25px;
    // line-height: 25px;
    font-size: 15px;
    color: #000000;
    @flex_a_bw();

    i {
      height: 18px;
      border: 1px solid red;
      display: inline-block;
      font-size: 12px;
      font-style: normal;
      line-height: 20px;
      padding: 0 5px;
      border-radius: 4px;
      margin-left: 10px;
    }
  }

  .public_query_titleSrc {
    padding: 10px 15px 3px 15px;

    display: flex;
    flex-direction: column;
    background: #ffffff;

    .public_query_wrapper {
      @flex_a_bw();

      // margin-top: 5px;
      .public_query_titleSrc_item {
        width: 50%;
        height: 100%;
        overflow: hidden;
        position: relative;
        vertical-align: top;
        border: none;
        padding: 10px 0 3px 0;

        div {
          height: 100%;
          width: calc(100% - 40px);
          padding: 0 30px 0 10px;
          border: none;
          font-size: 15px;
          line-height: 35px;
          color: #333333;
          text-align: left;
        }

        input,
        .selectOne {
          height: 100%;
          padding: 0 10px 4px 0px;
          border: none;
          font-size: 15px;
          line-height: 35px;
          color: #333333;
          vertical-align: top;
          border-bottom: 1px solid #eee;
          text-align: right;
        }

        .van-icon {
          position: absolute;
          left: 5px;
          top: 0;
          bottom: -10px;
          width: 30px;
          text-align: center;
          font-size: 22px;
          @flex_a_j();
          color: #aaa;
          background-color: #ffffff;
        }

        .query_icon {
          width: 22px;
          height: 22px;
          margin-left: 10px;
        }
      }
    }
  }

  .public_list_title {
    height: 40px;
    @flex_a_bw();
    margin-top: 5px;
    padding: 0 5px;

    div {
      height: 40px;
      line-height: 40px;
      font-size: 15px;
      text-align: center;
      width: calc(25% - 10px);
      padding: 0 5px;
      font-weight: 500;
      color: #333333;
    }

    div:first-child {
      width: calc(50% - 10px);
      text-align: left;
    }

    div:last-child {
      width: calc(25% - 10px);
      text-align: right;
    }
  }
}

.item_attr_qty_wrapper {
  width: 100%;
  display: flex;
  flex-wrap: wrap;
  justify-content: flex-end;
  align-items: center;

  .item_attr_title {
    color: #999;
    font-size: 14px;
    margin-right: 10px;
  }

  .item_attr_qty_content {
    font-size: 10px !important;
    color: black;
    border: 1px solid rgb(238, 42, 42);
    padding: 1px 2px;
    margin-right: 3px;
    border-radius: 5px;
    margin-bottom: 2px;
  }
}

.addSheetRowOne {
  background-color: transparent !important;
  height: 100%;
  width: 100%;
  overflow: hidden;

  .add_goods_wrapper {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    align-items: flex-end;
    justify-content: flex-end;

    .goods_attr_wrapper {
      background-color: transparent;
      display: flex;
      width: 100%;
      flex: 1;
      flex-direction: column;
      overflow-y: hidden;

      .goods_attr {
        background-color: #fff;
        height: 100%;
      }

      .layout_content {
        width: 100%;
        flex: 1;
      }
    }

    .goods_add_wrapper {
      width: 100%;
      display: flex;
      flex-direction: column;
      align-items: flex-end;
      justify-content: flex-end;

      .class_add_goods {
        width: 100%;
        height: 100%;
        background-color: #fff;
        //padding-top: 5px;
        padding: 5px 0 0;
      }
    }
  }
}

.myslide-right-enter-active,
.myslide-right-leave-active,
.myslide-left-enter-active,
.myslide-left-leave-active {
  transition: all 0.4s ease;
}

.myslide-right-enter {
  transform: translateX(-100%);
}

.myslide-right-leave-active {
  transform: translateX(0%);
}

.myslide-left-enter {
  transform: translateX(100%);
}

.myslide-left-leave-active {
  transform: translateX(100%);
}

.other-info {
  margin-right: 15px;
  color: #bbb;
  height: 10px;
  font-size: 13px;
  margin-bottom: 0px;
  text-align: left;
  height: auto;
}

.profit-info {
  color: #999;
  line-height: 1.5;
}

.content-code-wrapper {
  width: 100%;
  min-height: 400px;
  background-color: #eee;
  border-right: 1px solid #eee;
  display: flex;
  box-shadow: 3px 3px 17px #333333;
  flex-direction: column;
  box-sizing: border-box;

  .content-code-title {
    width: 100%;
    height: 60px;
    box-sizing: border-box;
    padding: 5px 10px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    border-bottom: 1px solid #eeeeee;
  }

  .content-code-qr-code {
    height: 300px;
    background-color: #fff;
    border-bottom: 1px solid #eee;
    box-sizing: border-box;
    padding: 10px;
    display: flex;
    flex-direction: column;

    img {
      width: 100%;
      height: 100%;
    }

    .content-wrapper {
      width: 100%;
      min-height: 100px;
      display: flex;
      flex-direction: column;
      margin-bottom: 40px;

      .content-item {
        width: 100%;
      }

      .content-illustrate {
        margin-top: 10px;
      }
    }
  }

  .content-code-btn {
    display: flex;
    height: 60px;
    background-color: #fff;
    justify-content: center;
    padding-top: 10px;

    button {
      width: 80%;
      background-color: #fde3e4;
      border-radius: 10px;
    }
  }
}

/deep/ .van-checkbox__icon--checked .van-icon {
  background-color: #8d8d8d !important;
  border-color: #8d8d8d;
}

.hide-show-div-enter-active {
  transition: opacity 0.5s;
}

.hide-show-div-enter {
  opacity: 0;
}

.hide-show-div-leave-active {
  transition: opacity 0.5s;
}

.hide-show-div-leave-to {
  opacity: 0;
}

.van-cell::after {
  border-bottom: 0.5px solid #ddd;
}

.payway-balance {
  font-size: 10px;
  text-align: left !important;
  color: #888;
  margin-left: 23px;
  margin-top: -10px;
}

.helper-class-slick-item {
  width: 100vw;
  background-color: #f2f2f2;

  .sales_ul_li {
    border-bottom-style: solid;
    border-width: 1px;
    border-color: #f2f2f2;
  }
}

.progress-container {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 9999;
  min-width: 280px;
  min-height: 60px;
  display: flex;
  justify-content: center;
  align-items: center;
}

.van-progress {
  width: 100%;
}

.branch-selection-modal {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  padding: 20px;
  background-color: white;
  border: 1px solid #ddd;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}
</style>
