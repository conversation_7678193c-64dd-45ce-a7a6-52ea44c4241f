package com.lai.geolocation.baidu;

import org.apache.cordova.CallbackContext;
import org.apache.cordova.CordovaPlugin;
import org.apache.cordova.PluginResult;
import org.apache.cordova.PermissionHelper;
import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

import com.baidu.location.BDLocation;
import com.baidu.location.BDLocationListener;
import com.fasterxml.jackson.databind.util.BeanUtil;
import com.lai.geolocation.w3.PositionOptions;

import android.app.Activity;
import android.content.Context;
import android.location.Address;
import android.location.Location;
import android.location.LocationListener;
import android.location.LocationManager;
import android.os.Build;
import android.os.Bundle;

import android.util.Log;
import android.util.SparseArray;
import android.Manifest;
import android.content.pm.PackageManager;
import android.widget.Toast;

import androidx.annotation.RequiresApi;
import androidx.core.app.ActivityCompat;

import java.util.List;

public class GeolocationPlugin extends CordovaPlugin {

  private static final String TAG = "GeolocationPlugin";

  private static final int GET_CURRENT_POSITION = 0;
  private static final int WATCH_POSITION = 1;
  private static final int CLEAR_WATCH = 2;
    private static final int GET_NATIVE_CURRENT_POSITION = 3;


  private Location curLocation = null;

  private SparseArray<BDGeolocation> store = new SparseArray<BDGeolocation>();
  private String [] permissions = { Manifest.permission.ACCESS_COARSE_LOCATION, Manifest.permission.ACCESS_FINE_LOCATION };

  private JSONArray requestArgs;
  private CallbackContext context;

  // 添加静态变量来存储监听器
  private static LocationListener staticGpsListener = null;
  private static LocationListener staticNetworkListener = null;

  @Override
  public boolean execute(String action, JSONArray args, CallbackContext callbackContext) throws JSONException {
    Log.i(TAG, "插件调用");
    JSONObject options = new JSONObject();

    requestArgs = args;
    context = callbackContext;

      switch (action) {
          case "getCurrentPosition":
              getPermission(GET_CURRENT_POSITION);
              try {
                  options = args.getJSONObject(0);
              } catch (JSONException e) {
                  Log.v(TAG, "options 未传入");
              }
              return getCurrentPosition(options, callbackContext);
          case "getNativePosition":
              getPermission(GET_CURRENT_POSITION);
              try {
                  options = args.getJSONObject(0);
              } catch (JSONException e) {
                  Log.v(TAG, "options 未传入");
              }
              if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
                  return getNativePosition(options, callbackContext);
              }
              break;
          case "watchMyPosition": {
              getPermission(WATCH_POSITION);
              try {
                  options = args.getJSONObject(0);
              } catch (JSONException e) {
                  Log.v(TAG, "options 未传入");
              }
              int watchId = args.getInt(1);
              return watchMyPosition(options, watchId, callbackContext);
          }
          case "clearWatch": {
              getPermission(CLEAR_WATCH);
              int watchId = args.getInt(0);
              return clearWatch(watchId, callbackContext);
          }
          case "openFrontLocationService": {
              int watchId = args.getInt(0);
              return openFrontLocationService(watchId);
          }
          case "closeFrontLocationService": {
              int watchId = args.getInt(0);
              return closeFrontLocationService(watchId);
          }
      }
    return false;
  }

    private boolean openFrontLocationService(int watchId) {
        Log.i(TAG, "开启前台定位服务");
        BDGeolocation geolocation = store.get(watchId);
        geolocation.openFrontLocationService();
        return true;
    }

  private boolean closeFrontLocationService(int watchId) {
      Log.i(TAG, "关闭前台定位服务，同时移除通知栏");
      BDGeolocation geolocation = store.get(watchId);
      geolocation.closeFrontLocationService();
      return true;
  }

  private boolean clearWatch(int watchId, CallbackContext callback) {
    Log.i(TAG, "停止监听");
    BDGeolocation geolocation = store.get(watchId);
    store.remove(watchId);
    geolocation.clearWatch();
    callback.success();
    return true;
  }

  private boolean watchMyPosition(JSONObject options, int watchId, final CallbackContext callback) {
    Log.i(TAG, "监听位置变化");
    Activity activity = cordova.getActivity();
    Context ctx = activity.getApplicationContext();
    PositionOptions positionOpts = new PositionOptions(options);
    BDGeolocation geolocation = new BDGeolocation(ctx, activity);
    store.put(watchId, geolocation);
    return geolocation.watchMyPosition(positionOpts, new BDLocationListener() {
      @Override
      public void onReceiveLocation(BDLocation location) {
        JSONArray message = new MessageBuilder(location).build();
        PluginResult result = new PluginResult(PluginResult.Status.OK, message);
        result.setKeepCallback(true);
        callback.sendPluginResult(result);
      }
    });
  }

  private boolean getCurrentPosition(JSONObject options, final CallbackContext callback) {
    Log.i(TAG, "请求当前地理位置");
    Activity activity = cordova.getActivity();
    Context ctx = activity.getApplicationContext();
    PositionOptions positionOpts = new PositionOptions(options);
    BDGeolocation geolocation = new BDGeolocation(ctx, activity);
    return geolocation.getCurrentPosition(positionOpts, new BDLocationListener() {
      @Override
      public void onReceiveLocation(BDLocation location) {
        JSONArray message = new MessageBuilder(location).build();
        callback.success(message);
      }
    });
  }
    // 原来的 getNativePosition 方法（已注释，存在缓存问题）
    
    @RequiresApi(api = Build.VERSION_CODES.M)
    private boolean getNativePosition_old(JSONObject options, final CallbackContext callback) {
        Log.i(TAG, "请求当前地理位置");
        Activity activity = cordova.getActivity();
        Context ctx = activity.getApplicationContext();
        PositionOptions positionOpts = new PositionOptions(options);
        LocationManager mLocationManager = (LocationManager) ctx.getSystemService(Context.LOCATION_SERVICE ); // 位置
         String locationProvider;

        List<String> providers = mLocationManager.getProviders( true );
        
        if (providers.contains( LocationManager.GPS_PROVIDER )) {
            //如果是GPS定位
            Log.d( TAG, "如果是GPS定位" );
            locationProvider = LocationManager.GPS_PROVIDER;
            if (!mLocationManager.isProviderEnabled(LocationManager.GPS_PROVIDER)) {
                Log.d(TAG, "GPS 未开启");
                callback.error("GPS 未开启，请打开GPS");
                return false;
            }
        }
        else if (providers.contains( LocationManager.NETWORK_PROVIDER )) {
            //如果是网络定位
            Log.d( TAG, "如果是网络定位" );
            locationProvider = LocationManager.NETWORK_PROVIDER;
        } 
         else {
            locationProvider = "";
            Log.d( TAG, "没有可用的位置提供器" );
        }
        locationProvider = LocationManager.GPS_PROVIDER;
        // 需要检查权限,否则编译报错,想抽取成方法都不行,还是会报错。只能这样重复 code 了。
        if (Build.VERSION.SDK_INT >= 23 &&
                ActivityCompat.checkSelfPermission( ctx, Manifest.permission.ACCESS_FINE_LOCATION ) != PackageManager.PERMISSION_GRANTED
                // &&
                //ActivityCompat.checkSelfPermission( ctx, Manifest.permission.ACCESS_COARSE_LOCATION ) != PackageManager.PERMISSION_GRANTED
                ) {
        }
        //if (ActivityCompat.checkSelfPermission( ctx, Manifest.permission.ACCESS_FINE_LOCATION ) != PackageManager.PERMISSION_GRANTED && ActivityCompat.checkSelfPermission( ctx, Manifest.permission.ACCESS_COARSE_LOCATION ) != PackageManager.PERMISSION_GRANTED) {
       // }
        //3.获取上次的位置，一般第一次运行，此值为null
        BDLocation bdLocation = new BDLocation();

        // 监视地理位置变化，第二个和第三个参数分别为更新的最短时间minTime和最短距离minDistace
        mLocationManager.requestLocationUpdates( LocationManager.GPS_PROVIDER, 0, 0, locationListener );
        if (curLocation != null) {
            bdLocation = toBDLocation( curLocation );
        }
        else {
            Location location = mLocationManager.getLastKnownLocation( locationProvider );
            bdLocation = toBDLocation( location );
        }
        JSONArray message = new MessageBuilder(bdLocation).build();
        callback.success(message);
        return true;
    }
    

    // 新的 getNativePosition 方法（解决缓存问题，获取实时位置）
    @RequiresApi(api = Build.VERSION_CODES.M)
    private boolean getNativePosition(JSONObject options, final CallbackContext callback) {
        Log.i(TAG, "请求当前地理位置（原生定位-可配置模式）");
        Log.i(TAG, "options: " + options.toString());
        
        Activity activity = cordova.getActivity();
        Context ctx = activity.getApplicationContext();

        // 解析选项
        PositionOptions positionOpts = new PositionOptions(options);
        // 从选项中获取超时时间，默认为8秒
        final int timeout = positionOpts.getTimeout() > 0 ? (int)positionOpts.getTimeout() : 8000;
        // 从选项中获取是否优先精度，默认为false
        final boolean highAccuracy = positionOpts.isEnableHighAccuracy();
        // 从选项中获取定位方式，默认为gps-net
        String positionModeTemp = positionOpts.getpositionMode();
        Log.i(TAG, "positionMode from options: " + positionModeTemp);
        // 从选项中获取GPS超时时间，默认为4秒
        final long gpsTimeout = positionOpts.getGpsTimeout();
        
        // 根据定位方式和可用提供器调整实际使用的提供器
        boolean useGps = false;
        boolean useNetwork = false;
        
        // 检查可用的位置提供器
        LocationManager mLocationManager = (LocationManager) ctx.getSystemService(Context.LOCATION_SERVICE);
        
        // 先清理可能存在的旧监听器
        cleanupStaticListeners(mLocationManager);
        
        List<String> providers = mLocationManager.getProviders(true);
        final boolean hasGps = providers.contains(LocationManager.GPS_PROVIDER);
        final boolean hasNetwork = providers.contains(LocationManager.NETWORK_PROVIDER);
        
        if ("gps".equals(positionModeTemp)) {
            // 仅使用GPS
            useGps = hasGps;
            if (!useGps) {
                Log.d(TAG, "请求仅使用GPS定位，但GPS不可用");
                callback.error("GPS不可用，请开启GPS");
                return false;
            }
        } else if ("net".equals(positionModeTemp)) {
            // 仅使用网络
            useNetwork = hasNetwork;
            if (!useNetwork) {
                Log.d(TAG, "请求仅使用网络定位，但网络定位不可用");
                callback.error("网络定位不可用，请检查网络连接");
                return false;
            }
        } else if ("gps-net".equals(positionModeTemp)) {
            // 优先GPS，超时后使用网络
            useGps = hasGps;
            useNetwork = hasNetwork;
            if (!useGps && !useNetwork) {
                Log.d(TAG, "没有可用的位置提供器");
                callback.error("没有可用的位置提供器");
                return false;
            }
        } else if ("net-gps".equals(positionModeTemp)) {
            // 先网络快速返回，后GPS精确修正
            useGps = hasGps;
            useNetwork = hasNetwork;
            if (!useGps && !useNetwork) {
                Log.d(TAG, "没有可用的位置提供器");
                callback.error("没有可用的位置提供器");
                return false;
            }
        } else {
            // 默认使用gps-net模式
            positionModeTemp = "gps-net";
            useGps = hasGps;
            useNetwork = hasNetwork;
            if (!useGps && !useNetwork) {
                Log.d(TAG, "没有可用的位置提供器");
                callback.error("没有可用的位置提供器");
                return false;
            }
        }
        
        // 将positionModeTemp赋值给final变量positionMode
        final String positionMode = positionModeTemp;
        
        Log.d(TAG, "定位选项: 超时=" + timeout + "ms, 高精度=" + highAccuracy + 
              ", 定位方式=" + positionMode + ", GPS超时=" + gpsTimeout + "ms");
        
        // 检查权限
        if (Build.VERSION.SDK_INT >= 23 &&
                ActivityCompat.checkSelfPermission(ctx, Manifest.permission.ACCESS_FINE_LOCATION) != PackageManager.PERMISSION_GRANTED) {
            callback.error("缺少定位权限");
            return false;
        }
        
        // 用于跟踪是否已经返回了结果
        final boolean[] resultReturned = {false};
        
        // 创建一个用于存储最佳位置的对象
        final Location[] bestLocation = {null};
        
     
       
        // 创建GPS位置监听器
        if (useGps) {
            staticGpsListener = new LocationListener() {
                @Override
                public void onLocationChanged(Location location) {
                    Log.d(TAG, "GPS位置已获取: " + location.getLatitude() + ", " + location.getLongitude());
                    
                    if ("gps".equals(positionMode) || "gps-net".equals(positionMode)) {
                        // 对于GPS优先模式，立即返回GPS位置
                        synchronized (resultReturned) {
                            if (!resultReturned[0]) {
                                resultReturned[0] = true;
                                BDLocation bdLocation = toBDLocation(location);
                                bdLocation.setLocType(61); // 设置为GPS定位结果
                                
                                // 添加位置来源信息
                                JSONArray message = new MessageBuilder(bdLocation)
                                    .addProperty("provider", "gps")
                                    .build();
                                
                                // 清理所有监听器
                                cleanupStaticListeners(mLocationManager);
                                
                                // 返回结果
                                callback.success(message);
                            }
                        }
                    } else if ("net-gps".equals(positionMode)) {
                        // 对于网络优先模式，发送GPS位置作为更新
                        BDLocation bdLocation = toBDLocation(location);
                        bdLocation.setLocType(61); // 设置为GPS定位结果
                        
                        // 创建包含更新标志和位置来源的消息
                        JSONArray message = new MessageBuilder(bdLocation)
                            .addProperty("isUpdate", true)
                            .addProperty("provider", "gps")
                            .build();
                        
                        // 清理所有监听器
                        cleanupStaticListeners(mLocationManager);
                        
                        // 发送更新结果
                        PluginResult result = new PluginResult(PluginResult.Status.OK, message);
                        result.setKeepCallback(false); // 不再保持回调活跃，这是最终结果
                        callback.sendPluginResult(result);
                    }
                }
                
                @Override
                public void onStatusChanged(String provider, int status, Bundle extras) {}
                
                @Override
                public void onProviderEnabled(String provider) {}
                
                @Override
                public void onProviderDisabled(String provider) {
                    if ("gps".equals(positionMode)) {
                        synchronized (resultReturned) {
                            if (!resultReturned[0]) {
                                resultReturned[0] = true;
                                
                                // 清理所有监听器
                                cleanupStaticListeners(mLocationManager);
                                
                                // 返回错误
                                callback.error("GPS已禁用");
                            }
                        }
                    }
                }
            };
            
            try {
                mLocationManager.requestLocationUpdates(LocationManager.GPS_PROVIDER, 0, 0, staticGpsListener);
                Log.d(TAG, "请求GPS位置更新");
            } catch (SecurityException e) {
                Log.e(TAG, "请求GPS位置更新失败: " + e.getMessage());
            }
        }
        
        // 创建网络位置监听器
        if (useNetwork) {
            staticNetworkListener = new LocationListener() {
                @Override
                public void onLocationChanged(Location location) {
                    Log.d(TAG, "网络位置已获取: " + location.getLatitude() + ", " + location.getLongitude());
                    
                    // 保存网络位置
                    bestLocation[0] = location;
                    
                    if ("net".equals(positionMode)) {
                        // 对于网络优先模式，立即返回网络位置
                        cleanupStaticListeners(mLocationManager);
                        synchronized (resultReturned) {
                            if (!resultReturned[0]) {
                                resultReturned[0] = true;
                                BDLocation bdLocation = toBDLocation(location);
                                bdLocation.setLocType(161); // 设置为网络定位结果
                                
                                // 添加位置来源信息
                                JSONArray message = new MessageBuilder(bdLocation)
                                    .addProperty("provider", "network")
                                    .build();
                                
                                // 清理所有监听器

                                
                                // 返回结果
                                callback.success(message);
                            }
                        }
                    } else if ("net-gps".equals(positionMode)) {
                        // 对于网络优先模式，立即返回网络位置，但保持回调活跃以便后续GPS更新
                        cleanupStaticNetworkListeners(mLocationManager);
                        synchronized (resultReturned) {
                            if (!resultReturned[0]) {
                                resultReturned[0] = true;
                                BDLocation bdLocation = toBDLocation(location);
                                bdLocation.setLocType(161); // 设置为网络定位结果
                                
                                // 添加位置来源信息
                                JSONArray message = new MessageBuilder(bdLocation)
                                    .addProperty("provider", "network")
                                    .addProperty("isFirst", true)
                                    .build();
                           

                                // 发送初始结果，但保持回调活跃以便后续更新
                                PluginResult result = new PluginResult(PluginResult.Status.OK, message);
                                result.setKeepCallback(true);
                                callback.sendPluginResult(result);
                            }
                        }
                    } else if ("gps-net".equals(positionMode)) {
                        cleanupStaticNetworkListeners(mLocationManager);
                        // 对于GPS优先模式，保存网络位置，但不立即返回
                        // 等待GPS超时后再决定是否使用
                    }
                }
                
                @Override
                public void onStatusChanged(String provider, int status, Bundle extras) {}
                
                @Override
                public void onProviderEnabled(String provider) {}
                
                @Override
                public void onProviderDisabled(String provider) {
                    if ("net".equals(positionMode)) {
                        synchronized (resultReturned) {
                            if (!resultReturned[0]) {
                                resultReturned[0] = true;
                                
                                // 清理所有监听器
                                cleanupStaticListeners(mLocationManager);
                                
                                // 返回错误
                                callback.error("网络定位已禁用");
                            }
                        }
                    }
                }
            };
            
            try {
                mLocationManager.requestLocationUpdates(LocationManager.NETWORK_PROVIDER, 0, 0, staticNetworkListener);
                Log.d(TAG, "请求网络位置更新");
            } catch (SecurityException e) {
                Log.e(TAG, "请求网络位置更新失败: " + e.getMessage());
            }
        }
        
        try {
            // 根据定位方式启动相应的定位请求
            if ("gps".equals(positionMode)) {
                // 仅使用GPS
                Log.d(TAG, "仅请求GPS位置更新");
                mLocationManager.requestLocationUpdates(LocationManager.GPS_PROVIDER, 0, 0, staticGpsListener);
            } else if ("net".equals(positionMode)) {
                // 仅使用网络
                Log.d(TAG, "仅请求网络位置更新");
                mLocationManager.requestLocationUpdates(LocationManager.NETWORK_PROVIDER, 0, 0, staticNetworkListener);
            } else if ("gps-net".equals(positionMode)) {
                // 优先GPS，超时后使用网络
                if (useGps) {
                    Log.d(TAG, "请求GPS位置更新（优先）");
                    mLocationManager.requestLocationUpdates(LocationManager.GPS_PROVIDER, 0, 0, staticGpsListener);
                }
                
                if (useNetwork) {
                    // 同时请求网络位置，但不立即返回
                    Log.d(TAG, "同时请求网络位置更新（备用）");
                    mLocationManager.requestLocationUpdates(LocationManager.NETWORK_PROVIDER, 0, 0, staticNetworkListener);
                    
                    // 设置GPS等待超时机制
                    cordova.getThreadPool().execute(new Runnable() {
                        @Override
                        public void run() {
                            try {
                                // 等待GPS位置一段时间
                                Thread.sleep(gpsTimeout);
                                
                                // 如果GPS超时，使用网络定位结果
                                synchronized (resultReturned) {
                                    if (!resultReturned[0] && bestLocation[0] != null) {
                                        resultReturned[0] = true;
                                        Log.i(TAG, "GPS等待超时(" + gpsTimeout + "ms)，使用网络定位结果");
                                        
                                        BDLocation bdLocation = toBDLocation(bestLocation[0]);
                                        bdLocation.setLocType(161); // 设置为网络定位结果
                                        
                                        // 添加位置来源信息
                                        JSONArray message = new MessageBuilder(bdLocation)
                                            .addProperty("provider", "network")
                                            .addProperty("reason", "gps_timeout")
                                            .build();
                                        
                                        // 清理所有监听器
                                        cleanupStaticListeners(mLocationManager);
                                        
                                        // 返回结果
                                        callback.success(message);
                                    }
                                }
                            } catch (InterruptedException e) {
                                Thread.currentThread().interrupt();
                            }
                        }
                    });
                }
            } else if ("net-gps".equals(positionMode)) {
                // 先网络快速返回，后GPS精确修正
                if (useNetwork) {
                    Log.d(TAG, "请求网络位置更新（快速）");
                    mLocationManager.requestLocationUpdates(LocationManager.NETWORK_PROVIDER, 0, 0, staticNetworkListener);
                }
                
                if (useGps) {
                    Log.d(TAG, "请求GPS位置更新（精确）");
                    mLocationManager.requestLocationUpdates(LocationManager.GPS_PROVIDER, 0, 0, staticGpsListener);
                }
            }
            
            // 设置总超时机制
            cordova.getThreadPool().execute(new Runnable() {
                @Override
                public void run() {
                    try {
                        Thread.sleep(timeout);
                        
                        // 如果超时后还没有返回结果
                        synchronized (resultReturned) {
                            if (!resultReturned[0]) {
                                resultReturned[0] = true;
                                
                                // 如果有任何位置，则返回最佳位置
                                if (bestLocation[0] != null) {
                                    Log.i(TAG, "总超时(" + timeout + "ms)，返回最佳可用位置");
                                    BDLocation bdLocation = toBDLocation(bestLocation[0]);
                                    bdLocation.setLocType(161); // 设置为网络定位结果
                                    
                                    // 添加位置来源信息
                                    JSONArray message = new MessageBuilder(bdLocation)
                                        .addProperty("provider", "network")
                                        .addProperty("reason", "timeout")
                                        .build();
                                    
                                    // 清理所有监听器
                                    cleanupStaticListeners(mLocationManager);
                                    
                                    // 返回结果
                                    callback.success(message);
                                } else {
                                    // 如果没有任何位置，则返回错误
                                    Log.i(TAG, "定位超时，没有获取到任何位置");
                                    
                                    // 清理所有监听器
                                    cleanupStaticListeners(mLocationManager);
                                    
                                    // 返回错误
                                    callback.error("定位超时，请检查GPS设置或网络连接");
                                }
                            }
                            
                            // 对于net-gps模式，发送完成标志
                            if ("net-gps".equals(positionMode)) {
                                // 发送完成标志
                                JSONObject finalObj = new JSONObject();
                                try {
                                    finalObj.put("complete", true);
                                } catch (JSONException e) {
                                    Log.e(TAG, "创建完成消息失败", e);
                                }
                                
                                // 清理所有监听器
                                cleanupStaticListeners(mLocationManager);
                                
                                // 发送完成消息
                                PluginResult result = new PluginResult(PluginResult.Status.OK, finalObj);
                                result.setKeepCallback(false); // 关闭回调
                                callback.sendPluginResult(result);
                            }
                        }
                    } catch (InterruptedException e) {
                        Thread.currentThread().interrupt();
                    }
                }
            });
            
        } catch (SecurityException e) {
            Log.e(TAG, "请求位置更新失败: " + e.getMessage());
            callback.error("请求位置更新失败: " + e.getMessage());
            return false;
        }
        
        return true;
    }
    /**
     * LocationListern监听器
     * 参数：地理位置提供器、监听位置变化的时间间隔、位置变化的距离间隔、LocationListener监听器
     */
    private BDLocation toBDLocation(Location location) {
        BDLocation bdLocation = new BDLocation();
        bdLocation.setLatitude(location.getLatitude());
        bdLocation.setLongitude(location.getLongitude());
        bdLocation.setAltitude(location.getAltitude());
        bdLocation.setAddrStr("");
        bdLocation.setAddr(null);
        bdLocation.setLocationDescribe("");
        return bdLocation;
    }
    LocationListener locationListener = new LocationListener() {

        /**
         * 当某个位置提供者的状态发生改变时
         */
        @Override
        public void onStatusChanged(String provider, int status, Bundle arg2) {
        }

        /**
         * 某个设备打开时
         */
        @Override
        public void onProviderEnabled(String provider) {
        }

        /**
         * 某个设备关闭时
         */
        @Override
        public void onProviderDisabled(String provider) {
        }

        /**
         * 手机位置发生变动
         */
        @Override
        public void onLocationChanged(Location location) {
           // location.getAccuracy();//精确度
            curLocation = location;
            //    setLocation( location );
        }


    };

  /**
   * 获取对应权限
   * int requestCode Action代码
   */
  public void getPermission(int requestCode){
    if(!hasPermisssion()){
      PermissionHelper.requestPermissions(this, requestCode, permissions);
    }
  }

  /**
   * 权限请求结果处理函数
   * int requestCode Action代码
   * String[] permissions 权限集合
   * int[] grantResults 授权结果集合
   */
  @RequiresApi(api = Build.VERSION_CODES.M)
  public void onRequestPermissionResult(int requestCode, String[] permissions,
                                        int[] grantResults) throws JSONException
   {
       PluginResult result;
       //This is important if we're using Cordova without using Cordova, but we have the geolocation plugin installed
       if(context != null) {
           for (int r : grantResults) {
               if (r == PackageManager.PERMISSION_DENIED) {
                   Log.d(TAG, "Permission Denied!");
                   result = new PluginResult(PluginResult.Status.ILLEGAL_ACCESS_EXCEPTION);
                   context.sendPluginResult(result);
                   return;
               }

           }
           switch(requestCode)
           {
               case GET_NATIVE_CURRENT_POSITION:
                   getNativePosition(this.requestArgs.getJSONObject(0), this.context);
                   break;
               case GET_CURRENT_POSITION:
                   getCurrentPosition(this.requestArgs.getJSONObject(0), this.context);
                   break;
               case WATCH_POSITION:
                   watchMyPosition(this.requestArgs.getJSONObject(0), this.requestArgs.getInt(1), this.context);
                   break;
               case CLEAR_WATCH:
                   clearWatch(this.requestArgs.getInt(0), this.context);
                   break;
           }
       }
   }

   /**
    * 判断是否有对应权限
    */
   public boolean hasPermisssion() {
       for(String p : permissions)
       {
           if(!PermissionHelper.hasPermission(this, p))
           {
               return false;
           }
       }
       return true;
   }

   /*
    * We override this so that we can access the permissions variable, which no longer exists in
    * the parent class, since we can't initialize it reliably in the constructor!
    */

   public void requestPermissions(int requestCode)
   {
       PermissionHelper.requestPermissions(this, requestCode, permissions);
   }

// 添加一个辅助方法，用于清理静态监听器
private void cleanupStaticListeners(LocationManager manager) {
    // 默认清理所有监听器
    cleanupStaticGpsListeners(manager);
    cleanupStaticNetworkListeners(manager);
}
private void cleanupStaticNetworkListeners(LocationManager manager) {
    if (manager == null) return; 
    if (staticNetworkListener != null) {
        try {
            manager.removeUpdates(staticNetworkListener);
            Log.d(TAG, "已清理网络监听器");
        } catch (SecurityException e) {
            Log.e(TAG, "清理网络监听器失败: " + e.getMessage());
        }
        staticNetworkListener = null;
    }
}
private void cleanupStaticGpsListeners(LocationManager manager) {
    if (manager == null) return; 
    if (staticGpsListener != null) {
        try {
            manager.removeUpdates(staticGpsListener);
            Log.d(TAG, "已清理GPS监听器");
        } catch (SecurityException e) {
            Log.e(TAG, "清理GPS监听器失败: " + e.getMessage());
        }
        staticGpsListener = null;
    }
}

}
