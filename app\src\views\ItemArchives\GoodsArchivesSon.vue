<template>
  <div class="pages">
    <van-nav-bar
      title="商品档案"
      left-title
      left-arrow
      @click-left="goBack"
      safe-area-inset-top
    >
      <template #right>

        <van-button
          style="background-color:#fff;border:none;padding:10px;margin-right:5px;margin-top:2px;"
          type="info"
          v-if="!FormData.item_id && !isCopying"
          @click="scanBarcodeFill_click()"
        >
          <svg
            width="25px"
            height="25px"
            fill="#666"
          >
            <use xlink:href="#icon-barcodeScan"></use>
          </svg>
        </van-button>

        <div
          style="background-color:#fff;color:#000;border:none;padding:5px;margin-right:10px;margin-top:2px;"
          type="info"
          v-if="editInfoItem && FormData.item_id"
          @click="btnCopy_click()"
        >
          复制
        </div>
        <div
          style="background-color:#fff;color:#000; border:none;padding:5px;margin-right:-5px;margin-top:2px;"
          type="info"
          @click="btnSave_click(null)"
          v-if="FormData.approve_status !== 'wait approve'"
        >
          保存
        </div>

        <div
          class="icon_filter"
          v-else-if="editLog && editLog.oper_action == 'EDIT'"
          @click="showChangeLog = true"
          style="color:#444;font-style: normal;font-size: 14px;"
        >查看修改记录</div>

        <div
          class="icon_filter"
          v-else
          @click="handleApprove(true)"
          style="color:#444;font-style: normal;font-size: 14px;"
        >审核</div>

      </template>
    </van-nav-bar>
    <my-preview-image
      v-if="showImagePreviewFlag"
      @closePreviewEvent="showImagePreviewFlag = false"
      :isShow="imagePreviewObj.status"
      :index="imagePreviewObj.index"
      :images="imagePreviewObj.images"
    ></my-preview-image>
    <div class="public_box2">
      <div
        class="public_box2_t"
        id="public_box2_t_scroll"
      >
        <van-form v-model="FormData">
          <h4>基本信息</h4>
          <van-field
            :input-align="inputAlign"
            :readonly="!editInfoItem"
            v-model="FormData.item_name"
            required
            label="商品名称"
            @input="onInputItemName"
          />
          <van-field
            :input-align="inputAlign"
            :readonly="!editInfoItem"
            v-model="FormData.py_str"
            label="商品助记名"
          />
          <van-field
            :input-align="inputAlign"
            :readonly="!editInfoItem"
            v-model="FormData.item_no"
            label="商品编号"
          />
          <YjSelectTree
            ref="selectTreeRef"
            :target="'class'"
            :title="title"
            :originTreeData="originTreeData"
            :confirmColor="confirmColor"
            :rangeKey="rangeKey"
            :allowEditing="true"
            :canEdit="editInfoItem"
            :rootNode="rootNode"
            :idKey="idKey"
            :foldAll="true"
            :sonNode="sonNode"
            :parentSelectable="parentSelectable"
            :popupHeight="'85%'"
            @getRootNode="getRootNode"
            @handleConfirm="itemClassSelect"
          >
            <template #select-tree-content>
              <van-field
                :input-align="inputAlign"
                v-model="FormData.class_name"
                readonly
                required
                label="商品类别"
              />
            </template>
            <template #tree-edit-content>
              <div
                style="height: 40px;line-height: 40px;"
                v-show="editInfoItem"
                @click="getEditClass"
              >
                <van-icon
                  name="ellipsis"
                  :style="[{ color: confirmColor }]"
                  size="25"
                  style="transform: rotate(-90deg);line-height: 40px;"
                />
              </div>
            </template>
          </YjSelectTree>
          <select-one
            ref="selectOne"
            style="width:100%;"
            :label="'商品品牌'"
            :allowedit="editInfoBrand"
            :hasClearBtn="false"
            :target="'brand'"
            :formObj="FormData"
            :formFld="'item_brand'"
            :formNameFld="'brand_name'"
            @addClick="showAddBrand"
            @delClick="itemDel"
          />
          <select-one
            style="width:100%;"
            :label="'主供应商'"
            :allowedit="editInfoItem"
            :hasClearBtn="false"
            :target="'supplier'"
            :formObj="FormData"
            :formFld="'supplier_id'"
            :formNameFld="'supplier_name'"
          />
          <div class="valid-day">
            <van-field
              :readonly="!editInfoItem"
              :input-align="inputAlign"
              v-model="FormData.valid_days"
              label="保质期"
              type="number"
            />
            <van-field
              :input-align="inputAlign"
              readonly
              v-model="curValidDayType"
              placeholder="单位"
              style="width: 100px;"
              @click="showValidDayType = true"
            />
            <!-- <van-dropdown-menu z-index="999">
              <van-dropdown-item v-model="valid_day_type" :options="validDaysType" />
            </van-dropdown-menu> -->
          </div>
          <van-field
            :readonly="!editInfoItem"
            :input-align="inputAlign"
            v-model="FormData.item_spec"
            label="规格"
          />
          <van-field
            :readonly="!editInfoItem"
            :input-align="inputAlign"
            v-model="FormData.location"
            label="存放位置"
          />
          <van-field
            :readonly="!editInfoItem"
            :input-align="inputAlign"
            v-model="FormData.item_provenance"
            label="原产地"
          />
          <div class="choose_wrapper">
            <div class="choose_content">产期/批次</div>
            <div class="choose_radio">
              <van-radio-group
                v-model="FormData.batch_level"
                direction="horizontal"
                @change="handleBatchChange"
              >
                <van-radio
                  name="1"
                  @click="setBatchLevel"
                >产期</van-radio>
                <van-radio
                  name="2"
                  @click="setBatchLevel"
                >批次</van-radio>
              </van-radio-group>
            </div>
          </div>
          <van-field :readonly="true"  :input-align="inputAlign" v-model="FormData.output_tax_rate" label="销项税率" type="number" style="display:none;"/>
          <van-field :readonly="true"  :input-align="inputAlign" v-model="FormData.input_tax_rate" label="进项税率" type="number" style="display:none;"/>
          
          <div class="choose_wrapper">
            <div class="choose_content">商品状态</div>
            <div class="choose_radio">
              <van-radio-group
                v-model="FormData.status"
                direction="horizontal"
              >
                <van-radio name="1">正常</van-radio>
                <van-radio name="0">停用</van-radio>
              </van-radio-group>
            </div>
          </div>
          <div class="choose_wrapper">
            <div class="choose_content">商城上架单位</div>
            <div class="choose_radio">
              <van-checkbox-group
                v-model="mallUnitsChecked"
                direction="horizontal"
              >
                <van-checkbox name="b">大</van-checkbox>
                <van-checkbox name="m">中</van-checkbox>
                <van-checkbox name="s">小</van-checkbox>
              </van-checkbox-group>
            </div>
          </div>
          <div class="choose_wrapper">
            <div class="choose_content">商城展示价格</div>
            <div class="choose_radio">
              <van-checkbox-group
                  v-model="mallUnitsShowPriceChecked"
                  direction="horizontal"
              >
                <van-checkbox name="b">大</van-checkbox>
                <van-checkbox name="m">中</van-checkbox>
                <van-checkbox name="s">小</van-checkbox>
              </van-checkbox-group>
            </div>
          </div>
          <div class="choose_wrapper">
            <div class="choose_content">属性</div>
            <div class="choose_radio">
              <van-checkbox-group
                v-model="selectedAttributes"
                direction="horizontal"
                style="row-gap: 4px;"
              >
                <van-checkbox
                  v-for="attr in availAttributes"
                  :key="attr.attr_id"
                  :name="attr.attr_id"
                  :disabled="mum_attributes.some(item => item.attrID === attr.attr_id)"
                >
                  {{attr.attr_name}}
                </van-checkbox>
              </van-checkbox-group>
            </div>
          </div>
          <!-- 如果选择了口味，显示区分库存 -->
          <div class="choose_wrapper" v-if="selectedAttributes.length!==0">
            <div class="choose_content">区分库存</div>
            <div class="choose_radio">
              <van-radio-group
                v-model="attrInfo.distinctStock"
                direction="horizontal"
                :disabled="FormData.itemUsed||!distinctStockEditable"
              >
                <van-radio :name="true">区分</van-radio>
                <van-radio :name="false">不区分</van-radio>
              </van-radio-group>
            </div>
          </div>
          <div class="selected_attr_wrapper">
            <div
              v-for="attrId in selectedAttributes"
              :key="attrId"
              class="attr-row"
            >
              <div class="attr-header">
                <span
                  class="attr-name">{{availAttributes.find(attr => attr.attr_id === attrId)?.attr_name || ''}}</span>
                <div class="attr-values">
                  <div
                    v-for="(value, valueIndex) in mum_attributes.find(attr => attr.attrID === attrId)?.options"
                    :key="value.optID"
                    class="attr-value-tag"
                  >
                    {{ value.optName }}
                    <van-icon
                      name="cross"
                      class="delete-btn"
                      @click="handleDeleteAttributeValue(attrId, valueIndex,value.optID)"
                      v-if="new_attributes.includes(value.optID)"
                    />
                  </div>
                </div>
                <van-button
                  size="mini"
                  type="primary"
                  icon="plus"
                  round
                  color="#1989fa"
                  style="margin-top: 4px;"
                  @click="handleAddAttribute(attrId)"
                ></van-button>
              </div>
            </div>
          </div>
          <van-tabs
            v-model="tabActive"
            swipeable
            animated
            id="goods-archives-tabs"
            :swipe-threshold="1"
          >
            <van-tab title="主商品信息">
              <h4>小单位</h4>
              <van-field
                :input-align="inputAlign"
                v-model="FormData.sunit"
                readonly
                required
                label="小单位"
                :disabled="!allowChangeUnitfactor&&FormData.itemUsed == true ? true : false"
                @click="selectUnit('small')"
              />
              <van-field
                :input-align="inputAlign"
                :readonly="!editInfoItem"
                type='number'
                v-model="FormData.sweight"
                label="重量"
                @change="updateWeights"
              >
                <template #extra>
                  <span
                    v-if="FormData.sunit"
                    class="extra"
                  >kg /{{ FormData.sunit }}</span>
                  <span v-else></span>
                </template>
              </van-field>
              <van-field
                :readonly="!editInfoItem"
                :input-align="inputAlign"
                v-model="FormData.spprice"
                label="批发价"
                type="number"
                :formatter="checkInputFormatterPoint4"
                @input="inputPrice('spprice')"
              >
                <template #extra>
                  <span
                    v-if="FormData.sunit"
                    class="extra"
                  >￥/{{ FormData.sunit }}</span>
                  <span v-else></span>
                </template>
              </van-field>
              <van-field
                :readonly="!editInfoItem"
                :input-align="inputAlign"
                v-model="FormData.slprice"
                label="零售价"
                placeholder=""
                type="number"
                :formatter="checkInputFormatterPoint4"
                @input="inputPrice('slprice')"
              >
                <template #extra>
                  <span
                    v-if="FormData.sunit"
                    class="extra"
                  >￥/{{ FormData.sunit }}</span>
                  <span v-else></span>
                </template>
              </van-field>
              <van-field
                :readonly="!editInfoItem"
                :input-align="inputAlign"
                v-if="seeInPrice"
                v-model="FormData.sbprice"
                label="进价"
                placeholder=""
                type="number"
                :formatter="checkInputFormatterPoint4"
                @input="inputPrice('sbprice')"
              >
                <template #extra>
                  <span
                    v-if="FormData.sunit"
                    class="extra"
                  >￥/{{ FormData.sunit }}</span>
                  <span v-else></span>
                </template>
              </van-field>
              <van-field
                :readonly="!editInfoItem"
                :input-align="inputAlign"
                v-if="seeInPrice"
                v-model="FormData.scprice"
                label="承包价"
                placeholder=""
                type="number"
                :formatter="checkInputFormatterPoint4"
                @input="inputPrice('scprice')"
              >
                <template #extra>
                  <span
                    v-if="FormData.sunit"
                    class="extra"
                  >￥/{{ FormData.sunit }}</span>
                  <span v-else></span>
                </template>
              </van-field>
              <van-field
                :readonly="!editInfoItem"
                :input-align="inputAlign"
                v-if="seeInPrice"
                v-model="FormData.slowprice"
                label="最低售价"
                placeholder=""
                type="number"
                :formatter="checkInputFormatterPoint4"
                @input="inputPrice('slowprice')"
              >
                <template #extra>
                  <span
                    v-if="FormData.sunit"
                    class="extra"
                  >￥/{{ FormData.sunit }}</span>
                  <span v-else></span>
                </template>
              </van-field>
              <van-field
                readonly
                :input-align="inputAlign"
                v-if="seeInPrice"
                v-model="FormData.cost_price_avg"
                label="加权成本价"
                placeholder=""
                type="number"
                :formatter="checkInputFormatterPoint4"
              >
                <template #extra>
                  <span
                    v-if="FormData.cost_price_avg"
                    class="extra"
                  >￥/{{ FormData.sunit }}</span>
                  <span v-else></span>
                </template>
              </van-field>
              <div style="display:flex;">
                <van-field
                  :readonly="!editInfoItem"
                  :input-align="inputAlign"
                  :label-width="40"
                  v-model="FormData.sbarcode"
                  label="条码"
                />
                <van-button
                  style="background-color:#fff;border:none;width:60px;"
                  type="info"
                  @click="btnScanBarcode_click('s')"
                >
                  <svg
                    width="30px"
                    height="30px"
                    fill="#666"
                  >
                    <use xlink:href="#icon-barcodeScan"></use>
                  </svg>
                </van-button>
              </div>
              <van-field
                :input-align="inputAlign"
                v-model="FormData.mall_min_qty_s"
                label="商城起订量"
                placeholder=""
                type="number"
                @input="handleInput($event, 'mall_min_qty_s')"
              >
              </van-field>
              <van-field
                  :input-align="inputAlign"
                  v-model="FormData.mall_max_qty_s"
                  label="商城每单限购量"
                  placeholder=""
                  type="number"
                  @input="handleInput($event, 'mall_max_qty_s')"
              >
              </van-field>

              <h4>大单位</h4>
              <van-field
                :input-align="inputAlign"
                v-model="FormData.bunit"
                readonly
                label="大单位"
                :disabled="!allowChangeUnitfactor&&FormData.itemUsed == true&&FormData.unitrows>=2 ? true : false"
                @click="selectUnit('big')"
              />
              <van-field
                :readonly="!editInfoItem"
                :input-align="inputAlign"
                v-model="FormData.bfactor"
                type="number"
                :label="FormData.bunit ? '包装率 1 ' + FormData.bunit + ' =' : '包装率'"
                :disabled="!allowChangeUnitfactor&&FormData.itemUsed == true &&FormData.unitrows>=2 ? true : false"
                @input="inputPrice('bfactor')"
              >
                <template #right-icon>
                  <div
                    v-if="FormData.sunit"
                    class="short-extra"
                  >{{ FormData.sunit }}</div>
                </template>
              </van-field>
              <van-field
                :input-align="inputAlign"
                :readonly="!editInfoItem"
                type='number'
                v-model="FormData.bweight"
                label="重量"
              >
                <template #extra>
                  <span
                    v-if="FormData.bunit"
                    class="extra"
                  >kg /{{ FormData.bunit }}</span>
                  <span v-else></span>
                </template>
              </van-field>
              <!-- @touchstart.native.stop="showBfactor = true" -->
              <van-field
                :readonly="!editInfoItem"
                :input-align="inputAlign"
                v-model="FormData.bpprice"
                label="批发价"
                type="number"
                :formatter="checkInputFormatterPoint2"
                @input="inputPrice('bpprice')"
              >
                <template #extra>
                  <span
                    v-if="FormData.bunit"
                    class="extra"
                  >￥/{{ FormData.bunit }}</span>
                  <span v-else></span>
                </template>
              </van-field>
              <van-field
                :readonly="!editInfoItem"
                :input-align="inputAlign"
                v-model="FormData.blprice"
                label="零售价"
                type="number"
                :formatter="checkInputFormatterPoint2"
                @input="inputPrice('blprice')"
              >
                <template #extra>
                  <span
                    v-if="FormData.bunit"
                    class="extra"
                  >￥/{{ FormData.bunit }}</span>
                  <span v-else></span>
                </template>
              </van-field>
              <van-field
                :readonly="!editInfoItem"
                :input-align="inputAlign"
                v-if="seeInPrice"
                v-model="FormData.bbprice"
                label="进价"
                type="number"
                :formatter="checkInputFormatterPoint2"
                @input="inputPrice('bbprice')"
              >
                <template #extra>
                  <span
                    v-if="FormData.bunit"
                    class="extra"
                  >￥/{{ FormData.bunit }}</span>
                  <span v-else></span>
                </template>
              </van-field>
              <van-field
                :readonly="!editInfoItem"
                :input-align="inputAlign"
                v-if="seeInPrice"
                :value="toMoney(FormData.contract_price * FormData.bfactor) || ''"
                label="承包价"
                placeholder=""
                type="number"
                :formatter="checkInputFormatterPoint4"
                v-model="FormData.bcprice"
                @input="inputPrice('bcprice')"
              >
                <template #extra>
                  <span
                    v-if="FormData.bunit"
                    class="extra"
                  >￥/{{ FormData.bunit }}</span>
                  <span v-else></span>
                </template>
              </van-field>
              <van-field
                :readonly="!editInfoItem"
                :input-align="inputAlign"
                v-if="seeInPrice"
                :value="toMoney(FormData.lowest_price * FormData.bfactor) || ''"
                label="最低售价"
                placeholder=""
                type="number"
                :formatter="checkInputFormatterPoint4"
                v-model="FormData.blowprice"
                @input="inputPrice('blowprice')"
              >
                <template #extra>
                  <span
                    v-if="FormData.bunit"
                    class="extra"
                  >￥/{{ FormData.bunit }}</span>
                  <span v-else></span>
                </template>
              </van-field>
              <van-field
                readonly
                :input-align="inputAlign"
                v-if="seeInPrice"
                :value="toMoney(FormData.cost_price_avg * FormData.bfactor) || ''"
                label="加权成本价"
                placeholder=""
                type="number"
                :formatter="checkInputFormatterPoint4"
              >
                <template #extra>
                  <span
                    v-if="FormData.bunit"
                    class="extra"
                  >￥/{{ FormData.bunit }}</span>
                  <span v-else></span>
                </template>
              </van-field>
              <div style="display:flex;">
                <van-field
                  :readonly="!editInfoItem"
                  :input-align="inputAlign"
                  v-model="FormData.bbarcode"
                  :label-width="40"
                  label="条码"
                />
                <van-button
                  style="background-color:#fff;border:none;width:60px;"
                  type="info"
                  @click="btnScanBarcode_click('b')"
                >
                  <svg
                    width="30px"
                    height="30px"
                    fill="#666"
                  >
                    <use xlink:href="#icon-barcodeScan"></use>
                  </svg>
                </van-button>
              </div>
              <van-field
                :input-align="inputAlign"
                v-model="FormData.mall_min_qty_b"
                label="商城起订量"
                placeholder=""
                type="number"
                @input="handleInput($event, 'mall_min_qty_b')"
              >
              </van-field>
              <van-field
                  :input-align="inputAlign"
                  v-model="FormData.mall_max_qty_b"
                  label="商城每单限购量"
                  placeholder=""
                  type="number"
                  @input="handleInput($event, 'mall_max_qty_b')"
              >
              </van-field>
              <h4>中单位</h4>
              <van-field
                :input-align="inputAlign"
                v-model="FormData.munit"
                readonly
                label="中单位"
                :disabled="!allowChangeUnitfactor&&FormData.itemUsed == true &&FormData.unitrows>=3 ? true : false"
                @click="selectUnit('middle')"
              />
              <van-field
                :readonly="!editInfoItem"
                :input-align="inputAlign"
                v-model="FormData.mfactor"
                type="number"
                :label="FormData.munit ? '包装率 1 ' + FormData.munit + ' =' : '包装率'"
                :disabled="!allowChangeUnitfactor&&FormData.itemUsed == true &&FormData.unitrows>=3 ? true : false"
                @input="inputPrice('mfactor')"
              >
                <template #right-icon>
                  <div v-if="FormData.sunit">{{ FormData.sunit }}</div>
                </template>
              </van-field>
              <van-field
                :input-align="inputAlign"
                :readonly="!editInfoItem"
                type='number'
                v-model="FormData.mweight"
                label="重量"
              >
                <template #extra>
                  <span
                    v-if="FormData.munit"
                    class="extra"
                  >kg /{{ FormData.munit }}</span>
                  <span v-else></span>
                </template>
              </van-field>
              <van-field
                :readonly="!editInfoItem"
                :input-align="inputAlign"
                v-model="FormData.mpprice"
                label="批发价"
                type="number"
                :formatter="checkInputFormatterPoint4"
                @input="inputPrice('mpprice')"
              >
                <template #extra>
                  <span
                    v-if="FormData.munit"
                    class="extra"
                  >￥/{{ FormData.munit }}</span>
                  <span v-else></span>
                </template>
              </van-field>
              <van-field
                :readonly="!editInfoItem"
                :input-align="inputAlign"
                v-model="FormData.mlprice"
                label="零售价"
                type="number"
                :formatter="checkInputFormatterPoint4"
                @input="inputPrice('mlprice')"
              >
                <template #extra>
                  <span
                    v-if="FormData.munit"
                    class="extra"
                  >￥/{{ FormData.munit }}</span>
                  <span v-else></span>
                </template>
              </van-field>
              <van-field
                :readonly="!editInfoItem"
                :input-align="inputAlign"
                v-if="seeInPrice"
                v-model="FormData.mbprice"
                label="进价"
                type="number"
                :formatter="checkInputFormatterPoint4"
                @input="inputPrice('mbprice')"
              >
                <template #extra>
                  <span
                    v-if="FormData.munit"
                    class="extra"
                  >￥/{{ FormData.munit }}</span>
                  <span v-else></span>
                </template>
              </van-field>
              <van-field
                :readonly="!editInfoItem"
                :input-align="inputAlign"
                v-if="seeInPrice"
                :value="toMoney(FormData.contract_price * FormData.mfactor) || ''"
                label="承包价"
                placeholder=""
                type="number"
                :formatter="checkInputFormatterPoint4"
                v-model="FormData.mcprice"
                @input="inputPrice('mcprice')"
              >
                <template #extra>
                  <span
                    v-if="FormData.munit"
                    class="extra"
                  >￥/{{ FormData.munit }}</span>
                  <span v-else></span>
                </template>
              </van-field>
              <van-field
                :readonly="!editInfoItem"
                :input-align="inputAlign"
                v-if="seeInPrice"
                :value="toMoney(FormData.lowest_price * FormData.mfactor) || ''"
                label="最低售价"
                placeholder=""
                type="number"
                :formatter="checkInputFormatterPoint4"
                v-model="FormData.mlowprice"
                @input="inputPrice('mlowprice')"
              >
                <template #extra>
                  <span
                    v-if="FormData.munit"
                    class="extra"
                  >￥/{{ FormData.munit }}</span>
                  <span v-else></span>
                </template>
              </van-field>
              <van-field
                readonly
                :input-align="inputAlign"
                v-if="seeInPrice"
                :value="toMoney(FormData.cost_price_avg * FormData.mfactor) || ''"
                label="加权成本价"
                placeholder=""
                type="number"
                :formatter="checkInputFormatterPoint4"
              >
                <template #extra>
                  <span
                    v-if="FormData.munit"
                    class="extra"
                  >￥/{{ FormData.munit }}</span>
                  <span v-else></span>
                </template>
              </van-field>
              <div style="display:flex;">
                <van-field
                  :readonly="!editInfoItem"
                  :input-align="inputAlign"
                  v-model="FormData.mbarcode"
                  :label-width="40"
                  label="条码"
                />
                <van-button
                  style="background-color:#fff;border:none;width:60px;"
                  type="info"
                  @click="btnScanBarcode_click('m')"
                >
                  <svg
                    width="30px"
                    height="30px"
                    fill="#666"
                  >
                    <use xlink:href="#icon-barcodeScan"></use>
                  </svg>
                </van-button>
              </div>
              <van-field
                :input-align="inputAlign"
                v-model="FormData.mall_min_qty_m"
                label="商城起订量"
                placeholder=""
                type="number"
                @input="handleInput($event, 'mall_min_qty_m')"
              >
              </van-field>
              <van-field
                  :input-align="inputAlign"
                  v-model="FormData.mall_max_qty_m"
                  label="商城每单限购量"
                  placeholder=""
                  type="number"
                  @input="handleInput($event, 'mall_max_qty_m')"
              >
              </van-field>
              <h4>商品图片<div
                  v-if="editInfoItem"
                  style="margin-left:10px;border-bottom:1px solid;"
                  @click="getImagesByBarcode(FormData.sbarcode,FormData.item_name)"
                >搜图</div>
              </h4>
              <div
                style="display:flex;flex-flow: wrap;margin-left:30px;margin-top:10px;"
              >
                <div
                  v-for="(item, index) in images"
                  :key="index"
                >
                  <img
                    v-bind="{ src: item, id: 'image_' + index }"
                    @click="onShowImage(index)"
                    style="width:100px;height:100px;margin:3px;"
                  />
                  <div
                    class="exhibition_deli iconfont"
                    @click.stop="onDeleteItemImage(index)"
                  >
                    <van-icon name="close" />
                  </div>
                </div>
                <div id='image_add'>
                  <ul class="exhibition">
                    <li
                      @click="addItemImage()"
                      style="font-size: 30px;"
                    >
                      &#xe62e;
                    </li>
                  </ul>
                </div>
              </div>
              <!-- <h4 style="display: flex;justify-content: space-between;align-content: center;align-items: center;">
                  <div>价格方案</div>
                  <div ><van-icon name="plus" @click="tempClick"/></div>
              </h4> -->
              <!-- <div v-if="plans.length>0">
                <van-collapse v-model="activeName" accordion>
                  <van-collapse-item v-for="(plan,index) in plans"  :name="index">
                    <template #title>
                      <div>{{plan.plan_name}}</div>
                    </template>
                    <div>内容</div>
                  </van-collapse-item>
                </van-collapse>
              </div> -->
              <!-- <div v-else style="color: #969799;font-size: 0.37333rem;line-height: 1.33333rem;text-align: center;">该商品没有方案价,请添加~</div> -->
            </van-tab>
            <van-tab
              v-for="(item, index) in attrInfo.availAttrCombine"
              :title="item.optName"
              :key="item.son_options_id + '_' + index"
            >
              <AttributeInfoEntry
                :ref="'AttributeInfoEntry' + item.son_options_id"
                :id="'AttributeInfoEntry' + item.son_options_id"
                :FormData="FormData"
                :optionInfo="item"
              />
            </van-tab>
          </van-tabs>
        </van-form>
      </div>
      <van-action-sheet
        v-model="showImageSelect"
        :actions="imageSelectActions"
        cancel-text="取消"
        description="选择添加图片的方式"
        close-on-click-action
        @select="onImageWaySelected"
        style="height: auto;"
      />
      <input
        type="file"
        id="imgFile"
        accept="image/*"
        @input="getPicture($event)"
        style="display:none;"
      />
      <!-- <van-popup style="overflow: hidden!important;" v-model="showClass" position="bottom" :style="{ height: '85%', width: '100%' }">
        <class-selection @itemClassSelect="itemClassSelect"></class-selection>
      </van-popup> -->
      <!-- <van-popup style="overflow: hidden!important;" v-model="showBrand" position="bottom" :style="{ height: '85%', width: '100%' }">
        <select-brand @selectBrand="selectBrand"></select-brand>
      </van-popup> -->
      <van-popup
        style="overflow: hidden!important;"
        v-model="showUnit"
        position="bottom"
        :style="{ height: '40%', width: '100%' }"
      >
        <van-picker
          show-toolbar
          :columns="unitList"
          cancel-button-text="取消"
          @cancel="cancelUnit"
          @confirm="confirmUnit"
        />
      </van-popup>
      <van-popup
        v-model="showImageClipper"
        style="width:100%; height:100%; overflow: hidden;"
        closeable
      >
        <div
          class="before"
          style="display:none"
        ></div>
        <div class="save_button">
          <van-button
            v-if="showImageClipper"
            style="background:#444; color:white"
            @click="sureSava"
          >裁剪</van-button>
        </div>
        <div
          class="container"
          style="margin-top:25%; margin-left:10%; height:70%;"
        >
          <div class="img-container">
            <img
              :src="curr_image"
              ref="image"
              id="currImage"
              alt=""
            />
          </div>
          <div
            class="afterCropper"
            style="display:none"
          >
            <img
              :src="afterImg"
              alt=""
            />
          </div>
        </div>
      </van-popup>
    </div>

    <van-dialog
      width="320px"
      v-model="showScanDialog"
      :title="'扫描条码' + scanDialogResult"
      @confirm="confirmScanDialog"
      show-cancel-button
    >
      <van-radio-group v-model="radio">
        <div class="radio-scan-wrapper">
          <van-radio
            name="sbarcode"
            checked-color="#ee0a24"
          >小单位条码</van-radio>
          <van-radio
            name="mbarcode"
            checked-color="#ee0a24"
          >中单位条码</van-radio>
          <van-radio
            name="bbarcode"
            checked-color="#ee0a24"
          >大单位条码</van-radio>
        </div>
      </van-radio-group>
    </van-dialog>
    <van-popup
      v-model="attrNameShow"
      position="bottom"
      :style="{ height: '61%' }"
    >
      <div class="attr-wrapper">
        <div class="attr-input">
          <div
            class="attr-new"
            @click="onAttrConfim(false)"
          >新建</div>
          <van-search
            class="attr-search"
            v-model="attrNameInput"
            show-action
            placeholder="请输入"
            @clear="handleClearAttr"
            @input="onAttrSearch"
          >
            <template #action>
              <div @click="onAttrConfim(true)">{{ onAttrConfimTitle }}</div>
            </template>
          </van-search>
        </div>
        <van-picker
          :show-toolbar="false"
          :columns="attrNameFilter"
          value-key="opt_name"
          ref="GoodsArchivesSonPicker"
        />
      </div>
    </van-popup>
    <van-popup
      v-model="showAddPopup"
      round
      :style="{ padding: '20px', width: '310px', height: 'auto' }"
    >
      <add-or-edit-brand
        ref="handleBrand"
        :isNewRecord="isNewRecord"
        :brandTitle="brandTitle"
        @getAddBrand="getAddBrand"
        @hideAddBrand="hideAddBrand"
      ></add-or-edit-brand>
    </van-popup>
    <van-popup
      v-model="showValidDayType"
      position="bottom"
      :style="{ height: '61%' }"
    >
      <van-field
        v-for="(validDay,index) in validDaysType"
        :key="index"
        v-model="validDay.label"
        readonly
        @click="handleValidDayClick(validDay)"
      />
    </van-popup>
    <van-dialog
      width="320px"
      v-model="DeleteClassObj.deleteClass"
      :message="`确认删除${DeleteClassObj.class_name}吗？`"
      closeOnPopstate
      confirmButtonColor="#eaaba2"
      closeOnClickOverlay
      show-cancel-button
      show-confirm-button
      @confirm="deleteClassConfirm"
    >
    </van-dialog>
    <van-dialog
      width="320px"
      v-model="showChoosePictureDialog"
      :message="'请选择一张图片'"
      closeOnPopstate
      confirmButtonColor="#eaaba2"
      closeOnClickOverlay
      show-cancel-button
      show-confirm-button
      @confirm="choosePictureConfirm"
    >
      <div
        style='padding:8px;height:320px;display:grid;grid-template-columns:33% 33% 33%;grid-template-rows:33% 33% 33%;overflow-y:auto;'
      >
        <div
          @click='choosePicture(index)'
          style="position:relative;padding:2px;"
          v-for="item,index in searchPictures"
          :key="index"
        >
          <div
            style='position:absolute;border-radius:4px;background:#eaaba2;
          top:-2px;left:0px;
          color:#f0f0f0;width:100%;height:100%;z-index:-999'
            v-if='item.isChoose'
          ></div>
          <div
            style='position:absolute;font-size:12px;background:#eaaba2;
          padding:2px;color:#f0f0f0;border-radius:4px;'
            v-if='item.isMostMatch'
          >最匹配</div>
          <img
            :src="item.main"
            height="80"
          >
        </div>
      </div>
    </van-dialog>
    <van-popup
      @open="beforeOpenEditClass"
      @closed="closedEditClass"
      v-model="showAddOrEditClass"
      round
      :style="{ padding: '20px', width: '310px', height: 'auto' }"
    >
      <add-or-edit-goods-class
        ref="handleClass"
        :addForAll="addForAll"
        :classTitle="classTitle"
        :nowSelectedClass="nowSelectedClass"
        :isNewRecord="isNewClass"
        @saveClassNode="saveClassNode"
        @editClassNode="editClassNode"
        @closeAddOrEditClass="closeAddOrEditClass"
      >
      </add-or-edit-goods-class>
    </van-popup>
    <van-popup
      v-model="showHandleClassOptions"
      round
      :style="{ padding: '20px', width: '170px', height: 'auto' }"
    >
      <div
        v-if="nowSelectedClass.name === '全部'"
        class="popover-wrapper"
      >
        <div
          v-for="(action, popoverIndex) in allaction"
          @click="onSelect(action, true)"
          :class="popoverIndex === allaction.length - 1 ? 'popover-item noline' : 'popover-item'"
        >
          <van-icon
            style="margin-right: 10px;"
            :name="action.icon"
          />
          <div>{{ action.text }}</div>
        </div>
      </div>
      <div
        v-else
        class="popover-wrapper"
      >
        <div
          v-for="(action, popoverIndex) in actions"
          :key="'item_' + action.text"
          @click="onSelect(action, false)"
          :class="popoverIndex === actions.length - 1 ? 'popover-item noline' : 'popover-item'"
        >
          <van-icon
            style="margin-right: 10px;"
            :name="action.icon"
          />
          <div>{{ action.text }}</div>
        </div>
      </div>
    </van-popup>
    <!-- 修改记录 -->
    <van-popup
      v-model="showChangeLog"
      round
      position="bottom"
      :style="{ width: '100%', height: '80%' }"
    >
      <div class="change-log-box">
        <h4 class="title">商品档案审核</h4>
        <div
          class="change-log-items"
          v-if="diff_describe"
        >
          <span>修改内容</span>
          <template v-for="(item, key) in diff_describe">
            <div
              class="change-log-item"
              v-if="fieldLabel[key]!==void(0)"
            >
              <span
                style="margin-right:8px;width:120px;text-align: left;color:#646566;"
              >{{ fieldLabel[key]
                }}</span>
              <div
                class="change-log-value"
                v-if="key=='status'"
                style="display:flex;justify-content: space-between;width: 100%;"
              >
                <span class="newValue">{{ item.newValue=="1"?"正常":"停用" }}</span>
                <del
                  class="oldValue"
                  style="color:#aaa;"
                >{{ item.oldValue=="1"?"正常":"停用" }}</del>
              </div>
              <div
                class="change-log-value"
                v-else-if="key=='batch_level'"
                style="display:flex;justify-content: space-between;width: 100%;"
              >
                <span
                  class="newValue">{{ item.newValue=="1"?"开启产期":(item.newValue=="2"?"开启批次":"非严格") }}</span>
                <del
                  class="oldValue"
                  style="color:#aaa;"
                >{{ item.oldValue=="1"?"开启产期":(item.newValue=="2"?"开启批次":"非严格") }}</del>
              </div>
              <div
                class="change-log-value"
                v-else
                style="display:flex;justify-content: space-between;width: 100%;"
              >
                <span class="newValue">{{ item.newValue }}</span>
                <del
                  class="oldValue"
                  style="color:#aaa;"
                >{{ item.oldValue }}</del>
              </div>
            </div>
          </template>
        </div>
        <div
          class="approve-mod"
          v-if="editLog"
        >
          <!-- <button @click="showChangeLog = true" style="background-color: #07c160;color:#fff;border-radius: 5px;">查看修改记录</button> -->
          <div class="approve-btns">
            <button
              style="background-color: #ff8888;"
              @click="handleApprove(false)"
            >不通过</button>
            <button
              style="background-color: #07c160;"
              @click="handleApprove(true)"
            >通过</button>
          </div>
          <div class="approve-info">
            <span>编辑人:{{ editLog.edit_name }}</span>
            <span>{{ editLog.happen_time }}</span>
          </div>
          <div class="approve-brief">
            <van-field
              style="border:1px solid #ebedf0;"
              v-model="approve_brief"
              rows="2"
              autosize
              type="textarea"
              maxlength="50"
              placeholder="请输入审核备注"
              show-word-limit
            />
          </div>
        </div>
      </div>
    </van-popup>
  </div>
</template>

<script>
import {
  NavBar,
  Form,
  Field,
  Popup,
  Toast,
  Picker,
  Button,
  Dialog,
  RadioGroup,
  Radio,
  Icon, closeToast, showLoadingToast,
  ImagePreview,
  ActionSheet,
  Tab, Tabs, Search,
  DropdownItem,
  CellGroup,
  DropdownMenu,
  Collapse,
  CollapseItem,
  Checkbox,
  CheckboxGroup
} from 'vant'
import ClassSelection from "../components/ItemClass"
import SelectBrand from '../components/SelectBrand'
import { SaveItemInfo, GetUnitnoList, GetItemInfoByBarCode, CreateAttrName, DeleteBrand, DeleteClass, GetGoodsItemStock, GetPicturesByItemWorld, GetItemInfoDetail } from '../../api/api'
import pinyinCode from '../../util/PinyinCode.js'
import globalVars from "../../static/global-vars";
import AttributeEditable from './AttributeEditable'
import AttributeInfoEntry from "./AttributeInfoEntry";
import AddOrEditBrand from '../components/AddOrEditBrand.vue'
import AddOrEditGoodsClass from '../components/AddOrEditGoodsClass.vue'
import Vue from "vue";
import Cropper from "cropperjs";
import "cropperjs/dist/cropper.css";
import ImageUtil from '../service/Image'
import TakePhoto from '../service/TakePhoto'
import MyPreviewImage from '../VisitRecord/MyPreviewImage.vue'
import YjSelectTree from "../components/yjTree/YjSelectTree.vue";
import SelectOne from "../components/SelectOne.vue";
//此行代码会导致chunk-vendors.js编译时出现...等es6特性的代码，在android 6这样的低端机不能运行  import { start } from '../../../../platforms/android/cordova/lib/Adb'  
export default {
  name: "GoodsArchivesSon",
  data () {
    return {
      mallUnitsChecked: ['b', 'm', 's'],
      mallUnitsShowPriceChecked: ['b', 'm', 's'],
      isCopying: false,
      myCropper: null,
      curr_image: '',
      afterImg: '',
      showImageSelect: false,
      validDaysType: [
        { value: 'd', label: '天' },
        { value: 'm', label: '月' },
        { value: 'y', label: '年' },
      ],
      curValidDayType: '天',
      imageSelectActions: [{ name: '拍照' }, { name: '相册' }],
      FormData: {
        item_id: '',
        item_name: '',
        py_str: '',
        item_no: '',
        other_class: '',
        item_class: '',
        class_name: '',
        item_brand: '',
        brand_name: '',
        supplier_id: '',
        supplier_name: '',
        item_provenance: '',
        sunit: '',
        sweight: '',
        spprice: '',
        sbprice: '',
        slprice: '',
        scprice: '',
        bcprice: '',
        mcprice: '',
        sbarcode: '',
        bunit: '',
        bweight: '',
        bfactor: '',
        bpprice: '',
        bbprice: '',
        blprice: '',
        bcPrice: '',
        calcByPrice: '',
        bbarcode: '',
        munit: '',
        mweight: '',
        mfactor: '',
        mpprice: '',
        mbprice: '',
        mlprice: '',
        mcPrice: '',
        mbarcode: '',
        item_image: '',
        batch_level: '',
        status: '1',
        valid_day_type: '',
        mall_units: '',
        mall_units_show_price: '',
      },
      attrInfo: {
        distinctStock: false,
        itemMumAttributes: [],
        availAttrCombine: [],
        deleteItemList: []
      },
      selectedAttributes: [], // 存储选中的属性ID
      oldFormData: null,//用于判断审核人有没有直接更改
      editLog: null,
      diff_describe: null,
      showChangeLog: false,
      approve_brief: '',
      msgId: "",
      originFormDataJson: '',
      barcode: "",
      showClass: false,
      showBrand: false,
      showUnit: false,
      unitList: [],
      isUnit: '',
      buy_price: '',
      valid_days: '',
      item_spec: '',
      location: '',
      showScanDialog: false,
      scanDialogResult: '',
      radio: '',
      images: [],
      attrOptions: {},
      availAttributes: [],
      plans: [],
      mum_attributes: [],
      new_attributes: [],
      editMumAttributes: {},
      tabActive: 0,
      attrNameInput: '',
      attrNameInputTime: '',
      attrNameShow: false,
      attrNameAttrId: '',
      attrNameFilter: [],
      onAttrConfimTitle: '选择',
      showChoosePictureDialog: false,
      showImageClipper: false,
      showImagePreviewFlag: false,
      existBackListener: false,
      inputAlign: 'right',
      imagePreviewObj: {
        status: false,
        images: [],
        index: 0
      },
      showAddPopup: false,
      isNewRecord: false,
      brandTitle: '',
      title: '类别选择',
      rangeKey: 'name',
      idKey: 'id',
      sonNode: 'subNodes',
      asPage: true,
      parentSelectable: true,
      foldAll: false,
      confirmColor: '#e3a2a2',
      rootNode: {},
      //编辑类别相关
      originTreeData: [],
      showAddOrEditClass: false,
      DeleteClassObj: {
        deleteClass: false,
        class_name: '',
        class_id: '',
        operKey: ''
      },
      allowChangeUnitfactor: false,//方便直接判断
      addForAll: false,
      isNewClass: false,
      classTitle: '',
      showHandleClassOptions: false,
      nowSelectedClass: {},
      allaction: [
        { text: '添加下级类', icon: 'add-o' }
      ],
      actions: [
        { text: '编辑类', icon: 'records' },
        { text: '添加下级类', icon: 'add-o' },
        { text: '添加同级类', icon: 'add-o' },
        { text: '删除类', icon: 'delete-o' },
      ],
      showValidDayType: false,
      activeName: -1,
      checkFlag: false,
      searchPictures: [],
      fieldLabel: {
        "item_name": "商品名称",
        "item_alias": "别名",
        "item_no": "编号",
        "py_str": "助记名",
        "location": "存放位置",
        "class_name": "类别",
        "brand_name": "品牌",
        "item_order_index": "顺序号",
        "valid_days": "保质期保质期",
        "valid_day_type": "保质期类型",
        "item_spec": "规格",
        "item_provenance": "原产地",
        "status": "状态",
        "supplier_id": "主供应商",
        "manufactor_id": "生产商",
        "itemUsed": "已使用",
        // "mum_attributes":"",
        // "item_images":"",
        "batch_level": "产期/批次",
        "approve_status": "审核状态",
        // "price_plan_item":"",
        "s_unit_no": "单位",
        "s_unit_factor": "包装率",
        // "s_unit_type": "包装类型",
        "s_wholesale_price": "批发价",
        "s_retail_price": "零售价",
        "s_buy_price": "进价",
        "s_profit_rate": "利润率",
        "s_cost_price_spec": "预设成本",
        "s_lowest_price": "最低售价",
        "s_contract_price": "承包价",
        "s_cost_price_avg": "加权价",
        "s_cost_price_recent": "最近平均进价",
        "s_barcode": "条码",
        "s_weight": "重量kg",
        "s_volume": "体积m³",
        "m_unit_no": "单位(中)",
        "m_unit_factor": "包装率(中)",
        "m_barcode": "条码(中)",
        "m_weight": "重量kg(中)",
        "m_volume": "体积m³(中)",
        "b_unit_no": "单位(大)",
        "b_unit_factor": "包装率(大)",
        "b_barcode": "条码(大)",
        "b_weight": "重量kg(大)",
        "b_volume": "体积m³(大)",
        "mall_min_qty_s": "商城起订量(小)",
        "mall_min_qty_m": "商城起订量(中)",
        "mall_min_qty_b": "商城起订量(大)",
        "mall_max_qty_s": "商城每单限购量(小)",
        "mall_max_qty_m": "商城每单限购量(中)",
        "mall_max_qty_b": "商城每单限购量(大)",
      }
    }
  },
  computed: {
    seeInPrice () {
      return hasRight('delicacy.seeInPrice.value')
    },
    editInfoItem () {
      return hasRight('info.infoItem.edit')
    },
    editInfoBrand () {
      return hasRight('info.infoBrand.edit')
    },
    approveAuthority () {
      let auth = getRightValue('info.infoItem.approve')
      if (auth == "false") {
        return false
      } else {
        return true
      }
    },
    distinctStockEditable () {
      debugger
      //return false
      if (!this.availAttributes || !this.selectedAttributes) {
        return true;
      }

      // 获取所有选中的属性
      const selectedAttrs = this.availAttributes.filter(attr =>
        this.selectedAttributes.includes(attr.attr_id)
      );

      // 查找是否有不可编辑的属性
      const nonEditableAttr = selectedAttrs.find(attr =>
        attr.distinct_stock_editable === false
      );

      if (nonEditableAttr) {
        // 如果找到不可编辑的属性，将 distinctStock 设置为该属性的 distinct_stock 值
        this.$nextTick(() => {
          this.attrInfo.distinctStock = nonEditableAttr.distinct_stock;
        });
        return false;
      }

      return true;
    }
  },
  async mounted () {
    this.$store.commit('itemArchivePageOption', {
      type: 'see',
      formData: null
    })
    const itemID = this.$route.query.itemID
    this.msgId = this.$route.query.msgId ?? ''
    if (itemID) {
      const res = await GetItemInfoDetail({ itemID })
      this.FormData = res.data // default display itemInfo
      this.FormData.item_id = itemID
      this.FormData.itemUsed = res.itemUsed
      this.FormData.attrOptions = res.attrOptions
      this.FormData.availAttributes = res.availAttributes
      if (this.FormData.mall_units) {
        this.mallUnitsChecked = this.FormData.mall_units.split(',').map(unit => unit.trim());
      }
      if (this.FormData.mall_units_show_price) {
        this.mallUnitsShowPriceChecked = this.FormData.mall_units_show_price.split(',').map(unit => unit.trim());
      }
      console.log('formData', this.FormData)

      this.mum_attributes = JSON.parse(this.FormData.mum_attributes || '[]')
      this.attrInfo.itemMumAttributes = JSON.parse(this.FormData.mum_attributes || '[]')
      // 设置选中的属性
      if (this.mum_attributes && this.mum_attributes.length > 0) {
        this.selectedAttributes = this.mum_attributes.map(attr => attr.attrID)
        this.attrInfo.distinctStock = this.mum_attributes.some(item => item.distinctStock === true)
      }
      this.attrInfo.availAttrCombine = JSON.parse(this.FormData.avail_attr_combine || '[]')

      this.oldFormData = JSON.parse(JSON.stringify(res))
      this.editLog = res.editLog
      this.diff_describe = res.editLog && res.editLog.diff_describe ? JSON.parse(res.editLog.diff_describe) : null
    }
    else {
      this.FormData = this.$route.query
    }
    // this.FormData = this.$route.query.goodsDetailData
    if (this.FormData.mum_attributes === '[]') this.FormData.mum_attributes = ''
    if (this.FormData.status !== '0') {
      this.FormData.status = '1'
    }
    var operInfo = this.getOperInfo()
    if (window.getRightValue('delicacy.changeUnitfactorAfterNewSheet.value') == "allow") this.allowChangeUnitfactor = true
    this.FormData.valid_day_type = this.FormData.valid_day_type ? this.FormData.valid_day_type : operInfo.setting?.validDayType || ''
    // 属性
    this.attrOptions = this.FormData.attrOptions
    this.plans = this.FormData.plans
    this.availAttributes = this.FormData.availAttributes

    if (!this.FormData.py_str) {
      this.FormData.py_str = pinyinCode(this.FormData.sup_name)
    }
    if (!this.FormData.item_class && this.$store.state.ItemClassObj !== null && this.$store.state.ItemClassObj.name !== "全部") {
      this.FormData.item_class = this.$store.state.ItemClassObj.class_id,
        this.FormData.class_name = this.$store.state.ItemClassObj.class_name,
        this.FormData.other_class = this.$store.state.ItemClassObj.class_path
    }
    switch (this.FormData.valid_day_type) {
      case 'd':
        this.curValidDayType = '天';
        break;
      case 'm':
        this.curValidDayType = '月';
        break;
      case 'y':
        this.curValidDayType = '年';
        break;
      default:
        this.curValidDayType = '';
    }
    let that = this
    window.sayCode = function (result) {
      that.pageSayCode(result)
    }
    this.parseItemImages()
    // 修复input在安卓不生效需要添加 capture="camera" ，但这会导致iOS只能拍照
    // if(isiOS){
    //   document.getElementById("imgFile").removeAttribute("capture","camera");
    // }
    const queryBarcode = this.$route.query.queryBarcode
    if (queryBarcode) {
      const res = await GetItemInfoByBarCode({ barcode: queryBarcode })
      if (res.result && res.result != "OK") {
        this.$toast(res.msg)
        return
      }
      let source = res.source
      let productInfo = res.productInfo
      if (!source) {
        this.$toast("商品库暂无该条码信息，请手动输入商品信息")
        return
      }
      console.log(productInfo)
      Dialog.confirm({
        title: "匹配",
        message: `匹配到商品 ${productInfo.item_name}，是否添加`,
        width: '320px'

      }).then((res) => {
        this.fillBarcodeItemInfo(source, productInfo)
        this.parseItemImages()
        this.$forceUpdate()
      })
      return
    }
    this.new_attributes = []
    this.originFormDataJson = JSON.stringify(this.FormData)
  },
  activated () {
    let that = this
    window.sayCode = function (result) {
      that.pageSayCode(result)
    }
  },
  components: {
    AttributeInfoEntry,
    "van-nav-bar": NavBar,
    "van-button": Button,
    "van-form": Form,
    "van-field": Field,
    "van-popup": Popup,
    "van-picker": Picker,
    "van-icon": Icon,
    "class-selection": ClassSelection,
    "select-brand": SelectBrand,
    "van-radio-group": RadioGroup,
    "van-radio": Radio,
    "van-action-sheet": ActionSheet,
    "van-tabs": Tabs,
    "van-tab": Tab,
    "van-search": Search,
    "van-dropdown-item": DropdownItem,
    "van-cell-group": CellGroup,
    "van-dropdown-menu": DropdownMenu,
    [Dialog.Component.name]: Dialog.Component,
    AttributeEditable,
    'my-preview-image': MyPreviewImage,
    "select-one": SelectOne,
    YjSelectTree,
    AddOrEditBrand,
    AddOrEditGoodsClass,
    "van-collapse": Collapse,
    "van-collapse-item": CollapseItem,
    "van-checkbox": Checkbox,
    "van-checkbox-group": CheckboxGroup,
  },
  methods: {
    tempClick () {

    },
    //打开弹窗之前open
    beforeOpenEditClass () {
      if (!this.isNewClass) {
        this.$nextTick(() => {
          this.$refs.handleClass.editClass(this.isNewClass)
        })
      }
      else {
        this.$nextTick(() => {
          this.$refs.handleClass.addClass(this.isNewClass)
        })
      }
    },
    closedEditClass () {
      this.$nextTick(() => {
        this.$refs.handleClass.clearClassEdit()
      })
    },
    getEditClass () {
      this.showHandleClassOptions = true
      this.nowSelectedClass = this.$refs.selectTreeRef.$refs.tree.getChecked()[0]
    },
    onSelect (action, flag) {
      console.log("action", action)
      this.showHandleClassOptions = false
      this.addForAll = flag
      if (action.text === '编辑类') {
        this.classEdit()
      } else if (action.text === '添加同级类') {
        this.classAdd("same level")
      } else if (action.text === '添加下级类') {
        this.classAdd("lower level")
      } else if (action.text === "删除类") {
        this.classDelete(this.nowSelectedClass.id, this.nowSelectedClass.name)
      }
      // console.log("item",item)
    },
    classEdit () {
      this.isNewClass = false
      this.classTitle = '编辑类别'
      this.showAddOrEditClass = true
    },
    classAdd (type) {
      if (type === "same level") {
        this.classTitle = '添加同级类别'
      } else {
        this.classTitle = '添加下级类别'
      }
      this.isNewClass = true
      this.showAddOrEditClass = true
    },
    classDelete (id, name) {
      this.DeleteClassObj.deleteClass = !this.DeleteClassObj.deleteClass,
        this.DeleteClassObj.operKey = this.$store.state.operKey,
        this.DeleteClassObj.class_id = id.toString(),
        this.DeleteClassObj.class_name = name
    },
    async deleteClassConfirm () {
      await DeleteClass({
        class_id: this.DeleteClassObj.class_id,
        operKey: this.DeleteClassObj.operKey
      }).then(async res => {
        console.log(res)
        if (res.result === 'OK') {
          this.originTreeData = this.$refs.selectTreeRef.$refs.tree.getTreeList()
          this.$refs.selectTreeRef.getListData((_that) => {
            _that.$refs.tree.handleAddTreeNode()
          })
        } else {
          console.log("sssss")
          await Toast.fail(res.result)
        }
      })
    },
    saveClassNode (classObj) {
      // console.log("wokankan",classObj)
      this.originTreeData = this.$refs.selectTreeRef.$refs.tree.getTreeList()
      this.$refs.selectTreeRef.getListData((_that) => {
        _that.$refs.tree.handleAddTreeNode(classObj.class_id)
      })
      // console.log("saveRegionNode",regionObj)
    },
    editClassNode (classObj) {
      // console.log("wokankan",classObj)
      this.originTreeData = this.$refs.selectTreeRef.$refs.tree.getTreeList()
      // console.log("编辑节点",regionObj)
      this.$refs.selectTreeRef.getListData((_that) => {
        _that.$refs.tree.handleAddTreeNode(classObj.class_id)
      })
    },
    closeAddOrEditClass () {
      this.showAddOrEditClass = false
    },
    showAddBrand (type, item) {
      this.showAddPopup = true
      // console.log("编辑品牌",item)
      if (type == 'add') {
        this.isNewRecord = true
        this.brandTitle = '添加品牌'
      } else {
        this.isNewRecord = false
        this.brandTitle = '编辑品牌'
        this.$nextTick(() => {
          this.$refs.handleBrand.editBrand(item)
        })
      }
    },
    hideAddBrand () {
      this.showAddPopup = false
    },
    getAddBrand (brand) {
      this.$refs.selectOne.startNewPage()
      if (this.isNewRecord) {
        Toast.success("添加成功")
      } else {
        Toast.success("修改成功")
      }
    },
    itemDel (item) {
      console.log("删除的", item)
      Dialog.confirm({
        title: item.name,
        message: "确认删除该品牌吗？",
        width: '320px'
      })
        .then(() => {
          let params = {
            gridID: "gridItems",
            operKey: this.$store.state.operKey,
            rowIDs: item.id
          }
          console.log("删除品牌", params)
          DeleteBrand(params).then(res => {
            console.log(res)
            if (res.result === 'OK') {
              this.$refs.selectOne.startNewPage()
              Toast.success("删除成功");
            } else if (res.result === "Error") {
              Toast.fail(res.msg);
            }
          }).catch(err => {
            console.log(err)
          })
        })
    },
    getRootNode (node) {
      this.rootNode = node
    },
    onScanCancelled () {
      var that = this
      document.addEventListener("backbutton", onBackButtonUp, false);
      var backButtonIsUp = false
      setTimeout(function () {
        document.removeEventListener("backbutton", onBackButtonUp, false)
      }, 2000)
      function onBackButtonUp () {
        if (backButtonIsUp) {
          that.$toast("慢点~")
        }
        backButtonIsUp = true
        return false;
      }
    },
    getImgBase64 (url) {
      return new Promise((resolve, reject) => {
        var base64 = "";
        var img = new Image();
        img.src = url;
        img.setAttribute("crossOrigin", 'Anonymous')

        img.onload = function () {
          base64 = image2Base64(img);
          resolve(base64);
        }
        function image2Base64 (img) {
          var canvas = document.createElement("canvas");
          canvas.width = img.width;
          canvas.height = img.height;
          var ctx = canvas.getContext("2d");
          ctx.drawImage(img, 0, 0, img.width, img.height);
          var dataURL = canvas.toDataURL("image/png");
          return dataURL;
        }
      })

    },
    goBack () {
      if (!this.canEdit) {
        myGoBack(this.$router)
        return
      }
      const curFormDataJson = JSON.stringify(this.FormData)
      if (this.originFormDataJson === curFormDataJson) {
        myGoBack(this.$router)
        return
      }
      Dialog.confirm({
        message: "尚未保存，确定退出吗",
        confirmButtonText: "确定退出",
        cancelButtonText: "继续编辑",
        width: '320px'
      }).then(() => {
        myGoBack(this.$router)
      })
        .catch(() => {

        });
    },
    async parseItemImages () {
      // load images
      this.images = []
      const pref = globalVars.obs_server_uri + "/"
      let item_images_inited = this.FormData.item_images;
      if (item_images_inited != '' && item_images_inited != undefined) {
        try {
          let i_images_inited = JSON.parse(item_images_inited)
          console.log("item_images inited. main:" + i_images_inited.main + ", tiny:" + i_images_inited.tiny + ", other(length " + i_images_inited.other?.length + "):" + i_images_inited.other);
          if (i_images_inited.main) {
            let base64mainImage = ''
            if (i_images_inited.main.startsWith("http")) {
              base64mainImage = await this.getImgBase64(i_images_inited.main)
            } else {
              base64mainImage = await this.getImgBase64(pref + i_images_inited.main)
            }
            this.images.push(base64mainImage)
          }
          if (i_images_inited.other != null && i_images_inited.other.length > 0) {
            for (let i = 0; i < i_images_inited.other.length; i++) {
              let base64OtherImage = await this.getImgBase64(pref + i_images_inited.other[i])
              this.images.push(base64OtherImage)
            }
          }
          console.log("images:" + this.images)
        } catch (e) {
          console.log(e)
        }
      }
    },
    handleInput (value, field) {
      // 正整数的正则表达式，匹配 1, 2, 3, 4 ... 不允许 0 或负数
      const regex = /^[1-9]\d*$/;
      if (!regex.test(value)) {
        // 如果输入不符合正整数，清空输入框
        this.FormData[field] = '';
      }
    },
    cropInit () {
      /// crop document: https://blog.csdn.net/achejq/article/details/93240104
      let image = this.$refs.image;
      console.log(image)
      if (this.myCropper) {
        this.myCropper.replace(this.curr_image); // 必须使用replace来替代销毁重启,否则会有BUG
      }
      else {
        this.myCropper = new Cropper(image, {
          viewMode: 1,
          dragMode: 'move', // 只可以移动初始裁剪框
          initialAspectRatio: 1,
          checkCrossOrigin: false,
          //aspectRatio:1, // 使用默认值可以随意调整宽高比
          //preview:'.before',
          background: false,
          autoCropArea: 1, // 初始裁剪面积大小与图片的比例
          zoomOnWheel: false,
          toggleDragModeOnDblclick: false, // 禁止双击图片切换裁剪模式
        })
      }
    },
    async sureSava () {
      this.showImageClipper = false;
      this.afterImg = this.myCropper.getCroppedCanvas({
        fillColor: '#000',
        imageSmoothingQuality: 'high'
      }).toDataURL('image/jpeg');
      const compressImage = await ImageUtil.compress(this.afterImg)
      this.images.push(compressImage)
      this.$forceUpdate();
    },
    onShowImage (keys) {
      this.imagePreviewObj = {
        index: keys,
        images: this.images,
      }
      this.showImagePreviewFlag = true
      // ImagePreview({
      //   images: this.images,
      //   startPosition: keys,
      //   closeable: true,
      // });
    },
    // registerLongPress() {
    //   let that_ = this;
    //   // Get a reference to an element
    //   var img = document.querySelector("#imgContainer");
    //   // Create a manager to manager the element
    //   var manager = new Hammer.Manager(img);
    //   // Create a recognizer
    //   var Press = new Hammer.Press({
    //     time: 1000,
    //   });
    //   // Add the recognizer to the manager
    //   manager.add(Press);
    //   // Subscribe to desired event
    //   manager.on("press", function () {
    //     Dialog.confirm({
    //       message: "是否保存图片？",
    //     })
    //       .then(() => {
    //         that_.saveToPhone();
    //       })
    //       .catch(() => { });
    //   });
    // },
    onDeleteItemImage (index) {
      this.onDeleteImg(index, this.images);
    },
    onDeleteImg (keys, imgList) {
      console.log(imgList);
      Dialog.confirm({
        title: "删除图片",
        message: "确认删除当前图片？",
        width: '320px'
      }).then(() => {
        imgList.splice(keys, 1);
      });
    },
    addItemImage () {
      console.log("adding image... isiOS?" + isiOS);
      if (this.images.length >= 10) {
        Toast('一个商品最多十张图片')
        return
      }
      this.showImageSelect = true
      //if(!isiOS){
      //   this.showImageSelect = true
      // }
      // else{
      //   let fileDom = document.querySelector("#imgFile");
      //   fileDom.click();
      // }
    },
    async onImageWaySelected (item) {
      let image = ''
      switch (item.name) {
        case '拍照':

          image = await TakePhoto.takePhotos(Camera.PictureSourceType.CAMERA)
          console.log("拍照", image)
          break;
        case '相册':

          // let fileDom = document.querySelector("#imgFile");
          // fileDom.click();
          image = await TakePhoto.takePhotos(Camera.PictureSourceType.PHOTOLIBRARY)
          break;
      }
      this.processImageAndShow(image);
    },
    async processImageAndShow (image) {
      this.curr_image = await ImageUtil.compress(image);
      this.showImageClipper = true;
      setTimeout(() => {
        this.cropInit();

      }, 200);
      this.$forceUpdate();

      // let img = document.getElementById('dynamicImage')
      // let opt = {
      //   resultObj : img,
      //   aspectRatio : 1
      // }
      // this.selfClip(image, opt, This);
      // let img = document.getElementById('dynamicImage')
      // let e = {
      //   srcElement: document.getElementById('dynamicInput'),
      //   target: {
      //     files: [image]
      //   },
      //   DataTransfer: {
      //     files: [image]
      //   }
      // }
      // this.clip(e , {
      //   resultObj : img,
      //   aspectRatio : 1
      // })
      // const compressImage = document.getElementById('dynamicImage').src
      // const compressImage = await this.compressImage(image, w);
      // This.images.push(compressImage)
    },
    getPicture (e) {
      //let src = window.URL.createObjectURL(e.target.files[0]);
      //this.uploadImg.push(src);

      let that = this;
      //将图片文件转化成base64格式图片
      var reader = new FileReader();
      reader.readAsDataURL(e.target.files[0]);
      //reader.readAsDataURL(e.files[0]);

      reader.onload = (e) => {
        let bes = reader.result.split(",")[1];
        let img_b64 = "data:image/png;base64," + bes;
        //console.log(bes);
        that.processImageAndShow(img_b64, that)
        //this.images.push(img_b64);
      };

      // console.log(reader.readAsDataURL(e.target.files[0]));
    },

    async scanBarcodeFill_click () {
      if (this.FormData.item_id) {
        this.$toast("该商品已保存，不可扫码")
        return
      }

      try {
        const result = await this.scanBarcodeNew({
          unit_type: 'goods_archives_fill'
        })

        if (!result.code) {
          return
        }

        const res = await GetItemInfoByBarCode({ barCode: result.code })
        let source = res.source
        let productInfo = res.productInfo
        if (!source) {
          this.$toast("暂无商品信息，请手动添加")
          return
        }

        Dialog.confirm({
          title: "匹配",
          message: `从商品库匹配到商品 ${productInfo.item_name}，是否添加？`,
          width: '320px'
        }).then(async (res) => {
          this.fillBarcodeItemInfo(source, productInfo)
          await this.parseItemImages()
          this.$forceUpdate()
        })
      } catch (error) {
        console.error('扫码失败:', error)
        this.$toast('扫码失败，请重试')
      }
    },
    async getImagesByItemName (itemName) {
      console.log(itemName)

      if (!itemName) {
        this.$toast("商品名称不能为空")
        return
      }
      Toast.loading({
        message: '加载中...',
        forbidClick: true,
        loadingType: 'spinner',
      })
      const res = await GetPicturesByItemWorld({ itemName: itemName })
      Toast.clear()
      if (res.data.length != 0) {
        this.showChoosePictureDialog = true
        this.searchPictures = res.data.map(obj => {
          obj.main = JSON.parse(obj.item_images).main
          obj.isChoose = false
          obj.isMostMatch = false
          return obj
        })
        this.searchPictures[0].isMostMatch = true
        console.log(this.searchPictures)
      } else {
        Toast.fail({
          message: "该商品暂无图片,请手动添加"
        })
      }
      //this.FormData.item_images = res.productInfo.item_images
      //await this.parseItemImages()

    },
    choosePicture (index) {
      this.choosePictureIndex = index
      this.searchPictures = this.searchPictures.map(p => {
        p.isChoose = false
        return p
      })
      this.searchPictures[index].isChoose = true
    },
    async choosePictureConfirm () {
      this.FormData.item_images = this.searchPictures[this.choosePictureIndex].item_images
      await this.parseItemImages()
    },
    async getImagesByBarcode (sbarcode, itemName) {
      console.log(sbarcode)

      if (!sbarcode) {
        this.$toast("小单位条码不能为空")
        return
      }
      Toast.loading({
        message: '加载中...',
        forbidClick: true,
        loadingType: 'spinner',
      })

      const res = await GetItemInfoByBarCode({ barCode: sbarcode })
      Toast.clear()
      this.FormData.item_images = res.productInfo.item_images
      await this.parseItemImages()
      console.log(this.images)
      if (this.images.length == 0 && itemName) {
        Toast.loading({
          message: "该商品暂无图片,正在为您搜索商品库..."
        })
        await this.getImagesByItemName(itemName)


      }

    },
    fillBarcodeItemInfo (source, productInfo) {
      if (source === 'self') {
        this.FormData.item_name = productInfo.item_name
        this.FormData.bunit = productInfo.bunit
        this.FormData.munit = productInfo.munit
        this.FormData.sunit = productInfo.sunit

        this.FormData.bfactor = productInfo.bfactor
        this.FormData.mfactor = productInfo.mfactor
        this.FormData.sfactor = productInfo.sfactor

        this.FormData.bbarcode = productInfo.bbarcode
        this.FormData.mbarcode = productInfo.mbarcode
        this.FormData.sbarcode = productInfo.sbarcode

        this.FormData.item_images = productInfo.item_images
      }
      else if (source === 'standard') {
        this.FormData.item_name = productInfo.item_name
        this.FormData.sunit = productInfo.unit
        this.FormData.sbarcode = productInfo.barcode
        this.FormData.py_str = pinyinCode(productInfo.item_name)
      }
    },
    async btnScanBarcode_click (unit_type) {
      try {
        const result = await this.scanBarcodeNew({
          unit_type: `goods_archives_barcode_${unit_type}`
        })

        if (result.code == undefined || result.code == '' || result.code == null) {
          return
        }

        if (unit_type == 'b')
          this.FormData.bbarcode = result.code
        else if (unit_type == 'm')
          this.FormData.mbarcode = result.code
        else if (unit_type == 's')
          this.FormData.sbarcode = result.code
        this.$forceUpdate()
      } catch (error) {
        console.error('扫码失败:', error)
        this.$toast('扫码失败，请重试')
      }
    },
    onInputItemName () {
      this.FormData.py_str = pinyinCode(this.FormData.item_name)
    },
    itemClassSelect (objs) {
      if (objs.length > 0) {
        if (objs[0].id === this.rootNode.id) {
          Toast.fail(`${this.rootNode.name}片区不可选`)
          return
        }
        this.FormData.item_class = (objs[0].id).toString()
        this.FormData.class_name = objs[0].name
        this.FormData.other_class = objs[0].path
      } else {
        this.FormData.item_class = ''
        this.FormData.class_name = ''
        this.FormData.other_class = ''
      }
      this.$refs.selectTreeRef.handleCancel()
      // if (obj.itemObjs !== "") {
      //   this.FormData.item_class = obj.itemObjs.ids
      //   this.FormData.class_name = obj.itemObjs.titles
      //   this.FormData.other_class = obj.path
      // } else {
      //   this.FormData.item_class = ''
      //   this.FormData.class_name = ''
      //   this.FormData.other_class = ''
      // }
    },
    selectBrand (obj) {
      this.showBrand = obj.isBrandShow
      this.FormData.item_brand = obj.brand_id
      this.FormData.brand_name = obj.brand_name
    },
    selectUnit (obj) {

      if (!this.allowChangeUnitfactor && this.FormData.itemUsed == true) {
        var operateRow = 1
        if (obj == 'big') { operateRow = 2 }//如果为想操作大单位商品,则比较operateRow是否大于等于2
        else if (obj == 'middle') { operateRow = 3 }//如果为想操作中单位商品,则比较operateRow是否大于等于3
        if (operateRow == 1 || (operateRow == 2 && this.FormData.unitrows >= 2) || (operateRow == 3 && this.FormData.unitrows >= 3)) {
          Toast("已开单商品无法修改单位和包装率")
          return
        }
      }
      this.showUnit = true
      this.isUnit = obj
      let params = {}
      if (this.unitList.length == 0) {
        GetUnitnoList(params).then(res => {
          if (res.result === 'OK') {
            res.data.forEach(item => {
              this.unitList.push(item.unit_no)
            })
          }
        })
      }
    },
    updateWeights () {
      if (isNaN(this.FormData.sweight)) {
        Toast("重量请填写数字")
      } else {
        if (this.FormData.munit && !this.FormData.mweight) this.FormData.mweight = parseFloat((Number(this.FormData.sweight) * Number(this.FormData.mfactor)).toFixed(3)) || ''
        if (this.FormData.bunit && !this.FormData.bweight) this.FormData.bweight = parseFloat((Number(this.FormData.sweight) * Number(this.FormData.bfactor)).toFixed(3)) || ''
      }

    },
    cancelUnit () {
      this.showUnit = false
      if (this.isUnit === 'small') {
        this.FormData.sunit = ''
        this.FormData.sweight = ''
        this.FormData.spprice = ''
        this.FormData.slprice = ''
        this.FormData.sbprice = ''
        this.FormData.scprice = ''
        this.FormData.sbarcode = ''
      } else if (this.isUnit === 'big') {
        this.FormData.bunit = ''
        this.FormData.bweight = ''
        this.FormData.bfactor = ''
        this.FormData.bpprice = ''
        this.FormData.blprice = ''
        this.FormData.bbprice = ''
        this.FormData.bcprice = ''
        this.FormData.bbarcode = ''
      } else if (this.isUnit === 'middle') {
        this.FormData.munit = ''
        this.FormData.mweight = ''
        this.FormData.mfactor = ''
        this.FormData.mpprice = ''
        this.FormData.mlprice = ''
        this.FormData.mbprice = ''
        this.FormData.mcprice = ''
        this.FormData.mbarcode = ''
      }

    },
    confirmUnit (value) {
      this.showUnit = false
      if (this.isUnit === 'small') {
        this.FormData.sunit = value
      } else if (this.isUnit === 'big') {
        if (value === this.FormData.sunit) {
          this.$toast.fail('大单位和小单位不能一致')
        } else {
          this.FormData.bunit = value
        }
      } else if (this.isUnit === 'middle') {
        if (value === this.FormData.sunit) {
          this.$toast.fail('中单位和小单位不能一致')
        } else {
          this.FormData.munit = value
        }
      }
    },
    inputPrice (priceType, _chanedByCode) {
      if (_chanedByCode != undefined) this.bChangedByCode = _chanedByCode
      if (this.bChangedByCode) return
      var unitPriceRelated = window.getSettingValue('unitPriceRelated').toLowerCase() != 'false'

      switch (priceType) {
        case 'bpprice':
          if (!this.FormData.bpprice || !this.FormData.bfactor) return
          this.bChangedByCode = true
          if (unitPriceRelated || this.FormData.mpprice == '') {
            if (this.FormData.mfactor) {
              this.FormData.mpprice = toMoney(Number(this.FormData.bpprice) / (Number(this.FormData.bfactor) / Number(this.FormData.mfactor)), 4)
            }
          }
          if (unitPriceRelated || this.FormData.spprice == '') {
            if (this.FormData.mfactor) {
              this.FormData.spprice = toMoney(Number(this.FormData.mpprice) / Number(this.FormData.mfactor), 4)
            } else {
              this.FormData.spprice = toMoney(Number(this.FormData.bpprice) / Number(this.FormData.bfactor), 4)
            }
          }
          break
        case 'spprice':
          if (!this.FormData.spprice) return
          this.bChangedByCode = true
          if (unitPriceRelated || this.FormData.bpprice == '') {
            if (this.FormData.bfactor)
              this.FormData.bpprice = toMoney(Number(this.FormData.spprice) * Number(this.FormData.bfactor))
          }
          if (unitPriceRelated || this.FormData.mpprice == '') {
            if (this.FormData.mfactor)
              this.FormData.mpprice = toMoney(Number(this.FormData.spprice) * Number(this.FormData.mfactor), 4)
          }
          break
        case 'mpprice':
          if (!this.FormData.mpprice || !this.FormData.mfactor) return
          this.bChangedByCode = true
          if (unitPriceRelated || this.FormData.spprice == '') {
            this.FormData.spprice = toMoney(Number(this.FormData.mpprice) / Number(this.FormData.mfactor), 4)
          }
          if (unitPriceRelated || this.FormData.bpprice == '') {
            if (this.FormData.bfactor)
              this.FormData.bpprice = toMoney(Number(this.FormData.mpprice) * (Number(this.FormData.bfactor) / Number(this.FormData.mfactor)))
          }
          break
        //进价
        case 'bbprice':
          if (!this.FormData.bbprice || !this.FormData.bfactor) return
          this.bChangedByCode = true
          if (unitPriceRelated || this.FormData.mbprice == '') {
            if (this.FormData.mfactor) {
              this.FormData.mbprice = toMoney(Number(this.FormData.bbprice) / (Number(this.FormData.bfactor) / Number(this.FormData.mfactor)), 4)
            }
          }
          if (unitPriceRelated || this.FormData.sbprice == '') {
            if (this.FormData.mfactor) {
              this.FormData.sbprice = toMoney(Number(this.FormData.mbprice) / Number(this.FormData.mfactor), 4)
            } else {
              this.FormData.sbprice = toMoney(Number(this.FormData.bbprice) / Number(this.FormData.bfactor), 4)
            }
          }
          break
        case 'sbprice':
          if (!this.FormData.sbprice) return
          this.bChangedByCode = true
          if (unitPriceRelated || this.FormData.bbprice == '') {
            if (this.FormData.bfactor)
              this.FormData.bbprice = toMoney(Number(this.FormData.sbprice) * Number(this.FormData.bfactor))
          }
          if (unitPriceRelated || this.FormData.mbprice == '') {
            if (this.FormData.mfactor)
              this.FormData.mbprice = toMoney(Number(this.FormData.sbprice) * Number(this.FormData.mfactor), 4)
          }
          break

        case 'mbprice':
          if (!this.FormData.mbprice || !this.FormData.mfactor) return
          this.bChangedByCode = true
          if (unitPriceRelated || this.FormData.sbprice == '') {
            this.FormData.sbprice = toMoney(Number(this.FormData.mbprice) / Number(this.FormData.mfactor), 4)
          }
          if (unitPriceRelated || this.FormData.bbprice == '') {
            if (this.FormData.bfactor)
              this.FormData.bbprice = toMoney(Number(this.FormData.mbprice) * (Number(this.FormData.bfactor) / Number(this.FormData.mfactor)), 4)
          }
          break
        //承包价
        case 'bcprice':
          if (!this.FormData.bcprice || !this.FormData.bfactor) return
          this.bChangedByCode = true
          if (unitPriceRelated || this.FormData.mcprice == '') {
            if (this.FormData.mfactor) {
              this.FormData.mcprice = toMoney(Number(this.FormData.bcprice) / (Number(this.FormData.bfactor) / Number(this.FormData.mfactor)), 4)
            }
          }
          if (unitPriceRelated || this.FormData.scprice == '') {
            if (this.FormData.mfactor) {
              this.FormData.scprice = toMoney(Number(this.FormData.mcprice) / Number(this.FormData.mfactor), 4)
            } else {
              this.FormData.scprice = toMoney(Number(this.FormData.bcprice) / Number(this.FormData.bfactor), 4)
            }
          }
          break
        case 'scprice':
          if (!this.FormData.scprice) return
          this.bChangedByCode = true
          if (unitPriceRelated || this.FormData.bcprice == '') {
            if (this.FormData.bfactor)
              this.FormData.bcprice = toMoney(Number(this.FormData.scprice) * Number(this.FormData.bfactor))
          }
          if (unitPriceRelated || this.FormData.mcprice == '') {
            if (this.FormData.mfactor)
              this.FormData.mcprice = toMoney(Number(this.FormData.scprice) * Number(this.FormData.mfactor), 4)
          }
          break
        case 'mcprice':
          if (!this.FormData.mcprice || !this.FormData.mfactor) return
          this.bChangedByCode = true
          if (unitPriceRelated || this.FormData.scprice == '') {
            this.FormData.scprice = toMoney(Number(this.FormData.mcprice) / Number(this.FormData.mfactor), 4)
          }
          if (unitPriceRelated || this.FormData.bcprice == '') {
            if (this.FormData.bfactor)
              this.FormData.bcprice = toMoney(Number(this.FormData.mcprice) * (Number(this.FormData.bfactor) / Number(this.FormData.mfactor)), 4)
          }
          break
        //最低售价
        case 'blowprice':
          if (!this.FormData.blowprice || !this.FormData.bfactor) return
          this.bChangedByCode = true
          if (unitPriceRelated || this.FormData.mlowprice == '') {
            if (this.FormData.mfactor) {
              this.FormData.mlowprice = toMoney(Number(this.FormData.blowprice) / (Number(this.FormData.bfactor) / Number(this.FormData.mfactor)), 4)
            }
          }
          if (unitPriceRelated || this.FormData.slowprice == '') {
            if (this.FormData.mfactor) {
              this.FormData.slowprice = toMoney(Number(this.FormData.mlowprice) / Number(this.FormData.mfactor), 4)
            } else {
              this.FormData.slowprice = toMoney(Number(this.FormData.blowprice) / Number(this.FormData.bfactor), 4)
            }
          }
          break
        case 'slowprice':
          if (!this.FormData.slowprice) return
          this.bChangedByCode = true
          if (unitPriceRelated || this.FormData.blowprice == '') {
            if (this.FormData.bfactor)
              this.FormData.blowprice = toMoney(Number(this.FormData.slowprice) * Number(this.FormData.bfactor))
          }
          if (unitPriceRelated || this.FormData.mlowprice == '') {
            if (this.FormData.mfactor)
              this.FormData.mlowprice = toMoney(Number(this.FormData.slowprice) * Number(this.FormData.mfactor), 4)
          }
          break
        case 'mlowprice':
          if (!this.FormData.mlowprice || !this.FormData.mfactor) return
          this.bChangedByCode = true
          if (unitPriceRelated || this.FormData.slowprice == '') {
            this.FormData.slowprice = toMoney(Number(this.FormData.mlowprice) / Number(this.FormData.mfactor), 4)
          }
          if (unitPriceRelated || this.FormData.blowprice == '') {
            if (this.FormData.bfactor)
              this.FormData.blowprice = toMoney(Number(this.FormData.mlowprice) * (Number(this.FormData.bfactor) / Number(this.FormData.mfactor)), 4)
          }
          break
        //最低售价end
        //零售价
        case 'blprice':
          if (!this.FormData.blprice || !this.FormData.bfactor) return
          this.bChangedByCode = true
          if (unitPriceRelated || this.FormData.mlprice == '') {
            if (this.FormData.mfactor)
              this.FormData.mlprice = toMoney(Number(this.FormData.blprice) / (Number(this.FormData.bfactor) / Number(this.FormData.mfactor)), 4)
          }
          if (unitPriceRelated || this.FormData.slprice == '') {
            if (this.FormData.mfactor) {
              this.FormData.slprice = toMoney(Number(this.FormData.mlprice) / Number(this.FormData.mfactor), 4)
            } else {
              this.FormData.slprice = toMoney(Number(this.FormData.blprice) / Number(this.FormData.bfactor), 4)
            }
          }

          break
        case 'slprice':
          if (!this.FormData.slprice) return
          this.bChangedByCode = true
          if (unitPriceRelated || this.FormData.blprice == '') {
            if (this.FormData.bfactor)
              this.FormData.blprice = toMoney(Number(this.FormData.slprice) * Number(this.FormData.bfactor))
          }
          if (unitPriceRelated || this.FormData.mlprice == '') {
            if (this.FormData.mfactor)
              this.FormData.mlprice = toMoney(Number(this.FormData.slprice) * Number(this.FormData.mfactor), 4)
          }
          break
        case 'mlprice':
          if (!this.FormData.mlprice || !this.FormData.mfactor) return
          this.bChangedByCode = true
          if (unitPriceRelated || this.FormData.slprice == '') {
            this.FormData.slprice = toMoney(Number(this.FormData.mlprice) / Number(this.FormData.mfactor))
          }
          if (unitPriceRelated || this.FormData.blprice == '') {
            if (this.FormData.bfactor)
              this.FormData.blprice = toMoney(Number(this.FormData.mlprice) * (Number(this.FormData.bfactor) / Number(this.FormData.mfactor)))
          }
          break
        case 'bfactor':
          if (!parseFloat(this.FormData.bfactor)) return

          if (this.FormData.spprice) {
            this.inputPrice('spprice', false)
          } else if (this.FormData.bpprice) {
            this.inputPrice('bpprice', false)
          }

          if (this.FormData.slprice) {
            this.inputPrice('slprice', false)
          } else if (this.FormData.blprice) {
            this.inputPrice('blprice', false)
          }

          if (this.FormData.sbprice) {
            this.inputPrice('sbprice', false)
          } else if (this.FormData.bbprice) {
            this.inputPrice('bbprice', false)
          }
          break

        case 'mfactor':
          if (!parseFloat(this.FormData.mfactor)) return


          if (this.FormData.spprice) {
            this.inputPrice('spprice', false)
          } else if (this.FormData.mpprice) {
            this.inputPrice('mpprice', false)
          }

          if (this.FormData.slprice) {
            this.inputPrice('slprice', false)
          } else if (this.FormData.mlprice) {
            this.inputPrice('mlprice', false)
          }

          if (this.FormData.sbprice) {
            this.inputPrice('sbprice', false)
          } else if (this.FormData.mbprice) {
            this.inputPrice('mbprice', false)
          }
          break
      }
      if (this.bChangedByCode) {
        var hInt = setTimeout(() => {
          if (this.bChangedByCode) {
            this.bChangedByCode = false
          }
        }, 1)
      }
    },
    btnCopy_click () {
      this.FormData.item_id = ''
      this.FormData.itemUsed = false
      this.isCopying = true
      Toast.success('已复制')
    },
    checkItemStock (params) {
      if (this.FormData.item_id && this.FormData.batch_level != this.oldFormData.batch_level && this.FormData.batch_level !== "2" && this.FormData.batch_level !== "1") {
        GetGoodsItemStock({ batchLevel: this.FormData.batch_level, itemId: this.FormData.item_id }).then(res => {
          if (res.result == "OK") {
            this.saveItemInfo(params)
          } else {
            this.formData = this.oldFormData
            Toast.fail(res.msg)
          }
        })
      } else {
        this.saveItemInfo(params)
      }
    },
    btnSave_click (approveFlag) {
      if (this.FormData.item_id && !this.editInfoItem) {
        Toast("暂无编辑权限")
        return
      }
      let params = this.getFormData(approveFlag)
      if (!params) return
      this.checkItemStock(params)
    },
    saveItemInfo (params) {
      SaveItemInfo(params).then(res => {
        if (res.result === 'OK') {
          if (!this.FormData.item_id) {
            if (window.g_fromPage && window.g_fromPage.addRow) {
              window.g_fromPage.addRow(this.FormData)
            }
          }
          this.FormData.item_id = res.item_id
          this.FormData.item_images = res.item_images
          Toast.success('已保存')
          this.$store.commit('itemArchivePageOption', {
            type: 'update',
            formData: this.FormData
          })
          this.jump()
          // myGoBack(this.$router)
        } else {
          this.formData = this.oldFormData
          Toast.fail(res.msg)
        }
      })
    },
    jump () {
      const source = this.$route.query.source
      if (source === 'SelectItems_Items') {
        const barcode = this.$route.query.barcode
        this.$router.push({
          path: "/SelectItems",
          query: {
            searchStr: barcode
          }
        })
      } else {
        myGoBack(this.$router)
      }
    },
    /*saveGoodsArchivesDel() {
      let params = this.getFormData()
      if (!params) return
      SaveItemInfo(params).then(res => {
        if (res.result === 'OK') {
          this.FormData.item_id = res.data
          Toast.success('已保存')
        } else {
          Toast.fail(res.msg)
        }
      })
    },*/
    transformItemImageStr () {
      // var tiny;
      // if (this.FormData.item_images) {
      //   try {
      //     var image_parsed = JSON.parse(this.FormData.item_images)
      //     tiny = image_parsed.tiny
      //   } catch (e) {
      //     tiny = null;
      //   }
      // }
      let imgParams = {
        main: this.images[0],
        tiny: this.images[0],
        other: []
      }
      for (var i = 1; i < this.images.length; i++) {
        imgParams.other.push(this.images[i])
      }
      return JSON.stringify(imgParams);
    },
    getFormData (approveFlag) {
      this.FormData.item_images = this.transformItemImageStr()
      var that = this
      let checkOK = true
      let formData = this.FormData

      function isEmpty (value, info) {
        if (!value) {
          that.$toast.fail(info)
          checkOK = false
        }
      }

      if (Number(this.FormData.bfactor) == 0) this.FormData.bfactor = ''
      if (Number(this.FormData.mfactor) == 0) this.FormData.mfactor = ''
      /*if (Number(this.FormData.bfactor) == 1) {
        this.$toast.fail('大单位包装率不能为1')
        return
      }
      if (Number(this.FormData.mfactor) == 1) {
        this.$toast.fail('中单位包装率不能为1')
        return
      }*/
      if (formData.munit)
        isEmpty(formData.mfactor, '请输入中单位包装率')
      if (formData.bunit)
        isEmpty(formData.bfactor, '请输入大单位包装率')
      isEmpty(formData.sunit, '请选择小单位')
      isEmpty(formData.class_name, '请选择商品类别')
      isEmpty(formData.item_name, '请输入商品名称')

      if (!checkOK) {
        return
      }

      if (formData.munit && !formData.bunit) {
        this.$toast.fail('必须输入大单位后才能输入中单位')
        return
      }

      if (formData.mfactor && !formData.bfactor) {
        this.$toast.fail('必须输入大单位后才能输入中单位')
        return
      }

      if (formData.mfactor && parseFloat(formData.mfactor) >= parseFloat(formData.bfactor)) {
        this.$toast.fail('中单位包装率必须小于大单位')
        return
      }

      /*if (formData.mfactor && formData.bfactor) {
        var n = formData.bfactor % formData.mfactor
        if (Math.abs(n) > 0.1) {
          this.$toast.fail('大单位包装率必须是中单位的倍数')
          return
        }
      }*/

      if (formData.sweight != undefined && formData.sweight != null && formData.sweight != '') {
        if (isNaN(formData.sweight)) {
          this.$toast.fail('小单位重量请输入数字')
          return
        }
      }

      if (formData.mweight != undefined && formData.mweight != null && formData.mweight != '') {
        if (isNaN(formData.mweight)) {
          this.$toast.fail('中单位重量请输入数字')
          return
        }
      }

      if (formData.bweight != undefined && formData.bweight != null && formData.mbeight != '') {
        if (isNaN(formData.bweight)) {
          this.$toast.fail('大单位重量请输入数字')
          return
        }
      }

      this.mum_attributes = this.mum_attributes.map(attr => {
        attr.distinctStock = this.attrInfo.distinctStock
        return attr
      })
      this.attrInfo.itemMumAttributes = this.mum_attributes
      if(this.attrInfo.itemMumAttributes.length === 1) {
        const mumAttribute = this.attrInfo.itemMumAttributes[0]
        mumAttribute.options.forEach(mumOptitem => {
          const findItem = this.attrInfo.availAttrCombine.find(availItem => mumOptitem.optID === availItem.son_options_id)
          if (findItem) {
            mumOptitem.bBarcode = findItem.bBarcode
            mumOptitem.bPrice = findItem.bPrice
            mumOptitem.mBarcode = findItem.mBarcode
            mumOptitem.mPrice = findItem.mPrice
            mumOptitem.sBarcode = findItem.sBarcode
            mumOptitem.sPrice = findItem.sPrice
          }
        })
      }
      // 获取旧的属性组合
      const oldAvailAttrCombine = JSON.parse(this.FormData.avail_attr_combine || '[]').filter(item => !item.item_id.toString().startsWith('nanoid'))
      if (!this.attrInfo.distinctStock) {
        this.attrInfo.deleteItemList = oldAvailAttrCombine
        // 将所有组合的item_id替换为nanoid开头的新id
        this.attrInfo.availAttrCombine = this.attrInfo.availAttrCombine.map(item => {
          return {
            ...item,
            item_id: 'nanoid' + this.nanoid()
          }
        })
      } else {
        // 找出被删除的组合
        const deletedCombines = oldAvailAttrCombine.filter(oldItem => {
          return !this.attrInfo.availAttrCombine.some(newItem => {
            return oldItem.combine.length === newItem.combine.length &&
              oldItem.combine.every(val => newItem.combine.includes(val))
          })
        })
        // 将删除的组合添加到deleteItemList中
        this.attrInfo.deleteItemList = deletedCombines
      }

      let params = {
        operKey: this.$store.state.operKey,
        item_id: this.FormData.item_id || '',
        item_name: this.FormData.item_name || '',
        py_str: this.FormData.py_str || '',
        item_no: this.FormData.item_no || '',
        other_class: this.FormData.other_class || '',
        item_class: this.FormData.item_class || '',
        item_brand: this.FormData.item_brand || '',
        supplier_id: this.FormData.supplier_id || '',
        supplier_name: this.FormData.supplier_name || '',
        unit_no: this.FormData.sunit || '',
        unit_weight: this.FormData.sweight || '',
        unit_factor: "1",
        wholesale_price: this.FormData.spprice || '',
        retail_price: this.FormData.slprice || '',
        buy_price: this.FormData.sbprice || '',
        contract_price: this.FormData.scprice || '',
        lowest_price: this.FormData.slowprice || '',
        barcode: this.FormData.sbarcode || '',
        big_unit_no: this.FormData.bunit || '',
        big_unit_weight: this.FormData.bweight || '',
        big_unit_factor: this.FormData.bfactor || '',
        big_wholesale_price: this.FormData.bpprice || '',
        big_buy_price: this.FormData.bbprice || '',
        big_retail_price: this.FormData.blprice || '',
        big_contract_price: this.FormData.bcprice || '',
        big_lowest_price: this.FormData.blowprice || '',
        big_barcode: this.FormData.bbarcode || '',
        medium_unit_no: this.FormData.munit || '',
        medium_unit_weight: this.FormData.mweight || '',
        medium_unit_factor: this.FormData.mfactor || '',
        medium_wholesale_price: this.FormData.mpprice || '',
        medium_buy_price: this.FormData.mbprice || '',
        medium_retail_price: this.FormData.mlprice || '',
        medium_contract_price: this.FormData.mcprice || '',
        medium_lowest_price: this.FormData.mlowprice || '',
        medium_barcode: this.FormData.mbarcode || '',
        valid_days: this.FormData.valid_days || '',
        valid_day_type: this.FormData.valid_day_type || '',
        item_spec: this.FormData.item_spec || '',
        location: this.FormData.location || '',
        item_provenance: this.FormData.item_provenance || '',
        item_images: this.FormData.item_images || '',
        mall_min_qty_s: this.FormData.mall_min_qty_s || '',
        mall_min_qty_b: this.FormData.mall_min_qty_b || '',
        mall_min_qty_m: this.FormData.mall_min_qty_m || '',
        mall_max_qty_s: this.FormData.mall_max_qty_s || '',
        mall_max_qty_b: this.FormData.mall_max_qty_b || '',
        mall_max_qty_m: this.FormData.mall_max_qty_m || '',
        status: this.FormData.status || '',
        mum_attributes: JSON.stringify(this.mum_attributes),
        batch_level: this.FormData.batch_level,
        mall_units: this.mallUnitsChecked.join(','),
        mall_units_show_price: this.mallUnitsShowPriceChecked.join(','),
        attrInfo: this.attrInfo,
        son_options_id: this.FormData.son_options_id || ''
      }
      if (approveFlag) {//审核
        params.approve_brief = this.approve_brief
        params.flow_id = this.editLog.flow_id
        params.receiverId = this.editLog.oper_id
        params.msg_id = this.msgId
        params.approve_status = ""
        if (approveFlag.approve) {
          params.approve_flag = this.editLog.oper_action == 'CREATE' ? 'APPROVED_FROM_CREATE' : 'APPROVED_FROM_EDIT'
        } else {
          params.approve_flag = this.editLog.oper_action == 'CREATE' ? 'REFUSED_FROM_CREATE' : 'REFUSED_FROM_EDIT'
          //根据diff_describe把client档案恢复到原来的数据
          for (let key in this.diff_describe) {
            params[key] = this.diff_describe[key].oldValue
          }
        }
      } else {//新建&&编辑
        params.approve_status = "wait approve"//
        if (!this.FormData.item_id) {
          params.approve_flag = "CREATE"
        } else {
          params.approve_flag = "EDIT"
        }
      }
      if (this.approveAuthority && !approveFlag) {//有审核权限且是新建或编辑
        params.approve_status = ""
        if (!this.FormData.item_id) {
          params.approve_flag = "CREATE_AND_APPROVED"
        } else {
          params.approve_flag = "EDIT_AND_APPROVED"
        }
      }
      return params
    },
    formatter (value) {
      // 判断
      console.log(value)
      if (value.toString().split('.')[1] !== "") {

      }
      return value


    },
    pageSayCode (result) {
      this.scanDialogResult = result
      this.showScanDialog = true
    },
    confirmScanDialog () {
      if (this.tabActive === 0) {
        this.FormData[this.radio] = this.scanDialogResult
      } else {
        if (this.radio === 'sbarcode') {
          this.attrInfo.availAttrCombine[this.tabActive - 1].sBarcode = this.scanDialogResult
        } else if (this.radio === 'mbarcode') {
          this.attrInfo.availAttrCombine[this.tabActive - 1].mBarcode = this.scanDialogResult
        } else if (this.radio === 'bbarcode') {
          this.attrInfo.availAttrCombine[this.tabActive - 1].bBarcode = this.scanDialogResult
        }
      }
    },
    handleAttrChange (changeFlag) {
    },
    handleAddAttribute (attrId) {
      this.attrNameAttrId = attrId
      this.attrNameFilter = this.attrOptions[attrId] || []
      this.attrNameInput = ''
      this.attrNameShow = true
      this.onAttrConfimTitle = this.attrNameFilter.length > 0 ? '选择' : ''
    },
    onAttrSearch (val) {
      if (this.attrNameInputTime) {
        clearTimeout(this.attrNameInputTime)
      }
      this.attrNameInputTime = setTimeout(() => {
        this.attrNameFilter = this.attrOptions[this.attrNameAttrId] || []
        this.attrNameFilter = this.attrNameFilter.filter(item => {
          return item.opt_name.includes(val)
        })
        this.onAttrConfimTitle = this.attrNameFilter.length > 0 ? '选择' : ''
      }, 300)
    },
    async onAttrConfim (selectFlag = true) {
      // 从可用属性中获取当前编辑的属性信息
      const currentAttr = this.availAttributes.find(attr =>
        attr.attr_id === this.attrNameAttrId
      );

      // 如果在mum_attributes中不存在该属性，则创建新的属性对象
      let attrObj = this.mum_attributes.find(attr => attr.attrID === currentAttr.attr_id)
      if (!attrObj) {
        attrObj = {
          attrID: currentAttr.attr_id,
          attrName: currentAttr.attr_name,
          groupID: "",
          options: [],
          distinctStock: currentAttr.distinct_stock,
          specOptInItem: currentAttr.spec_opt_in_item,
          distinct_stock_editable: currentAttr.distinct_stock_editable
        }
        this.mum_attributes.push(attrObj)
      }

      // 判断是选择已有选项还是新建选项
      if (this.onAttrConfimTitle === '选择' && selectFlag) {
        // 选择已有选项
        const selectObj = this.$refs.GoodsArchivesSonPicker.getValues()[0]

        // 检查选项是否已存在
        const exists = attrObj.options.some(opt => opt.optID === selectObj.opt_id)
        if (exists) {
          Toast.fail('该选项已存在')
          return
        }

        // 添加选项
        attrObj.options.push({
          optID: selectObj.opt_id,
          optName: selectObj.opt_name
        })
        this.new_attributes.push(selectObj.opt_id)

      } else {
        // 新建选项
        if (!this.attrNameInput) {
          Toast.fail('请输入')
          return
        }

        // 检查选项名称是否重复
        const nameExists = attrObj.options.some(opt =>
          opt.optName === this.attrNameInput
        )
        if (nameExists) {
          Toast.fail('该选项名称已存在')
          return
        }

        // 调用接口创建新选项
        const params = {
          attr_id: currentAttr.attr_id,
          opt_name: this.attrNameInput,
          operKey: this.$store.state.operKey,
        }

        try {
          const resultData = await CreateAttrName(params)
          if (resultData.result === 'OK') {
            attrObj.options.push({
              optID: resultData.data.opt_id,
              optName: resultData.data.opt_name
            })
            this.new_attributes.push(resultData.data.opt_id,)
          } else {
            return
          }
        } catch (err) {
          Toast.fail('创建失败')
          return
        }
      }

      // 重置并关闭弹窗
      this.handleCloseAddAttribute()
      // 更新组合
      this.generateAttrCombinations()
    },
    handleClearAttr () {
      this.attrNameFilter = this.attrOptions[this.attrNameAttrId] || []
    },
    handleCloseAddAttribute () {
      this.attrNameInput = ''
      clearTimeout(this.attrNameInputTime)
      this.attrNameShow = false

    },
    handleBatchChange () {
      this.checkFlag = false
      // if(this.formData.batch_level)
    },
    setBatchLevel () {
      if (this.checkFlag) {
        this.FormData.batch_level = ''
      }
      this.checkFlag = true
    },
    handleValidDayClick (validDay) {
      this.FormData.valid_day_type = validDay.value
      this.curValidDayType = validDay.label
      this.showValidDayType = false
    },
    async handleApprove (flag) {
      if (this.approveAuthority) {
        if (flag) {
          this.btnSave_click({ approve: true })
        } else {
          this.btnSave_click({ approve: false })
        }
      } else {
        Toast("暂无审核权限")
      }

    },
    handleDeleteAttributeValue (attrId, valueIndex,optID) {
      // 判断
      if (this.FormData.itemUsed && this.attrInfo.distinctStock) {
        Toast('商品已经使用，不能删除区分库存属性选项。可在下方进行停用');
        return
      }

      Dialog.confirm({
        title: '删除确认',
        message: '确定要删除这个属性值吗？',
        width: '320px'
      }).then(() => {
        // 找到对应的属性对象
        const attrObj = this.mum_attributes.find(attr => attr.attrID === attrId)
        if (attrObj) {
          // 从选项数组中删除指定索引的值
          attrObj.options.splice(valueIndex, 1)
          const index = this.new_attributes.indexOf(optID);
          if (index > -1) {
            this.new_attributes.splice(index, 1);
          }
          Toast.success('删除成功')
          // 更新组合
          if (attrObj.options.length === 0) {
            this.mum_attributes = this.mum_attributes.filter(attr => attr.attrID !== attrId)
          }
          this.generateAttrCombinations()
        }
      }).catch(() => {
        // 取消删除
      })
    },
    // 生成属性组合
    generateAttrCombinations () {
      // 获取所有选中的属性
      const selectedAttrs = this.mum_attributes.filter(attr =>
        this.selectedAttributes.includes(attr.attrID)
      );

      if (selectedAttrs.length === 0) {
        this.attrInfo.availAttrCombine = [];
        return;
      }

      // 获取每个属性的选项数组
      const optionsArrays = selectedAttrs.map(attr =>
        attr.options.map(opt => ({
          attrId: attr.attrID,
          optId: opt.optID,
          optName: opt.optName,
          distinctStock: attr.distinctStock
        }))
      );

      // 生成笛卡尔积
      const combinations = this.cartesianProduct(optionsArrays);

      // 保存当前的组合用于比较
      const oldCombinations = [...(this.attrInfo.availAttrCombine || [])];
      const newCombinations = [];

      // 处理每个组合
      combinations.forEach(combo => {
        const optIds = combo.map(item => item.optId);
        const attrIds = combo.map(item => item.attrId);
        const optNames = combo.map(item => item.optName);

        // 查找是否存在相同的组合（通过compare数组比较）
        const existingCombination = oldCombinations.find(old => {
          // 如果old.combine不存在，使用son_options_id分割得到的数组
          const oldCombine = old.combine || old.son_options_id.split('_');
          // 检查两个数组是否包含相同的元素（忽略顺序）
          return optIds.length === oldCombine.length &&
            optIds.every(id => oldCombine.includes(id));
        });

        if (existingCombination) {
          // 如果组合已存在，保留原有数据，只更新可能变化的字段
          const optName = optNames.join('_');
          const itemName = `${this.FormData.item_name}(${optName})`;

          existingCombination.distinctStock = this.attrInfo.distinctStock;
          existingCombination.item_name = itemName;
          existingCombination.py_str = pinyinCode(itemName);
          existingCombination.son_options_id = optIds.join('_');
          existingCombination.attrID = attrIds.join('_');
          existingCombination.optName = optName;
          existingCombination.combine = optIds;

          newCombinations.push(existingCombination);
        } else {
          // 如果是新组合，创建新对象
          const optName = optNames.join('_');
          const itemName = `${this.FormData.item_name}(${optName})`;

          newCombinations.push({
            distinctStock: this.attrInfo.distinctStock,
            item_id: 'nanoid' + this.nanoid(),
            son_mum_item: '',
            status: 1,
            son_options_id: optIds.join('_'),
            attrID: attrIds.join('_'),
            optName: optName,
            combine: optIds,
            bPrice: '',
            mPrice: '',
            sPrice: '',
            bBarcode: '',
            mBarcode: '',
            sBarcode: '',
            item_name: itemName,
            py_str: pinyinCode(itemName)
          });
        }
      });

      // 更新可用组合
      this.attrInfo.availAttrCombine = newCombinations;
    },

    nanoid (t = 21) {
      return crypto.getRandomValues(new Uint8Array(t)).reduce(((t, e) => t += (e &= 63) < 36 ? e.toString(36) : e < 62 ? (e - 26).toString(36).toUpperCase() : e > 62 ? "-" : "_"), "")
    },

    // 计算笛卡尔积的辅助方法
    cartesianProduct (arrays) {
      if (arrays.length === 0) return [];

      // 从第一个数组开始，依次与后面的数组计算笛卡尔积
      return arrays.reduce((results, currentArray) => {
        const newResults = [];

        // 如果是第一个数组，直接将其元素转换为数组形式
        if (results.length === 0) {
          return currentArray.map(item => [item]);
        }

        // 将已有结果与当前数组的每个元素组合
        results.forEach(result => {
          currentArray.forEach(item => {
            newResults.push([...result, item]);
          });
        });

        return newResults;
      }, []);
    },
  },
  watch: {
    selectedAttributes: {
      handler (newVal) {
        // 当取消选择属性时，从mum_attributes中移除对应的属性及其选项
        this.mum_attributes = this.mum_attributes.filter(attr =>
          newVal.includes(attr.attrID)
        );
        this.generateAttrCombinations();
      }
    },
    'attrInfo.distinctStock': {
      handler () {
        this.generateAttrCombinations();
      }
    },
    'FormData.item_name': {
      handler () {
        this.generateAttrCombinations();
      }
    }
  }
}
</script>

<style lang="less" scoped>
/deep/.van-search {
  padding: 0 0.43rem;
}

/deep/.van-search__content.van-search__content--square {
  padding: 0;
}

/deep/ .van-search__label {
  padding: 0;
  -webkit-box-flex: 0;
  flex: none;
  box-sizing: border-box;
  width: 6.2em;
  margin-right: 0.32em;
  color: #646566;
  text-align: left;
  word-wrap: break-word;
  font-size: 15px;
  margin-right: 22px;
}

/deep/.van-search__content {
  border-bottom: 0.026rem solid #ebedf0;
}

/deep/ .van-radio__icon--checked .van-icon {
  border-color: #444;
}
@flex_acent_jc: {
  display: flex;
  align-items: center;
  justify-content: center;
};

@flex_acent_jb: {
  display: flex;
  align-items: center;
  justify-content: space-between;
};

@flex_acent_jst: {
  display: flex;
  align-items: center;
  justify-content: flex-start;
};

#image_add {
  width: 100px;
  height: 100px;
  margin-right: 10px;
  margin-bottom: 10px;
  border: 1px solid transparent;
  color: #cccccc;
  position: relative;
  border-radius: 5px !important;
  border-color: #eee !important;
  display: flex;
  align-items: center;
  justify-content: center;
}

.extra {
  color: #aaa;
  margin-left: 10px;
}

.short-extra {
  color: #aaa;
  margin-left: 20px;
}

.exhibition {
  // clear: both;
  padding: 0 10px;
  font-family: "iconfont" !important;
}

@flex_a_j: {
  display: flex;
  align-items: center;
  justify-content: center;
};

.exhibition_deli {
  position: relative;
  right: -85px;
  top: -120px;
  color: #ee0a24;
  font-size: 20px;
  width: 30px;
  height: 30px;
  @flex_a_j();
}

///deep/.van-cell{
//  padding: 8px 20px;
//}
/deep/ .van-field__control {
  font-size: 15px;
}

.van-action-sheet__item {
  height: auto;
}

.van-action-sheet__cancel {
  height: auto;
}

.add_icon {
  color: #000;
  font-size: 15px;
}

.public_box2_t {
  height: 100%;
  overflow-x: hidden;
  overflow-y: auto;

  h4 {
    height: 30px;
    font-size: 15px;
    font-weight: 500;
    background: #eee;
    @flex_acent_jst();
    padding: 0 10px;
  }

  /deep/ .van-field {
    padding: 12px 30px;
  }

  /deep/ .van-cell {
    span {
      font-size: 15px;
    }
  }

  .valid-day {
    position: relative;
    display: flex;
  }
}

.van-cell--required::before {
  left: 20px;
}

/deep/ .van-cell::after {
  border-bottom: 1px solid #ddd;
}

/deep/ .van-cell:last-child::after {
  display: block;
}

.radio-scan-wrapper {
  height: 150px;
  display: flex;
  flex-direction: column;
  justify-content: space-around;
  box-sizing: border-box;
  padding: 20px 10px;
  align-items: center;

  div {
    margin: 5px;

    /deep/ .van-radio__label {
      font-size: 20px !important;
    }

    /deep/ .van-icon-success {
      border-color: #f88 !important;
    }
  }
}

.del-wrapper {
  display: flex;
  color: #fff;
  align-items: center;
  justify-content: center;
  background: #ddd;
  border: 1px solid #d8d8d8;
  border-radius: 10px;
  width: 65%;
  height: 40px;
  margin: 15px auto;
  color: #000;
}

.choose_wrapper {
  display: flex;
  justify-content: space-between;
  padding: 12px 30px;
  position: relative;

  .choose_content {
    width: 100px;
    margin-right: 10px;
    text-align: left;
    color: #646566;
    font-size: 16px;
    line-height: 24px;
  }
}

.choose_wrapper::after {
  position: absolute;
  box-sizing: border-box;
  content: " ";
  pointer-events: none;
  right: 0.42667rem;
  bottom: 0;
  left: 0.42667rem;
  border-bottom: 1px solid #ddd;
  -webkit-transform: scaleY(0.5);
  -ms-transform: scaleY(0.5);
  transform: scaleY(0.5);
}

// cropperjs
.container {
  display: flex;
  justify-content: center;
  align-items: center;
}

.before {
  width: 100px;
  height: 100px;
  overflow: hidden;
  /* 这个属性可以得到想要的效果 */
}

.img-container {
  height: auto;
  overflow: hidden;
  justify-content: center;
  align-items: center;
}

.afterCropper {
  flex: 1;
  margin-left: 20px;
  border: 1px solid salmon;
  text-align: center;
}

.afterCropper img {
  width: 150px;
  margin-top: 30px;
}

.save_button {
  width: 100%;
  height: 50px;
  position: absolute;
  bottom: 0px;
  background-color: #444;

  // box-sizing: border-box;
  // vertical-align: top;
  button {
    z-index: 9999;
    width: 100%;
    height: 100%;
    // vertical-align: top;
  }
}

.popover-wrapper {
  width: 100%;
  display: flex;
  font-size: 20px;
  flex-direction: column;
  box-sizing: border-box;
  justify-content: flex-start;

  .popover-item {
    border-bottom: 1px solid #d8cbcb;
    height: 50px;
    display: flex;
    padding: 0 10px;
    box-sizing: border-box;
    align-items: center;
    justify-content: flex-start;
  }

  .noline {
    border-bottom: 0;
  }
}

// cropperjs
.attr-wrapper {
  display: flex;
  flex-direction: column;

  .attr-input {
    display: flex;
    align-items: center;
    box-sizing: border-box;
    padding: 0 10px;

    .attr-new {
    }

    .attr-search {
      flex: 1;
    }
  }
}
/deep/.van-tab__text--ellipsis {
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
  display: block;
  overflow: visible;
}
.change-log-box {
  padding: 10px 0;
  width: 100%;
  height: 100%;
  box-sizing: border-box;
  overflow: auto;

  .change-log-items {
    margin-bottom: 185px;

    & > span {
      display: flex;
      padding: 8px;
      text-align: left;
    }

    .change-log-item {
      position: relative;
      padding: 10px;
      border-bottom: 1px solid #eee;
      display: flex;
      align-items: center;
      width: 100%;
      box-sizing: border-box;
    }
  }

  .approve-mod {
    position: absolute;
    bottom: 0;
    padding: 10px;
    width: 100%;
    box-sizing: border-box;
    background-color: #fff;

    .approve-btns {
      padding: 10px 0;
      display: flex;
      justify-content: space-between;

      button {
        padding: 5px;
        border-radius: 8px;
        font-size: 14px;
        color: #fff;
      }
    }

    .approve-info {
      display: flex;
      justify-content: space-between;
      font-size: 14px;
      color: #aaa;
    }
  }
}

.selected_attr_wrapper {
  padding: 12px 30px;

  .attr-row {
    margin-bottom: 16px;

    &:last-child {
      margin-bottom: 0;
    }
  }

  .attr-header {
    display: flex;
    align-items: start;
    gap: 8px;
  }

  .attr-name {
    font-size: 14px;
    color: #323233;
    min-width: 60px;
    margin-top: 4px;
    text-align: left;
  }

  .attr-values {
    flex: 1;
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
  }

  .attr-value-tag {
    display: flex;
    align-items: center;
    padding: 4px 8px;
    background: #f7f8fa;
    border-radius: 2px;
    font-size: 14px;

    .delete-btn {
      margin-left: 4px;
      color: #ee0a24;
    }
  }
}
</style>
