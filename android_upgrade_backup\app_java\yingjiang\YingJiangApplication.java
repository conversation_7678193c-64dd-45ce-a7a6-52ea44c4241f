/*
       Licensed to the Apache Software Foundation (ASF) under one
       or more contributor license agreements.  See the NOTICE file
       distributed with this work for additional information
       regarding copyright ownership.  The ASF licenses this file
       to you under the Apache License, Version 2.0 (the
       "License"); you may not use this file except in compliance
       with the License.  You may obtain a copy of the License at

         http://www.apache.org/licenses/LICENSE-2.0

       Unless required by applicable law or agreed to in writing,
       software distributed under the License is distributed on an
       "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
       KIND, either express or implied.  See the License for the
       specific language governing permissions and limitations
       under the License.
 */

package com.yingjiang.app;

import android.app.Application;
import android.util.Log;
import com.exception.CustomExceptionHandler;

public class YingJiangApplication extends Application {
    private static final String TAG = "YingJiangApplication";

    @Override
    public void onCreate() {
        super.onCreate();
        
        Log.i(TAG, "应用启动，注册全局异常处理器");

        // 尽早注册异常处理器，确保能捕获启动过程中的崩溃
        Thread.setDefaultUncaughtExceptionHandler(new CustomExceptionHandler(this));

        Log.i(TAG, "全局异常处理器注册完成");
    }
}
