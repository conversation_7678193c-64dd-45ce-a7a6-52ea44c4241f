/**
 * 蓝牙权限和初始化助手类
 * 用于处理蓝牙权限申请、设备搜索等操作，避免第一次打开页面时的权限申请导致的错误提示
 */
class BluetoothPermissionHelper {
  constructor() {
    this.isInitializing = false
    this.permissionCheckTimeout = 5000 // 权限检查超时时间
    this.retryDelay = 500 // 重试延迟时间
  }

  /**
   * 检查蓝牙是否可用（iOS）
   * @param {Function} successCallback 成功回调
   * @param {Function} errorCallback 失败回调
   */
  checkBluetoothEnabled(successCallback, errorCallback) {
    if (window.isiOS) {
      bluetoothSerial.isEnabled(
        () => {
          console.log("iOS蓝牙已启用")
          successCallback()
        },
        () => {
          console.log("iOS蓝牙未启用")
          errorCallback("请先在系统设置中开启蓝牙")
        }
      )
    } else {
      // Android平台直接返回成功，具体检查在后续操作中进行
      successCallback()
    }
  }

  /**
   * 安全地搜索蓝牙设备，处理权限申请过程中的错误
   * @param {Object} options 搜索选项
   * @param {Function} options.successCallback 成功回调
   * @param {Function} options.errorCallback 错误回调
   * @param {Function} options.searchFunction 实际搜索函数
   * @param {boolean} options.isFirstTime 是否是第一次搜索
   */
  async safeSearchDevices(options) {
    const { successCallback, errorCallback, searchFunction, isFirstTime = false } = options

    // 如果是第一次搜索，给权限申请留出时间
    if (isFirstTime) {
      console.log("第一次搜索设备，延迟执行以等待权限申请")
      setTimeout(() => {
        this.executeSearch(searchFunction, successCallback, errorCallback)
      }, this.retryDelay)
    } else {
      this.executeSearch(searchFunction, successCallback, errorCallback)
    }
  }

  /**
   * 执行搜索操作
   * @param {Function} searchFunction 搜索函数
   * @param {Function} successCallback 成功回调
   * @param {Function} errorCallback 错误回调
   */
  async executeSearch(searchFunction, successCallback, errorCallback) {
    try {
      await searchFunction()
      if (successCallback) successCallback()
    } catch (error) {
      console.error("搜索设备失败:", error)
      this.handleSearchError(error, errorCallback)
    }
  }

  /**
   * 处理搜索错误
   * @param {Error} error 错误对象
   * @param {Function} errorCallback 错误回调
   */
  handleSearchError(error, errorCallback) {
    const errorMessage = error.toString().toLowerCase()
    
    // 权限相关错误不显示错误提示，因为可能正在申请权限
    if (errorMessage.includes('permission') || 
        errorMessage.includes('denied') ||
        errorMessage.includes('unauthorized')) {
      console.log("检测到权限相关错误，不显示错误提示")
      return
    }

    // 蓝牙未开启的错误
    if (errorMessage.includes('bluetooth') && 
        (errorMessage.includes('disabled') || errorMessage.includes('off'))) {
      if (errorCallback) errorCallback("请先开启蓝牙")
      return
    }

    // 其他错误
    if (errorCallback) errorCallback("搜索设备失败，请稍后重试")
  }

  /**
   * 创建带权限检查的设备列表获取函数
   * @param {Function} originalListFunction 原始列表获取函数
   * @returns {Function} 包装后的函数
   */
  createSafeListFunction(originalListFunction) {
    return () => {
      return new Promise((resolve, reject) => {
        try {
          originalListFunction(
            (devices) => {
              console.log("成功获取设备列表:", devices)
              resolve(devices)
            },
            (error) => {
              console.error("获取设备列表失败:", error)
              // 权限相关错误静默处理
              if (this.isPermissionError(error)) {
                console.log("检测到权限错误，静默处理")
                resolve([]) // 返回空数组而不是抛出错误
              } else {
                reject(error)
              }
            }
          )
        } catch (error) {
          if (this.isPermissionError(error)) {
            console.log("检测到权限错误，静默处理")
            resolve([])
          } else {
            reject(error)
          }
        }
      })
    }
  }

  /**
   * 判断是否是权限相关错误
   * @param {*} error 错误对象
   * @returns {boolean} 是否是权限错误
   */
  isPermissionError(error) {
    if (!error) return false
    const errorMessage = error.toString().toLowerCase()
    return errorMessage.includes('permission') || 
           errorMessage.includes('denied') ||
           errorMessage.includes('unauthorized') ||
           errorMessage.includes('access')
  }

  /**
   * 延迟执行函数，用于给权限申请留出时间
   * @param {Function} func 要执行的函数
   * @param {number} delay 延迟时间（毫秒）
   */
  delayExecution(func, delay = this.retryDelay) {
    return new Promise((resolve) => {
      setTimeout(() => {
        resolve(func())
      }, delay)
    })
  }

  /**
   * 创建重试机制
   * @param {Function} func 要重试的函数
   * @param {number} maxRetries 最大重试次数
   * @param {number} retryDelay 重试间隔
   */
  async retryWithDelay(func, maxRetries = 3, retryDelay = 1000) {
    for (let i = 0; i < maxRetries; i++) {
      try {
        return await func()
      } catch (error) {
        console.log(`第${i + 1}次尝试失败:`, error)
        
        // 如果是权限错误且不是最后一次重试，继续重试
        if (this.isPermissionError(error) && i < maxRetries - 1) {
          console.log(`权限错误，${retryDelay}ms后重试...`)
          await new Promise(resolve => setTimeout(resolve, retryDelay))
          continue
        }
        
        // 最后一次重试失败或非权限错误，抛出错误
        if (i === maxRetries - 1) {
          throw error
        }
      }
    }
  }
}

export default BluetoothPermissionHelper
