// 智能网络请求 - 自动检测并使用最佳网络方案
import { Toast } from "vant"
import axios from "axios"
import store from "../store/store.js"
class SmartHttpService {
  constructor() {
    this.networkCapabilities = {
      hasNativeHttp: false,
      hasNativeFetch: false,
      hasXHR: true
    }

    this.defaultTimeout = 20000

    // 运行时策略：首选通道与是否允许降级
    this.preferredMethod = null // 可取值：'native' | 'fetch' | 'axios' | 'xhr'
    this.allowFallback = true

    this.fallbackOnError = false // 遇到网络错误时是否尝试其他通道（默认不降级）

    this.defaultRetryTimes = 3
    this.defaultRetryDelay = 1000

    // WebView 状态检测
    this.webViewInfo = {
      engine: 'unknown',
      isX5Available: false,
      hasPermissionIssues: false
    }

    // 等待 deviceready 再判断插件可用性
    this._cordovaReady = false
    document.addEventListener('deviceready', () => {
      this._cordovaReady = true
      this.detectWebViewEngine()
    }, false)

    this.init()
  }

  isNativeHttpAvailable() {
    if (!this._cordovaReady) return false
    const http = window.cordova?.plugin?.http
    return !!(http && typeof http.sendRequest === 'function')
  }

  init() {
    this.detectNetworkCapabilities()
    this.logCapabilities()
  }

  detectNetworkCapabilities() {
    // 检测cordova-plugin-advanced-http
    if (window.cordova && window.cordova.plugin && window.cordova.plugin.http) {
      this.networkCapabilities.hasNativeHttp = true
      this.configureNativeHttp()
    }

    // 检测原生fetch API
    if (typeof fetch !== 'undefined' && window.cordova) {
      this.networkCapabilities.hasNativeFetch = true
    }

    console.log('网络能力检测完成:', this.networkCapabilities)
  }

  configureNativeHttp() {
    try {
      const http = window.cordova.plugin.http
      if (typeof http.setDataSerializer === 'function') {
        http.setDataSerializer('json')
      }
      if (typeof http.setRequestTimeout === 'function') {
        http.setRequestTimeout(this.defaultTimeout / 1000)
      }
      if (typeof http.setServerTrustMode === 'function') {
        //http.setServerTrustMode('default')
        http.setServerTrustMode('default', 
          function() {
            console.log('Server trust mode set to default successfully');
          }, 
          function(error) {
            console.warn('Failed to set server trust mode:', error);
          }
        )
      } else {
        if (typeof http.enableSSLPinning === 'function') http.enableSSLPinning(false)
        if (typeof http.acceptAllCerts === 'function') http.acceptAllCerts(false)
      }
      if (typeof http.setFollowRedirect === 'function') {
        http.setFollowRedirect(true)
      } else if (typeof http.disableRedirect === 'function') {
        http.disableRedirect(false)
      }
      console.log('cordova-plugin-advanced-http 配置成功')
    } catch (error) {
      console.warn('配置cordova-plugin-advanced-http失败:', error)
      this.networkCapabilities.hasNativeHttp = false
    }
  }

  logCapabilities() {
    const capabilities = []
    if (this.networkCapabilities.hasNativeHttp) {
      capabilities.push('Native HTTP')
    }
    if (this.networkCapabilities.hasNativeFetch) {
      capabilities.push('Native Fetch')
    }
    if (this.networkCapabilities.hasXHR) {
      capabilities.push('XMLHttpRequest')
    }

    console.log(`可用网络方案: ${capabilities.join(' > ')}`)
  }

  setPreferredMethod(method) { 
    this.preferredMethod = method
 
  }

  setFallbackOnError(allow) {
    this.fallbackOnError = !!allow
    console.log('SmartHttp 网络错误时是否降级:', this.fallbackOnError)
  }
  setAllowFallback(allow) {
    this.allowFallback = !!allow
    console.log('SmartHttp 自动降级已设置为:', this.allowFallback)
  }


  async request(method, url, params = {}, options = {}) {
    const {
      headers = {},
      retryDelay = this.defaultRetryDelay,
      timeout = this.defaultTimeout
    } = options

    let requestMethod = (store?.state?.operInfo?.http_style || '').toLowerCase()
    console.log('🔍 SmartHttp Debug - 原始http_style:', store?.state?.operInfo?.http_style)
    if (!['native','fetch','axios','xhr'].includes(requestMethod)) requestMethod = 'native'
    if(window.isiOS)
        requestMethod = 'axios'//临时测试 - 注释掉以允许iOS使用native
    if (requestMethod === 'native' && !this.isNativeHttpAvailable()) {
       console.log('⚠️ Native HTTP不可用，降级到fetch')
       requestMethod = 'fetch'
    }
    console.log('🔍 SmartHttp Debug - 最终使用的方法:', requestMethod, '| URL:', url, '| paramsType:', options.paramsType)
    let lastError = null

    // 简化策略：仅约束“单次尝试超时”和“总超时时间”
    const startTime = Date.now()
 
    // 仅以剩余时间作为是否再试的依据，不再限制重试次数
    while (true) {
      const elapsed = Date.now() - startTime
      const remainingTime = timeout - elapsed
      // 剩余时间不足，直接停止
      if (remainingTime <= 0) break

      // 每次尝试使用剩余时间，但不超过一个合理的单次限制
      const singleTimeout = Math.min(remainingTime, 15000)

      try {
        console.log(`尝试使用 ${requestMethod} 发送请求:`, method, url)
        const result = await this.executeRequest(requestMethod, method, url, params, {
          timeout: singleTimeout,
          headers,
          paramsType: options.paramsType
        })
      
        console.log(`${requestMethod} 请求成功`)
        return result
      } 
      catch (error) {
        lastError = error
        console.warn(`${requestMethod} 请求失败:`, error.message)

        // 使用shouldRetry统一判断是否应该重试
        if (!this.shouldRetry(error)) break

        // 检查是否还有时间进行下一次重试
        const nextRemainingTime = timeout - (Date.now() - startTime) - retryDelay
        if (nextRemainingTime <= 0) break

        await this.delay(retryDelay)
        continue
      }
    }

    // 显示错误并抛出异常 - 由外部ToastManager管理
    const errorMsg = this.formatError(lastError)
    throw new Error(errorMsg)
  }

  getRequestMethods(forceMethod) {
    if (forceMethod) {
      return [forceMethod]
    }

  
    const methods = []

    // 如果设置了首选通道，先将其放在首位
    const preferred = this.preferredMethod

    const pushIfAvailable = (name, available) => {
      if (available) methods.push(name)
    }

    // 可用性检测
    const canNative = this.isNativeHttpAvailable()
    const canFetch = this.networkCapabilities.hasNativeFetch
    const canAxios = true
    const canXhr = this.networkCapabilities.hasXHR

    const order = ['native', 'fetch', 'axios', 'xhr']

  
    if (preferred && order.includes(preferred)) {
      // 先推入首选通道（若可用）
      switch (pref) {
        case 'native': pushIfAvailable('native', canNative); break
        case 'fetch': pushIfAvailable('fetch', canFetch); break
        case 'axios': pushIfAvailable('axios', canAxios); break
        case 'xhr': pushIfAvailable('xhr', canXhr); break
      }
      // 其余按默认顺序补齐
      order.filter(m => m !== pref).forEach(m => {
        switch (m) {
          case 'native': pushIfAvailable('native', canNative); break
          case 'fetch': pushIfAvailable('fetch', canFetch); break
          case 'axios': pushIfAvailable('axios', canAxios); break
          case 'xhr': pushIfAvailable('xhr', canXhr); break
        }
      })
    } else {
      // 无首选，按默认顺序
      pushIfAvailable('native', canNative)
      pushIfAvailable('fetch', canFetch)
      pushIfAvailable('axios', canAxios)
      pushIfAvailable('xhr', canXhr)
    }

    return methods
  }

  async executeRequest(requestMethod, method, url, params, options) {
    const keys = Object.keys(params);
    for (let i = 0; i < keys.length; i++) {
        const k = keys[i];
        if (params[k] === undefined ||params[k] === null) {
            delete params[k];
        }
    }
    switch (requestMethod) {
      case 'native':  
        return this.nativeHttpRequest(method, url, params, options)
      case 'fetch':
        return this.fetchRequest(method, url, params, options)
      case 'axios':
        return this.axiosRequest(method, url, params, options)
      case 'xhr':
        return this.xhrRequest(method, url, params, options)
      default:
        throw new Error(`不支持的请求方法: ${requestMethod}`)
    }
  }

 getTypeOf(object) {
    switch (Object.prototype.toString.call(object)) {
      case '[object Array]':
        return 'Array';
      case '[object Boolean]':
        return 'Boolean';
      case '[object Function]':
        return 'Function';
      case '[object Null]':
        return 'Null';
      case '[object Number]':
        return 'Number';
      case '[object Object]':
        return 'Object';
      case '[object String]':
        return 'String';
      case '[object Undefined]':
        return 'Undefined';
      default:
        return 'Unknown';
    }
  }
  
  // cordova-plugin-advanced-http（Native） 请求
  async nativeHttpRequest(method, url, params, options) {
   if(this.getTypeOf(params) !== 'Object'){
      alert('参数不对'+url+' '+params.toString())
   }
    return new Promise((resolve, reject) => {
      console.log('🚀 Native HTTP Request 开始:', { method, url, params, options })

      const http = window.cordova.plugin.http
      if (!http) {
        console.error('❌ cordova.plugin.http 不存在!')
        reject(new Error('Native HTTP plugin not available'))
        return
      }

      const { headers, paramsType = 'json' } = options
      console.log('🔧 设置序列化器:', paramsType === 'form' ? 'urlencoded' : 'json')

      // 设置序列化器
      if (paramsType === 'form') {
        http.setDataSerializer('urlencoded')
      } else {
        http.setDataSerializer('json')
      }

      // 转换参数为字符串格式（cordova-plugin-advanced-http 要求）
      const convertParamsToStrings = (obj) => {
        if (!obj || typeof obj !== 'object') return obj
        const converted = {}
        Object.keys(obj).forEach(key => {
          const value = obj[key]
          if (value === null || value === undefined) {
            // 跳过 null 和 undefined 值
            return
          } else if (Array.isArray(value)) {
            // 数组保持原样（插件支持数组）
            converted[key] = value
          } else {
            // 其他类型转换为字符串
            converted[key] = String(value)
          }
        })

        return converted
      }

      const successCallback = (response) => {
        try {
          let data = response.data
          if (typeof data === 'string') {
            try { data = JSON.parse(data) } catch (e) {}
          }
          resolve(data)
        } catch (error) {
          reject(new Error('解析响应失败: ' + error.message))
        }
      }

      const errorCallback = (error) => {
        reject(new Error(this.formatNativeHttpError(error)))
      }

      const methodLower = method.toLowerCase()

      // 使用插件的标准API而不是sendRequest
      if (methodLower === 'get') {
        console.log('📤 发送GET请求:', { url, params: convertParamsToStrings(params), headers })
        http.get(url, convertParamsToStrings(params), headers || {}, successCallback, errorCallback)
      } else if (methodLower === 'post') {
        const data = paramsType === 'form' ? convertParamsToStrings(params) : params
        console.log('📤 发送POST请求:', { url, data, headers, paramsType, serializer: paramsType === 'form' ? 'urlencoded' : 'json' })
        http.post(url, data, headers || {}, successCallback, errorCallback)
      } else if (methodLower === 'put') {
        const data = paramsType === 'form' ? convertParamsToStrings(params) : params
        console.log('📤 发送PUT请求:', { url, data, headers, paramsType })
        http.put(url, data, headers || {}, successCallback, errorCallback)
      } else if (methodLower === 'patch') {
        const data = paramsType === 'form' ? convertParamsToStrings(params) : params
        console.log('📤 发送PATCH请求:', { url, data, headers, paramsType })
        http.patch(url, data, headers || {}, successCallback, errorCallback)
      } else if (methodLower === 'delete') {
        console.log('📤 发送DELETE请求:', { url, params: convertParamsToStrings(params), headers })
        http.delete(url, convertParamsToStrings(params), headers || {}, successCallback, errorCallback)
      } else if (methodLower === 'head') {
        console.log('📤 发送HEAD请求:', { url, params: convertParamsToStrings(params), headers })
        http.head(url, convertParamsToStrings(params), headers || {}, successCallback, errorCallback)
      } else {
        reject(new Error(`Native HTTP 不支持方法: ${method}`))
        return
      }
    })
  }

  // Native Fetch 请求
  async fetchRequest(method, url, params, options) {
    const { timeout, headers, paramsType = 'json' } = options

    let requestOptions = {
      method: method.toUpperCase(),
      headers: { ...headers }
    }

    // 根据 paramsType 设置 body 与 Content-Type
    if (method.toUpperCase() === 'GET') {
      if (params && Object.keys(params).length > 0) {
        const queryString = new URLSearchParams(params).toString()
        url += (url.includes('?') ? '&' : '?') + queryString
      }
    } else {
      if (paramsType === 'form') {
        const form = new FormData()
        Object.keys(params || {}).forEach(k => form.append(k, params[k]))
        requestOptions.body = form
        // 让浏览器自动设置 multipart 边界，不手动写 Content-Type
      } else {
        requestOptions.headers = { 'Content-Type': 'application/json', ...requestOptions.headers }
        requestOptions.body = JSON.stringify(params)
      }
    }

    // 添加超时控制
    const controller = new AbortController()
    const timeoutId = setTimeout(() => controller.abort(), timeout)
    requestOptions.signal = controller.signal

    try {
      const response = await fetch(url, requestOptions)
      clearTimeout(timeoutId)

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`)
      }

      const data = await response.json()
      return data
    } catch (error) {
      clearTimeout(timeoutId)
      if (error.name === 'AbortError') {
        throw new Error('请求超时')
      }
      throw error
    }
  }

  // Axios 请求
  async axiosRequest(method, url, params, options) {
    const { timeout, headers, paramsType = 'json' } = options

    let config = {
      timeout,
      headers
    }

    if (method.toUpperCase() === 'GET') {
      config.params = params
      const response = await axios.get(url, config)
      return response.data
    } else {
      if (paramsType === 'form') {
        const form = new FormData()
        Object.keys(params || {}).forEach(k => form.append(k, params[k]))
        const response = await axios.post(url, form, config)
        return response.data
      } else {
        const response = await axios.post(url, params, config)
        return response.data
      }
    }
  }

  // XMLHttpRequest 请求
  async xhrRequest(method, url, params, options) {
    return new Promise((resolve, reject) => {
      const xhr = new XMLHttpRequest()
      const { timeout, headers, paramsType = 'json' } = options

      xhr.timeout = timeout

      let requestUrl = url
      let requestData = null

      if (method.toUpperCase() === 'GET') {
        if (params && Object.keys(params).length > 0) {
          const queryString = new URLSearchParams(params).toString()
          requestUrl += (url.includes('?') ? '&' : '?') + queryString
        }
      } else {
        if (paramsType === 'form') {
          const form = new FormData()
          Object.keys(params || {}).forEach(k => form.append(k, params[k]))
          requestData = form
        } else {
          requestData = JSON.stringify(params)
        }
      }

      xhr.open(method.toUpperCase(), requestUrl, true)

      if (paramsType !== 'form') {
        xhr.setRequestHeader('Content-Type', 'application/json;charset=UTF-8')
      }

      if (headers) {
        Object.keys(headers).forEach(key => {
          xhr.setRequestHeader(key, headers[key])
        })
      }

      xhr.onload = () => {
        if (xhr.status >= 200 && xhr.status < 300) {
          try {
            const response = JSON.parse(xhr.responseText)
            resolve(response)
          } catch (e) {
            resolve(xhr.responseText)
          }
        } else {
          reject(new Error(`HTTP ${xhr.status}: ${xhr.statusText}`))
        }
      }

      xhr.onerror = () => reject(new Error('网络连接失败'))
      xhr.ontimeout = () => reject(new Error('请求超时'))

      xhr.send(requestData)
    })
  }

  formatNativeHttpError(error) {
    if (error.status === 0 || error.status === -1) {
      return "网络连接失败"
    } else if (error.status === -2) {
      return "请求超时"
    } else if (error.status >= 400 && error.status < 500) {
      return `客户端错误 (${error.status})`
    } else if (error.status >= 500) {
      return `服务器错误 (${error.status})`
    } else {
      return error.error || "网络请求失败"
    }
  }

  formatError(error) {
    if (!error) return "未知网络错误"

    const message = error.message || error.toString()

    if (message.includes('网络连接失败') || message.includes('Network Error')) {
      return "网络连接失败，请检查网络设置"
    } else if (message.includes('超时') || message.includes('timeout')) {
      return "请求超时，请稍后重试"
    } else if (message.includes('HTTP 4')) {
      return "请求参数错误"
    } else if (message.includes('HTTP 5')) {
      return "服务器错误，请稍后重试"
    } else {
      return message
    }
  }

 

  shouldRetry(error) {
    // 简化重试逻辑：只要不是超时错误就可以重试
    const message = error.message || error.toString()
    return !message.includes('超时') && !message.includes('timeout') && !message.includes('time out')
  }

  delay(ms) {
    return new Promise(resolve => setTimeout(resolve, ms))
  }

  /**
   * 检测 WebView 引擎类型
   */
  detectWebViewEngine() {
    try {
      // 检测 X5 WebView
      if (window.navigator.userAgent.includes('TBS/')) {
        this.webViewInfo.engine = 'X5'
        this.webViewInfo.isX5Available = true
        console.log('检测到 X5 WebView 引擎')
      }
      // 检测 Crosswalk
      else if (window.navigator.userAgent.includes('Crosswalk/')) {
        this.webViewInfo.engine = 'Crosswalk'
        console.log('检测到 Crosswalk WebView 引擎')
      }
      // 系统 WebView
      else {
        this.webViewInfo.engine = 'System'
        console.log('使用系统 WebView 引擎')
      }

      // 检测权限问题
      this.checkWebViewPermissions()

    } catch (error) {
      console.warn('WebView 引擎检测失败:', error)
      this.webViewInfo.engine = 'unknown'
    }
  }

  /**
   * 检查 WebView 权限问题
   */
  checkWebViewPermissions() {
    try {
      // 检查是否有 Android 10+ 权限问题的迹象
      if (window.device && window.device.platform === 'Android') {
        const androidVersion = parseInt(window.device.version);
        if (androidVersion >= 10) {
          console.log('检测到 Android 10+，可能存在权限限制')
          this.webViewInfo.hasPermissionIssues = true
        }
      }
    } catch (error) {
      console.warn('权限检查失败:', error)
    }
  }

  /**
   * 获取 WebView 信息
   */
  getWebViewInfo() {
    return {
      ...this.webViewInfo,
      userAgent: window.navigator.userAgent,
      platform: window.device ? window.device.platform : 'unknown',
      version: window.device ? window.device.version : 'unknown'
    }
  }

  // 便捷方法
  async get(url, params = {}, options = {}) {
    return this.request('GET', url, params, options)
  }

  async post(url, params = {}, options = {}) {
    return this.request('POST', url, params, options)
  }

  // 获取当前网络能力信息
  getNetworkInfo() {
    return {
      capabilities: this.networkCapabilities,
      preferredMethod: this.getRequestMethods()[0] || 'xhr'
    }
  }
}

// 创建单例
const smartHttp = new SmartHttpService()

export default smartHttp

// 导出便捷函数
export const smartApiGet = (url, params, showLoading = false, timeout = 15000) => {
  return smartHttp.get(url, params, { showLoading, timeout })
}

export const smartApiPost = (url, params, showLoading = false, timeout = 15000) => {
  return smartHttp.post(url, params, { showLoading, timeout })
}

export const getNetworkInfo = () => {
  return smartHttp.getNetworkInfo()
}
