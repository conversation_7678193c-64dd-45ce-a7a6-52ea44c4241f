<template>
  <div class="pages">
    <div class="serchWrapper">
      <div class="go_back" @click="myGoBack($router)">
        <van-icon name="arrow-left" />
      </div>
      <div class="search_content">
        <van-search id="txtSearch" v-model="searchStr" left-icon placeholder="请输入搜索关键词" @input="onSrcItem" @click="handleSrcClick">
          <template #right-icon>
            <!-- <van-icon name="search" /> -->
            <i class="iconfont" style="font-size:12px">&#xe63c;</i>
          </template>
        </van-search>
      </div>
      <div class="search_layout">
        <van-button class="footer_iconBt" @click="btnScanBarcode_click" style="border: none;background-color: transparent">
          <svg width="30px" height="30px" fill="#666">
            <use xlink:href="#icon-barcodeScan"></use>
          </svg>
        </van-button>
      </div>
      <div class="search_layout">
        <div class="iconfont icon_right" @click="orderByOptions = !orderByOptions">
          &#xe690;
        </div>
      </div>
    </div>
    <div class="class_box">
      <div class="class_box_l">
        <SelectItemsClasses :sheet="sheet" />
      </div>
      <div class="class_box_r">
        <SelectStoreStockItemsItems :sheet="sheet" ref="selectStoreStockItemsItems" @onItemsSelect_OK="onItemsSelect_OK" @handleNewItemClik="handleNewItemClik" @handleEditItem="handleEditItem" />
      </div>
    </div>
    <div class="class_footers">
      <div class="class_footer_left" @click="onStock" :style="{'color':queryCondition.showStockOnly ? '#000':'#aaa'}">
        <div class="footer_left_top">
          <div>只看有库存</div>
          <div>
            <van-icon v-if="queryCondition.showStockOnly" name="checked" color="#000" size="18" />
            <div v-else style="width:15px;height:15px;border-radius:50%;border:1px solid #aaa"></div>
          </div>
        </div>
        <div class="footer_left_bottom">

        </div>
      </div>
      <div class="class_footer_center"> </div>
      <div class="class_footer_right">
        <van-badge v-if="(productLists.length ? productLists.length : 0) !== 0" :content="productLists.length ? productLists.length : 0">
          <button class="selectOkBtn" @click="btnSelectItemOK_click">选好了</button>
        </van-badge>
        <button v-else class="selectOkBtn" @click="closePage">选好了</button>
      </div>
    </div>

    <!--     多选组件 -->
    <van-popup v-model="popupAddStoreStockSheetRow" :lazy-render="true" position="bottom" style="height:100vh;overflow:hidden">
      <StoreStockMultiSelect v-if="popupAddStoreStockSheetRow" ref="multiSelect" :sheet="sheet" @closePage="closePage" @closeSingleChoicePage="closeSingleChoicePage" @onAddRow_OK="onAddRow_OK">
      </StoreStockMultiSelect>
    </van-popup>

    <van-popup v-model="popupEditSheetRows" position="bottom" :style="{ height: '426px' }">
      <StoreStockEditSheetRows ref="editSheetRows" @closeEdit="closeEdit" :sheet="currentSheet" :editSheetRowsInfo="editSheetRowsInfo" />
    </van-popup>
    <van-popup class="van_popup" duration="0.4" v-model="orderByOptions" position="right" safe-area-inset-bottom
      :style="{ width: '85%', height: '100%' }">
      <div class="choose_wrapper">
         <div class="choose_text">排序方式</div>
           <van-dropdown-menu>
             <van-dropdown-item v-model="sort_type" :options="sortOption" />
           </van-dropdown-menu>
      </div>
      <div class="btnWrapper">
        <button class="selectOkBtn" @click="saveOrderByOption">保存配置</button>
      </div>
    </van-popup>
  </div>
  <!-- </div> -->
</template>
<script>
import { Search, Icon, Popup, Badge, Button, DropdownMenu, DropdownItem } from "vant"
import SelectItemsClasses from "../SaleSheet/SelectItems_Classes"
import StoreStockMultiSelect from "./StoreStockMultiSelect";
import SelectStoreStockItemsItems from "./SelectStoreStockItemsItems";
import StoreStockEditSheetRows from "./StoreStockEditSheetRows";

export default {
  name: 'StoreStockSelectItems',
  data() {
    return {
      popupAddStoreStockSheetRow: false,
      productId: {},
      productIdActive: '',/* 当前活动的产品 ID */
      fromBranchID: '',/* fromBranchID 和 toBranchID：存储分支 ID */
      toBranchID: '',
      productLists: [],
      submitBtn: 0,/* 提交按钮状态 */
      searchStr: '',/* 搜索字符串 */
      classID: '',/* 类别 ID */
      selectedItems: [],
      sheet: {},
      queryCondition: { showStockOnly: false },/* 查询条件对象，包括 showStockOnly 标志 */
      moveType: '',
      popupEditSheetRows: false,/* 控制编辑行弹出框的显示 */
      editSheetRowsInfo: [],/* 编辑行信息 */
      orderByOptions: false,/* 控制排序选项弹出框的显示 */
      sort_type:"default",/* sort_type 和 sortOption：排序类型和选项 */
      sortOption: [
        { text: '默认', value: 'default' },
        { text: '商品名称', value: 'item_name' },
        { text: '序号', value: 'order_index' }
      ],
    }
  },
  computed: {
    currentSheet() {/* 定义计算属性。 */
      return this.$store.state.currentSheet/* 返回 store 中的 currentSheet 状态 */
    },
    canSeeStock() {
      return window.hasBranchOperRight(this.sheet.branch_id, 'query_stock')/* 检查是否有查询库存的权限。 */
    }
  },
  components: {/* 定义组件使用的子组件 */
    "van-button": Button,
    SelectStoreStockItemsItems,
    StoreStockMultiSelect,
    "van-search": Search,
    "van-icon": Icon,
    "van-popup": Popup,
    "van-badge": Badge,
    "van-dropdown-menu":DropdownMenu,
    "van-dropdown-item":DropdownItem,
    SelectItemsClasses,
    StoreStockEditSheetRows
  },
  activated() {/* 定义组件被激活时的钩子函数 */
    this.queryCondition.showStockOnly = this.$store.state.showStockOnly/* 设置为 store 中的 showStockOnly 状态 */
  },
  watch: {/* 定义侦听器 */
    '$store.state.classId': function () {/* 当 store 中的 classId 变化时，更新组件的 classID 并启动新查询。 */
      this.classID = this.$store.state.classId;
      let condi = {
        classID: this.classID,
        searchStr: this.searchStr,
        branchID: this.sheet.branch_id,
        clientID: this.sheet.client_id,
        showStockOnly: this.queryCondition.showStockOnly
      }
      this.startNewQuery(condi)
      console.log("$store.state.classId")
    },
    'queryCondition.showStockOnly': {/* 深度侦听 queryCondition.showStockOnly 的变化，并同步到 store */
      handler: function (val, oldVal) {
        this.$store.state.showStockOnly = val
      },
      deep: true
    },
  },
  mounted() {/* 定义组件挂载后的钩子函数 */
    this.sheet = this.$route.query.sheet/* 从路由查询参数中获取 sheet 和 searchStr */
    this.searchStr = this.$route.query.searchStr ? this.$route.query.searchStr : ''
    this.queryCondition.showStockOnly = this.$store.state.showStockOnly/* 设置 queryCondition.showStockOnly 和 branchID */
    this.queryCondition.branchID = this.sheet.branch_id//必须如此赋值才能通过provide把queryCondition传给子组件selectItem_Class

    let condi = {/* 定义一个 condi 对象，用于存储查询条件 */
      classID: '',
      searchStr: this.searchStr,
      showStockOnly: this.queryCondition.showStockOnly,
      clientID: this.sheet.client_id,
      branchID: this.sheet.branch_id
    }
    this.sort_type = this.$store.state.queryItemSortType ?? "default"/* 设置 sort_type，优先使用 store 中的 queryItemSortType，如果没有则使用 "default" */

    this.startNewQuery(condi)/* 调用 startNewQuery 方法，传入 condi 对象，启动新的查询 */
    console.log("mounted")
  },
  beforeRouteLeave(to, from, next) {/* 定义路由离开之前的钩子函数 */
    to.query.selectedItems = this.selectedItems/* 将当前组件的 selectedItems 添加到即将前往的路由的查询参数中 */
    next()/* 调用 next 函数，允许导航进行 */
  },
  provide() {/* 定义一个 provide 函数，用于将 queryCondition 对象提供给子组件 */
    return {/* 返回一个包含 queryCondition 的对象，子组件可以通过 inject 使用它 */
      queryCondition: this.queryCondition
    }
  },
  methods: {/* 定义组件的方法 */
    saveOrderByOption() {/* 保存排序选项的方法 */

      this.orderByOptions = false/* 关闭排序选项弹出框 */
      this.queryCondition.sortType = this.sort_type/* 将当前的排序类型保存到 queryCondition 对象中 */
      this.$store.commit("queryItemSortType",this.sort_type)/* 将当前的排序类型提交到 store */
      this.startNewQuery(this.queryCondition)/* 调用 startNewQuery 方法，使用更新后的 queryCondition 启动新的查询*/
    }, 
    startNewQuery(condi) {
      condi.sortType = this.queryCondition.sortType/* 将排序类型添加到 condi 对象中 */
      this.$refs.selectStoreStockItemsItems.startNewQuery(condi)/* 调用子组件 SelectStoreStockItemsItems 的 startNewQuery 方法，传入 condi 对象 */
    },
    onStock() {/* 处理库存显示切换的方法 */
      this.queryCondition.showStockOnly = !this.queryCondition.showStockOnly/* 切换 showStockOnly 的值 */
      let condi = {
        searchStr: this.searchStr,
        showStockOnly: this.queryCondition.showStockOnly,
        clientID: this.sheet.client_id,
        branchID: this.sheet.branch_id
      }
      this.startNewQuery(condi)/* 启动新的查询 */
      console.log("onStock")

    },
    handleSrcClick() {/* 处理搜索框点击事件的方法 */
      if (this.searchStr !== '') {/* 如果 searchStr 不为空，调用 onSrcItem 方法，传入空字符串 */
        this.onSrcItem('')
      }
    },
    onSrcItem(value) {/* 当用户输入搜索关键字时调用的方法 */

      this.searchStr = value;
      if (this.inputTimer) clearTimeout(this.inputTimer)/* 如果 inputTimer 已存在，清除定时器 */
      this.inputTimer = 0/* 将 inputTimer 重置为 0 */
      this.inputTimer = setTimeout(() => {/* 启动一个定时器，延迟 300 毫秒后执行内部代码 */
        this.inputTimer = 0
        let condi = {/* 创建查询条件对象 condi */
          searchStr: this.searchStr,
          showStockOnly: this.queryCondition.showStockOnly,
          classID: this.classID,
          clientID: this.sheet.client_id,
          branchID: this.sheet.branch_id
        }
        this.startNewQuery(condi)/* 调用 startNewQuery 方法，传入查询条件 condi */
      }, 300)


    },
    onloadSumBtn() {/* 加载提交按钮数量的方法 */
      this.submitBtn = this.$store.state.itemList.length/* 将 submitBtn 设置为 store 中 itemList 的长度 */
    },
    onItemsSelect_OK(obj) {/* 处理项目选择确认的方法 */
      this.productLists = obj
      this.$store.commit("shoppingCarObj", {/* 向 store 提交 shoppingCarObj 变更，包含 updateFlag、sheetType 和 val 属性 */
        updateFlag: true,
        sheetType: this.moveType,
        val: this.productLists
      })
    },
    onBtnSunmber() {/* 更新提交按钮数量的方法 */
      this.submitBtn = this.productLists.length/* 将 submitBtn 设置为 productLists 的长度 */
    },
    // 弹窗新增
    btnSelectItemOK_click() {/* 处理“选好了”按钮点击事件的方法 */
      this.popupAddStoreStockSheetRow = true/* 设置 popupAddStoreStockSheetRow 为 true，显示弹窗 */
      this.$store.commit("multiSelectOpenFlag", this.popupAddStoreStockSheetRow)/* 向 store 提交 multiSelectOpenFlag 变更 */
      this.$store.commit("attrShowFlag", false)/* 向 store 提交 attrShowFlag 变更 */
      setTimeout(() => {/* 在 360 毫秒后调用 loadData 方法，传入 productLists */
        this.$refs.multiSelect.loadData(this.productLists)
      }, 360);

    },
    onAddRow_OK() {/* 处理添加行确认的方法 */
      this.$emit("onAddRow_OK")/* 触发 onAddRow_OK 事件,触发自己的事件通常是为了将事件从子组件传递到父组件 */
    },
    // 单选
    handleNewItemClik(item) {/* 处理单选项目点击的方法 */
      this.$store.commit('distinctStockFlag', false)/* 向 store 提交 distinctStockFlag 变更 */
      this.$store.commit("shoppingCarFinish", false)/* 向 store 提交 shoppingCarFinish 变更 */
      let itemObj = JSON.parse(JSON.stringify(item))/* 深拷贝 item 对象 */
      itemObj.isSelectFlag = false
      itemObj.singleChoice = true
      itemObj.distinctStockFlag = false
      if (item.b_unit_no) itemObj.b_unit_qty = '' // 录入数量
      if (item.m_unit_no) itemObj.m_unit_qty = '' // 录入数量
      itemObj.s_unit_qty = '' // 录入数量
      itemObj.remark = '' // 录入数量
      if (itemObj.mum_attributes) {/* 处理 mum_attributes，如果存在 distinctStock 属性，设置相关标志位 */
        if (!itemObj.mum_attributes.forEach) itemObj.mum_attributes = JSON.parse(itemObj.mum_attributes)/* 如果 mum_attributes 不是一个数组（即没有 forEach 方法），则尝试将其解析为 JSON */
        if (itemObj.mum_attributes.find(attr => attr.distinctStock)) {/* 使用 find 方法遍历 mum_attributes 数组，查找第一个 distinctStock 属性为 true 的元素 .attr => attr.distinctStock: 箭头函数，用于检查每个元素的 distinctStock 属性*/
          this.$store.commit('distinctStockFlag', true)/* 如果找到 distinctStock 属性，将全局状态 distinctStockFlag 设置为 true，并在 itemObj 中设置 distinctStockFlag 标志 */
          itemObj.distinctStockFlag = true
        }
      }
      this.popupAddStoreStockSheetRow = true/* 显示弹窗 */
      this.$store.commit("multiSelectOpenFlag", true)/*  store 提交 multiSelectOpenFlag 变更 */
      this.$store.commit("attrShowFlag", false)/* 向 store 提交 attrShowFlag 变更 */
      setTimeout(() => {/* 在 310 毫秒后调用 loadData 方法，传入 [itemObj] */
        this.$refs.multiSelect.loadData([itemObj])
      }, 310);
    },
    // onSheetRowAdd_OK(item) {
    //   let temp = this.$store.state.currentSheet
    //   row_UnitsProps2Array(item)
    //   this.$store.commit("currentSheet",temp)
    // },
    closePage(flag) {/* 关闭页面的方法 */
      this.popupAddStoreStockSheetRow = false/* 隐藏弹窗 */
      this.$store.commit("multiSelectOpenFlag", false)/* 向 store 提交 multiSelectOpenFlag 变更 */
      this.$store.commit("shoppingCarFinish", false)
      this.$store.commit('shoppingCarObj', {/* 向 store 提交 shoppingCarObj 变更，清除购物车对象 */
        sheetType: "SS",
        clearFlag: true
      })
      if (flag !== 'noBack') {/* 如果 flag 不等于 noBack，调用 myGoBack 方法返回上一页 */
        myGoBack(this.$router)
      }

    },
    closeSingleChoicePage() {/* 关闭单选页面的方法 */
      this.$store.commit("multiSelectOpenFlag", false)/* 向 store 提交 multiSelectOpenFlag 变更 */
      this.$store.commit("shoppingCarFinish", false)
      this.popupAddStoreStockSheetRow = false/* 隐藏弹窗 */
      this.$forceUpdate()/* 强制更新组件 */
    },
    handleEditItem(editSheetRowsInfo) {/* 处理编辑项目的方法 */
      this.editSheetRowsInfo = editSheetRowsInfo.sheetRows
      this.popupEditSheetRows = true/* 显示编辑弹窗 */
      setTimeout(() => {/* 在 350 毫秒后调用 loadData 方法 */
        this.$refs.editSheetRows.loadData()
      }, 350);
    },
    closeEdit() {/* 关闭编辑弹窗的方法 */
      this.$refs.editSheetRows.clearState()/* 调用 editSheetRows 组件的 clearState 方法 */
      this.popupEditSheetRows = false/* 隐藏编辑窗口 */
    },
    async btnScanBarcode_click() {
      try {
        const result = await this.scanBarcodeNew({
          unit_type: 'store_stock_select_items'
        })

        if (!result.code) {
          return
        }

        this.onSrcItem(result.code)
      } catch (error) {
        console.error('扫码失败:', error)
        this.$toast('扫码失败，请重试')
      }
    },

    // setDateAndBatchNo(productLists){
    //   this.$refs.multiSelect.loadData(this.productLists)
    // }
  }
}
</script>
<style lang="less" scoped>
@flex_acent_jbw: {
  display: flex;
  align-items: center;
  justify-content: space-between;
};
@flex_a_j: {
  display: flex;
  align-items: center;
  justify-content: center;
};
@flex_acent_jend: {
  display: flex;
  align-items: center;
  justify-content: flex-end;
};
.iconfont {
  color: #2760ba;
}
.serchWrapper {
  // background-color: #eee;
  background-image: linear-gradient(to bottom, #fff 0%, #eee 100%);
  height: 46PX;
  //border-bottom: 1px solid #f0f0f0;
  display: flex;
  align-items: center;
  .go_back {
    flex: 1;
  }
  .search_content {
    flex: 3;
  }
  .search_layout {
    flex: 1;
  }
}
.van-search {
  background: transparent;
  padding: 0;
  .van-search__content {
    background: transparent;
  }
  /deep/.van-field__body {
    border-bottom: 1PX solid #e6e6e6 !important;
    width: 80%;
    margin: auto;
    font-size: 15PX;
  }
  /deep/#txtSearch {
    height: 20PX;
  }
  /deep/.van-field__right-icon {
    height: 24PX;
    line-height: 24PX;
  }
}
.iconfont {
  color: #bbb;
}
.class_box {
  height: calc(100% - 100PX);
  @flex_acent_jbw();
  .class_box_l {
    width: 32%;
    height: 100%;
    background: #eee;

    overflow-x: hidden;
    overflow-y: auto;
  }
  .class_box_l::-webkit-scrollbar {
    display: none;
  }
  .class_box_r {
    width: 75%;
    height: 100%;
  }
}
.class_footers {
  padding-top: 5PX;
  height: 40PX;
  border-top: 2PX solid #f2f2f2;
  background: #ffffff;
  display: flex;
  z-index: 1;
  .class_footer_left {
    width: 100%;
    flex: 1;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    .footer_left_top {
      width: 100%;
      display: flex;
      justify-content: center;
      :nth-child(1) {
        font-size: 16PX;
      }
      :nth-child(2) {
        padding: 3PX 5PX;
        display: flex;
        align-items: center;
        justify-content: center;
      }
    }
    .footer_left_bottom {
      font-size: 15PX;
      width: 100%;
      display: flex;
      justify-content: center;
      align-items: center;
    }
  }
  .class_footer_center {
    flex: 1;
    display: flex;
    justify-content: center;
    align-items: center;
    color: #aaa;
    font-size: 15PX;
  }
  .class_footer_right {
    flex: 1;
    display: flex;
    justify-content: center;
    align-items: center;
    .selectOkBtn {
      background-color: #fde3e4;
      height: 36PX;
      width: 80PX;
      margin-bottom: 4PX;
      border-radius: 10PX;
      font-size: 16PX;
      // font-weight: bolder;
      color: #000;
      padding: 0;
    }
  }
}
.custom_h5 {
  height: 46px;
  line-height: 46px;
  font-size: 16px;
  color: #333333;
  background: #f2f2f2;
  color: #333333;
  position: relative;
  .icon_h5 {
    position: absolute;
    height: 46px;
    width: 46px;
    right: 0;
    top: 0;
    display: block;
    @flex_a_j();
    font-size: 20px;
  }
}
.class_add_goods {
  height: calc(100% - 46px);
  overflow: auto;
  background: #f2f2f2;
}
body {
  height: 100%;
  overflow: hidden;
}
.van_popup {
  display: flex;
  flex-direction: column;
  width: 100%;

  .btnWrapper {
    width: 100%;
    box-sizing: border-box;
    padding: 0 20px;
    display: flex;
    position: fixed;
    bottom: 50px;
    justify-content: flex-end;

    .selectOkBtn {
      background-color: #fdd3d4;
      height: 36px;
      width: 80px;
      border-radius: 10px;
      // font-weight: bolder;
      color: #000;
      padding: 0;
    }
  }
}
</style>
