<template>
  <div class="wrapper">
    <div class="wrapper-head">
      <van-nav-bar left-arrow safe-area-inset-top title="报损单" @click-left="goback">
        <template #right>
          <div class="font-action" @click="handleShowSheetRowsForOtherSheet">导入</div>
          <div class="submitSalesSlip" @click="handleSubmitClick">
            <svg style="margin:2px 2px 0 0 " width="30px" height="30px" stroke-width="1.3" class="black">
              <use :xlink:href="'#icon-plane'"></use>
            </svg>
          </div>
        </template>
      </van-nav-bar>
    </div>

    <div class="content">
      <div class="content-query">
        <div class="content-query-item">
          <van-icon name="wap-home-o" @click="onSelectBranch" />
          <input type="text" v-model="sheet.branch_name" placeholder="仓库" readonly @click="onSelectBranch" />
        </div>
      </div>
      <div class="public_query_title" v-if="sheet.sheet_no" style="margin-bottom: 1px">
        <div class="public_query_title_t">
          <span>{{ sheet.sheet_no }}</span>
          <span>{{ sheet.approve_time }}</span>
        </div>
      </div>
      <ConcaveDottedCenter />
      <div class="content-sheet-rows">
        <InventoryChangeSheetRows ref="InventoryChangeSheetRows" :sheet="sheet" @handleItemDelete="handleItemDelete" @handleItemEdit="handleItemEdit" />
        <InventorySheetRowsTotalInformation ref="InventoryChangeSheetRowsTotalInformation" :sheet="sheet" />
      </div>
    </div>
    <div class="footer">
      <input id="codes" type="text" style="
              width: 160px;
              font-size: 14px;
              border-style: none;
              border-bottom:1px solid #eee;
              background-color: #eee;
              height: 35px;
              padding-left: 3px;
            " v-model="searchStr" @keydown="onSearchInputKeyDown($event)" placeholder="名称/简拼/条码/货号" :disabled="!!sheet.approve_time" :style="{ backgroundColor: sheet.approve_time ? '#f2f2f2' : '' }" />
      <van-button class="footer_iconBt" :disabled="sheet.approve_time !== '' || IsSubmiting" @click="btnClassView_click">
        <svg width="35px" height="35px" fill="#F56C6C">
          <use xlink:href="#icon-add"></use>
        </svg>
      </van-button>
      <van-button class="footer_iconBt" type="info" @click="btnScanBarcode_click">
        <svg width="30px" height="30px" fill="#555">
          <use xlink:href="#icon-barcodeScan"></use>
        </svg>
      </van-button>
    </div>
    <InventoryChangeForOtherSheet
      ref="inventoryChangeForOtherSheetRef"
      :branchId="sheet.branch_id"
      @handleFillItem="handleFillItem"
    />
    <van-popup v-model="bPopupBranchPicker" round position="bottom">
      <van-picker show-toolbar title="选择仓库" :columns="branchList" value-key="branch_name" @cancel="bPopupBranchPicker = false" @confirm="onConfirmBranch" />
    </van-popup>
    <van-popup v-model="showUnsubmitedSheets" round>
      <div class="lowItem" style="width: 320px;">
        <h4>点击打开未提交单据</h4>
        <ul class="lowItem_ul">
          <li v-for="(item, index) in unsubmitedSheets" :key="index">
            <div class="lowItem_ull" @click="onUnsubmitedSheetSelected(item)">
              {{ item.branch_name }}
            </div>
            <div class="lowItem_ulr" @click="onUnsubmitedSheetSelected(item)">
              {{ item.saveTime }}
            </div>
            <div style="width: 70px; line-height: 40px" class="btn-delete" @click="onUnsubmitedSheetDelete(index)">
              删除
            </div>
          </li>
        </ul>
        <van-button type="default" @click="showUnsubmitedSheets = false">新建单据
        </van-button>
      </div>
    </van-popup>
    <van-popup v-model="popupEditSheetRows" position="bottom" :style="{ height: '426px' }">
      <InventoryEditSheetRows ref="editSheetRows" @closeEdit="closeEdit" :sheet="sheet" :editSheetRowsInfo="editSheetRowsInfo" />
    </van-popup>
    <van-popup v-model="popupAddInventoryChangeSheetRow" :lazy-render="true" position="bottom" style="height:100vh;overflow:hidden">
      <InventoryMultiSelect v-if="popupAddInventoryChangeSheetRow" ref="multiSelect" :sheet="sheet" @closePage="closePage" @closeSingleChoicePage="closeSingleChoicePage">
      </InventoryMultiSelect>
    </van-popup>
    <van-popup v-model="showSubmitPopup" duration="0.4" :style="{ height: '100%', width: '80%' }" class="van_popup" position="right">
      <div style="height:30px;border-top:1px solid #ccc"></div>
      <div class="other_operate">
        <van-field style="white-space: nowrap;" v-model="sheet.make_brief" label="备注" label-width="40px" placeholder="请输入备注" :disabled="!!sheet.approve_time" />
        <div style="height:30px"></div>
        <div class="other_operate_content">
          <button v-if="canMake" :disabled="IsSubmiting || sheet.sheet_id !== ''" @click="handleSheetSave" style="height: 45px;border-radius:12px;background-color: #ffcccc;">保存</button>
          <button v-if="canApprove" :disabled="sheet.approve_time !== ''" @click="handleSheetApprove" style="height: 45px;border-radius:12px;background-color: #ffcccc;">审核</button>
        </div>
        <div class="other_operate_content">
          <button style="height: 45px;border-radius:12px" :style="{
              color: sheet.red_flag !== '' ? '#ff9999' : '#ee0a24',
              borderColor: sheet.red_flag !== '' ? '#ff9999' : '#ee0a24',
            }" v-if="canRed && sheet.approve_time" :disabled="sheet.red_flag !== ''" @click="handleSheetRed">红冲</button>
          <button v-if="sheet.sheet_id && !sheet.approve_time && canDelete" style="height: 45px;border-radius:12px" @click="handleSheetDelete">删除</button>
          <button @click="handleSheetPrint" v-if="canPrint" style="height: 45px;border-radius:12px" :disabled="(sheetStatusForPrint === 'saved' && !sheet.sheet_id) || (sheetStatusForPrint === 'approved' && !sheet.approve_time)||isPrinting">打印</button>
        </div>
        <template v-if="canPrint">
          <van-divider>打印条码</van-divider>
          <van-radio-group v-model="printBarcodeStyle" style="font-size: 12px; margin-left: 0; display: flex; justify-content: center;padding-top: 10px;">
            <van-radio name="noBarcode" style="margin-right: 10px">不打印</van-radio>
            <van-radio name="actualUnit" style="margin-right: 10px">实际单位</van-radio>
            <van-radio name="smallUnit" style="margin-right: 10px">小单位</van-radio>
          </van-radio-group>
          <van-checkbox v-if="printBarcodeStyle === 'actualUnit' || printBarcodeStyle === 'smallUnit' " shape="square" v-model="printBarcodePic" icon-size="20px" style="font-size: 12px; margin-left: 50px; margin-top: 10px">打印条码图</van-checkbox>
        </template>

        <van-divider style="margin: 20px 0" @click="moreOptions = !moreOptions">

          <svg v-if="moreOptions" width="26px" height="26px" fill="#d8d8d8">
            <use xlink:href="#icon-double_arrow"></use>
          </svg>

          <svg style="-webkit-transform: rotate(180deg);" v-else width="26px" height="30px" fill="#d8d8d8">
            <use xlink:href="#icon-double_arrow"></use>
          </svg>
        </van-divider>
        <template v-if="moreOptions">
          <div class="other_operate_content">
            <van-button type="default" :disabled="IsSubmiting || sheet.approve_time !== ''" @click="onEmpty">清空</van-button>
            <van-button type="default" @click="btnOut">退出</van-button>
          </div>
        </template>
      </div>
    </van-popup>
  </div>
</template>

<script>
import { NavBar, Icon, Picker, Toast, Button, Dialog, RadioGroup, Radio, Divider, Checkbox, Field } from "vant"
import {
  GetBranchList,
  AppSheetInventoryChangeLoad,
  AppSheetInventoryChangeSubmit,
  AppSheetInventoryChangeRed,
  AppSheetInventoryChangeDelete,
  AppSheetInventoryChangeSave,
  AppSheetInventoryChangeGetItemList
} from "../../api/api";
import InventoryChangeSheetRows from "./InventoryChangeSheetRows";
import InventorySheetRowsTotalInformation from "./InventorySheetRowsTotalInformation";
import ConcaveDottedCenter from "../components/ConcaveDottedCenter"
import InventoryMixin from "./InventoryMixin";
import mixins from '../SaleSheet/sheetMixin/mixin'

import InventoryEditSheetRows from "./InventoryEditSheetRows";
import Printing from "../Printing/Printing";
import InventoryMultiSelect from "./InventoryChangeMultiSelect";
import YjSelectCalendarOptions from "../components/yjSelect/yjCalendar/YjSelectCalendarOptions.vue";
import InventoryChangeForOtherSheet from "./InventoryChangeForOtherSheet.vue";

export default {
  name: "InventoryChangeSheet",
  mixins: [InventoryMixin, mixins],
  components: {
    InventoryChangeForOtherSheet,
    YjSelectCalendarOptions,
    InventorySheetRowsTotalInformation,
    InventoryMultiSelect,
    InventoryEditSheetRows,
    InventoryChangeSheetRows,
    "van-nav-bar": NavBar,
    "van-icon": Icon,
    "van-button": Button,
    "van-picker": Picker,
    ConcaveDottedCenter,
    "van-radio-group": RadioGroup,
    "van-radio": Radio,
    "van-divider": Divider,
    "van-checkbox": Checkbox,
    'van-field': Field
  },
  data() {
    return {
      sheet: {
        sheetType: 'BS',
        sheet_no: "",
        sheet_id: "",
        approve_time: "",
        make_time: "",
        sheetRows: []
      },
      branchList: [],
      bPopupBranchPicker: false,
      IsSubmiting: false,
      searchStr: '',
      showUnsubmitedSheets: false,
      unsubmitedSheets: [],
      editSheetRowsInfo: [],
      popupEditSheetRows: false,
      showSubmitPopup: false,
      printBarcodeStyle: "noBarcode",
      moreOptions: false,
      printBarcodePic: false,
      isPrinting: false,
      popupAddInventoryChangeSheetRow: false,
    }
  },
  mounted() {
    this.loadSheet()
    this.handleRegisterSayCode()
    this.loadBranches()
  },
  activated() {
    this.$refs.InventoryChangeSheetRows.handleUpdate()
  },
  beforeRouteLeave(to, from, next) {
    if (to.name !== 'InventoryChangeSelectItems' && this.sheet.branch_name && this.sheet.sheet_no === '') {
      this.saveCurSheetToCache(this.sheet)
    }
    next();
  },
  watch: {
    "sheet.sheetRows": {
      handler: function () {
        this.handleSelectedSheetRows('BS')
      },
      deep: true
    },
    popupEditSheetRows: {
      handler: function (newVal) {
        if (newVal === false) {
          this.$refs.InventoryChangeSheetRows.handleUpdate()
        }
      },
      deep: true
    },
  },
  computed: {
    canEdit() {
      if (this.sheet.approve_time)
        return false
      if (!this.canApprove && this.sheet.sheet_id) return false
      return true
    },
    canRed() {
      return hasRight("stock.sheetInventReduce.red");
    },
    canMake() {
      return hasRight("stock.sheetInventReduce.make");
    },
    canDelete() {
      return hasRight("stock.sheetInventReduce.delete");
    },
    canPrint() {
      return hasRight("stock.sheetInventReduce.print");
    },
    canApprove() {
      return hasRight("stock.sheetInventReduce.approve");
    },
    allowPrintBeforeApprove() {
      return hasRight("delicacy.allowPrintBeforeApprove.value");
    },
    allowPrintBeforeSave() {
      return hasRight("delicacy.allowPrintBeforeSave.value");
    },
    sheetStatusForPrint() {
      return window.getRightValue('delicacy.moveSheetStatusForPrint.value');
    },
  },
  methods: {
    goback() {
      // eslint-disable-next-line no-undef
      myGoBack(this.$router)
    },
    async loadSheet() {
      // this.sheet.sheetType=sheetType
      const sheetID = this.$route.query.sheetID || "";
      if (sheetID) {
        this.showUnsubmitedSheets = false;
      }
      let params = {
        sheetID: sheetID,
      }
      await AppSheetInventoryChangeLoad(params).then(res => {
        if (res.result !== "OK") {
          // Toast.fail("请求失败")
          return
        }
        if (!res.sheet) {
          Toast.fail("无表单数据")
          return
        }
        this.sheet = res.sheet
        this.handleSheetRowsForLoad()
        this.$store.commit("attrOptions", res.attrOptions)
        const sheetID = this.sheet.sheet_id
        if (!sheetID) {
          this.showSheetsFromCache();
        }
      }).catch(err => {
        Toast(err)
      })
    },
    onSelectBranch() {
      if (this.sheet.approve_time) {
        return
      }
      this.bPopupBranchPicker = true
    },
    // 根据权限获取仓库列表
    loadBranches() {
      this.showCustomer = false
      let params = {}
      GetBranchList(params).then((res) => {
        this.branchList = []
        if (res.result === "OK") {
          for (let i = 0; i < res.data.length; i++) {
            let branch = res.data[i];

            let branchValid = window.hasBranchSheetRight(branch.branch_id, 'PD')
            // if (branchValid) {
            //   this.branchList.push(branch)
            // }
            if (branchValid) {
              let branchPosition = JSON.parse(branch.branch_position)
              let newBranchPosition = []
              branchPosition.forEach(e=>{
                if(e.branch_position !=="0"){
                  newBranchPosition.push(e)
                }
              })
              this.branchList.push({
                branch_id:branch.branch_id,
                branch_name:branch.branch_name,
                branch_position:newBranchPosition,
                branch_type:branch.branch_type
              })
            }
          }
          if(this.sheet.branch_id){
            this.branchList.some(b=>{
              if(b.branch_id.toString() == this.sheet.branch_id.toString()){
                this.$store.commit("setCurBranchPositionList",b.branch_position)
                return true
              }
            })
          }
        }
      })
    },
    // 确认仓库
    onConfirmBranch(value) {
      this.sheet.branch_id = value.branch_id;
      this.sheet.branch_name = value.branch_name;
      this.bPopupBranchPicker = false;
      this.branchList.some(b=>{
        if(b.branch_id == value.branch_id){
          this.$store.commit("setCurBranchPositionList", b.branch_position)
          return true
        }
      })
    },
    handleRegisterSayCode() {
      window.sayCode = result => {
        this.pageSayCode(result)
      }
    },
    pageSayCode(result) {
      this.searchStr = result;
      this.queryScan()
    },
    onSearchInputKeyDown(e) {
      if (e.keyCode === 13) {
        this.queryScan()
      }
    },
    queryScan() {
      console.log(this.searchStr)
      this.$store.commit("currentSheet", this.sheet);
      let params = {
        searchStr: this.searchStr,
        showStockOnly: false,
        classID: '',
        branchID: this.sheet.branch_id,
        pageSize: 20,
        startRow: 0,
        brandIDs: ''
      }
      AppSheetInventoryChangeGetItemList(params).then((res) => {
        if (res.result === "OK") {
          if (res.data.length === 0) {
            Toast('未找到对应商品')
            return
          } else if (res.data.length === 1) {
            let item = res.data[0]
            item.item_images = this.handleImage(item.item_images)
            console.log(item)
            this.$store.commit('distinctStockFlag', false)
            this.$store.commit("shoppingCarFinish", false)
            let itemObj = JSON.parse(JSON.stringify(item))
            itemObj.isSelectFlag = false
            itemObj.singleChoice = true
            itemObj.distinctStockFlag = false
            if (item.b_unit_no) itemObj.b_unit_qty = '' // 录入数量
            if (item.m_unit_no) itemObj.m_unit_qty = '' // 录入数量
            itemObj.s_unit_qty = '' // 录入数量
            itemObj.remark = '' // 录入数量
            if (itemObj.mum_attributes) {
              if (!itemObj.mum_attributes.forEach) itemObj.mum_attributes = JSON.parse(itemObj.mum_attributes)
              if (itemObj.mum_attributes.find(attr => attr.distinctStock)) {
                this.$store.commit('distinctStockFlag', true)
                itemObj.distinctStockFlag = true
              }
            }
            this.popupAddInventoryChangeSheetRow = true
            this.$store.commit("multiSelectOpenFlag", true)
            this.$store.commit("attrShowFlag", false)
            setTimeout(() => {
              this.$refs.multiSelect.loadData([itemObj])
            }, 310);
          } else if (res.data.length > 1) {
            this.btnClassView_click();
          }
        }
        this.searchStr = ''
      })
    },

    async btnScanBarcode_click() {
      try {
        const result = await this.scanBarcodeNew({
          unit_type: 'inventory_change'
        })

        if (!result.code) {
          return
        }

        this.searchStr = result.code
        this.btnClassView_click()
      } catch (error) {
        console.error('扫码失败:', error)
        this.$toast('扫码失败，请重试')
      }
    },
    btnClassView_click() {
      if (!this.sheet.branch_id) {
        Toast.fail("请选择仓库");
      } else {
        let query = {
          searchStr: this.searchStr || "",
          sheet: this.sheet,
        };
        this.$store.commit("currentSheet", this.sheet);
        this.$router.push({ path: "/InventoryChangeSelectItems", query: query });
      }
    },
    handleItemDelete(item, index) {
      this.sheet.sheetRows.splice(index, 1)
    },
    handelGetSheet() {
      //let tempTemp = {...this.sheet}
      this.sheet.IsFromWeb = false
      this.sheet.cost_price_type='3';
      if(this.$store.state.operInfo.setting!=null && this.$store.state.operInfo.setting.costPriceType!=""){
        this.sheet.cost_price_type = this.$store.state.operInfo.setting.costPriceType
      } 
      this.sheet.recentPriceTime='3';
      if(this.$store.state.operInfo.setting!=null && this.$store.state.operInfo.setting.recentPriceTime!=""){
        this.sheet.recentPriceTime = this.$store.state.operInfo.setting.recentPriceTime
      } 
      this.sheet.maker_name = this.sheet.maker_name === '' ? this.$store.state.operInfo.oper_name : this.sheet.maker_name
      if (!this.sheet.seller_id) this.sheet.seller_id = this.$store.state.operInfo.oper_id

      this.sheet.operKey = this.$store.state.operKey
      this.sheet.sheetType = 'BS'
      this.sheet.sheet_id_red_me = ''
      this.sheet.sheet_type = 'SHEET_INVENT_REDUCE'
      this.sheet.buy_amount = 0
      this.sheet.cost_amount_avg = 0
      this.sheet.wholesale_amount = 0
      this.sheet.sheetRows.forEach(item => {
        this.sheet.wholesale_amount += (Number(item.wholesale_amount))
        this.sheet.buy_amount += (Number(item.buy_amount))
        this.sheet.cost_amount_avg += (Number(item.cost_amount_avg))
        //item.cost_price_avg = item.cost_price_avg /item.unit_factor
      })
      this.sheet.wholesale_amount = toMoney(this.sheet.wholesale_amount)
      this.sheet.buy_amount = toMoney(this.sheet.buy_amount)
      this.sheet.cost_amount_avg = toMoney(this.sheet.cost_amount_avg)
    },
    handleItemEdit(item) {
      if (!this.canEdit) return;
      this.editSheetRowsInfo = [item]
      this.popupEditSheetRows = true
      setTimeout(() => {
        this.$refs.editSheetRows.loadData()
      }, 350);
    },
    closeEdit() {
      this.$refs.InventoryChangeSheetRows.handleUpdate()
      this.popupEditSheetRows = false
      this.$refs.editSheetRows.clearState()
    },
    handleSubmitClick() {
      if (!this.sheet && this.sheet.sheetRows.length <= 0) {
        Toast.fail("请添加商品")
        return
      }
      this.showSubmitPopup = true
    },
    handleSheetSave() {
      // 保存单据
      if (this.sheet.branch_id === '') {
        Toast.fail('请选择仓库');
        return
      }
      this.handelGetSheet()
      if (this.sheet.sheetRows.length === 0) {
        Toast.fail("请选择商品");
      } else {
        this.IsSubmiting = true
        console.log(this.sheet)
        AppSheetInventoryChangeSave(this.sheet).then((res) => {
          if (res.result === "OK") {
            this.sheet.sheet_no = res.sheet_no;
            this.sheet.sheet_id = res.sheet_id;
            this.sheet.make_time = res.currentTime;
            setTimeout(() => {
              this.removeCurSheetFromCache()
            }, 300)
            Toast.success("提交成功");
          } else {
            Toast.fail('提交成功');
          }
        }).catch((err) => {
          console.log(err);
        })
      }
    },
    handleSheetApprove() {
      Dialog.confirm({
        title: "审核单据",
        message: "请确认是否审核?",
        width:'320px'
      }).then(() => {
        this.handelGetSheet()
        if (!this.sheet.approver_name) {
          this.sheet.approver_name = this.$store.state.operInfo.oper_name;
        }
        this.IsSubmiting = true;
        console.log(this.sheet)
        AppSheetInventoryChangeSubmit(this.sheet).then(res => {
          this.IsSubmiting = false;
          if (res.result === "OK") {
            this.sheet.sheet_no = res.sheet_no;
            this.sheet.sheet_id = res.sheet_id;
            this.sheet.make_time = res.currentTime;
            this.sheet.approve_time = res.approve_time;
            if (window.g_curSheetInList) {
              // window.g_curSheetInList.approve_time=res.approve_time
              window.g_curSheetInList.state = "approved";
            }
            this.removeCurSheetFromCache()
            Toast.success("审核成功");
          } else {
            Toast.fail(res.msg);
          }
        })
      })
    },
    handleSheetRed() {
      Dialog.confirm({
        title: "红冲单据",
        message: "请确认是否红冲",
        width:'320px'
      }).then(() => {
        let params = {
          operKey: this.$store.state.operKey,
          sheetID: this.sheet.sheet_id,
        };
        AppSheetInventoryChangeRed(params).then((res) => {
          if (res.result === "OK") {
            Toast.success("红冲成功,即将退出该页面");
            this.removeCurSheetFromCache()
            setTimeout(() => {
              this.btnOut();
            }, 1000);
          } else {
            Toast.fail("红冲失败:" + res.msg);
          }
        });
      })
    },
    handleSheetDelete() {
      Dialog.confirm({
        title: "删除单据",
        message: "请确认是否删除",
        width:'320px'
      }).then(() => {
        // var delFunc;
        // delFunc = SheetMoveDelete;
        let params = {
          operKey: this.$store.state.operKey,
          sheet_id: this.sheet.sheet_id,
        };
        AppSheetInventoryChangeDelete(params).then((res) => {
          if (res.result === "OK") {
            Toast.success("删除成功,即将退出该页面");
            this.removeCurSheetFromCache()
            setTimeout(() => {
              this.btnOut();
            }, 1000);
          } else {
            Toast.fail("删除失败:" + res.msg);
          }
        });
      }).catch(() => {
        Toast("取消删除");
      });
    },
    handleSheetPrint() {
      console.log('ss')
      this.isPrinting = true
      this.handelGetSheet()
      const printSheet = JSON.parse(JSON.stringify(this.sheet))
      const showTotalSheetInfo = this.$refs.InventoryChangeSheetRowsTotalInformation.showTotalSheetInfo
      const defaultPrinter = window.getDefaultPrinter()
      if (defaultPrinter.type === "cloud") {
        Toast("云打印功能持续开发中，敬请期待，请选择蓝牙打印")
      } else {
        Printing.printInventoryChangeSheet(printSheet, showTotalSheetInfo, this.printBarcodeStyle, this.printBarcodePic, null, res => {
          this.isPrinting = false
          if (res.result === "OK") {
            Toast.success("打印成功");
          } else {
            Toast.fail(res.msg);
          }
        });
      }
    },
    btnOut() {
      myGoBack(this.$router);
    },
    onEmpty() {
      Dialog.confirm({
        title: "清空单据",
        message: "请确认是否清空",
        width:'320px'
      }).then(() => {
        this.sheet.sheet_no = ''
        this.sheet.sheet_id = ''
        this.sheet.approve_time = ''
        this.sheet.make_time = ''
        this.sheet.sheetRows = []
        this.sheet.buy_amount = ''
        this.sheet.cost_amount_avg = ''
        this.sheet.wholesale_amount = ''
      }).catch(() => {
        Toast("取消删除");
      });
    },
    closePage(flag) {
      this.popupAddInventoryChangeSheetRow = false
      this.$store.commit("multiSelectOpenFlag", false)
      this.$store.commit("shoppingCarFinish", false)
      this.$store.commit('shoppingCarObj', {
        sheetType: this.moveType,
        clearFlag: true
      })
    },
    closeSingleChoicePage() {
      this.$store.commit("multiSelectOpenFlag", false)
      this.$store.commit("shoppingCarFinish", false)
      this.popupAddInventoryChangeSheetRow = false
      this.$forceUpdate()
    },
    handleSheetRowsForLoad() {
      if (this.sheet.sheetRows.length > 0) {
        // 处理打开单据的时候,盘盈盘亏信息
        this.sheet.sheetRows.forEach(sheetRow => {
          sheetRow.real_quantity = (sheetRow.b_unit_qty || 0) * (sheetRow.b_unit_factor || 0) + (sheetRow.m_unit_qty || 0) * (sheetRow.m_unit_factor || 0) + (sheetRow.s_unit_qty || 0) * 1
          const difference_qty = sheetRow.quantity - sheetRow.real_quantity;
          sheetRow.difference_qty = difference_qty
          let cost_price = sheetRow.cost_price_buy
          let costPriceType = sheetRow.cost_price_type
          if (costPriceType === '2') {
            cost_price = sheetRow.cost_price_avg
          }
          else if (costPriceType === '3') {
            cost_price = sheetRow.cost_price_buy
          }
          else if (costPriceType === '1') {
            cost_price = sheetRow.cost_price_prop
          }
          else if (costPriceType === '4') {
            cost_price = sheetRow.cost_price_recent
          }
          // 报损单使用实际数量而不是盘点差异来计算成本
          sheetRow.cost_amount = toMoney(sheetRow.quantity * cost_price * sheetRow.unit_factor)

          if (difference_qty > 0) {
            let profit_wholesale_amount = toMoney(difference_qty * sheetRow.wholesale_price)
            let profit_cost_amount = toMoney(difference_qty * cost_price*sheetRow.unit_factor)
            //let profit_buy_amount = toMoney(difference_qty * sheetRow.buy_price)
            sheetRow.profit_wholesale_amount = profit_wholesale_amount
            sheetRow.profit_cost_amount = profit_cost_amount
            //sheetRow.profit_buy_amount = profit_buy_amount
            sheetRow.loss_qty = ""
            sheetRow.loss_wholesale_amount = ''
            sheetRow.loss_cost_amount = ''
            sheetRow.loss_buy_amount = ''
            sheetRow.b_loss_qty = 0
            sheetRow.m_loss_qty = 0
            sheetRow.s_loss_qty = 0
            const res = this.getQtyUnit(difference_qty, sheetRow.b_unit_no, sheetRow.b_unit_factor, sheetRow.m_unit_no, sheetRow.m_unit_factor, sheetRow.unit_no)
            sheetRow.b_profit_qty = res.b_qty
            sheetRow.m_profit_qty = res.m_qty
            sheetRow.s_profit_qty = res.s_qty
            sheetRow.profit_qty = res.qtyUnit
          } else if (difference_qty < 0) {
            let loss_wholesale_amount = toMoney(difference_qty * sheetRow.wholesale_price);
            let loss_cost_amount = toMoney(difference_qty * cost_price*sheetRow.unit_factor);
          //  let loss_buy_amount = toMoney(difference_qty * sheetRow.buy_price);
            sheetRow.loss_wholesale_amount = loss_wholesale_amount
            sheetRow.loss_cost_amount = loss_cost_amount
          //  sheetRow.loss_buy_amount = loss_buy_amount
            sheetRow.profit_qty = ""
            sheetRow.profit_wholesale_amount = ""
            sheetRow.profit_cost_amount = ""
            sheetRow.profit_buy_amount = ""
            sheetRow.b_profit_qty = 0
            sheetRow.m_profit_qty = 0
            sheetRow.s_profit_qty = 0
            var res = this.getQtyUnit(difference_qty, sheetRow.b_unit_no, sheetRow.b_unit_factor, sheetRow.m_unit_no, sheetRow.m_unit_factor, sheetRow.unit_no)
            sheetRow.b_loss_qty = res.b_qty
            sheetRow.m_loss_qty = res.m_qty
            sheetRow.s_loss_qty = res.s_qty
            sheetRow.loss_qty = res.qtyUnit
          } else if (difference_qty === 0) {
            sheetRow.profit_qty = ""
            sheetRow.profit_wholesale_amount = ""
            sheetRow.profit_cost_amount = ""
           // sheetRow.profit_buy_amount = ""
            sheetRow.loss_qty = ''
            sheetRow.loss_wholesale_amount = ''
            sheetRow.loss_cost_amount = ''
          //  sheetRow.loss_buy_amount = ''
            sheetRow.b_profit_qty = 0
            sheetRow.m_profit_qty = 0
            sheetRow.s_profit_qty = 0
            sheetRow.b_loss_qty = 0
            sheetRow.m_loss_qty = 0
            sheetRow.s_loss_qty = 0
          }
        })
      }
    },
    handleShowSheetRowsForOtherSheet() {
      if (this.sheet.branch_id === '') {
        Toast.fail('请选择仓库')
        return
      }
      if (this.sheet.approve_time !== '' || this.sheet.red_flag !== '') {
        return
      }
      this.$refs.inventoryChangeForOtherSheetRef.handleShowPopup()
    },
    handleFillItem(items) {
      let addItemIds = items.map(item => item.item_id).join(',')
      let param = {
        classID: null,
        searchStr: '',
        branchID: this.sheet.branch_id,
        showStockOnly: false,
        pageSize: items.length,
        startRow: 0,
        brandIDs: '',
        addItemIds: addItemIds
        }
      this.handleAppSheetInventoryChangeGetItemList(param, (apiData) => {
        items.forEach(item => {
          let apiItemInfo = apiData.find(apiDataItem => apiDataItem.item_id === item.item_id)
          apiItemInfo.item_images = this.handleImage(apiItemInfo.item_images)
          if (apiItemInfo) {
            this.handleItemToSheetRows(item, apiItemInfo)
          }
        })
      })
    },
    async handleAppSheetInventoryChangeGetItemList(param, callBack) {
      await AppSheetInventoryChangeGetItemList(param).then(res => {
        if (res.result !== "OK") {
          Toast(res.msg)
          return
        }
        if (callBack) callBack(res.data)
      }).catch(err => {
        Toast("获取商品列表失败")
      })
    },

    handleItemToSheetRows(row, apiItemInfo) {
      let itemObj = JSON.parse(JSON.stringify(apiItemInfo))
      itemObj.isSelectFlag = false
      itemObj.distinctStockFlag = false
      if(apiItemInfo.b_unit_no) itemObj.b_unit_qty = '' // 录入数量
      if(apiItemInfo.m_unit_no) itemObj.m_unit_qty = '' // 录入数量
      itemObj.s_unit_qty = '' // 录入数量

      let b_qty = 0, m_qty = 0, s_qty = 0;

      if (row.b_unit_no && row.b_unit_factor) {
        b_qty = parseInt(Number(row.s_qty) / Number(row.b_unit_factor))
        s_qty = Number(row.s_qty) % Number(row.b_unit_factor)
        if (row.m_unit_no && row.m_unit_factor) {
          m_qty = parseInt(Number(s_qty) / Number(row.m_unit_factor))
          s_qty = Number(s_qty) % Number(row.m_unit_factor)
        }
      }
      else {
        s_qty = Number(row.s_qty);
      }
      itemObj.b_unit_qty = b_qty
      itemObj.m_unit_qty = m_qty
      itemObj.s_unit_qty = s_qty
      itemObj.remark = '' // 录入数量
      if(itemObj.mum_attributes) {
        if(!itemObj.mum_attributes.forEach) itemObj.mum_attributes=JSON.parse(itemObj.mum_attributes)
        if(itemObj.mum_attributes.find(attr=>attr.distinctStock)) {
          itemObj.distinctStockFlag = true
        }
      }
      let tempSheetRows = []
      rowUnitsProps2Array(itemObj)

      for (let i = 0; i < tempSheetRows.length; i++) {
        let tempItem = tempSheetRows[i]
        
        var costPriceType =window.getSettingValue('costPriceType')
        var cost_price=0
        if (costPriceType === '2') {
            cost_price = tempItem.cost_price_avg
          }
          else if (costPriceType === '3') {
            cost_price = tempItem.cost_price_buy
          }
          else if (costPriceType === '1') {
            cost_price = tempItem.cost_price_prop
          }
          else if (costPriceType === '4') {
            cost_price = tempItem.cost_price_recent
          }

        tempItem.cost_price=cost_price
        tempItem.cost_amount=cost_price * tempItem.unit_factor * tempItem.quantity
        
        let pushFlag = true
        for (let j = 0; j < this.sheet.sheetRows.length; j++) {
          let currentSheetRow = this.sheet.sheetRows[j]
          if (currentSheetRow.nanoid && currentSheetRow.nanoid.length > 2) {
            if (currentSheetRow.nanoid === tempItem.nanoid) {
              currentSheetRow.son_item_id = tempItem.son_item_id
              currentSheetRow.son_item_name = tempItem.son_item_name
              currentSheetRow.item_id = tempItem.item_id
              currentSheetRow.item_name = tempItem.item_name
              currentSheetRow.quantity = tempItem.quantity
              // 此处需要测试
              currentSheetRow.wholesale_amount = tempItem.wholesale_amount
              currentSheetRow.cost_amount_avg = tempItem.cost_amount_avg
              currentSheetRow.buy_amount = tempItem.buy_amount
              pushFlag = false
            }
          } else {
            if (
              tempItem.item_id === currentSheetRow.item_id &&
              tempItem.unit_no === currentSheetRow.unit_no
            ) {
              if (tempItem.isSelectFlag) {
                this.sheet.sheetRows[j] = { ...tempItem }
                pushFlag = false
              } else {
                if (tempItem.remark === currentSheetRow.remark) {
                  this.sheet.sheetRows[j].quantity = Number(this.sheet.sheetRows[j].quantity) + Number(tempItem.quantity)
                  this.sheet.sheetRows[j].wholesale_amount = Number(this.sheet.sheetRows[j].wholesale_amount) + Number(tempItem.wholesale_amount)
                  this.sheet.sheetRows[j].cost_amount_avg = Number(this.sheet.sheetRows[j].cost_amount_avg) + Number(tempItem.cost_amount_avg)
                  this.sheet.sheetRows[j].buy_amount = Number(this.sheet.sheetRows[j].buy_amount) + Number(tempItem.buy_amount)
                  pushFlag = false
                }
              }
              break
            }
          }

        }
        if (pushFlag) {
          this.sheet.sheetRows.push(tempItem)
        }
      }
      this.handleSelectedSheetRows('BS')
      function rowUnitsProps2Array(item) {
        if (item.b_unit_no && item.b_unit_qty) {
          tempSheetRows.push({
            b_barcode: item.b_barcode,
            m_barcode: item.m_barcode,
            s_barcode: item.s_barcode,
            b_unit_factor: item.b_unit_factor,
            m_unit_factor: item.m_unit_factor,
            s_unit_factor: 1,
            item_id: item.item_id,
            item_name: item.item_name,
            unit_no: item.b_unit_no,
            unit_factor: item.b_unit_factor,
            inout_flag: -1,
            quantity: item.b_unit_qty,
            wholesale_price: item.b_wholesale_price,
            wholesale_amount: Number(item.b_unit_qty) * Number(item.b_wholesale_price),
            cost_price_prop: item.cost_price_prop,  
            cost_price_avg: Number(item.cost_price_avg),
            cost_price_buy: item.cost_price_buy,
            cost_price_recent: item.cost_price_recent,
            cost_price_prop_unit: (Number(item.cost_price_prop)*item.b_unit_factor)||0,
            cost_price_avg_unit: (Number(item.cost_price_avg)*item.b_unit_factor)||0,
            cost_price_buy_unit: (Number(item.cost_price_buy)*item.b_unit_factor)||0,
            cost_price_recent_unit:(Number(item.cost_price_recent)*item.b_unit_factor)||0,

          //  buy_price:item.cost_price_buy,
          //  cost_amount_buy: Number(item.b_unit_qty) * Number(item.b_buy_price),
            stock_qty_unit: item.current_qty,
            remark: item.bremark,
            mum_attributes: item.mum_attributes,
            itemImages: item.item_images,
            unit_type: 'b',
            son_mum_item: item?.son_mum_item !== undefined ? item.son_mum_item : item.item_id,
            nanoid: "b_" + (item.nanoid ? item.nanoid : '')
          })
        }
        if (item.m_unit_no && item.m_unit_qty) {
          tempSheetRows.push({
            b_barcode: item.b_barcode,
            m_barcode: item.m_barcode,
            s_barcode: item.s_barcode,
            b_unit_factor: item.b_unit_factor,
            m_unit_factor: item.m_unit_factor,
            s_unit_factor: 1,
            item_id: item.item_id,
            item_name: item.item_name,
            unit_no: item.m_unit_no,
            unit_factor: item.m_unit_factor,
            inout_flag: -1,
            quantity: item.m_unit_qty,
            wholesale_price: item.m_wholesale_price,
            wholesale_amount: Number(item.m_unit_qty) * Number(item.m_wholesale_price),
            cost_price_prop: item.cost_price_prop,  
            cost_price_avg: Number(item.cost_price_avg),
            cost_price_buy: item.cost_price_buy,
            cost_price_recent: item.cost_price_recent,
            cost_price_prop_unit: (Number(item.cost_price_prop)*item.m_unit_factor)||0,
            cost_price_avg_unit: (Number(item.cost_price_avg)*item.m_unit_factor)||0,
            cost_price_buy_unit: (Number(item.cost_price_buy)*item.m_unit_factor)||0,
            cost_price_recent_unit:(Number(item.cost_price_recent)*item.m_unit_factor)||0,

            cost_amount_avg: Number(item.m_unit_qty) * Number(item.m_unit_factor) * Number(item.cost_price_avg),
           // cost_amount_prop: '', // 未启用 
            buy_price: item.m_buy_price,
            buy_amount: Number(item.m_unit_qty) * Number(item.m_buy_price),
            stock_qty_unit: item.current_qty,
            remark: item.mremark,
            mum_attributes: item.mum_attributes,
            itemImages: item.item_images,
            unit_type: 'm',
            son_mum_item: item?.son_mum_item !== undefined ? item.son_mum_item : item.item_id,
            nanoid: "m_" + (item.nanoid ? item.nanoid : '')
          })
        }
        if (item.unit_no && item.s_unit_qty) {
          tempSheetRows.push({
            b_barcode: item.b_barcode,
            m_barcode: item.m_barcode,
            s_barcode: item.s_barcode,
            b_unit_factor: item.b_unit_factor,
            m_unit_factor: item.m_unit_factor,
            s_unit_factor: 1,
            item_id: item.item_id,
            item_name: item.item_name,
            unit_no: item.unit_no,
            unit_factor: 1,
            inout_flag: -1,
            quantity: item.s_unit_qty,
            wholesale_price: item.s_wholesale_price,
            wholesale_amount: Number(item.s_unit_qty) * Number(item.s_wholesale_price),
            cost_price_prop: item.cost_price_prop,  
            cost_price_avg: Number(item.cost_price_avg),
            cost_price_buy: item.cost_price_buy,
            cost_price_recent: item.cost_price_recent,
            cost_price_prop_unit: (Number(item.cost_price_prop)*item.m_unit_factor)||0,
            cost_price_avg_unit: (Number(item.cost_price_avg)*item.m_unit_factor)||0,
            cost_price_buy_unit: (Number(item.cost_price_buy)*item.m_unit_factor)||0,
            cost_price_recent_unit:(Number(item.cost_price_recent)*item.m_unit_factor)||0,

            cost_amount_avg: Number(item.s_unit_qty) * Number(item.cost_price_avg),
            buy_price: item.s_buy_price,
            buy_amount: Number(item.s_unit_qty) * Number(item.s_buy_price),
            stock_qty_unit: item.current_qty,
            remark: item.sremark,
            mum_attributes: item.mum_attributes,
            itemImages: item.item_images,
            unit_type: 's',
            son_mum_item: item?.son_mum_item !== undefined ? item.son_mum_item : item.item_id,
            nanoid: "s_" + (item.nanoid ? item.nanoid : '')
          })
        }
      }
    }
  }
}

</script>

<style lang="less" scoped>
@flex_w: {
  display: flex;
  flex-wrap: wrap;
};
@flex_a_j: {
  display: flex;
  align-items: center;
  justify-content: center;
};
@flex_a_bw: {
  display: flex;
  align-items: center;
  justify-content: space-between;
};
.wrapper {
  width: 100%;
  height: 100%;
  box-sizing: border-box;
  background-color: #fff;
  display: flex;
  flex-direction: column;
  .wrapper-head {
    .font-action {
      padding-right: 10px;
    }
  }
  .content {
    width: 100%;
    box-sizing: border-box;
    background-color: #fff;
    overflow-y: auto;
    display: flex;
    flex-direction: column;
    padding: 10px 10px 60px 10px;
    .content-query {
      box-sizing: border-box;
      width: 100%;
      display: flex;
      flex-direction: column;
      padding: 10px 15px 3px 15px;
      margin-bottom: 10px;
      .content-query-item {
        height: 25px;
        line-height: 25px;
        display: flex;

        input {
          height: 100%;
          width: 100%;
          padding: 0 10px 4px 0px;
          border: none;
          font-size: 15px;
          line-height: 35px;
          color: #333333;
          vertical-align: top;
          border-bottom: 1px solid #eee;
          text-align: right;
        }

        .van-icon {
          width: 30px;
          text-align: center;
          font-size: 22px;
          display: flex;
          align-items: center;
          justify-content: center;
          color: #aaa;
          background-color: #ffffff;
        }
      }
    }
    .public_query_title {
      background: #ffffff;
    }
    .public_query_title_t {
      height: 25px;
      line-height: 25px;
      font-size: 15px;
      color: #000000;
      padding: 10px 10px 0;
      @flex_a_bw();
    }
  }
  .lowItem {
    width: 300px;
    height: auto;
    overflow: hidden;
    padding: 10px;

    h4 {
      height: 40px;
      font-size: 16px;
      font-weight: 500;
      color: #333333;
      @flex_a_j();
    }

    .lowItem_ul {
      height: auto;
      overflow: hidden;
      margin-bottom: 10px;
      .lowItem_ull{
        white-space: nowrap;
      }

      li {
        height: auto;
        overflow: hidden;
        padding: 10px;
        font-size: 14px;
        @flex_a_bw();
        border-bottom: 1px solid #f2f2f2;
      }

      li:last-child {
        border-bottom: none;
      }
    }
  }
  .footer {
    width: 100%;
    height: 50px;
    position: absolute;
    bottom: 0;
    box-shadow: 0 1px 5px rgba(100, 100, 100, 0.2);
    background-color: #fff;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 10px;
    box-sizing: border-box;

    .footer_input {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 190px;
      height: 50px;
    }

    .footer_iconBt {
      display: flex;
      align-items: center;
      justify-content: center;
      background-color: transparent;
      border: none;
      margin-left: 20px;
      width: 50px;
      height: 50px;
    }
  }
  .van_popup {
    height: 100%;
    overflow: hidden;
  }
  .other_operate {
    width: 100%;
    height: auto;
    .other_operate_content {
      height: 40px;
      vertical-align: top;
      margin-bottom: 15px;
      @flex_a_j();
      button {
        width: 100px;
        height: 100%;
        vertical-align: top;
        margin: 0 15px;
      }
    }
  }
}
</style>
