package com.lai.geolocation.w3;

import org.json.JSONException;
import org.json.JSONObject;

import android.util.Log;

public class PositionOptions {

  private static final String TAG = "PositionOptions";

  Boolean enableHighAccuracy = false;
  long maximumAge;
  long timeout;
  String coorType;
  String positionMode;
  long gpsTimeout;

  public PositionOptions(JSONObject options) {
    try {
      this.enableHighAccuracy = options.getBoolean("enableHighAccuracy");
    } catch (JSONException e) {
      Log.v(TAG, "enableHighAccuracy 未定义");
    }
    try {
      this.coorType = options.getString("coorType");
    } catch (JSONException e) {
      Log.v(TAG, "coorType 未定义");
    }
    try {
      this.positionMode = options.getString("positionMode");
    } catch (JSONException e) {
      Log.v(TAG, "positionMode 未定义");
      this.positionMode = "gps-net"; // 默认值
    }
    try {
      this.gpsTimeout = options.getLong("gpsTimeout");
    } catch (JSONException e) {
      Log.v(TAG, "gpsTimeout 未定义");
      this.gpsTimeout = 4000; // 默认值4秒
    }
  }

  public String getCoorType() {
    return this.coorType;
  }

  public PositionOptions setCoorType(String coorType) {
    this.coorType = coorType;
    return this;
  }

  public Boolean isEnableHighAccuracy() {
    return enableHighAccuracy;
  }

  public PositionOptions setEnableHighAccuracy(Boolean enableHighAccuracy) {
    this.enableHighAccuracy = enableHighAccuracy;
    return this;
  }

  public long getMaximumAge() {
    return maximumAge;
  }

  public PositionOptions setMaximumAge(long maximumAge) {
    this.maximumAge = maximumAge;
    return this;
  }

  public long getTimeout() {
    return timeout;
  }

  public PositionOptions setTimeout(long timeout) {
    this.timeout = timeout;
    return this;
  }
  
  // 注意：方法名改为小写开头以匹配调用代码
  public String getpositionMode() {
    return positionMode;
  }
  
  public PositionOptions setpositionMode(String positionMode) {
    this.positionMode = positionMode;
    return this;
  }
  
  public long getGpsTimeout() {
    return gpsTimeout;
  }
  
  public PositionOptions setGpsTimeout(long gpsTimeout) {
    this.gpsTimeout = gpsTimeout;
    return this;
  }
}