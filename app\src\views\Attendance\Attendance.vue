<template>
  <div class="pages">
    <van-nav-bar
      title="考勤打卡"
      left-arrow
      safe-area-inset-top
      @click-left="onBack"
    />
    <div class="header">
    <div class="header-left">
       <div class="operName">{{operName}}</div>
       <div class="curDay">{{formatDay()}}</div>
    </div>
    <div class="header-right" v-if="isBoss()" @click="staffRecordsClick">
        员工考勤
    </div>
    <div class="header-right" v-else @click="myRecordsClick">
        我的考勤
    </div>
    </div>

    <!-- 主要内容区域 -->
    <div class="main-content">
      <div v-if="groupInfo" class="sign-btn-container">
       <div  v-if="canUseMultiAttendance">
            <!-- 已签到状态 - 纯文字显示 -->
            <div v-if="!m_canSignIn" class="status-text signed">
                {{signInTimeText}}
            </div>
            <!-- 签到按钮 - 可点击或禁用状态 -->
            <!-- 正常签到按钮 -->
            <div v-if="m_canSignIn && !isPositioning" @click="attendanceInBtnClick(displaySchedule.id)" class="attendanceBtn">
                <div>签到</div>
                <div>{{displaySchedule.start_time}}</div>
            </div>
            <!-- 定位中状态 - 纯文字显示 -->
            <div v-if="m_canSignIn && isPositioning" class="positioning-text">
                定位中,请稍候..
            </div>
            <!-- 未签到状态 - 纯文字显示 -->
            <div v-if="!m_hasSignedIn&&!m_hasSignedOut && !isPositioning" class="status-text unsigned">
                未签到
            </div>
            <!-- 签退按钮 - 可点击或禁用状态 -->
            <!-- 正常签退按钮 -->
            <div v-if="m_canSignOut && !isPositioning" @click="attendanceOutBtnClick(displaySchedule.id)" class="attendanceBtn">
                <div>签退</div>
                <div>{{displaySchedule.end_time}}</div>
            </div>
            <!-- 定位中状态 - 纯文字显示 -->
            <div v-if="m_canSignOut && isPositioning" class="positioning-text">
                定位中,请稍候..
            </div>
            <!-- 已签退状态 - 纯文字显示 -->
            <div v-if="m_hasSignedOut" class="status-text signed">
                {{signOutTimeText}}
            </div>
       </div>
        <div v-else class="sign-btn-container">
        <!-- 已签到状态 - 纯文字显示 -->
        <div v-if="!canSignIn" class="status-text signed">
            {{signInTimeText}}
        </div>
        <!-- 签到按钮 - 可点击或禁用状态 -->
        <!-- 正常签到按钮 -->
        <div v-if="canSignIn && !isPositioning" @click="attendanceInBtnClick()" class="attendanceBtn">
            <div>签到</div>
            <div>{{groupInfo.check_start_time}}</div>
        </div>
        <!-- 定位中状态 - 纯文字显示 -->
        <div v-if="canSignIn && isPositioning" class="positioning-text">
            定位中,请稍候..
        </div>
        <!-- 未签到状态 - 纯文字显示 -->
        <div v-if="!hasSignedIn&&!hasSignedOut && !isPositioning" class="status-text unsigned">
            未签到
        </div>
        <!-- 签退按钮 - 可点击或禁用状态 -->
        <!-- 正常签退按钮 -->
        <div v-if="canSignOut && !isPositioning" @click="attendanceOutBtnClick()" class="attendanceBtn">
            <div>签退</div>
            <div>{{groupInfo.check_end_time}}</div>
        </div>
        <!-- 定位中状态 - 纯文字显示 -->
        <div v-if="canSignOut && isPositioning" class="positioning-text">
            定位中,请稍候..
        </div>
        <!-- 已签退状态 - 纯文字显示 -->
        <div v-if="hasSignedOut" class="status-text signed">
            {{signOutTimeText}}
        </div>
        </div>

      </div>
    </div>
    <!-- 主要内容区域结束 -->
<van-popup position="bottom" :style="{ height: '75%' }" v-model="showStaffs">
    <div class="staff-container">
        <div class="staff-item" @click="selectedStaffClick(staff)" v-for="staff,index in staffs" :key="index">
            <div>{{staff.oper_name}}</div>
            <div style="color:#aeaeae">></div>
        </div>
    </div>
  </van-popup>
  <van-popup position="bottom"  :style="{ height: '75%',borderRadius:'10px 10px 0px 0px ' }" v-if="showAttendIn" v-model="showAttendIn" @close="onAttendInPopupClose">
        <attendance-sign ref="attendanceSign"  @signClick="confirmSignIn" :signBtnText="'确认签到'" :groupInfo="groupInfo" :curPositionInfo="curPositionInfo" :isSigning="isSigning"></attendance-sign>
  </van-popup>
  <van-popup position="bottom"  :style="{ height: '75%',borderRadius:'10px'  }" v-if="showAttendOut" v-model="showAttendOut" @close="onAttendOutPopupClose">
        <attendance-sign ref="attendanceSign"  @signClick="confirmSignOut" :signBtnText="'确认签退'" :groupInfo="groupInfo" :curPositionInfo="curPositionInfo" :isSigning="isSigning"></attendance-sign>
  </van-popup>
  <!-- <van-popup position="bottom" :style="{ height: '75%' }" v-model="showNaviPop">
        <navi-popup :targetPoint="{longitude:groupInfo.check_longitude,latitude:groupInfo.check_latitude}" :circleRadius='Number(groupInfo.check_distance)'></navi-popup>
  </van-popup> -->
   <van-popup position="top" :style="{ height: '40%' }" v-if="showDetail" v-model="showDetail">
        <attendance-detail :attendanceInfo="selectedAttendanceInfo"></attendance-detail>
   </van-popup>
   <van-popup position="bottom" style="border-radius:10px;" :style="{ height: '35%' }" v-model="showAttendRecords">
        <div><b>考勤汇总</b></div>
        <div class="month-count">
            <div style="display:flex;flex-direction:row;justify-content:space-between;" v-for="key,index in Object.keys(MonthCountMap)" :key='index'>
                <div>{{key}}</div>
                <div>{{MonthCountMap[key]}}</div>
            </div>
        </div>
        <div><b>查看明细</b></div>
        <div @click="chooseMonth=true">请选择年月:{{attendanceMonth.format("yyyy-MM")}}  ></div>
        <van-popup position="bottom" :style="{ height: '75%' }" v-model="chooseMonth">
        <date-time-picker @confirm="confirmDate"  v-model="attendanceMonth"  type="year-month"></date-time-picker>

        </van-popup>
        <div class="record-container">
        <van-calendar :show-confirm="false"  v-model="showCalendar" @confirm="confirmCalendarDay" :min-date="calendarMinDay" :formatter="formatter" :max-date="calendarMaxDay"></van-calendar>
            <!-- <div class="left">
                <van-picker  v-model="selectDayKey" @change="selectRecordDayPickerChange" title="日期" :columns="recordPickerColumns"></van-picker>
            </div>
            <div class="right">
                <div class="record-item" v-for="record,index in theDayAttendanceRecords" :key="index">
                    <div class="record-row">
                         <div>签到</div>
                         <div>{{record.start_time?new Date(record.start_time).format("hh:mm"):"无"}}</div>
                    </div>
                    <div  class="record-row">
                        <div>签退</div>
                        <div>{{record.end_time?new Date(record.end_time).format("hh:mm"):"无"}}</div>
                    </div>
                    <div  class="record-row">
                        <div>状态</div>
                        <div>{{record.status}}</div>
                    </div>
                    
                </div>
            </div> -->
       </div>
  </van-popup>
  <div class="my-toast" v-show="showNoPictureTip">
        请先拍照再进行签到！
  </div>
 <div class="my-toast" v-show="showDayStatus">
        {{clickDayStatus}}
  </div>
  </div>
</template>
<script>
import {
  Calendar,
  NavBar,
  Popup,
  Picker,
  Toast
} from "vant";
import AttendanceSign from '../Attendance/AttendanceSign'
import Position from '../../components/Position.js'
import NaviPopup from './NaviPopup.vue'
import { DatetimePicker,Dialog } from 'vant';
import AttendanceDetail from '../Attendance/AttendanceDetail'
import {GetAttendanceGroup,GetOperators,SaveSign,GetAttendanceRecords, ApiGetMyGroupInfo, GetTodayAttendanceInfo, GetTodayAttendanceInfos, GetHeartBeat} from '../../api/api.js'
import Time from '../service/Time'
import moment from 'moment'
// DebugLocationHelper会自动初始化，无需手动导入
export default {
    name:"Attendance",
    components:{
        "van-nav-bar":NavBar,
        "van-popup":Popup,
        "van-picker":Picker,
        "attendance-sign":AttendanceSign,
        'van-calendar':Calendar,
        "date-time-picker":DatetimePicker,
        "attendance-detail":AttendanceDetail,
        NaviPopup
    },
 
    data(){
        return{
            operName:"",
            curPositionInfo:{},
            groupInfo: null,
            todayAttendanceInfos:[],
            isSigning:false,
            showAttendIn:false,
            showAttendOut:false,
            showAttendRecords:false,
            showCalendar:false,
            showNaviPop:false,
            chooseMonth:false,
            attendanceMonth:new Date(),
            attendanceRecords:[],
            showType:'my',
            attendanceMap:{},
            selectedAttendanceInfo:{},
            showDetail:false,
            showStaffs:false,
            selectedStaff:{},
            selectDayKey:"",
            staffs:[],
            signingScheduleId:'',
            schedules:[],
            showDayStatus:false,
            showNoPictureTip:false,
            clickDayStatus:"",
            signStatus:{
                canSign:false,
                tip:""
            },
            signOutStatus:{
                canSignOut:false,
                tip:""
            },
            // 防重复点击状态
            isPositioning: false  // 定位中状态
        }
    },
    computed:{
        calendarMinDay() {
            var currentDate = this.attendanceMonth;
            var currentYear = currentDate.getFullYear();
            var currentMonth = currentDate.getMonth() ;
            var curMonthFirstDay = new Date(currentYear, currentMonth, 1);
            return curMonthFirstDay;
        },
        calendarMaxDay() {
            var currentDate = this.attendanceMonth;
            var currentYear = currentDate.getFullYear();
            var currentMonth = currentDate.getMonth();
            var lastDay = new Date(currentYear, currentMonth, 0);
            console.log(new Date(currentYear, currentMonth, lastDay.getDate()))
            return new Date(currentYear, currentMonth, lastDay.getDate());
        },
        canUseMultiAttendance(){
            return this.schedules&&this.schedules.length!=0&&typeof this.displaySchedule!='undefined'
        },
        displaySchedule(){
            var _displaySchedule = null
            console.log("11111111")
            if(this.lastTodayAttendanceInfo!=null && this.lastTodayAttendanceInfo.start_time && !this.lastTodayAttendanceInfo.end_time && this.lastTodayAttendanceInfo.schedule_id  ){
                    const lastTodayAttendanceSchedule = this.schedules.filter(schedule=>schedule.id === this.lastTodayAttendanceInfo.schedule_id)[0]
                    if(lastTodayAttendanceSchedule){
                        _displaySchedule = this.schedules.filter(schedule=>schedule.id === this.lastTodayAttendanceInfo.schedule_id)[0]
                    }
                }else{
                    console.log("11111112")
                    console.log(this.schedules)
                    const hasSignedScheduleIDs = this.todayAttendanceInfos.map(info=>{return info.schedule_id})
                    //schedules中 今日未签到的 schedule_id
                    let noSignSchedules = []
                    for (let index = 0; index < this.schedules.length; index++) {
                        const element = this.schedules[index];
                        if(hasSignedScheduleIDs.indexOf(element.id)==-1){
                            noSignSchedules.push(element)
                    }
                    }
                    _displaySchedule = noSignSchedules[0]
                }   
         
        //    this.schedules.forEach(schedule => {
             
        //    });
           return _displaySchedule
          
        },
        m_canSignIn(){
            console.log(this.lastTodayAttendanceInfo,this.displaySchedule)
            return this.lastTodayAttendanceInfo==null ||(this.lastTodayAttendanceInfo!=null && this.lastTodayAttendanceInfo.schedule_id !== this.displaySchedule.id) 
        },
        m_hasSignedIn(){
            return  this.lastTodayAttendanceInfo!=null  && this.lastTodayAttendanceInfo.schedule_id === this.displaySchedule.id &&  this.lastTodayAttendanceInfo.start_time
        },
        m_canSignOut(){
            return this.lastTodayAttendanceInfo!=null  && this.lastTodayAttendanceInfo.schedule_id === this.displaySchedule.id &&  this.lastTodayAttendanceInfo.start_time
        },
        m_hasSignedOut(){
            return this.lastTodayAttendanceInfo!=null  && this.lastTodayAttendanceInfo.schedule_id === this.displaySchedule.id &&  this.lastTodayAttendanceInfo.end_time
        },
        theDayAttendanceRecords(){
            return this.attendanceRecords[this.selectDayKey]
        },
        recordPickerColumns(){
            return Object.keys(this.attendanceRecords).map(key=>{
                return { text: key, value: key }
            })
        },
        MonthCountMap(){
            let monthCountMap={}
            Object.keys(this.attendanceRecords).map(key=>{
                this.attendanceRecords[key].map(record=>{
                    if(typeof monthCountMap[record.status]!='undefined'){
                        monthCountMap[record.status]++
                    } else{
                        monthCountMap[record.status]=1
                    }
                })
              
            })
            return monthCountMap
        },
        lastTodayAttendanceInfo(){
            return this.todayAttendanceInfos.length===0?null:this.todayAttendanceInfos[this.todayAttendanceInfos.length - 1]
        },
        isfinishRecord(){
            return this.lastTodayAttendanceInfo.end_time
        },
        canSignIn(){
            return !this.lastTodayAttendanceInfo
        },
        hasSignedIn(){
            return  this.lastTodayAttendanceInfo && !this.lastTodayAttendanceInfo.end_time
        },
        canSignOut(){
            return this.lastTodayAttendanceInfo && !this.lastTodayAttendanceInfo.end_time
        },
        hasSignedOut(){
            return this.lastTodayAttendanceInfo && this.lastTodayAttendanceInfo.end_time
        },
        // 格式化签到时间显示
        signInTimeText(){
            if(!this.lastTodayAttendanceInfo || !this.lastTodayAttendanceInfo.start_time) return ''
            const time = new Date(this.lastTodayAttendanceInfo.start_time)
            const hours = time.getHours().toString().padStart(2, '0')
            const minutes = time.getMinutes().toString().padStart(2, '0')
            return `${hours}:${minutes} 已签到`
        },
        // 格式化签退时间显示
        signOutTimeText(){
            if(!this.lastTodayAttendanceInfo || !this.lastTodayAttendanceInfo.end_time) return ''
            const time = new Date(this.lastTodayAttendanceInfo.end_time)
            const hours = time.getHours().toString().padStart(2, '0')
            const minutes = time.getMinutes().toString().padStart(2, '0')
            return `${hours}:${minutes} 已签退`
        }
    },
    async mounted(){
        console.log(this.selectDayKey)
        var operInfo=this.getOperInfo()
        this.operName=operInfo.oper_name
        const toast = Toast.loading({
        message: '考勤组信息正在加载中...',
        duration:0
      })
        this.todayAttendanceInfos = await this.getTodayAttendanceInfos()
        this.groupInfo = await this.getMyGroupInfo()
        toast.clear()
        console.log(this.groupInfo)
        this.schedules = JSON.parse(this.groupInfo.schedule)
        if(this.displaySchedule){
            this.displaySchedule.group_id = this.groupInfo.group_id
            this.groupInfo = this.displaySchedule
        }
        console.log("this.displaySchedule",this.displaySchedule)
        // this.checkAndChangeSignStatus()
        // this.checkAndChangeSignOutStatus()
       if(this.todayAttenceLeave()){
            this.forbidSign("请假")
            this.forbidSignOut("请假")
        }
        
    },

    methods:{
      confirmCalendarDay(e){
         const day = moment(e).format("YYYY-MM-DD")
         console.log(day)
         const status = this.attendanceRecords[day][0].status
         this.showDayStatus = true
         this.clickDayStatus = status
         setTimeout(()=>{
                this.showDayStatus = false
        },3000)
      },
    formatter (calendarDay)  {
      const month = ((calendarDay.date.getMonth() + 1)+"").padStart(2,"0");
      const day = ((calendarDay.date.getDate() )+"").padStart(2,"0");
    Object.keys(this.attendanceRecords).filter(key=>new Date(key).getTime() < new Date().getTime()).map(key=>{
 
        const record_month = key.split("-")[1]
        const record_day = key.split("-")[2]
        if(record_day == day && record_month == month){
            calendarDay.bottomInfo = this.attendanceRecords[key][0].status == "正常"?"正常":"异常"
        }
        calendarDay.topInfo = this.attendanceRecords[key]?.length+"条"
    })
    //   if (month === 10) {
    //     if (date === 1) {
    //       day.topInfo = '劳动节';
    //     } else if (date === 4) {
    //       day.topInfo = '青年节';
    //     } else if (date === 11) {
    //       day.text = '今天';
    //     }
    //   }

    //   if (day.type === 'start') {
    //     day.bottomInfo = '入住';
    //   } else if (day.type === 'end') {
    //     day.bottomInfo = '离店';
    //   }

      return calendarDay;
    },
    selectRecordDayPickerChange(e){
        this.selectDayKey = e.getValues()[0].value
    },
    async getPosition(){
        try{
            const params = {
                message: "需要定位权限来获取考勤位置",
                key: "positionAttendance",
                getAddr: true,
                needTryTimes: 3
            };
            this.curPositionInfo = await Position.getPosition(params);
            bGetPositionSuccess = true
        }catch(e){
            console.log("reject",e)
            bGetPositionSuccess = false
            if(!bGetPositionSuccess){
                Toast.fail({
                    duration: 5000,
                    message: e.msg,
                })
            }
        }
        return this.curPositionInfo
    },
    async getTodayAttendanceInfos(){
        const operID = this.$store.state.operInfo.oper_id
        const param={
            operKey: this.$store.state.operKey,
            operID
        }
        return await (await GetTodayAttendanceInfos(param)).data
    },
    async getMyGroupInfo(){
      const operID = this.$store.state.operInfo.oper_id
      //const {longitude,latitude} = await this.getPosition()
      let params={
        message: "需要定位权限进行考勤打卡",
        key: "positionAttendance"
        }
      var res = await Position.getPosition(params)
      if(res.result!="OK"){
         Toast.fail(res.msg)
         return
      }
      this.curPositionInfo = res
      const param={
          operKey: this.$store.state.operKey,
          longitude:res.longitude,
          latitude:res.latitude,
          operID
      }

      //var res =  await ApiGetMyGroupInfo(param).data
      var res =  await ApiGetMyGroupInfo(param)
      var data=res.data
      data.check_distance =parseFloat(data.check_distance)
      return data
     // return await (await ApiGetMyGroupInfo(param)).data
    },
    getTodayLastAttendance(){
        return this.todayAttendanceInfos[ this.todayAttendanceInfos.length - 1]
    },

    isBoss(){
      return window.isBoss()
    },
    parseTime(time){
        return isiOS? time?.replace(/-/g, '/'):time
    },
    abnormal(item){
        return item.status.split(",").map(s_item=>{
            if(s_item==='CD'){
                return "迟到"
            }
            if(s_item==='ZT'){
                return "早退"
            }
        }).join(",")
    },

    async selectedStaffClick(staff){
        this.selectedStaff=staff
        this.showStaffs=false
        await this.seeAttendanceRecords(staff.oper_id)
    },
    staffRecordsClick(){
        this.attendanceMap={}
        this.showStaffs=true
        this.showType='staff'
        GetOperators().then(res=>{
         const normalStatusUserData=res.data.filter(e=>Number(e.status)===1 || e.status==='')
         this.staffs=normalStatusUserData
        })
    },
    myRecordsClick(){
        this.attendanceMap={}
        this.showType='my'
        this.seeAttendanceRecords(this.$store.state.operInfo.oper_id)
    },
    showDetailClick(item){
        this.selectedAttendanceInfo=item
        this.showDetail=true
    },
    showAbnormalTip(msg){
        this.$toast(msg)
    },
    // checkAndChangeSignOutStatus(){
    //     if(!this.hasSigned()){
    //         this.forbidSignOut("未签到")
    //     }
    //     else if(this.hasSignedOut()){
    //         this.forbidSignOut("已签退")
    //     }
    //     else{
    //         this.signOutStatus.canSignOut=true
    //     }
    // },
    // checkAndChangeSignStatus(){
    //     if(this.hasSigned()){
    //         this.forbidSign("已签到")
    //     }
    //     // else if(!this.curTimeCanSign()){
    //     //     this.forbidSign(this.groupInfo.check_start_time)
    //     // }
    //     else{
    //         this.signStatus.canSign=true
    //     }
    // },

    // getAttendanceRecords(needDays){
    //     let map={}
    //     needDays.map(needDay=>{
    //        const  thisDayRecords=this.attendanceRecords.filter(attendanceRecord=>new Date(attendanceRecord.start_time).format("yyyy-MM-dd")===needDay)
    //         map[needDay]=thisDayRecords
    //     })
    //     return map
    // },
    async confirmDate(){
        this.attendanceMap={}
        this.showCalendar=true
        if(this.showType==='my'){
        await this.seeAttendanceRecords()
        }else{
        await this.seeAttendanceRecords(this.selectedStaff.oper_id)
        }
        this.chooseMonth=false
    },

    async abnormalInCheck(checkInfo,schedule_id){
        let tips=[]
        let {check_start_time,check_distance,fix_position,fix_in_position}=checkInfo
        var startTime= await this.getCurrentTimeFromServer()
        var checkStartTime = null 
        
        var checkDay = startTime.format("yyyy-MM-dd")
        if(isiOS){
            checkDay = checkDay.replace(/-/g, '/')
        }
        if(schedule_id){
            const schedule = this.displaySchedule
            checkStartTime = new Date(checkDay+" "+schedule.start_time)
        }else{
            checkStartTime = new Date(checkDay+" "+check_start_time)
        }
        //迟到
        if(Time.compare(startTime,checkStartTime)){
            tips.push("CD")
        }
        //距离过长
        const signDistance = await this.getSignDistance()
        if((fix_position==='True' ||  fix_in_position === 'True') && signDistance>check_distance){
            tips.push("IN_JLGC")
        }
        return{
            tips,
            tipString:tips.join(",")
        }
    },

    async abnormalOutCheck(checkInfo,schedule_id ){
        const {check_end_time,check_distance,fix_out_position,fix_position}=checkInfo
        let tips=[]
        var endTime= await this.getCurrentTimeFromServer()
        var checkEndTime = null 
        var checkDay = endTime.format("yyyy-MM-dd")
        if(isiOS){
            checkDay = checkDay.replace(/-/g, '/')
        }
        if(schedule_id){
            const schedule = this.schedules.filter(schedule=>schedule.id === schedule_id)[0]
            checkEndTime = new Date(checkDay +" "+schedule.end_time)
        }else{
            checkEndTime = new Date(checkDay +" "+ check_end_time)
        }
          //早退
         if(!Time.compare(endTime,checkEndTime)){
             tips.push("ZT")
         }
          //距离过长
         const signDistance=await this.getSignDistance()
         if((fix_position==='True' || fix_out_position === 'True') && signDistance>check_distance){
             tips.push("OUT_JLGC")
         }
        return{
            tips,
            tipString:tips.join(",")
        }
    },
    async getCurrentTimeFromServer(){
        const res =await GetHeartBeat({})
        if(res && res.server_time){
            return new Date(res.server_time)
        }
        return new Date()
    },
    async seeAttendanceRecords(operID){
        const query_month=this.attendanceMonth.format("yyyy-MM")
        let param={
            query_month,
            oper_id:operID
        }
        this.selectDayKey = this.parseTime(new Date().format("yyyy-MM-dd"))
        console.log(this.selectDayKey)
        let data=await GetAttendanceRecords(param)
        if(data.result==='OK'&&data.msg==='该业务员未设置考勤组！'){
            this.$toast(data.msg)
            return
        }
        console.log(data)
        if(!this.canUseMultiAttendance){
            data.rows.map(row=>{
                data.matchRowDictionary[row.day] = [row]
            })
        }
        this.attendanceRecords = data.matchRowDictionary
        console.log(this.attendanceRecords)
        this.showAttendRecords=true
    },
    async attendanceInBtnClick(scheduleID=''){
        // 防重复点击
        if (this.isPositioning) {
            return
        }

        this.isPositioning = true

        try {
            // 1. 获取定位
            let params={
                message: "需要定位权限进行考勤打卡",
                key: "positionAttendance",
                getAddr:true
            }
            this.curPositionInfo = await Position.getPosition(params)
            if (this.curPositionInfo.result != 'OK') {
                this.$toast(this.curPositionInfo.msg)
                this.isPositioning = false
                return
            }

            // 2. 检查距离限制
            let {check_longitude,check_latitude}=this.groupInfo
            var signDistance = this.getDistance(this.curPositionInfo.latitude,this.curPositionInfo.longitude,check_latitude,check_longitude)

            const {fix_position,check_distance,fix_in_position}=this.groupInfo
            if ((fix_position==='True' ||  fix_in_position === 'True') && signDistance>=check_distance) {
                this.$toast(`签到距离过长(${signDistance}米)，请前往考勤点范围${check_distance}米内进行考勤`)
                this.isPositioning = false
                return
            }

            // 3. 显示签到弹框（统一在弹框中处理所有逻辑）
            if(this.displaySchedule){
                this.displaySchedule.distance = signDistance
            }
            this.showAttendIn = true
            this.signingScheduleId = scheduleID
            this.isPositioning = false  // 定位成功后重置状态
            setTimeout(()=>{
                 this.$refs.attendanceSign.curPositionInfo = this.curPositionInfo
            },200)

        } catch (error) {
            console.error('签到过程中发生错误:', error)
            this.$toast('签到失败，请重试')
            this.isPositioning = false
        }
    },
    async attendanceOutBtnClick(scheduleID=''){
        // 防重复点击
        if (this.isPositioning) {
            return
        }

        this.isPositioning = true

        try {
            // 1. 获取定位
            const {fix_position,check_distance,fix_out_position}=this.groupInfo
            let params={
                message: "需要定位权限进行考勤打卡",
                key: "positionAttendance",
                getAddr:true
            }
            this.curPositionInfo = await Position.getPosition(params)
            if (this.curPositionInfo.result != 'OK') {
                this.$toast(this.curPositionInfo.msg)
                this.isPositioning = false
                return
            }

            // 2. 检查距离限制
            let {check_longitude,check_latitude}=this.groupInfo
            var signDistance = this.getDistance(this.curPositionInfo.latitude,this.curPositionInfo.longitude,check_latitude,check_longitude)

            if ((fix_position==='True' || fix_out_position === 'True') && signDistance>=check_distance) {
                this.$toast(`签退距离过长(${signDistance}米)，请前往考勤点范围${check_distance}米内进行考勤签退`)
                this.isPositioning = false
                return
            }

            // 3. 显示签退弹框（统一在弹框中处理所有逻辑）
            if(this.displaySchedule){
                this.displaySchedule.distance = signDistance
            }
            this.showAttendOut = true
            this.signingScheduleId = scheduleID
            this.isPositioning = false  // 定位成功后重置状态
            setTimeout(()=>{
                this.$refs.attendanceSign.curPositionInfo = this.curPositionInfo
            },200)

        } catch (error) {
            console.error('签退过程中发生错误:', error)
            this.$toast('签退失败，请重试')
            this.isPositioning = false
        }
    },
    async getSignDistance(){
        //var position = new Position(isiOS)
        //let {longitude,latitude} = await position.currentPosition()

        let params={
        message: "需要定位权限进行考勤打卡",
        key: "positionAttendance"
        }
        var res = await Position.getPosition(params)
        var longitude=res.longitude
        var latitude=res.latitude
        let {check_longitude,check_latitude}=this.groupInfo
        return this.getDistance(latitude,longitude,check_latitude,check_longitude)
        
    },
    formatDay(){
        var currentDay=new Date().format("yyyy-MM-dd")
        var week=new Date().getDay()
        var weekDictionary=['日','一','二','三','四','五','六']
        return `${currentDay} 星期${weekDictionary[week]}`
    },
    // 确认签到
    async confirmSignIn(attendanceForm){
        // 防重复提交
        if(this.isSigning){
            this.$toast("请勿重复点击")
            return
        }

        // 检查是否需要拍照
        if(this.groupInfo.photo_necessary==='True' && attendanceForm.photos.length===0){
            this.showNoPictureTip = true
            setTimeout(()=>{
                this.showNoPictureTip = false
            },3000)
            return
        }

        this.isSigning = true

        // 显示签到中的加载提示
        const loadingToast = Toast.loading({
            message: '正在签到..',
            forbidClick: true,
            duration: 0
        })

        try {
            // 构建签到表单
            attendanceForm.operKey = this.$store.state.operKey
            attendanceForm.addr = this.curPositionInfo.address
            attendanceForm.schedule_id = this.signingScheduleId
            attendanceForm.longitude = this.curPositionInfo.longitude
            attendanceForm.latitude = this.curPositionInfo.latitude
            attendanceForm.groupId = this.groupInfo.group_id

            // 检查签到状态
            const checkStatusRes = await this.abnormalInCheck(this.groupInfo, attendanceForm.schedule_id)
            attendanceForm.status = checkStatusRes.tips.join(",")

            // 提交签到
            await SaveSign(attendanceForm)

            // 关闭弹框
            this.showAttendIn = false

            // 更新加载提示为刷新信息
            loadingToast.message = '刷新考勤信息中..'
            this.todayAttendanceInfos = await this.getTodayAttendanceInfos()

            // 清除加载提示
            loadingToast.clear()

            this.isSigning = false
            this.isPositioning = false
            this.$toast('签到成功')

        } catch (error) {
            console.error('签到失败:', error)
            loadingToast.clear()
            this.$toast('签到失败，请重试')
            this.isSigning = false
            this.isPositioning = false
        }
    },

    // 确认签退
    async confirmSignOut(attendanceForm){
        // 防重复提交
        if(this.isSigning){
            this.$toast("正在处理中，请勿重复点击")
            return
        }

        // 检查是否需要拍照
        if(this.groupInfo.photo_necessary==='True' && attendanceForm.photos.length===0){
            this.showNoPictureTip = true
            setTimeout(()=>{
                this.showNoPictureTip = false
            },3000)
            return
        }

        this.isSigning = true

        // 显示签退中的加载提示
        const loadingToast = Toast.loading({
            message: '正在签退..',
            forbidClick: true,
            duration: 0
        })

        try {
            // 获取今日最后一条考勤记录
            let lastTodayAttendanceInfo = this.getTodayLastAttendance()
            if(!lastTodayAttendanceInfo || lastTodayAttendanceInfo.end_time) {
                loadingToast.clear()
                this.$toast('没有可签退的记录')
                this.isSigning = false
                this.isPositioning = false
                return
            }

            // 构建签退表单
            attendanceForm.operKey = this.$store.state.operKey
            attendanceForm.addr = this.curPositionInfo.address
            attendanceForm.schedule_id = this.signingScheduleId
            attendanceForm.longitude = this.curPositionInfo.longitude
            attendanceForm.latitude = this.curPositionInfo.latitude
            attendanceForm.flowId = lastTodayAttendanceInfo.flow_id
            attendanceForm.end_time = this.getCurrentTimeFromServer()

            // 检查签退状态
            const inStatus = lastTodayAttendanceInfo.status.split(",")
            const checkStatusRes = await this.abnormalOutCheck(this.groupInfo, attendanceForm.schedule_id)
            const status = inStatus.concat(checkStatusRes.tips)
            attendanceForm.status = status.join(",")

            // 提交签退
            await SaveSign(attendanceForm)

            // 关闭弹框
            this.showAttendOut = false

            // 更新加载提示为刷新信息
            loadingToast.message = '刷新考勤信息中..'
            this.todayAttendanceInfos = await this.getTodayAttendanceInfos()

            // 清除加载提示
            loadingToast.clear()

            this.isSigning = false
            this.isPositioning = false
            this.$toast('签退成功')

        } catch (error) {
            console.error('签退失败:', error)
            loadingToast.clear()
            this.$toast('签退失败，请重试')
            this.isSigning = false
            this.isPositioning = false
        }
    },

    hasSigned(){
        return this.todayAttendanceInfo
    },
    todayAttenceLeave(){
        return this.todayAttendanceInfo&&this.todayAttendanceInfo.attence_leave_id
    },
    // hasSignedOut(){
    //     return this.todayAttendanceInfo&&this.todayAttendanceInfo.end_time
    // },

    forbidSign(tip){
        this.signStatus.canSign=false
        this.signStatus.tip=tip
    },
    forbidSignOut(tip){
        this.signOutStatus.canSignOut=false
        this.signOutStatus.tip=tip
    },
    onBack() {
        myGoBack(this.$router)
    },

    // 签到弹窗关闭时重置状态
    onAttendInPopupClose() {
        this.isPositioning = false
    },

    // 签退弹窗关闭时重置状态
    onAttendOutPopupClose() {
        this.isPositioning = false
    },

    }
}
</script>
<style lang="less" scoped>
.header{
    width: 100%;
    display: flex;
    flex-direction: row;
    justify-content: space-around;
}
.header-left{
   margin: 6px;
    display: flex;
    flex-direction: column;
    align-items: flex-start;
}
.disabled{
    background: #f5f5f5 !important;
    color: #666 !important;
    animation: none !important;
    border-radius: 50% !important;
    opacity: 0.8;
    cursor: not-allowed !important;
}

/* 定位中文字样式 */
.positioning-text {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 120px;
    color: #666;
    font-size: 14px;
    text-align: center;
}

/* 状态文字样式 */
.status-text {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 120px;
    font-size: 16px;
    font-weight: 500;
    text-align: center;
}

/* 已签到/已签退状态 - 绿色 */
.status-text.signed {
    color: #529b2e;
}

/* 未签到状态 - 红色 */
.status-text.unsigned {
    color: #c40000;
}
.calendar-grid{
    display: grid;
    grid-template-columns: 25% 25% 25% 25%;
}
.calendar-grid-item{
    display: flex;
    flex-direction: column;
    border: 1px solid #eee;
}

.header-right{
    margin:6px;
    width: 80px;
    height: 30px;
    border: 1px;
    background: #ffece8;
    font-size: 16px;
    padding-top:6px;
    border-radius: 8px;
}
.operName{
    font-weight: 800;
}
.curDay{
    color:#777;
    font-size: 12px;
}

.main-content {
    /* 确保主要内容区域占据合适的空间 */
    min-height: calc(100vh - 120px); /* 减去导航栏和header的高度 */
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
    padding-top: 20px;
}
.attendanceBtn{
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    color: #000;
    border-radius: 50%;
    width: 120px;
    height: 120px;
    color: #940000;
    //#f66
    background-color: #fab6b6;
	animation: animated-border 1.5s infinite;
	/* 增加点击区域和视觉效果 */
	cursor: pointer;
	transition: all 0.2s ease;
	font-weight: bold;
	box-shadow: 0 4px 12px rgba(250, 182, 182, 0.4);
}

.attendanceBtn:hover:not(.disabled) {
    transform: translateY(-2px);
    box-shadow: 0 6px 16px rgba(250, 182, 182, 0.6);
}

.attendanceBtn:active:not(.disabled) {
    transform: translateY(0);
    box-shadow: 0 2px 8px rgba(250, 182, 182, 0.4);
}

/* 移动端触摸优化 */
@media (max-width: 768px) {
    .attendanceBtn {
        /* 增加移动端的点击区域 */
        width: 130px;
        height: 130px;
        /* 增加触摸反馈 */
        -webkit-tap-highlight-color: rgba(250, 182, 182, 0.3);
    }

    .sign-btn-container {
        /* 移动端增加更多间距 */
        padding: 30px 20px;
        margin-top: 40px;
    }

    /* 确保按钮在移动端屏幕中央偏上位置 */
    .main-content {
        justify-content: center;
        align-items: center;
    }
}

#baidumap{
    width: 100%;
    height: 300px;
}
.sign-btn-container{
    display: flex;
    flex-direction: row;
    width: 100%;
    justify-content: space-around;
    margin-top: 30px;
    margin-bottom: 30px;
    padding: 20px 0;
    /* 确保按钮区域在屏幕中央偏上的位置 */
    min-height: 140px;
    align-items: center;
}
.month-count{
    display: flex;
    flex-direction: column;
    padding: 10px;
}

.staff-item{
    display: flex;
    flex-direction: row;
    width: 100%;
    justify-content: space-between;
}
.staff-item>div{
    padding:10px;
}
@keyframes animated-border {
	0% {
		box-shadow: 0 0 0 0 #fad5ce;
	}

	100% {
		box-shadow: 0 0 0 20px rgba(218, 9, 9, 0);
	}
}
.my-toast{
    z-index: 9999;
    position: absolute;
    top: 50%;
    left: 20%;
    width: 200px;
    padding: 10px;
    color: #f0f0f0;
    background: #00000090;
}
.record-container{
    display: flex;
    flex-direction: row;
    justify-content: space-around;
}
.record-row{
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    margin-bottom: 10px;
}
.record-item{
    border-bottom: 1px solid;
    width: 100%;
    padding: 10px;
}
</style>
