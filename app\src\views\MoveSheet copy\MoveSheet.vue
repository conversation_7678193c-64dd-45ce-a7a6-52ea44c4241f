<template>
  <div ref="pages" class="pages">
    <van-nav-bar left-arrow safe-area-inset-top title="调拨单" @click-left="goback">
      <template #right>
        <div class="submitSalesSlip" :style="{ color: sheet.sheet_id ? '#cccccc' : '#000' }" @click="handlePopupFillFromSale">
          补货
        </div>
        <div class="submitSalesSlip" @click="btnSubmit_click">结算</div>
      </template>
    </van-nav-bar>
    <div ref="public_box3" class="public_box3" :style="{height:publicBox3Height}">
      <div class="public_header">

        <van-row gutter="10" v-if="sheet.sheet_no">
          <van-col span="12">
            <div class="public_header_l">
              <p>{{ sheet.sheet_no }}</p>
            </div>
          </van-col>
          <van-col span="12">
            <div class="public_header_r">
              <p>{{ sheet.make_time }}</p>
            </div>
          </van-col>
        </van-row>
        <van-row gutter="10">
          <van-col span="12">
            <van-field v-model="sheet.from_branch_name" label-width="40px" class="brand_style" placeholder="出仓" readonly @click="
                  () => {
                    if (!this.canEdit) return;
                    popFromBranchList = true;
                  }
                ">
              <template #right-icon>
                <i class="iconfont">&#xe6a2;</i>
              </template>
            </van-field>
          </van-col>
          <van-col span="12">
            <van-field v-model="sheet.to_branch_name" label-width="40px" class="brand_style" placeholder="入仓" readonly @click="
                  () => {
                    if (!this.canEdit) return;
                    popToBranchList = true;
                  }
                ">
              <template #right-icon>
                <i class="iconfont">&#xe66e;</i>
              </template>
            </van-field>
          </van-col>
        </van-row>

      </div>
      <div :style="{height:publicBox3Height}">
        <div class="sales_box_box">
          <ul class="sales_box_ul">
            <div class="sheet_state approved" v-if="
                sheet.approve_time &&
                (sheet.red_flag == '' || sheet.red_flag == '0')
              ">
              <img src="../../assets/images/approved.png" />
            </div>
            <div class="sheet_state reded" v-if="sheet.red_flag == '1'">
              <img src="../../assets/images/reded.png" />
            </div>
            <van-swipe-cell v-for="(item, index) in itemRows" :key="index">
              <li @click="onRowClick(item, index)">
                <h5>
                  <span>{{index+1}}. {{ item.item_name }}</span>
                  <span style="color: #eb6f6f">￥{{ fix(item.item_total) }}</span>
                </h5>
                <div class="sales_box_ul_title">
                  <van-row>
                    <van-col span="8">
                      <p>数量</p>
                    </van-col>
                    <van-col span="8">
                      <p>批发价</p>
                    </van-col>
                    <van-col span="8">
                      <p>批发金额</p>
                    </van-col>
                  </van-row>
                </div>
                <div class="sales_box_ul_item" v-for="(item_son, index_son) in item.rowUnits" :key="index_son">
                  <van-row>
                    <van-col span="8">
                      <p>{{ item_son.quantity }}{{ item_son.unit_no }}</p>
                    </van-col>
                    <van-col span="8">
                      <p>{{ item_son.wholesale_price }}</p>
                    </van-col>
                    <van-col span="8">
                      <p>
                        {{
                          fix(
                            Number(item_son.quantity) *
                              Number(item_son.wholesale_price)
                          )
                        }}
                      </p>
                    </van-col>
                  </van-row>
                  <div style="text-align:left;margin-left:10px;color:#bbb">
                    备注: {{item_son.remark}}
                  </div>
                </div>

              </li>
              <template #right>
                <van-button v-if="canEdit" class="removeRow" square type="danger" text="删除" @click="btnRemoveRow_click(index)" />
              </template>
            </van-swipe-cell>
          </ul>
        </div>
      </div>

    </div>

    <div class="sales_footer">
      <div ref="move_total" class="move_total">
        <div class="unit-amount-container">
          <div v-for="(item, index) in sheet.total_units_amount" :key="index">
            {{ item.value }}{{ item.key }}
          </div>
        </div>
        <div class="total-amount-container">
          共{{itemRows.length}}行
          合计: {{ Object.is(toMoney(sheet.total_amount || sheet.wholesale_amount),NaN)?0:toMoney(sheet.total_amount || sheet.wholesale_amount) }}
        </div>
      </div>
      <div>
        <van-search v-model="transStr" class="van_search_style" :disabled="!canEdit ? true : false" :style="{ backgroundColor: !canEdit ? '#f2f2f2' : '' }" left-icon="" placeholder="请输入搜索关键词" @search="onAddClass()">
        </van-search>
        <van-button class="footer_iconBt" :disabled="!this.canEdit ? true : false" @click="onAddClass" style="border: none">
          <svg width="35px" height="35px" fill="#F56C6C">
            <use xlink:href="#icon-add"></use>
          </svg>
        </van-button>
        <!-- <van-button class="van_search_btns" type="info" :disabled="sheet.approve_time?true:false" @click="onAddClass">添加</van-button> -->
        <van-button class="footer_iconBt" @click="btnScanBarcode_click" style="border: none">
          <svg width="30px" height="30px" fill="#666">
            <use xlink:href="#icon-barcodeScan"></use>
          </svg>
        </van-button>

      </div>

    </div>

    <van-popup v-model="popFromBranchList" position="bottom" :duration="0.4">
      <van-picker :columns="fromBranchList" show-toolbar title="仓库选择" value-key="branch_name" @cancel="popFromBranchList = false" @confirm="onFromBranchSelected" />
    </van-popup>

    <van-popup v-model="popToBranchList" position="bottom" :duration="0.4">
      <van-picker :columns="toBranchList" show-toolbar title="仓库选择" value-key="branch_name" @cancel="popToBranchList = false" @confirm="onToBranchSelected" />
    </van-popup>

    <van-popup v-model="m_bShowSubmitPopup" duration="0.4" :style="{ height: '100%', width: '80%' }" class="van_popup" position="right">
      <!-- <h5 class="custom_h5">
        其他选择
        <van-icon
          class="icon_h5"
          name="cross"
          @click="m_bShowSubmitPopup = false"
        />
      </h5> -->
      <div style="height:30px;border-top:1px solid #ccc"></div>
      <div class="other_operate">

        <van-field v-model="sheet.make_brief" label="备注" label-width="40px" placeholder="请输入备注" :disabled="sheet.approve_time ? true : false" />
        <div style="height:30px"></div>
        <div class="other_operate_content">
          <button v-if="canMake" plain type="info" :disabled="IsSubmiting || sheet.sheet_id !== ''" @click="btnConfirmSave_click" style="height: 45px;border-radius:12px;background-color: #ffcccc;">提交</button>
          <button v-if="canApprove" plain type="info" :disabled="sheet.approve_time !== ''" @click="btnConfirmApprove_click" style="height: 45px;border-radius:12px;background-color: #ffcccc;">审核</button>
        </div>

        <div class="other_operate_content">
          <button plain type="danger" style="height: 45px;border-radius:12px" :style="{
              color: sheet.red_flag !== '' ? '#ff9999' : '#ee0a24',
              borderColor: sheet.red_flag !== '' ? '#ff9999' : '#ee0a24',
            }" v-if="canRed && sheet.approve_time" :disabled="sheet.red_flag !== ''" @click="redBill">红冲</button>

          <button plain type="danger" v-if="sheet.sheet_id && !sheet.approve_time" style="height: 45px;border-radius:12px" @click="btnDeleteSheet_click">删除</button>
          <button plain type="info" @click="btnPrint_click" style="height: 45px;border-radius:12px" :disabled="(sheetStatusForPrint=='saved' && !sheet.sheet_id) || (sheetStatusForPrint=='approved' && !sheet.approve_time)||isPrinting">打印</button>
          <button type="default" @click="btnCopySheet_click" style="height: 45px;border-radius:12px">复制</button>
        </div>

        <van-divider>打印条码</van-divider>
        <van-radio-group v-model="printBarcodeStyle" style="
              font-size: 12px;
              margin-left: 0px;
              display: flex;
              justify-content: center;
              padding-top: 10px;
            ">
          <van-radio name="noBarcode" style="margin-right: 10px">不打印</van-radio>
          <van-radio name="actualUnit" style="margin-right: 10px">实际单位</van-radio>
          <van-radio name="smallUnit" style="margin-right: 10px">小单位</van-radio>
        </van-radio-group>
        <van-checkbox v-if="printBarcodeStyle === 'actualUnit' || printBarcodeStyle === 'smallUnit' " shape="square" v-model="printBarcodePic" icon-size="20px" style="font-size: 12px; margin-left: 50px; margin-top: 10px">打印条码图</van-checkbox>
        <van-divider style="margin: 20px 0px" @click="moreOptions = !moreOptions">

          <svg v-if="moreOptions" width="26px" height="26px" fill="#d8d8d8">
            <use xlink:href="#icon-double_arrow"></use>
          </svg>

          <svg style="-webkit-transform: rotate(180deg);" v-else width="26px" height="30px" fill="#d8d8d8">
            <use xlink:href="#icon-double_arrow"></use>
          </svg>
        </van-divider>
        <template v-if="moreOptions">
          <div class="other_operate_content">
            <van-button type="default" :disabled="IsSubmiting || sheet.approve_time !== ''" @click="onEmpty()">清空</van-button>
            <van-button type="default" @click="btnOut">退出</van-button>
          </div>
        </template>
      </div>
    </van-popup>

    <van-popup v-model="m_bPopupFillFromSale" duration="0.4" :style="{ height: '100%', width: '80%' }" class="van_popup" position="right">
      <h5 class="custom_h5">
        补货
        <van-icon class="icon_h5" name="cross" @click="m_bPopupFillFromSale = false" />
      </h5>
      <div>
        <van-search class="van_search" v-model="times" left-icon="" placeholder="请选择查询日期" right-icon="close" readonly @click="handleTimeShow" @click-right-icon.stop="clearDate" />
      </div>
      <div class="showBillDate">
        <van-radio-group v-model="timeRange">
          <van-radio name="today" style="margin: 10px">今日单据</van-radio>
          <van-radio name="yesterday" style="margin: 10px">昨日单据</van-radio>
          <van-radio name="sinceLastMove" style="margin: 10px">上次调拨之后</van-radio>
          <van-radio name="other" style="margin: 10px">其他</van-radio>
        </van-radio-group>
      </div>
      <div class="replenish">
        <van-button type="default" @click="btnFillBySale_click">按销补货</van-button>
        <van-button type="default" @click="btnFillByReturn_click">按退回库</van-button>
        <van-button type="default" @click="btnFillByStock_click" style="margin-top: 30px">
          按库存调拨
        </van-button>
      </div>
    </van-popup>
    <div>
      <van-calendar :readonly="canSelectCalendar" :min-date="minDate" :max-date="maxDate" v-model="timeShow" type="range" @confirm="confirmDateDuring" />
    </div>

    <van-popup v-model="popupAddMoveSheetRow" duration="0.4" :lazy-render="false" position="bottom" :style="{ height: '90%' }">
      <h5 class="custom_h5">
        修改商品数量
        <van-icon name="cross" class="icon_h5" @click="popupAddMoveSheetRow = false" />
      </h5>
      <div class="class_add_goods">
        <AddMoveSheetRow ref="dlgEditRow" @onAddRow_OK="onAddRow_OK" @delIndexItem="delIndexItem" />
      </div>
    </van-popup>

    <van-popup v-model="showUnsubmitedSheets" round>
      <div class="lowItem">
        <h4>以下是未提交的单据</h4>
        <ul class="lowItem_ul">
          <li v-for="(item, index) in unsubmitedSheets" :key="index">
            <div class="lowItem_ull" @click="onUnsubmitedSheetSelected(item)">
              {{ item.from_branch_name }}--{{ item.to_branch_name }}
            </div>
            <div class="lowItem_ulr" @click="onUnsubmitedSheetSelected(item)">
              {{ item.saveTime }}
            </div>
            <div style="width: 70px; line-height: 40px" class="btn-delete" @click="onUnsubmitedSheetDelete(index)">
              删除
            </div>
          </li>
        </ul>
        <van-button type="default" @click="showUnsubmitedSheets = false">新建单据</van-button>
      </div>
    </van-popup>
  </div>
</template>
<script>
import {
  NavBar,
  Button,
  Search,
  Toast,
  Popup,
  Field,
  Picker,
  Form,
  Col,
  Row,
  SwipeCell,
  Dialog,
  Divider,
  Icon,
  RadioGroup,
  Radio,
  Checkbox,
  Calendar
} from "vant";

import {
  AppSheetMoveLoad,
  AppSheetMoveSubmit,
  ApiSheetMoveSave,
  GetBranchList,
  AppSheetMoveGetFillItemsFromSale,
  AppSheetMoveGetFillItemsFromStock,
  SheetMoveRed,
  SheetMoveDelete,
} from "../../api/api";
import Printing from "../Printing/Printing";
import sheetCache from "../../util/sheetCache";
import AddMoveSheetRow from "./AddMoveSheetRow";
import toast from "vant/lib/toast";
// import SelectSeller from '../components/SelectSeller';
export default {
  name: "MoveSheet",
  data() {
    return {
      isPrinting: false,
      editingItemRowIndex: null,
      timeRange: "today",
      canApprove: false,
      canMake: false,
      popupAddMoveSheetRow: false,
      transStr: "",
      warehouseSelect: "",
      warehouseSelectName: "",
      userId: "",
      onload_remarks: [],
      salesPopup: false,
      PaymentPopup: false,
      m_bShowSubmitPopup: false,
      m_bPopupFillFromSale: false,
      showUnsubmitedSheets: false,
      unsubmitedSheets: [],
      printBarcodeStyle: "noBarcode",
      printBarcodePic: false,
      PaymentList: [],
      paymentSelects: "",
      bindSalesDats: {
        discount: 0,
        arrears: 0,
      },
      sheet: {
        total_units_amount: [],
        sheet_no: "",
        sheet_id: "",
        approve_time: "",
        make_time: "",
        sheetRows: [],
        startTime: "",
        endTime: "",
        make_brief: ""
      },
      IsSubmiting: false,
      srcDatas: [],
      Form: {
        qk: "",
        yh: "",
        zf1: "",
        zf2: "",
      },
      columns: [],
      qk: "",
      yh: "",
      warehouseData: [],
      totalsDatas: {},
      popFromBranchList: false,
      popToBranchList: false,
      fromBranchList: [],
      toBranchList: [],
      moveType: "",
      itemRows: [],
      minDate: new Date(2010, 0, 1),
      maxDate: new Date(),
      times: "",
      timeShow: false,
      canSelectCalendar: true,
      publicBox3Height: '',
      pageHeight: '',
      moreOptions: false
      /*list: [],
      loading: false,
      finished: false,
      isSubmit: false,
      itemDataList:[],
      
      */
    };
  },
  beforeUpdate() {
    this.changeHeight();
  },
  watch: {
    "$route.query": function () {
      //监听vuex中userName变化而改变header里面的值
      var selectedItems = this.$route.query.selectedItems;
      ;
      if (selectedItems) {
        this.addItems(selectedItems);
        this.updateTotal();
        sheetCache.saveCurSheetToCache(
          this,
          "unsubmitedSheets_DB" + this.moveType,
          3
        );
      }
    },
    timeRange: {
      handler: function (val, oldVal) {
        if (val === "other") {
          this.canSelectCalendar = false
        } else {
          this.canSelectCalendar = true
          this.clearDate()
        }
      },
      deep: true
    },
    itemRows: {
      handler(val, oldVal) {
        this.updateTotal();
      },
      deep: true
    }
  },
  mounted() {
    this.sheet.sheet_id = this.$route.query.sheetID;
    //var rights=this.$store.state.operRights
    var rights = this.$store.state.operInfo.operRights;

    if (rights && rights.stock.sheetMove.approve) {
      this.canApprove = true;
    }
    if (rights && rights.stock.sheetMove.make) {
      this.canMake = true;
    }

    this.moveType = this.$route.query.moveType;

    this.loadSheet();
    this.pageHeight = this.$refs.pages.offsetHeight
    this.changeHeight();
  },
  activated() {
    this.changeHeight();
  },
  components: {
    "van-nav-bar": NavBar,
    "van-button": Button,
    "van-search": Search,
    "van-popup": Popup,
    "van-field": Field,
    "van-picker": Picker,
    "van-form": Form,
    "van-row": Row,
    "van-col": Col,
    "van-divider": Divider,
    "van-icon": Icon,
    "van-swipe-cell": SwipeCell,
    "van-radio-group": RadioGroup,
    "van-radio": Radio,
    "van-checkbox": Checkbox,
    "van-calendar": Calendar,
    AddMoveSheetRow: AddMoveSheetRow,
  },
  computed: {
    canRed() {
      return hasRight("stock.sheetMove.red");
    },
    allowPrintBeforeApprove() {
      return hasRight("delicacy.allowPrintBeforeApprove.value");
    },
    allowPrintBeforeSave() {
      return hasRight("delicacy.allowPrintBeforeSave.value");
    },
    sheetStatusForPrint() {
      return window.getRightValue('delicacy.moveSheetStatusForPrint.value');
    },
    canEdit() {
      if (this.sheet.approve_time)
        return false
      if (!this.canApprove && this.sheet.sheet_id) return false
      return true
    }
  },
  methods: {
    clearDate() {
      this.sheet.startDate = "";
      this.sheet.endDate = "";
      this.times = "";
      this.sheet.startTime = "";
      this.sheet.endTime = "";
    },
    confirmDateDuring(e) {
      this.timeShow = false;

      this.sheet.startTime = this.formatDate(e[0]);
      this.sheet.endTime = this.formatDate(e[1]);
      this.times = `${this.sheet.startTime} 至 ${this.sheet.endTime}`;
    },
    // btnScanBarcode_click() {
    //   var that = this;
    //   if (that.sheet.sheet_id !== "") return;
    //   scanBar(
    //     (res) => {
    //       that.transStr = res;
    //       that.onAddClass();
    //     },
    //     (err) => {
    //       console.log("barcode scan fail:", err);
    //     }
    //   );
    // },

    async btnScanBarcode_click() {
      try {
        const result = await this.scanBarcodeNew({
          unit_type: 'move_sheet_copy'
        })

        if (!result.code) {
          return
        }

        this.transStr = result.code
        this.onAddClass()
      } catch (error) {
        console.error('扫码失败:', error)
        this.$toast('扫码失败，请重试')
      }
    },
    onUnsubmitedSheetSelected(obj) {
      this.loadSheetFromJson(obj);
      this.showUnsubmitedSheets = false;
    },
    onUnsubmitedSheetDelete(index) {
      var key = "unsubmitedSheets_DB" + this.moveType;
      this.unsubmitedSheets.splice(index, 1);
      this.$store.commit(key, this.unsubmitedSheets);
    },
    // 选择其他情况，日期提醒
    handleCalendarToast() {
      if (this.timeRange === "other" && (this.sheet.startTime === "" || this.sheet.startTime === undefined)) {
        Toast.fail("请选择日期")
        return false
      }
      return true
    },
    btnFillBySale_click() {
      if (this.handleCalendarToast()) {
        this.fillBySale(false);
      }
    },
    btnFillByReturn_click() {
      if (this.handleCalendarToast()) {
        this.fillBySale(true);
      }
    },
    row_UnitsArray2Props(arrRow) {
      var propRow = {};
      propRow.item_id = arrRow.item_id;
      propRow.item_name = arrRow.item_name;
      propRow.bunit = arrRow.b_unit_no;
      propRow.bfactor = arrRow.b_unit_factor;
      propRow.bpprice = arrRow.b_wholesale_price;

      propRow.munit = arrRow.m_unit_no;
      propRow.mfactor = arrRow.m_unit_factor;
      propRow.mpprice = arrRow.m_wholesale_price;

      propRow.sunit = arrRow.s_unit_no;
      propRow.sfactor = 1;
      propRow.spprice = arrRow.s_wholesale_price;

      arrRow.rowUnits.forEach(function (unit) {
        if (unit.unit_type == "b") {
          propRow.bunit = unit.unit_no;
          propRow.bfactor = unit.unit_factor;
          propRow.bunitSum = unit.quantity;
          propRow.bpprice = unit.wholesale_price;
          propRow.bremark = unit.remark
        } else if (unit.unit_type == "m") {
          propRow.munit = unit.unit_no;
          propRow.mfactor = unit.unit_factor;
          propRow.munitSum = unit.quantity;
          propRow.mpprice = unit.wholesale_price;
          propRow.mremark = unit.remark
        } else if (unit.unit_type == "s") {
          propRow.sunit = unit.unit_no;
          propRow.sfactor = unit.unit_factor;
          propRow.sunitSum = unit.quantity;
          propRow.spprice = unit.wholesale_price;
          propRow.sremark = unit.remark
        }
      });
      return propRow;
    },
    row_UnitsProps2Array(propRow) {
      var arrRow = {};
      arrRow.item_id = propRow.item_id;
      arrRow.item_name = propRow.item_name;

      arrRow.b_unit_no = propRow.bunit;
      arrRow.b_unit_factor = propRow.bfactor;
      arrRow.b_wholesale_price = propRow.bpprice;

      arrRow.m_unit_no = propRow.munit;
      arrRow.m_unit_factor = propRow.mfactor;
      arrRow.m_wholesale_price = propRow.mpprice;

      arrRow.s_unit_no = propRow.sunit;
      arrRow.s_wholesale_price = propRow.spprice;
      arrRow.m_barcode = propRow.m_barcode;
      arrRow.s_barcode = propRow.s_barcode;
      arrRow.b_barcode = propRow.b_barcode;

      arrRow.remarkAll = propRow.remarkAll;


      arrRow.item_total = 0;
      arrRow.rowUnits = [];
      if (propRow.bunit && propRow.bunitSum) {
        arrRow.rowUnits.push({
          item_id: propRow.item_id,
          item_name: propRow.item_name,
          unit_no: propRow.bunit,
          unit_type: "b",
          unit_factor: propRow.bfactor,
          quantity: propRow.bunitSum,
          wholesale_price: propRow.bpprice,
          remark: propRow.bremark

        });
        arrRow.item_total += this.fix(
          Number(propRow.bpprice || 0) * Number(propRow.bunitSum)
        );
      }
      if (propRow.munit && propRow.munitSum) {
        arrRow.rowUnits.push({
          item_id: propRow.item_id,
          item_name: propRow.item_name,
          unit_no: propRow.munit,
          unit_type: "m",
          unit_factor: propRow.mfactor,
          quantity: propRow.munitSum,
          wholesale_price: propRow.mpprice,
          remark: propRow.mremark
        });

        arrRow.item_total += this.fix(
          Number(propRow.mpprice || 0) * Number(propRow.munitSum)
        );
      }
      if (propRow.sunit && propRow.sunitSum) {
        arrRow.rowUnits.push({
          item_id: propRow.item_id,
          item_name: propRow.item_name,
          unit_no: propRow.sunit,
          unit_type: "s",
          unit_factor: propRow.sfactor,
          quantity: propRow.sunitSum,
          wholesale_price: propRow.spprice,
          remark: propRow.sremark
        });
        arrRow.item_total += this.fix(
          Number(propRow.spprice || 0) * Number(propRow.sunitSum)
        );
      }
      return arrRow;
    },
    onRowClick(row, index) {
      if (!this.canEdit) return;
      var propRow = this.row_UnitsArray2Props(row);
      this.popupAddMoveSheetRow = true;
      this.editingItemRowIndex = index;
      this.$refs.dlgEditRow.loadData([propRow], this.editingItemRowIndex);
    },
    onAddRow_OK(rowList) {
      var propRow = rowList[0];
      var arrRow = this.row_UnitsProps2Array(propRow);
      this.itemRows[this.editingItemRowIndex] = arrRow;
      this.popupAddMoveSheetRow = false;
      if (this.moveType)
        sheetCache.saveCurSheetToCache(
          this,
          "unsubmitedSheets_DB" + this.moveType
        );
      this.updateTotal();
    },
    delIndexItem(delIndexItem) {
      this.itemRows.splice(delIndexItem, 1)
      this.popupAddMoveSheetRow = false;
      if (this.moveType)
        sheetCache.saveCurSheetToCache(
          this,
          "unsubmitedSheets_DB" + this.moveType
        );
      this.updateTotal();
    },
    fillBySale(isFromReturn) {
      var params = {
        timeRange: this.timeRange,
        fromBanchID: this.sheet.from_branch_id,
        toBranchID: this.sheet.to_branch_id,
        isFromReturn: isFromReturn,
        startTime: this.sheet.startTime,
        endTime: this.sheet.endTime
      };
      var that = this;
      AppSheetMoveGetFillItemsFromSale(params).then((res) => {
        if (res.result === "OK") {
          if (res.data.length == 0) {
            Toast.fail("暂无数据");
            return;
          }
          var itemRows = res.data;
          var moveRows = [];
          itemRows.forEach(function (row) {
            var b_qty = 0,
              s_qty = 0;
            var b_wholesale_price;
            if (row.b_unit_no && row.b_unit_factor) {
              b_qty = parseInt(Number(row.s_qty) / Number(row.b_unit_factor));
              s_qty = Number(row.s_qty) % Number(row.b_unit_factor);
              if (row.b_wholesale_price)
                b_wholesale_price = Number(row.b_wholesale_price);
            } else {
              s_qty = Number(row.s_qty);
            }
            let moveRow = {
              item_id: row.item_id,
              b_unit_no: row.b_unit_no,
              b_unit_factor: row.b_unit_factor,
              b_wholesale_price: row.b_wholesale_price,
              b_barcode: row.b_barcode,
              s_unit_no: row.s_unit_no,
              s_unit_factor: row.s_unit_factor,
              s_barcode: row.s_barcode,
              s_wholesale_price: row.s_wholesale_price,
              m_unit_no: row.m_unit_no,
              m_unit_factor: row.m_unit_factor,
              m_wholesale_price: row.m_wholesale_price,
              m_barcode: row.m_barcode,
              item_name: row.item_name,
              item_total: 0,
              remark: '',
              rowUnits: [],
            };
            var unitRow;
            if (b_qty > 0) {
              unitRow = {
                item_id: row.item_id,
                item_name: row.item_name,
                unit_no: row.b_unit_no,
                unit_type: "b",
                wholesale_price: b_wholesale_price || 0,
                quantity: b_qty,
                unit_factor: row.b_unit_factor,
                remark: "",
              };
              moveRow.rowUnits.push(unitRow);
              moveRow.item_total += that.fix(
                unitRow.wholesale_price * unitRow.quantity
              );
            }
            if (s_qty > 0) {
              unitRow = {
                item_id: row.item_id,
                item_name: row.item_name,
                unit_no: row.s_unit_no,
                wholesale_price: row.s_wholesale_price || 0,
                quantity: s_qty,
                unit_factor: 1,
                unit_type: "s",
                remark: "",
              };
              moveRow.rowUnits.push(unitRow);
              moveRow.item_total += that.fix(
                unitRow.wholesale_price * unitRow.quantity
              );
            }
            moveRows.push(moveRow);
          });
          this.itemRows = moveRows;
          this.updateTotal();
        } else {
          Toast.fail(res.msg);
        }
      });
    },
    btnFillByStock_click() {
      if (this.handleCalendarToast()) {
        var params = {
          fromBanchID: this.sheet.from_branch_id,
        };
        var that = this;
        AppSheetMoveGetFillItemsFromStock(params).then((res) => {
          if (res.result === "OK") {
            
            if (res.data.length == 0) {
              Toast.fail("暂无数据");
              return;
            }
            var itemRows = res.data;
            var moveRows = [];
            itemRows.forEach(function (row) {
              var b_qty = 0,
                s_qty = 0;
              var b_wholesale_price;
              if (row.b_unit_no && row.b_unit_factor) {
                b_qty = parseInt(Number(row.s_qty) / Number(row.b_unit_factor));
                s_qty = Number(row.s_qty) % Number(row.b_unit_factor);
                if (row.b_wholesale_price)
                  b_wholesale_price = Number(row.b_wholesale_price);
              } else {
                s_qty = Number(row.s_qty);
              }
              let moveRow = {
                item_id: row.item_id,
                b_unit_no: row.b_unit_no,
                b_unit_factor: row.b_unit_factor,
                b_wholesale_price: row.b_wholesale_price,
                b_barcode: row.b_barcode,
                s_unit_no: row.s_unit_no,
                s_unit_factor: row.s_unit_factor,
                s_wholesale_price: row.s_wholesale_price,
                s_barcode: row.s_barcode,
                m_unit_no: row.m_unit_no,
                m_unit_factor: row.m_unit_factor,
                m_wholesale_price: row.m_wholesale_price,
                m_barcode: row.m_barcode,
                item_name: row.item_name,
                item_total: 0,
                rowUnits: [],
              };
              var unitRow;
              if (b_qty > 0) {
                unitRow = {
                  item_id: row.item_id,
                  item_name: row.item_name,
                  unit_no: row.b_unit_no,
                  unit_type: "b",
                  wholesale_price: b_wholesale_price || 0,
                  quantity: b_qty,
                  unit_factor: row.b_unit_factor,
                  remark: "",
                };
                moveRow.rowUnits.push(unitRow);
                moveRow.item_total += that.fix(
                  unitRow.wholesale_price * unitRow.quantity
                );
              }
              if (s_qty > 0) {
                unitRow = {
                  item_id: row.item_id,
                  item_name: row.item_name,
                  unit_no: row.s_unit_no,
                  unit_type: "s",
                  wholesale_price: row.s_wholesale_price || 0,
                  quantity: s_qty,
                  unit_factor: 1,
                  remark: "",
                };
                moveRow.rowUnits.push(unitRow);
                moveRow.item_total += that.fix(
                  unitRow.wholesale_price * unitRow.quantity
                );
              }
              moveRows.push(moveRow);
            });
            this.itemRows = moveRows;
            this.getSheet()
          } else {
            Toast.fail(res.msg);
          }
        });
      }
    },
    loadSheetFromJson(sheet) {
      this.sheet.sheet_no = sheet.sheet_no;
      this.sheet.make_time = sheet.happen_time;
      this.sheet.red_flag = sheet.red_flag;
      this.sheet.make_brief = sheet.make_brief;
      let listData = [];
      let total = [];
      let array = [];
      let itemIdList = sheet.sheetRows.map((item) => {
        return {
          item_id: item.item_id,
          item_name: item.item_name,
          b_unit_no: item.b_unit_no,
          b_unit_factor: item.b_unit_factor,
          b_wholesale_price: item.b_wholesale_price,
          b_barcode: item.b_barcode,
          s_unit_no: item.s_unit_no,
          s_wholesale_price: item.s_wholesale_price,
          s_barcode: item.s_barcode,
          m_unit_no: item.m_unit_no,
          m_unit_factor: item.m_unit_factor,
          m_wholesale_price: item.m_wholesale_price,
          m_barcode: item.m_barcode,
          remark: item.remark
        };
      });

      function unique(arr) {
        var unique = {};
        var newArr = []
        arr.forEach((el) => {
          if (!unique[el.item_id]) {
            newArr.push(el)
            unique[el.item_id] = el
          }
        });
        return newArr;
      }

      array = unique(itemIdList);
      let dataSum = 0;
      // 遍历商品
      var that = this;
      array.forEach((item) => {
        //  rowUnits = [];
        let itemRow = {
          item_id: item.item_id,
          item_name: item.item_name,
          b_unit_no: item.b_unit_no,
          b_unit_factor: item.b_unit_factor,
          b_wholesale_price: item.b_wholesale_price,
          b_barcode: item.b_barcode,
          s_unit_no: item.s_unit_no,
          s_wholesale_price: item.s_wholesale_price,
          s_barcode: item.s_barcode,
          m_unit_no: item.m_unit_no,
          m_wholesale_price: item.m_wholesale_price,
          m_unit_factor: item.m_unit_factor,
          m_barcode: item.m_barcode,
          item_total: 0,
          rowUnits: [],
        };

        sheet.sheetRows.forEach((item_son) => {
          // 匹配同个商品
          if (item.item_id == item_son.item_id) {
            let unitRow = {
              item_id: item_son.item_id,
              item_name: item_son.item_name,
              unit_no: item_son.unit_no,
              wholesale_price: item_son.wholesale_price,
              quantity: item_son.quantity,
              unit_factor: item_son.unit_factor,
              remark: item_son.remark,
            };
            if (unitRow.unit_no == item.b_unit_no) unitRow.unit_type = "b";
            else if (unitRow.unit_no == item.m_unit_no) unitRow.unit_type = "m";
            else if (unitRow.unit_no == item.s_unit_no) unitRow.unit_type = "s";
            // 同个商品总额
            //dataSum += unitRow.wholesale_price*unitRow.quantity;
            //   console.log(dataSum)
            // 存储同个商品
            itemRow.rowUnits.push(unitRow);
            itemRow.item_total += that.fix(
              unitRow.wholesale_price * unitRow.quantity
            );
          }
        });
        listData.push(itemRow);
      });
      this.itemRows = listData;
      this.sheet = sheet;
    },
    loadSheet() {
      let params = {
        sheetID: this.sheet.sheet_id,
      };

      let branchlist = {};
      AppSheetMoveLoad(params).then((res) => {
        if (res.result === "OK") {
          this.loadSheetFromJson(res.sheet);
          if (!this.sheet.sheet_id) {
            var setSheetBranchByCache = (branch, bFromOrTo) => {
              var c = "dr";
              if (bFromOrTo) c = "dc";
              if (branch) {
                var branchValid = window.hasBranchSheetRight(
                  branch.branch_id,
                  c
                );
                if (branchValid) {
                  if (bFromOrTo) {
                    this.sheet.from_branch_id = branch.branch_id;
                    this.sheet.from_branch_name = branch.branch_name;
                  } else {
                    this.sheet.to_branch_id = branch.branch_id;
                    this.sheet.to_branch_name = branch.branch_name;
                  }
                }
              }
            };

            if (this.moveType == "moveOut") {
              setSheetBranchByCache(this.$store.state.moveOutFromBranch, true);
              setSheetBranchByCache(this.$store.state.moveOutToBranch, false);
            } else if (this.moveType == "moveIn") {
              setSheetBranchByCache(this.$store.state.moveInFromBranch, true);
              setSheetBranchByCache(this.$store.state.moveInToBranch, false);
            }

            this.showSheetsFromCache();
          }
        }
      });

      GetBranchList(branchlist).then((res) => {
        if (res.result === "OK") {
          this.branchList = [];
          for (var i = 0; i < res.data.length; i++) {
            var branch = res.data[i];
            var branchValid = window.hasBranchSheetRight(
              branch.branch_id,
              "dc"
            );
            if (branchValid) this.fromBranchList.push(branch);
            branchValid = window.hasBranchSheetRight(branch.branch_id, "dr");
            if (branchValid) this.toBranchList.push(branch);
          }
        }
      });
    },
    btnRemoveRow_click(index) {
      Dialog.confirm({
        title: "删除数据",
        message: "请确认是否删除本条数据?",
        width:"320px"
      }).then(() => {
        this.itemRows.splice(index, 1);
        sheetCache.saveCurSheetToCache(
          this,
          "unsubmitedSheets_DB" + this.moveType
        );
        this.updateTotal();

      })
    },
    updateTotalUnitObj(totalUnitObj, unit, quantity) {

      if (unit.unit_type === "s") {
        if (typeof totalUnitObj[unit.unit_no] === "undefined") {
          totalUnitObj[unit.unit_no] = quantity;
        } else {
          totalUnitObj[unit.unit_no] += quantity;
        }
      }
      if (unit.unit_type === "b") {


        if (typeof totalUnitObj[unit.unit_no] === "undefined") {
          totalUnitObj[unit.unit_no] = quantity;

        } else {
          totalUnitObj[unit.unit_no] += quantity;
        }
      }
      if (unit.unit_type === "m") {
        if (typeof totalUnitObj[unit.unit_no] === "undefined") {
          totalUnitObj[unit.unit_no] = quantity;
        } else {
          totalUnitObj[unit.unit_no] += quantity;
        }
      }
      return totalUnitObj;
    },
    transformTotalUnitObjectToList(totalUnitObj) {
      const unitList = [];
      Object.keys(totalUnitObj).forEach((key) => {
        unitList.push({
          key,
          value: totalUnitObj[key],
        });
      });
      return unitList;
    },
    calTotalUnitAmount() {
      let totalUnitObj = {};
      this.itemRows.forEach((item) => {
        item.rowUnits.forEach((u) => {
          const quantity = Number(u.quantity);

          totalUnitObj = this.updateTotalUnitObj(totalUnitObj, u, quantity);
        });
      });
      return this.transformTotalUnitObjectToList(totalUnitObj);
    },
    updateTotal() {
      var total = 0;
      this.itemRows.forEach((item) => {
        total += Number(item.item_total);
      });
      this.sheet.total_units_amount = this.calTotalUnitAmount();
      this.sheet.total_amount = toMoney(total);
      this.sheet.wholesale_amount = toMoney(total);
    },
    onAddClass() {
      if (this.sheet.from_branch_id && this.sheet.to_branch_id) {
        let obj = {
          searchStr: this.transStr,
          fromBranchID: this.sheet.from_branch_id,
          toBranchID: this.sheet.to_branch_id,
        };
        this.$store.commit("currentSheet", this.sheet);
        this.$router.push({ path: "/SelectMoveItems", query: obj });
        this.transStr = "";
      } else {
        Toast.fail("请选择仓库");
      }
    },
    showSheetsFromCache() {
      if (
        this.$store.state["unsubmitedSheets_DB" + this.moveType] &&
        this.$store.state["unsubmitedSheets_DB" + this.moveType].length > 0
      ) {
        this.showUnsubmitedSheets = true;
        this.unsubmitedSheets = this.$store.state[
          "unsubmitedSheets_DB" + this.moveType
        ];
      }
    },
    addItems(newRows) {
      var that = this;
      if (newRows && newRows.length > 0) {
        let tmpItemRows = this.itemRows;
        newRows.map((item) => {
          var itemRow = that.row_UnitsProps2Array(item);
          tmpItemRows.push(itemRow);

          // let total = []
          //  let itemRow = {
          //   item_id:item.item_id,
          //   item_name:item.item_name,
          //   item_total:0,
          //   rowUnits:[]
          // }
          // if (item.bunitTotle){
          //   let obj_son = {
          //     item_id: item.item_id,
          //     item_name: item.item_name,
          //     unit_no: item.bunit,
          //     wholesale_price: item.bpprice,
          //     quantity: item.bunitSum,
          //     unit_factor: item.bfactor,
          //     unit_type:'b',
          //     remark: ""
          //   }
          //   itemRow.item_total+=Number(item.bunitTotle)
          //   itemRow.rowUnits.push(obj_son)
          // }
          // if(item.munitTotle){
          //   let obj_son = {
          //     item_id: item.item_id,
          //     item_name: item.item_name,
          //     unit_no: item.munit,
          //     wholesale_price: item.mpprice,
          //     quantity: item.munitSum,
          //     unit_factor: item.mfactor,
          //     unit_type:'m',
          //     remark: ""
          //   }
          //   itemRow.item_total+=Number(item.munitTotle)
          //   itemRow.rowUnits.push(obj_son)
          // }
          // if (item.sunitTotle){
          //   let obj_son = {
          //     item_id: item.item_id,
          //     item_name: item.item_name,
          //     unit_no: item.sunit,
          //     wholesale_price: item.spprice,
          //     quantity: item.sunitSum,
          //     unit_factor: item.sfactor,
          //     unit_type:'s',
          //     remark: ""
          //   }
          //   itemRow.item_total+=Number(item.sunitTotle)
          //   itemRow.rowUnits.push(obj_son)
          // }
        });
        this.itemRows = tmpItemRows;
      }
    },
    WarehouseSelectDatas(obj) {
      this.srcDatas = obj;
      this.warehouseSelect = obj[1].branch_id;
      this.branch_name = obj[1].branch_name;
    },
    onFromBranchSelected(value) {
      this.sheet.from_branch_name = value.branch_name;
      this.sheet.from_branch_id = value.branch_id;
      this.popFromBranchList = false;
      if (this.moveType == "moveOut")
        this.$store.commit("moveOutFromBranch", value);
      else if (this.moveType == "moveIn")
        this.$store.commit("moveInFromBranch", value);
    },
    onToBranchSelected(value) {
      this.sheet.to_branch_id = value.branch_id;
      this.sheet.to_branch_name = value.branch_name;
      this.popToBranchList = false;
      if (this.moveType == "moveOut")
        this.$store.commit("moveOutToBranch", value);
      else if (this.moveType == "moveIn")
        this.$store.commit("moveInToBranch", value);
    },
    onPayment(obj) {
      this.paymentSelects = obj;
      this.PaymentPopup = true;
    },
    onConfirmPayment(value) {
      this.PaymentPopup = false;
      let objs = {
        ids: value.sub_id,
        titles: value.sub_name,
      };
      if (this.paymentSelects === "payment1") {
        if (this.payment1.ids !== value.sub_id) this.payment2 = objs;
      } else {
        if (this.payment2.ids !== value.sub_id) this.payment1 = objs;
      }
    },
    /*
    onRrears(value){
      this.qk = value;
      let objs = {
        discount:this.yh,
        arrears:this.qk
      }
      this.bindSalesDats = objs
    },
    onDiscount(value){
      this.yh = value;
      let objs = {
        discount:this.yh,
        arrears:this.qk
      }
      this.bindSalesDats = objs
    },*/
    btnCopySheet_click() {
      Dialog.confirm({
        title: "复制单据",
        message: "请确认是否复制?",
        width:"320px"
      })
        .then(() => {
          this.sheet.sheet_id = "";
          this.sheet.sheet_no = "";
          this.sheet.approve_time = "";
          this.sheet.approver_id = "";
          this.sheet.happen_time = "";
          this.sheet.make_time = "";
          this.sheet.maker_id = "";
          this.sheet.red_flag = "";
          sheetCache.saveCurSheetToCache(
            this,
            "unsubmitedSheets_DB" + this.moveType,
            3
          );
          Toast("复制成功");
        })
        .catch(() => {
          Toast("取消复制");
        });
    },
    onEmpty() {
      Dialog.confirm({
        title: "清空单据",
        message: "请确认是否清空单据?",
        width:"320px"
      }).then(() => {
        this.$store.commit("itemList", []);
        this.$store.commit("productist", []);
        this.itemRows = [];
        this.sheet.total_amount = 0;
        this.sheet.wholesale_amount = 0;
        sheetCache.removeCurSheetFromCache(
          this,
          "unsubmitedSheets_DB" + this.moveType
        );
        Toast.success("已清空");
        this.salesPopup = false;
      })

    },
    btnOut() {
      myGoBack(this.$router);
    },
    goback() {
      myGoBack(this.$router);
      this.$store.commit("classId", this.$store.state.AllItemClassId);
    },
    // bindtotalsUpdata(obj){
    //   let attrDatas = []
    //   obj.datas.map(item=>{
    //     let objs = {
    //       item_id: item.item_id,
    //       item_name: item.item_name,
    //       unit_no: item.unit_no,
    //       real_price: item.real_price? item.real_price:0,
    //       quantity: item.quantity,
    //       unit_factor:item.unit_factor,
    //       sub_amount: item.sub_amount,
    //       remark: item.remarkName,
    //     }
    //     attrDatas.push(objs)
    //   })
    //   this.totalsDatas.totles = obj.totles?obj.totles:0
    //   this.totalsDatas.datas = attrDatas
    // },
    getSheet() {
      let sheetRows = [];
      ;
      this.itemRows.map((item) => {
        if (item.rowUnits && item.rowUnits.length) {
          item.rowUnits.map((item_son) => {
            item_son.s_unit_no = item.s_unit_no;
            item_son.s_unit_factor = item.s_unit_factor;
            item_son.s_wholesale_price = item.s_wholesale_price;

            item_son.b_unit_no = item.b_unit_no;
            item_son.b_unit_factor = item.b_unit_factor;
            item_son.b_wholesale_price = item.b_wholesale_price;

            item_son.m_unit_no = item.m_unit_no;
            item_son.m_unit_factor = item.m_unit_factor;
            item_son.m_wholesale_price = item.m_wholesale_price;
            item_son.s_barcode = item.s_barcode;
            item_son.m_barcode = item.m_barcode;
            item_son.b_barcode = item.b_barcode;
            sheetRows.push(item_son);
          });
        }
      });
      var sheet = this.sheet;
      sheet.operKey = this.$store.state.operKey;
      sheet.sheetType = "DB";
      sheet.sheetRows = sheetRows;
      if (!sheet.maker_name) {
        sheet.maker_name = this.$store.state.operInfo.oper_name;
      }
      if (!sheet.maker_id) {
        sheet.maker_id = this.$store.state.operInfo.oper_id;
      }
      this.updateTotal();
      return sheet;
    },
    btnSubmit_click() {
      if (!this.itemRows && this.itemRows.length <= 0) {
        Toast.fail("请添加商品");
        return;
      }
      this.m_bShowSubmitPopup = true;
    },
    btnConfirmSave_click() {
      var sheet = this.getSheet();
      if (sheet.from_branch_id === sheet.to_branch_id) {
        Toast.fail("出仓与入仓相同,请重新选择");
        return;
      }

      if (!sheet.sheetRows.length) Toast.fail("请添加调拨商品");
      else {
        this.IsSubmiting = true;
        ApiSheetMoveSave(sheet).then((res) => {
          this.IsSubmiting = false;
          if (res.result === "OK") {
            sheet.sheet_no = res.sheet_no;
            sheet.sheet_id = res.sheet_id;
            sheet.make_time = res.currentTime;
            sheetCache.removeCurSheetFromCache(
              this,
              "unsubmitedSheets_DB" + this.moveType
            );
            Toast.success("提交成功");
          } else {
            Toast.fail(res.msg);
          }
        });
      }
    },
    btnConfirmApprove_click() {
      Dialog.confirm({
        title: "审核单据",
        message: "请确认是否审核?",
        width:"320px"
      }).then(() => {
        var sheet = this.getSheet();
        if (sheet.from_branch_id === sheet.to_branch_id) {
          Toast.fail("出仓与入仓相同,请重新选择");
          return;
        }
        if (!sheet.approver_name) {
          sheet.approver_name = this.$store.state.operInfo.oper_name;
        }
        this.IsSubmiting = true;
        AppSheetMoveSubmit(sheet).then((res) => {
          this.IsSubmiting = false;
          if (res.result === "OK") {
            sheet.sheet_no = res.sheet_no;
            sheet.sheet_id = res.sheet_id;
            sheet.make_time = res.currentTime;
            this.sheet.approve_time = res.approve_time;
            if (window.g_curSheetInList) {
              // window.g_curSheetInList.approve_time=res.approve_time
              window.g_curSheetInList.state = "approved";
            }
            sheetCache.removeCurSheetFromCache(
              this,
              "unsubmitedSheets_DB" + this.moveType
            );
            Toast.success("审核成功");
          } else {
            Toast.fail(res.msg);
          }
        });
      })

    },
    redBill() {
      Dialog.confirm({
        title: "红冲单据",
        message: "请确认是否红冲?",
        width:"320px"
      }).then(() => {
        let params = {
          operKey: this.$store.state.operKey,
          sheetID: this.sheet.sheet_id,
        };
        SheetMoveRed(params).then((res) => {
          if (res.result === "OK") {
            this.sheet.red_flag = "1";
            if (window.g_curSheetList) {
              var redSheet = JSON.parse(JSON.stringify(this.sheet));
              redSheet.sheet_id = res.sheet_id;
              redSheet.happen_time = res.happen_time;
              redSheet.oper_name = this.$store.state.operInfo.oper_name;
              redSheet.f_branch = this.sheet.from_branch_name;
              redSheet.t_branch = this.sheet.to_branch_name;
              redSheet.wholesale_amount = this.sheet.wholesale_amount;
              redSheet.state = "red";
              window.g_curSheetList.unshift(redSheet);
              window.g_curSheetInList.state = "reded";
            }
            Toast.success("红冲成功");
          } else {
            Toast.fail("红冲失败:" + res.msg);
          }
        });
      })
    },
    btnPrint_click() {
      this.isPrinting = true
      var sheet = this.getSheet();
      var imageBase64 = null;
      // if (this.$store.state.company_cachet)
      //   imageBase64 = "data:image/png;base64," + this.$store.state.company_cachet;
      Printing.printMoveSheet(sheet, this.printBarcodeStyle, this.printBarcodePic, imageBase64, res => {
        this.isPrinting = false
        if (res.result == "OK") {
          Toast.success("打印成功");
        } else {
          Toast.fail(res.msg);
        }
      });
    },
    fix(num, n = 2) {
      var pn = Math.pow(10, n);
      return Math.round(Number(num) * pn) / pn;
    },
    // 删除未审核单据
    btnDeleteSheet_click() {
      //sheet.sheet_id
      var that = this;
      Dialog.confirm({
        title: "删除单据",
        message: "请确认是否删除",
        width:"320px"
      }).then(() => {
        var delFunc;
        delFunc = SheetMoveDelete;
        let params = {
          operKey: this.$store.state.operKey,
          sheet_id: this.sheet.sheet_id,
        };
        delFunc(params).then((res) => {
          if (res.result === "OK") {
            Toast.success("删除成功,即将退出该页面");
            setTimeout(function () {
              that.btnOut();
            }, 1000);
          } else {
            Toast.fail("删除失败:" + res.msg);
          }
        });
      })

    },
    handleTimeShow() {
      if (this.canSelectCalendar) {
        Toast.fail("请点击其他，在进行日期选择")
      } else {
        this.timeShow = true
      }

    },
    handlePopupFillFromSale() {
      if (this.sheet.sheet_no) return;
      if (this.sheet.from_branch_id === this.sheet.to_branch_id && this.sheet.from_branch_id !== "" && this.sheet.to_branch_id !== "") {
        Toast.fail("出仓与入仓相同,请重新选择");
        return;
      }
      if (this.sheet.from_branch_id === "") {
        Toast.fail("请选择出仓");
        return;
      }
      if (this.sheet.to_branch_id === "") {
        Toast.fail("请选择入仓");
        return;
      }
      this.m_bPopupFillFromSale = true;
    },
    changeHeight() {

      // .sales_no_submit {
      //   height: calc(100% - 44px - 25px - 29px);
      // }
      // .sales_box_list {
      //   height: calc(100% - 44px - 25px);
      // }  
      this.publicBox3Height = Number(this.pageHeight) - 44 - 100 - Number(this.$refs.move_total.offsetHeight)
      if (this.sheet.sheet_no) {
        this.publicBox3Height -= 29
      }
      this.publicBox3Height += 'px'
    }
  },
};
</script>
<style lang="less" scoped>
@flex_w: {
  display: flex;
  flex-wrap: wrap;
};
@flex_a_j: {
  display: flex;
  align-items: center;
  justify-content: center;
};
@flex_a_bw: {
  display: flex;
  align-items: center;
  justify-content: space-between;
};
@posAblot: {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
};
.iconfont {
  color: #b197c2;
}
.brand_style {
  font-size: 15px;
}
.clear_bill {
  margin-top: 30px;
}
.sales_no_submit {
  height: calc(100% - 44px - 25px - 29px);
}
.sales_box_list {
  height: calc(100% - 44px - 25px);
}
.sales_box_box {
  width: 100%;
  height: calc(100% - 2px);
  overflow-x: hidden;
  overflow-y: auto;
  .sales_box_ul {
    height: auto;
    overflow: hidden;
    padding: 10px 10px 0 10px;
    li {
      width: 100%;
      border-radius: 4px;
      margin: 0 auto 10px auto;
      background: #ffffff;
      h5 {
        height: 40px;
        @flex_a_bw();
        padding: 0 15px;
        font-size: 16px;
        border-bottom: 1px solid #f2f2f2;
        color: #333333;
        span {
          text-align: left;
        }
      }
      .van-col:first-child {
        p {
          text-align: left;
        }
      }
      .van-col:last-child {
        p {
          text-align: right;
        }
      }
      .van-col {
        p {
          font-size: 15px;
          color: #333333;
          padding: 0 5px;
          margin: 0;
        }
      }
      .sales_box_ul_title {
        height: 30px;
        line-height: 30px;
        padding: 0 7.5px;
        font-size: 15px;
        font-weight: 500;
        color: #333333;
      }
      .sales_box_ul_item {
        padding: 5px 7.5px;
        color: #666666;
      }
    }
    .removeRow {
      height: inherit;
    }
    .sheet_state {
      z-index: 999;
      position: fixed;
    }
    .approved {
      width: 105px;
      height: 60px;
      top: 95px;
      left: 125px;
    }
    .reded {
      width: 86px;
      height: 75px;
      top: 75px;
      left: 125px;
    }
  }
}
.unit-amount-container {
  display: flex;
  flex-direction: row;
  float: left;
}
.total-amount-container {
  right: 0;
}
.move_total {
  display: flex;
  flex-direction: row;
  justify-content: space-between;

  background: #fff;
  font-size: 16px;
  padding: 0 20px;
  text-align: right;
  border-top: 1px solid #f2f2f2;
  border-bottom: 1px solid #f2f2f2;
  box-shadow: 0 2px 5px #f2f6fc;
}
.sales_footer {
  @posAblot();
  background: #ffffff;
  border-top: 1px solid #f2f2f2;
  padding: 0 10px;
  :last-child {
    @flex_a_bw();
  }
}
.van_popup {
  height: 100%;
  overflow: hidden;
}
.custom_h5 {
  height: 46px;
  line-height: 50px;
  font-size: 16px;
  color: #333333;
  border-bottom: 1px solid #cccccc;
  box-shadow: 1px 1px 1px #f2f2f2;
  color: steelblue;
  position: relative;
  .icon_h5 {
    position: absolute;
    height: 46px;
    width: 46px;
    right: 0;
    top: 0;
    display: block;
    @flex_a_j();
    font-size: 20px;
  }
}
.showBillDate {
  height: auto;
  font-size: 16px;
  @flex_a_j();
  margin: 30px 0;
}
.replenish {
  height: 45px;
  vertical-align: top;
  button {
    height: 100%;
    vertical-align: top;
    margin: 0 20px;
  }
}
// .sales_more{
//   height: calc(100% - 46px);
//   margin-top: 10px;
//   .filter{
//     border: 1px solid #eeeeee;
//     .seller_info{
//       font-size: 15px;
//     }
//   }
// }
/deep/.inputZf {
  .van-field__control {
    text-align: right;
  }
}
.van_search_style {
  display: contents;
  border-top: 1px solid #f2f2f2;
}
.van_search_btns {
  margin-left: 10px;
}
.submitSalesSlip {
  color: #000;
  font-size: 15px;
  margin: 0 15px 0 0;
  input {
    border: none;
    background: #555555;
  }
}
.submitSalesSlip:last-child {
  margin: 0;
}
.lowItem {
  width: 300px;
  height: auto;
  overflow: hidden;
  padding: 10px;
  h4 {
    height: 40px;
    font-size: 16px;
    font-weight: 500;
    color: #333333;
    @flex_a_j();
  }
  .lowItem_ul {
    height: auto;
    overflow: hidden;
    margin-bottom: 10px;

    li {
      height: auto;
      overflow: hidden;
      padding: 10px;
      font-size: 14px;
      @flex_a_bw();
      border-bottom: 1px solid #f2f2f2;
    }
    li:last-child {
      border-bottom: none;
    }
  }
}
.other_operate {
  width: 100%;
  height: auto;
  .other_operate_content {
    height: 40px;
    vertical-align: top;
    margin-bottom: 15px;
    @flex_a_j();
    button {
      width: 100px;
      height: 100%;
      vertical-align: top;
      margin: 0 15px;
    }
  }
}
</style>