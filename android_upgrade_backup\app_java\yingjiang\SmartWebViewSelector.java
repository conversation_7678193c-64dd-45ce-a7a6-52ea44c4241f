package com.yingjiang.app;

import android.content.Context;
import android.content.pm.PackageInfo;
import android.content.pm.PackageManager;
import android.os.Build;
import android.util.Log;

/**
 * 智能 WebView 选择器
 * 根据设备情况选择最适合的 WebView 实现
 */
public class SmartWebViewSelector {
    private static final String TAG = "SmartWebViewSelector";
    
    /**
     * 检查是否应该优先使用 X5 WebView
     * 在老设备上，X5 通常比系统 WebView 性能更好
     */
    public static boolean shouldUseX5WebView(Context context) {
        // 🔧 临时禁用X5测试性能问题 - 2025-09-08
        // 修改原因: 测试X5 WebView是否是20秒加载缓慢的根本原因
        boolean enableX5 = false; // 临时禁用X5，测试系统WebView性能
        if (!enableX5) {
            Log.i(TAG, "X5 WebView 已被临时禁用，使用系统WebView测试性能");
            return false; // 修复逻辑错误：禁用X5时应该返回false
        }

        // 🔧 Android 10+ 权限问题检查 - 暂时禁用以避免权限问题
        if (Build.VERSION.SDK_INT >= 34) {
            //android 14
            Log.i(TAG, "Android 10+ detected, using system WebView for better stability");
            return false; // Android 10+ 优先使用系统 WebView
        }
        else
        {
            return true;
        }
        // Android 6.0 以下，优先使用 X5（老设备确实需要）
       /* if (Build.VERSION.SDK_INT < Build.VERSION_CODES.M) {
            Log.i(TAG, "Android version < 6.0, prefer X5 WebView for better performance");
            return true;
        }

        // 系统 WebView 版本过旧时使用 X5
        if (isSystemWebViewTooOld(context)) {
            Log.i(TAG, "System WebView too old, prefer X5 WebView");
            return true;
        }

        // 低内存设备（<2GB），X5 内存管理可能更好
        if (isLowMemoryDevice()) {
            Log.i(TAG, "Low memory device, X5 has better memory management");
            return true;
        }

        // 🔧 移除国产设备判断 - 避免所有国产设备都使用 X5
        // 现代国产设备的系统 WebView 已经足够好了
        Log.i(TAG, "Modern device with good system WebView, using system WebView");
        return false;*/
    }

    /**
     * 检查是否需要使用兼容性 WebView（保持向后兼容）
     */
    public static boolean needCompatibilityWebView(Context context) {
        return shouldUseX5WebView(context);
    }
    
    /**
     * 检查系统 WebView 版本是否过旧
     */
    private static boolean isSystemWebViewTooOld(Context context) {
        try {
            PackageManager pm = context.getPackageManager();
            PackageInfo webViewPackage = pm.getPackageInfo("com.google.android.webview", 0);
            
            // 检查 WebView 版本号
            String versionName = webViewPackage.versionName;
            if (versionName != null) {
                // 提取主版本号
                String[] parts = versionName.split("\\.");
                if (parts.length > 0) {
                    try {
                        int majorVersion = Integer.parseInt(parts[0]);
                        // Chrome WebView 60 以下认为过旧
                        return majorVersion < 60;
                    } catch (NumberFormatException e) {
                        Log.w(TAG, "Failed to parse WebView version: " + versionName);
                    }
                }
            }
        } catch (PackageManager.NameNotFoundException e) {
            Log.w(TAG, "WebView package not found", e);
            return true; // 找不到 WebView 包，认为需要兼容性方案
        }
        
        return false;
    }
    
    /**
     * 检查是否为低内存设备
     */
    private static boolean isLowMemoryDevice() {
        try {
            Runtime runtime = Runtime.getRuntime();
            long maxMemory = runtime.maxMemory();

            // 更严格的低内存判断：小于 1GB 且 Android 版本较老
            boolean isLowMemory = maxMemory < 1024 * 1024 * 1024; // 1GB
            boolean isOldAndroid = Build.VERSION.SDK_INT < Build.VERSION_CODES.N; // Android 7.0

            // 只有同时满足低内存和老系统才认为需要 X5
            boolean needX5 = isLowMemory && isOldAndroid;

            if (needX5) {
                Log.i(TAG, "Low memory (" + (maxMemory / 1024 / 1024) + "MB) + old Android, may benefit from X5");
            }

            return needX5;
        } catch (Exception e) {
            Log.w(TAG, "Failed to check memory", e);
            return false;
        }
    }
    
    /**
     * 获取推荐的 WebView 引擎类名
     */
    public static String getRecommendedWebViewEngine(Context context) {
        if (shouldUseX5WebView(context)) {
            // 优先使用 X5 WebView（特别是老设备）
            if (isX5Available()) {
                Log.i(TAG, "Using X5 WebView for better performance on this device");
                return "org.jeremyup.cordova.x5engine.X5WebViewEngine";
            }
            // X5 不可用时，尝试 Crosswalk
            if (isCrosswalkAvailable()) {
                Log.i(TAG, "X5 not available, fallback to Crosswalk");
                return "org.crosswalk.engine.XWalkWebViewEngine";
            }
            // 最后降级到系统 WebView
            Log.i(TAG, "No alternative WebView available, using system WebView");
            return "org.apache.cordova.engine.SystemWebViewEngine";
        } else {
            // 新设备直接使用系统 WebView
            Log.i(TAG, "Using system WebView for modern device");
            return "org.apache.cordova.engine.SystemWebViewEngine";
        }
    }

    /**
     * 检查是否为国产设备
     */
    private static boolean isChineseDevice() {
        String manufacturer = Build.MANUFACTURER.toLowerCase();
        String brand = Build.BRAND.toLowerCase();

        String[] chineseBrands = {
            "huawei", "xiaomi", "oppo", "vivo", "oneplus", "meizu",
            "lenovo", "zte", "coolpad", "gionee", "letv", "nubia"
        };

        for (String chineseBrand : chineseBrands) {
            if (manufacturer.contains(chineseBrand) || brand.contains(chineseBrand)) {
                return true;
            }
        }

        return false;
    }
    
    /**
     * 检查 X5 WebView 是否可用
     */
    private static boolean isX5Available() {
        try {
            Class.forName("org.jeremyup.cordova.x5engine.X5WebViewEngine");
            // 进一步检查 TBS SDK 是否正常
            Class.forName("com.tencent.smtt.sdk.WebView");
            return true;
        } catch (ClassNotFoundException e) {
            return false;
        }
    }

    /**
     * 检查 Crosswalk 是否可用
     */
    private static boolean isCrosswalkAvailable() {
        try {
            Class.forName("org.crosswalk.engine.XWalkWebViewEngine");
            return true;
        } catch (ClassNotFoundException e) {
            return false;
        }
    }
}
