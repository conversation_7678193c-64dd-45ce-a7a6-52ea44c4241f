<template>
  <div>
    <van-dialog v-model="showDialog" get-container="body" :overlay="false" :show-confirm-button="false" width="320px">
      <div class="content-code-wrapper">
        <div class="content-code-title">
          <div class="code-title-first">*【{{ sheet.sup_name }}】客户扫码</div>
        </div>
        <div class="content-code-qr-code" :style="{backgroundColor:backgroundColor}">
          <div class="content-wrapper">
            <QrCodeTicket class="content-item" :qrCodeParams="qrCodeParams" />
            <div class="content-illustrate">
              * 说明：微信扫码关注公众号可获得订阅新单据通知、单据分享...等等功能
            </div>
          </div>
          <div class="content-wrapper">
            <van-button type="default" @click="handleMiniMallClick">商城</van-button>
            <div class="content-illustrate">
              * 说明：微信扫码绑定可以使用 商城 功能
            </div>
<!--            <div class="content-illustrate">-->
<!--              公司仓库： {{ mallSettingInfo.branch_name === '' ? '未设置' : mallSettingInfo.branch_name  }}-->
<!--            </div>-->
<!--            <div class="content-illustrate" v-if="showDeptBranchInfo">-->
<!--              前置仓： {{ findMatchingDept.dept_name }} &ndash;&gt; {{ findMatchingDept.branch_name }}-->
<!--            </div>-->
          </div>
        </div>
        <div class="content-code-btn" :style="{backgroundColor:backgroundColor}">
          <van-button type="default" @click="handleCloseDialog">关闭</van-button>
        </div>
      </div>
    </van-dialog>
    <MiniQrCode ref="miniQrCodeRef" :imgUrl="miniQrCodeBase64" :backgroundColor="backgroundColor" :titleShow="mallSettingInfo.mini_app_private ? '' : '营匠'" @handleClearMiniQrCodeBase64="handleClearMiniQrCodeBase64" />
  </div>
</template>

<script>
import QrCodeTicket from "./QrCodeTicket";
import { Dialog, Button, Toast } from 'vant';
import { CreateMiniQrCodeBase64, QueryMallSettingInfo } from "../../../api/api";
import MiniQrCode from "./MiniQrCode";
import globalVars from "../../../static/global-vars";

export default {
  name: "WeChatSaleSheetQrCode",
  components: {
    MiniQrCode,
    QrCodeTicket,
    [Dialog.Component.name]: Dialog.Component,
    "van-button": Button
  },
  props: {
    sheet: {
      type: Object,
      default: () => {
      }
    },
    backgroundColor:{
      type:String,
      default:'#fff'
    }
  },
  data() {
    return {
      showDialog: false,
      hasMall:false,
      mallSettingInfo: {
        company_id: '',
        mall_type: '',
        default_branch_id: '',
        branch_name: '',
        enable_branch: '',
        branch_map: [],
        qr_code_enable_expired: '',
        qr_code_alive_time: '',
        mini_app_id: '',
        // mini_app_secret: '',
        mini_app_private: ''
      },
      findMatchingDept: {
        nanoid: '',
        dept_id: '',
        branch_id: '',
        dept_name: '',
        dept_path: '',
        branch_name: '',
      }, // 专业版匹配的部门仓库信息
      showQrCodeFlag: false,
      qrCodeParameter: {
        qrCodeExpiresTime: 0,
        operKey: '',
        supcustId: '',
        qrScene: '',
        otherInfo: {}
      },
      miniQrCodeBase64: ''
    }
  },
  mounted() {
  },
  computed: {
    showDeptBranchInfo() {
      return this.mallSettingInfo.mall_type === 'p' && this.findMatchingDept.dept_id !== ''
    },
    qrCodeParams() {
      return {
        company_id: this.sheet.company_id,
        supcust_id: this.sheet.supcust_id,
        mobile: this.sheet.mobile,
        contact_nick_name: this.sheet.sup_name
      }
    },
  },
  methods: {
    handleOpenDialog() {
      this.showDialog = true
      this.handleInitMiniConfigInfo()
    },
    handleCloseDialog() {
      this.showDialog = false
    },
    async handleGenerateWxQrCode(){
      await this.handleInitMiniConfigInfo()
      this.handleMiniMallClick()
    },
    // region 获取商城配置信息，处理商城参数
    async handleInitMiniConfigInfo() {
      await QueryMallSettingInfo({
        operKey: this.$store.state.operKey
      }).then(res => {
        if (res.code === 0) {
          this.mallSettingInfo = res.result
          this.hasMall=true
          this.handelSettingMiniConfigInfo()
        } else {
         // Toast.fail(res.message)
        }
      }).catch(err => {
        console.log(err)
      })
    },
    handelSettingMiniConfigInfo() {
      this.mallSettingInfo.qr_code_enable_expired = this.mallSettingInfo.qr_code_enable_expired === 'True'
      this.mallSettingInfo.mini_app_private = this.mallSettingInfo.mini_app_private === 'True'
      // 在进行校验
      let yingjiangMiniAppId = globalVars.wechatConf.miniAppId
      if (this.mallSettingInfo.mini_app_id !== '' && this.mallSettingInfo.mini_app_id !== yingjiangMiniAppId) {
        this.mallSettingInfo.mini_app_private = true
      } else {
        this.mallSettingInfo.mini_app_private = false
      }
      // if (this.mallSettingInfo.mall_type === 'p') {
      //   // 专业版需要进行匹配
      //   let operDeptPath = this.$store.state.operInfo.oper_dept_path
      //   const findDept = findMatchingDept(operDeptPath, this.mallSettingInfo.branch_map);
      //   if (findDept) {
      //     this.findMatchingDept = findDept
      //   }
      // }
      //
      // function findMatchingDept(operDeptPath, branchMap) {
      //   let matchDept = null;
      //   let potentialDepts = [];
      //   for (let dept of branchMap) {
      //     if (operDeptPath === dept.dept_path) {
      //       // 找到了完全匹配的部门，直接返回
      //       return dept;
      //     } else if (operDeptPath.startsWith(dept.dept_path)) {
      //       // 当前部门是 oper_dept_path 的父部门，记录下来
      //       potentialDepts.push(dept);
      //     }
      //   }
      //   if (potentialDepts.length > 0) {
      //     // 如果有潜在的符合条件的部门，找到最深层级的部门并返回
      //     let maxDepth = 0;
      //     for (let dept of potentialDepts) {
      //       let depth = dept.dept_path.split('/').filter(p => p !== '').length;
      //       if (depth > maxDepth) {
      //         maxDepth = depth;
      //         matchDept = dept;
      //       }
      //     }
      //   }
      //   return matchDept;
      // }
    },
    // endregion

    // region 生成二维码
    handleMiniMallClick() {
      // 判断是否开通、专业版还是私版
      let errMsg = this.handleCheckMallSettingInfo()
      if (errMsg) {
        Toast.fail(errMsg)
        return
      }
      this.handleCreateMiniQrCodeBase64()
    },
    handleCheckMallSettingInfo() {
      let errMsg = ''
      if (this.mallSettingInfo.mall_type === '') {  // 未开通小程序
        errMsg = '当前未开通小程序'
      } else {
        if (this.mallSettingInfo.default_branch_id === '') {
          errMsg = '未设置公司仓库'
        }
        // if (this.mallSettingInfo.mall_type === 's') { // 标准版判断
        //
        // } else if (this.mallSettingInfo.mall_type === 'p') {
        //   if (!this.findMatchingDept.dept_id && this.mallSettingInfo.default_branch_id === '') {
        //     errMsg = '未找到匹配仓库信息'
        //   }
        // }
      }
      return errMsg
    },
    async handleCreateMiniQrCodeBase64() {
      this.$refs.miniQrCodeRef.handleOpenMiniMall()
      this.qrCodeParameter.qrCodeExpiresTime = this.mallSettingInfo.qr_code_enable_expired ? Number(this.mallSettingInfo.qr_code_alive_time) : 0
      this.qrCodeParameter.operKey = this.$store.state.operKey
      this.qrCodeParameter.supcustId = Number(this.sheet.supcust_id)
      this.qrCodeParameter.qrScene = 'w'
      this.qrCodeParameter.otherInfo = {}

      this.qrCodeParameter.miniAppPrivate = this.mallSettingInfo.mini_app_private
      this.qrCodeParameter.miniAppId = this.mallSettingInfo.mini_app_id
       CreateMiniQrCodeBase64(this.qrCodeParameter).then(res => {
        if (res.code === 0) {
          this.miniQrCodeBase64 = "data:image/png;base64," + res.result
        } else {
          Toast.fail('创建二维码失败，请重试')
        }
      }).catch(err => {
        Toast.fail(err)
      })
    },
    handleClearMiniQrCodeBase64() {
      this.miniQrCodeBase64 = ''
    }
    // endregion
  }
}
</script>

<style scoped lang="less">
/deep/ .van-button {
  width: 100%;
  background-color: #fde3e4;
}

.content-code-wrapper {
  width: 100%;
  min-height: 400px;
  background-color: #eee;
  border-right: 1px solid #eee;
  display: flex;
  box-shadow: 3px 3px 17px #333333;
  flex-direction: column;
  box-sizing: border-box;

  .content-code-title {
    width: 100%;
    height: 60px;
    box-sizing: border-box;
    padding: 5px 10px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    border-bottom: 1px solid #eeeeee;
  }

  .content-code-qr-code {
    height: 300px;
    background-color: #fff;
    border-bottom: 1px solid #eee;
    box-sizing: border-box;
    padding: 10px;
    display: flex;
    flex-direction: column;

    img {
      width: 100%;
      height: 100%;
    }

    .content-wrapper {
      width: 100%;
      min-height: 100px;
      display: flex;
      flex-direction: column;
      margin-bottom: 40px;

      button {
        min-height: 45px;
        border-radius: 12px;
      }

      .content-item {
        width: 100%;
      }

      .content-illustrate {
        margin-top: 10px;
        color: #aaa;
      }
    }
  }

  .content-code-btn {
    display: flex;
    height: 60px;
    background-color: #fff;
    justify-content: center;
    padding-top: 10px;

    button {
      width: 80%;
      background-color: #fde3e4;
      border-radius: 10px;
    }
  }
}
</style>
