package com.exception;

import android.content.Context;
import android.util.Log;
import java.io.IOException;
import java.io.PrintWriter;
import java.io.StringWriter;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Locale;

import okhttp3.Call;
import okhttp3.Callback;
import okhttp3.MediaType;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.RequestBody;
import okhttp3.Response;

public class CustomExceptionHandler implements Thread.UncaughtExceptionHandler {
    private static final String TAG = "CustomExceptionHandler";
    private Thread.UncaughtExceptionHandler defaultHandler;
    private Context context;

    public CustomExceptionHandler() {
        defaultHandler = Thread.getDefaultUncaughtExceptionHandler();
    }

    public CustomExceptionHandler(Context context) {
        this.context = context;
        defaultHandler = Thread.getDefaultUncaughtExceptionHandler();
    }

    @Override
    public void uncaughtException(Thread thread, Throwable throwable) {
        Log.e(TAG, "捕获到未处理异常", throwable);

        try {
            // 1. 记录异常信息到字符串
            String exceptionDetails = formatExceptionDetails(throwable);

            // 2. 尝试上传到服务器（异步，不阻塞崩溃处理）
            uploadLogToServerAsync(exceptionDetails);

        } catch (Exception e) {
            Log.e(TAG, "处理异常时发生错误", e);
        }

        // 3. 交给默认的异常处理程序处理
        if (defaultHandler != null) {
            defaultHandler.uncaughtException(thread, throwable);
        }
    }

    private String formatExceptionDetails(Throwable throwable) {
        try {
            // 获取当前时间
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss", Locale.getDefault());
            String timestamp = sdf.format(new Date());

            // 获取详细的堆栈跟踪
            StringWriter sw = new StringWriter();
            PrintWriter pw = new PrintWriter(sw);
            throwable.printStackTrace(pw);
            String stackTrace = sw.toString();

            // 构建详细的异常日志内容
            StringBuilder sb = new StringBuilder();
            sb.append("=== 应用崩溃报告 ===\n");
            sb.append("时间: ").append(timestamp).append("\n");
            sb.append("时间戳: ").append(System.currentTimeMillis()).append("\n");
            sb.append("线程: ").append(Thread.currentThread().getName()).append("\n");
            sb.append("异常类型: ").append(throwable.getClass().getName()).append("\n");
            sb.append("异常消息: ").append(throwable.getMessage()).append("\n");
            sb.append("详细堆栈:\n").append(stackTrace);
            sb.append("=== 报告结束 ===\n\n");

            return sb.toString();
        } catch (Exception e) {
            Log.e(TAG, "格式化异常信息失败", e);
            return "格式化异常信息失败: " + e.getMessage();
        }
    }

    private void uploadLogToServerAsync(String logDetails) {
        // 异步上传，不阻塞崩溃处理流程
        new Thread(new Runnable() {
            @Override
            public void run() {
                uploadLogToServer(logDetails);
            }
        }).start();
    }

    private void uploadLogToServer(String logDetails) {
        try {
            Log.i(TAG, "开始上传崩溃日志到服务器");

            OkHttpClient client = new OkHttpClient.Builder()
                    .connectTimeout(10, java.util.concurrent.TimeUnit.SECONDS)
                    .writeTimeout(10, java.util.concurrent.TimeUnit.SECONDS)
                    .readTimeout(10, java.util.concurrent.TimeUnit.SECONDS)
                    .build();

            RequestBody body = RequestBody.create(MediaType.parse("text/plain; charset=utf-8"), logDetails);

            Request request = new Request.Builder()
                    .url("https://www.yingjiang168.com/AppApi/Login/AndroidDumpReport")
                    .post(body)
                    .addHeader("Content-Type", "text/plain; charset=utf-8")
                    .build();

            client.newCall(request).enqueue(new Callback() {
                @Override
                public void onFailure(Call call, IOException e) {
                    Log.e(TAG, "上传崩溃日志失败", e);
                }

                @Override
                public void onResponse(Call call, Response response) throws IOException {
                    if (response.isSuccessful()) {
                        Log.i(TAG, "崩溃日志上传成功");
                    } else {
                        Log.e(TAG, "服务器返回错误: " + response.code() + " " + response.message());
                    }
                    response.close();
                }
            });

        } catch (Exception e) {
            Log.e(TAG, "上传崩溃日志时发生异常", e);
        }
    }
}
