<template>
  <div class="pages" ref="pages">
    <div ref="navBarTitle">
      <van-nav-bar title="库存查询" left-arrow @click-left="myGoBack($router)" safe-area-inset-top>
        <template #right>
        <van-button style="background-color:transparent;border:none;padding:10px;margin-right:10px;" type="info" @click="btnScanBarcode_click()">
          <svg width="26px" height="26px" fill="#444">
            <use xlink:href="#icon-barcodeScan"></use>
          </svg>
        </van-button>
          <div class="iconfont icon_filter" @click="() => { showPopup = !showPopup; }">
            &#xe690;
          </div>
        </template>
      </van-nav-bar>
    </div>

    <!--      筛选信息-->
    <div class="content">
      <van-popup class="popQueryContent" v-model="queryInfoWrapperShowFlag" position="top" :overlay="false" :style="{ width: '100%','margin-top': navBarHeight + 'px','overflow-y':'hidden' }">
        <div class="query_info_wrapper">
          <div class="query_info_1">
            <van-form class="query_from">
              <van-search v-model="itemName" class="van-search search_name" left-icon="" right-icon="search" placeholder="品名/条码/货号/助记码" @input="searchByItemName" />
              <YjSelectTree
              style="width: 50%;"
              ref="selectTreeRef"
              :target="'class'"
              :title="title"
              :confirmColor="confirmColor"
              :rangeKey="rangeKey"
              :rootNode="rootNode"
		          :idKey="idKey"
              :sonNode="sonNode"
              :parentSelectable="parentSelectable"
              :popupHeight="'85%'"
              @getRootNode="getRootNode"
              @handleConfirm="itemClassSelect"
              >
                <template #select-tree-content>
                  <van-search style="width: 100%;" v-model="className" class="van-search" readonly left-icon="" right-icon="apps-o" placeholder="类别"/>
                </template>
              </YjSelectTree>
            </van-form>
          </div>
          <div class="query_info_2">
            <van-form>
              <van-search v-model="branchName" class="van-search" readonly left-icon="" right-icon="wap-home-o" placeholder="仓库" @click="showPicker = true" />
              <van-search v-model="branchPositionName" class="van-search" readonly left-icon="" right-icon="wap-home-o" placeholder="库位" @click="showPositionPicker = true" />
            </van-form>
          </div>

          <div class="query_info_title">
            <div>商品名称</div>
            <div v-if="showAvailItems">可用库存</div>
            <div>实际库存</div>
          </div>
          <div style="height: 2px">
            <div style="background-image: linear-gradient(to right,transparent 0%, #ccc 12%, #999 18%, transparent 30%); width:100%; height: 1.5px; background-size: 10px 40px; background-repeat: repeat-x;"></div>

          </div>

        </div>
      </van-popup>

      <!--      列表区域-->
      <div class="list_wrapper" ref="listWrapper" :style="{height:listWrapperHeight}">
        <div class="list_layout"></div>
        <template v-if="listData && listData.length > 0">
          <van-list class="van_list" v-model="loading" :finished="finished" finished-text="到底了~" @load="nextPage">
            <!-- <ul class="receive_ul">
              <li v-for="(item, index) in listData" :key="index">
                <div>{{ item.item_name }}</div>
                <div v-if="allowViewStockamount">{{item.costs}}</div>
                <div>{{ item.qty}}</div>
              </li>
            </ul> -->

            <div class="good_info_wrapper" v-for="(item, index) in listDataJson" :key="index">
              <!-- 分类名字 -->
              <div class="good_class_wrapper">
                <div class="good_class_title">{{ item.className }}</div>
                <div class="good_class_cs">
                  <!--  <div class="good_class_costs" style="min-width: 30px;" v-if="allowViewStockamount">{{item.class_costs}}</div>  -->
                  <div class="good_class_stock">{{ item.total_stock }}</div>
                </div>

              </div>
              <!-- 商品信息 -->
              <div class="good_info_content" v-for="(itemRow, itemRowsIndex) in item.itemRows" :key="itemRowsIndex" @click="enterStockDetail(itemRow)">
                <!--  <div class="good_content_name"></div>-->
                <div class="good_content_bottom">
                  <div class="good_content_layout" style="text-align:left;">
                    <div><span v-if="itemRow.status=='0'" style="color:#f66;">停用</span> {{ itemRow.item_name }}</div>
                    <div style="color:#bbb;"> {{ itemRow.unit_rates }}</div>

                  </div>
                  <div v-if="showAvailItems" class="good_content_costs">
                    <span v-if="qtyType=='s_qty'">{{ itemRow.avail_stock_qty+itemRow.s_unit_no}}</span>
                    <span v-else>{{ itemRow.avail_qty?itemRow.avail_qty:0}}</span>
                  </div>
                  <div class="good_content_qty">
                    
                    <div v-if="qtyType=='s_qty'">{{ itemRow.stock_qty + itemRow.s_unit_no}}</div>
                    <div v-else>{{ itemRow.qty ? itemRow.qty:0}}</div>
                    <template v-if=" showStockAmount">
                      <div STYLE="color:#aaa">
                        <div v-if="costsOrwholesaleFlag == 'cost'&&canSeeInPrice ">
                          成本:{{ itemRow.costs }}
                        </div>
                        <div v-if="costsOrwholesaleFlag == 'unit_cost'&&itemRow.stock_qty ">
                          成本:<a>
                            {{parseFloat(itemRow.costs/itemRow.stock_qty).toFixed(2)+itemRow.s_unit_no }}
                          </a>
                          <a v-if="itemRow.b_unit_factor">
                            {{parseFloat((itemRow.costs*itemRow.b_unit_factor)/itemRow.stock_qty).toFixed(2)+ itemRow.b_unit_no }}
                          </a>
                        </div>
                        <div v-if=" costsOrwholesaleFlag == 'wholesaleAmount' ">
                          批发: ￥{{toMoney(itemRow.wholesale_amount,2,true) }}
                        </div>
                        <div v-if=" costsOrwholesaleFlag == 'contractAmount' ">
                          承包: ￥{{toMoney(itemRow.contract_amount,2,true) }}
                        </div>
                      </div>
                    </template>
                  </div>
                </div>
                <div class="stock-detail-info" >
                    <div class="branch-info" style="padding:5px 0;color:#BBB;display:flex;justify-content: space-between;align-items: center;" v-if="(itemRow.branch_id !=branchID && itemRow.branch_id) || ( itemRow.branch_position !=branchPosition && itemRow.branch_position_name)">
                      <div class="branch-id" v-if="itemRow.branch_id!=branchID && itemRow.branch_id ">仓库:{{ itemRow.branch_name }}</div>
                      <div class="branch-position" v-if="itemRow.branch_position!=branchPosition && itemRow.branch_position_name">库位:{{ itemRow.branch_position_name }}</div>
                    </div>
                    <div class="batch_info" style="padding:5px 0;color:#BBB;display:flex;justify-content: space-between;align-items: center;">
                      <div class="batch_no" v-if="itemRow.batch_no">批次:{{ itemRow.batch_no }}</div>
                      <div class="produce_date" v-if="itemRow.produce_date">产期:{{ itemRow.produce_date }}</div>
                    </div>
                  </div>
              </div>
            </div>

          </van-list>
        </template>
        <div class="report_no_box" v-else>
          <div class="whole_box_no_icon iconfont">&#xe664;</div>
          <p>未查询到信息</p>
        </div>
      </div>
      <!--      底部区域-->
      <div class="stock_footer">
        <div class="stock_footer_left">合计</div>
        <div class="stock_footer_right">
          <template v-if="showStockAmount">
            <div class="stock_footer_right_top" v-if="costsOrwholesaleFlag == 'cost'|| costsOrwholesaleFlag == 'unit_cost'">
              成本:{{ total_cost }}
            </div>
            <div class="stock_footer_right_top" v-if=" costsOrwholesaleFlag == 'wholesaleAmount' ">
              批发额: ￥{{toMoney(wholesale_total,2,true) }}
            </div>
            <div class="stock_footer_right_top" v-if=" costsOrwholesaleFlag == 'contractAmount' ">
              承包额: ￥{{toMoney(contract_total,2,true) }}
            </div>
          </template>

          <div class="stock_footer_right_bottom" :style="showStockAmount ?'line-height:40px;':''">
            <span v-if="qtyType=='s_qty'">小单位数: {{ total_stock_qty }} </span>
            <span v-else>{{ total }} </span>
          </div>
        </div>
      </div>
    </div>

    <!-- <van-popup style="overflow: hidden !important" v-model="showClass" position="bottom" :style="{ height: '85%', width: '100%' }">
      <class-selection :parentSelectable="true" @itemClassSelect="onClassSelect"></class-selection>
    </van-popup> -->
    <van-popup v-model="showPicker" :style="{ height: '375px' }" @click-overlay="handleBranchIdConfirm" position="bottom">
        <div class="branch_list_wrapper">
          <div class="branch_list_btns">
            <div class="btns_left" @click="handleBranchIdSelectAll">
              <van-checkbox checked-color="#FF6173" v-model="branchSelectAllFlag">全选</van-checkbox>
            </div>
            <div class="btns_right" @click="handleBranchIdConfirm">
              确认
            </div>
          </div>
          <div class="branch_list_content">
            <van-checkbox-group ref="checkboxGroup" v-model="branchIDArr">
              <van-cell-group>
                <div v-for="(item, index) in branchList" :key="item.branch_id" class="branch_list_content_item">
                  <van-checkbox :name="item" ref="checkboxes" checked-color="#FF6173" class="branch_list_content_item_checkbox" />
                  <van-cell clickable :title="item.branch_name" @click="handeleToggle(index)" />
                </div>
              </van-cell-group>
            </van-checkbox-group>
          </div>
        </div>
    </van-popup>
    <van-popup v-model="showPositionPicker" :style="{ height: '375px' }" @click-overlay="handleBranchPositionConfirm" position="bottom">
        <div class="branch_list_wrapper">
          <div class="branch_list_btns">
            <div class="btns_left" @click="handleBranchPositionSelectAll">
              <van-checkbox checked-color="#FF6173" v-model="branchPositionSelectAllFlag">全选</van-checkbox>
            </div>
            <div class="btns_right" @click="handleBranchPositionConfirm">
              确认
            </div>
          </div>
          <div class="branch_list_content">
            <van-checkbox-group ref="checkboxGroupPositionGroup" v-model="branchPositionArr">
              <van-cell-group>
                <div v-for="(item, index) in branchPositionList" :key="item.branch_position_name" class="branch_list_content_item">
                  <van-checkbox :name="item" ref="checkboxesPosition" checked-color="#FF6173" class="branch_list_content_item_checkbox" />
                  <van-cell clickable :title="item.branch_position_name" @click="handelePositionToggle(index)" />
                </div>
              </van-cell-group>
            </van-checkbox-group>
          </div>
        </div>
    </van-popup>
    <!-- <van-popup v-model="showPicker" :style="{ height: '375px' }" @click-overlay="handleBranchInfoConfirm" position="bottom">
      <div class="branch_list_wrapper">
        <div class="branch_list_btns">
          <div class="btns_left">
            <van-checkbox checked-color="#FF6173" v-model="branchSelectAllFlag" @click="handleBranchSelectAll">全选</van-checkbox>
          </div>
          <div class="btns_right" @click="handleBranchInfoConfirm">
            确认
          </div>
        </div>
        <div class=branch_info_content>
          <div class="branch_list_content">
            <van-cell-group>
              <div v-for="(item, index) in branchList" :key="item.branch_id" :class="['branch_list_content_item',curIndex == index?'branch_list_content_cur_item':'']">
                <van-cell clickable :title="item.branch_name" @click="handleToggleBranch(index)"/>
              </div>
            </van-cell-group>
          </div>
          <div class="branch_position_list_content">
            <van-cell-group>
              <div v-for="(item, index) in curBranchPositionList" :key="item.branch_position" class="branch_position_list_content_item">
                <van-checkbox :name="item" ref="checkboxes" checked-color="#FF6173" v-model="item.isSelect" @click="handleToggle(item)" class="branch_position_list_content_item_checkbox" />
                <van-cell clickable :title="item.branch_position_name"  />
              </div>
            </van-cell-group>
          </div>
        </div>
      </div>
    </van-popup> -->
    <!-- 侧滑栏 -->
    <van-popup class="van_popup" v-model="showPopup" position="right" close-on-click-overlay close-on-popstate safe-area-inset-bottom :duration="0.3" :style="{ width: '80%', height: '100%' }">
      <h5>
        其他选择
        <van-icon name="cross" @click="showPopup = false" />
      </h5>
      <div class="alloc_list">
        <div class="alloc_list_box">
          <!-- <van-divider :style="{ color: '#1989FA', borderColor: '#1989FA' }">
            其他选择
          </van-divider> -->
        
          <van-cell center title="显示零库存商品">
            <template #right-icon>
              <van-switch v-model="showZeroStock" size="24" active-color="#ee0a24" @change="selectZeroStock" />
            </template>
          </van-cell>
          <van-cell center title="展示停用商品">
            <template #right-icon>
              <van-switch v-model="showExpiredItems" size="24" active-color="#ee0a24" @change="selectStopItem" />
            </template>
          </van-cell>
          <van-cell center title="展示可用库存">
            <template #right-icon>
              <van-switch v-model="showAvailItems" size="24" active-color="#ee0a24" @change="selectAvailItem" />
            </template>
          </van-cell>
          <div class="choose_wrapper">
            <div class="choose_text">排序方式</div>
              <van-dropdown-menu>
                <van-dropdown-item v-model="sortType" :options="sortOption" />
              </van-dropdown-menu>
          </div>
          <div class="choose_wrapper">
            <div class="choose_text">库存展示方式</div>
              <van-dropdown-menu>
                <van-dropdown-item v-model="qtyType" :options="qtyOption" />
              </van-dropdown-menu>
          </div>
          <van-cell center title="显示库位">
            <template #right-icon>
              <van-switch v-model="showPositionStock" size="24" active-color="#ee0a24" @change="selectPositionStock" />
            </template>
          </van-cell>
          <van-cell center title="显示批次">
            <template #right-icon>
              <van-switch v-model="showBatchStock" size="24" active-color="#ee0a24" @change="selectBatchStock" />
            </template>
          </van-cell>
          <van-cell center title="展示金额" v-if="allowViewStockAmount">
            <template #right-icon>
              <van-switch v-model="showStockAmount" size="24" active-color="#ee0a24" @change="selectShowStockAmount" />
            </template>
          </van-cell>
        
          <van-radio-group v-model="costsOrwholesaleFlag" v-if="showStockAmount">
            <van-cell-group>
              <van-cell title="成本总额" clickable @click="handleAmountFlag('cost')" v-if="canSeeInPrice">
                <template #right-icon>
                  <van-radio name="cost" />
                </template>
              </van-cell>
              <van-cell title="成本单价" clickable @click="handleAmountFlag('unit_cost') " v-if="canSeeInPrice">
                <template #right-icon>
                  <van-radio name="unit_cost" />
                </template>
              </van-cell>
              <van-cell title="批发金额" clickable @click=" handleAmountFlag('wholesaleAmount') ">
                <template #right-icon>
                  <van-radio name="wholesaleAmount" />
                </template>
              </van-cell>
              <van-cell title="承包金额" clickable @click=" handleAmountFlag('contractAmount') ">
                <template #right-icon>
                  <van-radio name="contractAmount" />
                </template>
              </van-cell>
            </van-cell-group>
          </van-radio-group>
          <div class="choose_wrapper">
            <div class="choose_text">排序方式</div>
              <van-dropdown-menu>
                <van-dropdown-item v-model="sortType" :options="sortOption" />
              </van-dropdown-menu>
          </div>
        </div>
        <div class="alloc_list_box other">

          <!--          <van-cell-->
          <!--              center-->
          <!--              title="扫一扫"-->
          <!--              style="display: none"-->
          <!--          >-->
          <!--            <template #right-icon>-->
          <!--              <i class="iconfont" style="width: 50px">&#xe607;</i>-->
          <!--            </template>-->
          <!--          </van-cell>-->

          <div class="btns_wrapper">
            <div class="btns_top">
              <div class="query_btn" @click="handleQuery">查询</div>
            </div>
            <div class="btns_bottom">
              <div class="btn_other" @click="selectBluetoothPrint">打印</div>
              <div class="btn_other" @click="clearSelect">重置</div>
            </div>

          </div>
        </div>
      </div>
      <!--      <div class="clearSelect">-->
      <!--        <van-button type="default" plain @click="myGoBack($router)"-->
      <!--        >退出页面-->
      <!--        </van-button-->
      <!--        >-->
      <!--        <van-button color="#7232dd" plain @click="clearSelect"-->
      <!--        >清除选择-->
      <!--        </van-button-->
      <!--        >-->
      <!--      </div>-->
    </van-popup>
    <!-- 侧滑栏 -->
  </div>
</template>
<script>
import {
  NavBar,
  Form,
  Search,
  List,
  Popup,
  Cell,
  Switch,
  Divider,
  Picker,
  Button,
  Toast,
  Icon,
  RadioGroup,
  DropdownMenu,
  DropdownItem,
  Radio,
  CellGroup,
  Checkbox,
  CheckboxGroup,
} from "vant"
import { GetStockReport, GetBranchList } from "../../api/api"
import ClassSelection from "../components/ItemClass"
import Printing from "../Printing/Printing"
import YjSelectTree from "../components/yjTree/YjSelectTree.vue";
import toast from 'vant/lib/toast'
import { throws } from "assert"

export default {
  name: "Stock",
  data() {
    return {
      loading: false,
      finished: false,
      itemName: '',
      startRow: 0,
      pageSize: 200,
      listData: [],
      listDataJson: {},
      showPopup: false, //侧滑栏
      showZeroStock: false,
      showExpiredItems: true,
      showAvailItems: true,
      showStockAmount: false,
      scanCode: false,
      bluetoothPrint: false,
      classID: '', //商品类别id
      branchID: '',
      branchIDArr: [],
      className: '',
      branchName: '',
      timer:null,
      showClass: false,
      branchList: [],
      showPicker: false,
      showPositionPicker:false,
      warehouse_id: '',
      avail_total: '',
      total: '',
      total_stock_qty:'',
      total_avail_stock_qty:'',
      total_cost: '',
      wholesale_total: '',
      contract_total: '',
      printInfo: {}, // 打印信息,
      costsOrwholesaleFlag: '',
      queryInfoWrapperShowFlag: true,
      queryInfoWrapperTop: 0,
      queryInfoScoll: 0,
      navBarHeight: 46,
      listWrapperHeight: '',
      title:'类别选择',
			rangeKey: 'name',
      idKey: 'id',
      sonNode:'subNodes',
      asPage:true,
			parentSelectable: true,
      foldAll: false,
			confirmColor:'#e3a2a2',
      rootNode:{},
      branchPosition:"",
      branchPositionName:"",
      // showPickerPosition:false,
      branchPositionList:[],
      // curBranchPositionList:[],
      branchPositionArr: [],
      positionLen:0,
      curIndex:0,
      // branchSelectAllFlag:false,
      showPositionStock:true,
      showBatchStock:true,
      sortType:"default",
      sortOption: [
        { text: '默认', value: 'default' },
        { text: '商品名称', value: 'item_name' },
        { text: '序号', value: 'order_index' }
      ],
      qtyType:'default',
      qtyOption: [
        { text: '辅助数量', value: 'default' },
        { text: '小单位数', value: 's_qty' },
        { text: '大单位数', value: 'b_qty' }
      ],
      totalCount:0,
    }
  },
  computed: {
    branchSelectAllFlag: {
      get: function () {
        return this.branchIDArr.length == this.branchList.length
      },
      set: function () {
        return this.branchIDArr.length == this.branchList.length
      }
    },
    branchPositionSelectAllFlag: {
      get: function () {
        return this.branchPositionArr.length == this.branchPositionList.length
      },
      set: function () {
        return this.branchPositionArr.length == this.branchPositionList.length
      }
    },
    canSeeInPrice() {
      return window.getRightValue('delicacy.seeInPrice.value') === 'true'
    },
    allowViewStockAmount() {
      return window.getRightValue('delicacy.allowViewStockAmount.value') !== 'false'

    },
    // branchInfoShow(){
    //   if(this.branchIDArr.length ==1 && this.branchPositionArr.length<=1){
    //     return this.branchName+ (this.branchPosition?'/'+this.branchPositionName:'')
    //   }else{
    //     return this.branchName
    //   }
    // },
    // branchPositionInfoShow(){
    //   if(this.branchIDArr.length ==1 && this.branchPositionArr.length<=1){
    //     return this.branchName+ (this.branchPosition?'/'+this.branchPositionName:'')
    //   }else{
    //     return this.branchName
    //   }
    // },

  },

  mounted() {
    this.listDataJson = {}
    // if(this.allowViewStockAmount){
    //   if (this.canSeeInPrice) {
    //     this.costsOrwholesaleFlag = 'cost'
    //   } else {
    //     this.costsOrwholesaleFlag = 'wholesaleAmount'
    //   }
    // }
    let params = {}
    this.sortType = this.$store.state.queryItemSortType??"default"
    this.qtyType = this.$store.state.stockQtyType??"default"
    
    this.showZeroStock = this.$store.state.showZeroStock ?? false
    this.showPositionStock = this.$store.state.showPositionStock ?? false
    this.showBatchStock = this.$store.state.showBatchStock ?? false
    this.showAvailItems = this.$store.state.showAvailItems ?? true
    this.showExpiredItems = this.$store.state.showExpiredItems ?? true
  console.log({showExpiredItems:this.$store.state.showExpiredItems })
    GetBranchList(params).then((res) => {
      this.branchList = []
      if (res.result === "OK") {
        let branchPositionObj = {}
        for (var i = 0; i < res.data.length; i++) {
          var branch = res.data[i]
          var branchValid = window.hasBranchOperRight(branch.branch_id, 'query_stock')
          if (branchValid) {
            branch.branch_position = JSON.parse(branch.branch_position)
            // let hasPositionInfoFlag = true
            branch.branch_position.forEach(bp=>{
              if(branchPositionObj[bp.branch_position_name] == void(0)) {
                if(bp.branch_position !== "0"){
                  this.branchPositionList.push(bp) 
                  branchPositionObj[bp.branch_position_name] = bp
                }
              }
              // bp.isSelect = false
              // if(bp.branch_position == "0") hasPositionInfoFlag = false
              // this.branchPositionList.push(bp) 
            })
            // if(hasPositionInfoFlag){
            //   let defaultPosition = {
            //     isSelect:false,
            //     branch_position:'0',
            //     branch_position_name:' '
            //   }
            //   branch.branch_position.push(defaultPosition)
            //   this.branchPositionList.push(defaultPosition) 
            // }
            this.branchList.push(branch) 
          }
        }
        if(this.branchPositionList.length>0){
          this.branchPositionList.push({
            branch_position:"0",
            branch_position_name:"默认库位"
          })
        }
        // this.curBranchPositionList = this.branchList.length?this.branchList[0].branch_position:[]
        this.handleBranch()
        if (this.branchID)
          this.newQuery()
      }
    })
    this.$refs.listWrapper.addEventListener('scroll', this.handleScroll)
    //this.navBarHeight = this.$refs.navBarTitle.offsetHeight
    this.navBarHeight = this.$refs.navBarTitle.getBoundingClientRect().height
    console.log("$refs.pages.offsetHeight", this.$refs.pages.offsetHeight)
    this.listWrapperHeight = (this.$refs.pages.offsetHeight - this.navBarHeight - 70) + 'px'

    if (this.allowViewStockAmount) {
      this.getAmountFlag()
    }

  },
  components: {
    "van-nav-bar": NavBar,
    "van-form": Form,
    "van-search": Search,
    "van-list": List,
    "van-popup": Popup,
    "van-cell": Cell,
    "van-switch": Switch,
    "class-selection": ClassSelection,
    "van-picker": Picker,
    "van-button": Button,
    "van-icon": Icon,
    "van-radio-group": RadioGroup,
    "van-radio": Radio,
    "van-cell-group": CellGroup,
    "van-checkbox": Checkbox,
    "van-checkbox-group": CheckboxGroup,
    "van-divider": Divider,
    "van-dropdown-menu": DropdownMenu,
    "van-dropdown-item": DropdownItem,
    YjSelectTree
  },
  methods: {
    async btnScanBarcode_click() {
      try {
        const res = await this.scanBarcodeNew({
          unit_type: 'stock_query'
        })

        if (!res.code) {
          return
        }

        console.log('扫码结果:', res.code, '格式:', res.format)
        this.searchStr = res.code
        this.itemName = res.code
        this.newQuery()
      } catch (error) {
        console.error('扫码失败:', error)
        this.$toast('扫码失败，请重试')
      }
    },
    handleScroll() {
      this.queryInfoWrapperTop = this.$refs.listWrapper.scrollTop
      let scollTemp = this.queryInfoWrapperTop - this.queryInfoScoll
      this.queryInfoScoll = this.queryInfoWrapperTop
      if (scollTemp < 0) {
        this.queryInfoWrapperShowFlag = true
      } else {
        if (this.queryInfoWrapperTop >= 50) {
          this.queryInfoWrapperShowFlag = false
        }
      }
    },
    newQuery(cbDone) {
      if (!this.branchID) {
        toast.fail('请选择仓库')
        return
      }
      this.startRow = 0
      this.listData = []
      this.listDataJson = {}
      this.finished = false
      setTimeout(() => {
        this.nextPage(cbDone)
      }, 100)
    },
    nextPage(cbAfterLoad) {
      if (this.finished) return
      let params = {
        searchStr: this.itemName,
        classID: this.classID,
        branchID: this.branchID,
        branchPositionName: this.branchPositionName,
        showZeroStock: this.showZeroStock,
        showPositionStock: this.showPositionStock,
        showBatchStock: this.showBatchStock,
        showExpiredItems: this.showExpiredItems,
        showAvailItems: this.showAvailItems,
        startRow: this.startRow,
        sortType :this.sortType,
        pageSize: this.pageSize,
      }
      GetStockReport(params).then((res) => {
        
        if (res.result === "OK") {
          if (params.startRow === 0) {
            this.listData = []
            this.listDataJson = {}
          }
          if (this.startRow == 0) {
            this.total = res.total
            this.avail_total = res.avail_total
            this.total_stock_qty=res.total_stock_qty
            this.total_avail_stock_qty=res.total_avail_stock_qty
            this.total_cost = res.total_cost
            this.wholesale_total = res.wholesale_total
            this.contract_total = res.contract_total
            this.totalCount = res.count
          }

          res.records.map((item) => {
            var qty = ''
            if (parseFloat(item.b_stock)) qty += toMoney(item.b_stock) + item.b_unit_no
            if (parseFloat(item.m_stock)) qty += toMoney(item.m_stock) + item.m_unit_no
            if (parseFloat(item.s_stock)) qty += toMoney(item.s_stock,3) + item.s_unit_no
            item.qty = qty
            qty = ''
            if (parseFloat(item.avail_b_stock)) qty += toMoney(item.avail_b_stock) + item.b_unit_no
            if (parseFloat(item.avail_m_stock)) qty += toMoney(item.avail_m_stock) + item.m_unit_no
            if (parseFloat(item.avail_s_stock)) qty += toMoney(item.avail_s_stock,3) + item.s_unit_no
            item.avail_qty = qty
            this.listData.push(item)
          })
          this.handleListDataToJson(this.listData, res.class_total)
          this.loading = false
          this.startRow = Number(this.startRow) + this.pageSize
          if (this.listData.length >= this.totalCount) {
            this.finished = true
          }
          if (cbAfterLoad) cbAfterLoad()
        }
      })
    },
    searchByItemName() {
      var reg = new RegExp("'", "g")
      this.itemName = this.itemName.replace(reg, "")
      
      this.mydebounce(this.newQuery,800);
    },
    mydebounce(callback,time){
        clearTimeout(this.timer);
        this.timer=setTimeout(function(){
          callback();
        },time);
    },
    getRootNode(node) {
      this.rootNode=node
    },
    itemClassSelect(obj) {
      // if (obj.itemObjs !== "") {
      //   this.classID = obj.itemObjs.ids
      //   this.className = obj.itemObjs.titles
      // } else {
      //   this.classID = ""
      //   this.className = ""
      // }
      if(obj.length>0){
          this.classID =obj[0].id,
          this.className =obj[0].name
        } else {
          this.classID = ''
          this.className = ''
        }
      this.$refs.selectTreeRef.handleCancel()
      this.newQuery()
    },
    // handleToggle(item) {
    //   let sum = 0
    //   this.branchPositionArr.length = 0
    //   this.branchIDArr.length = 0
    //   this.branchList.forEach(bl=>{
    //     let flag = false
    //     bl.branch_position.forEach(bp=>{
    //       if(bp.isSelect){
    //         flag = true
    //         sum +=1
    //         this.branchPositionArr.push({
    //           branch_position:bp.branch_position,
    //           branch_position_name:bp.branch_position_name
    //         })
    //       }
    //     })
    //     if(flag)  this.branchIDArr.push({
    //       branch_id:bl.branch_id,
    //       branch_name:bl.branch_name,
    //     })
    //   })
    //   if(sum === this.branchPositionList.length) this.branchSelectAllFlag = true
    //   else this.branchSelectAllFlag = false

    // },
    // handleToggleBranch(index){
    //   this.curBranchPositionList = this.branchList[index].branch_position
    //   this.curIndex = index
    // },
    handeleToggle(index) {
      this.$refs.checkboxes[index].toggle();
    },
    handelePositionToggle(index) {
      this.$refs.checkboxesPosition[index].toggle();
    },
    // handleBranchSelectAll() {
    //   if (!this.branchSelectAllFlag) {
    //     this.branchPositionArr.length = 0
    //     this.branchIDArr.length = 0
    //     this.branchPositionList.forEach(bpl=>{
    //       bpl.isSelect = false
    //     })
    //   } else {
    //     this.branchPositionArr.length = 0
    //     this.branchIDArr.length = 0
    //     this.branchPositionList.forEach(bpl=>{
    //       bpl.isSelect = true
    //       this.branchPositionArr.push({
    //           branch_position:bpl.branch_position,
    //           branch_position_name:bpl.branch_position_name
    //         })
    //     })
    //     this.branchList.forEach(bl=>{
    //       this.branchIDArr.push({
    //         branch_id:bl.branch_id,
    //         branch_name:bl.branch_name,
    //       })
    //     })
    //   }
    // },
    handleBranchIdSelectAll() {
      if (this.branchSelectAllFlag) {
        this.$refs.checkboxGroup.toggleAll();
      } else {
        this.$refs.checkboxGroup.toggleAll(true);
      }
    },
    handleBranchPositionSelectAll() {
      if (this.branchPositionSelectAllFlag) {
        this.$refs.checkboxGroupPositionGroup.toggleAll();
      } else {
        this.$refs.checkboxGroupPositionGroup.toggleAll(true);
      }
    },
    handleBranchIDSelect() {
      this.branchName = ""
      this.branchID = ""
      this.branchIDArr.forEach(item => {
        this.branchName += item.branch_name + ','
        this.branchID += item.branch_id + ','
      })
      this.branchID = this.branchID.substring(0, this.branchID.length - 1)
      this.branchName = this.branchName.substring(0, this.branchName.length - 1)
      this.$store.state.stockBranchInfo.branchID = this.branchID
      this.$store.state.stockBranchInfo.branchName = this.branchName
      // this.branchName = ""
      // this.branchID = ""
      // this.branchPositionName = ""
      // this.branchPosition = ""
      // this.branchIDArr.forEach(item => {
      //   this.branchName += item.branch_name + ','
      //   this.branchID += item.branch_id + ','
      // })
      // this.branchPositionArr.forEach(item => {
      //   this.branchPositionName += item.branch_position_name + ','
      //   this.branchPosition += item.branch_position + ','
      // })
      // this.branchID = this.branchID.substring(0, this.branchID.length - 1)
      // this.branchName = this.branchName.substring(0, this.branchName.length - 1)
      // this.branchPosition = this.branchPosition.substring(0, this.branchPosition.length - 1)
      // this.branchPositionName = this.branchPositionName.substring(0, this.branchPositionName.length - 1)
      // this.$store.state.stockBranchInfo.branchID = this.branchID
      // this.$store.state.stockBranchInfo.branchName = this.branchName
    },
    handleBranchPositionSelect() {
      this.branchPositionName = ""
      this.branchPosition = ""
      this.branchPositionArr.forEach(item => {
        this.branchPositionName += item.branch_position_name + ','
        this.branchPosition += item.branch_position + ','
      })
      this.branchPosition = this.branchPosition.substring(0, this.branchPosition.length - 1)
      this.branchPositionName = this.branchPositionName.substring(0, this.branchPositionName.length - 1)
    },
    // handleBranchPositionSelect() {
    //   this.branchPositionName = ""
    //   this.branchPosition = ""
    //   this.branchPositionArr.forEach(item => {
    //     this.branchPositionName += item.branch_position_name + ','
    //     this.branchPosition += item.branch_position + ','
    //   })
    //   this.branchPosition = this.branchPosition.substring(0, this.branchPosition.length - 1)
    //   this.branchPositionName = this.branchPositionName.substring(0, this.branchPositionName.length - 1)
    //   // this.$store.state.stockBranchInfo.branchID = this.branchID
    //   // this.$store.state.stockBranchInfo.branchName = this.branchName
    // },
    handleBranchIdConfirm() {
      this.handleBranchIDSelect()
      this.showPicker = false
      this.newQuery()
    },
    handleBranchPositionConfirm() {
      this.handleBranchPositionSelect()
      this.showPositionPicker = false
      this.newQuery()
    },
    // handleBranchInfoConfirm() {
    //   this.handleBranchIDSelect()
    //   this.showPicker = false
    //   this.newQuery()
    // },
    // handleBranchPositionConfirm() {
    //   this.handleBranchPositionSelect()
    //   // this.showPickerPosition = false
    //   this.newQuery()
    // },

    handleAmountFlag(costsOrwholesaleFlag) {
      this.costsOrwholesaleFlag = costsOrwholesaleFlag
      this.$store.commit('costsOrwholesaleFlag', costsOrwholesaleFlag)
    },

    onCancel() {
      this.branchName = ""
      this.branchID = ""
      this.newQuery()
      this.showPicker = false
    },
    selectZeroStock(value) {
      this.showZeroStock = value
      this.$store.commit("showZeroStock",value)
    },
    selectPositionStock(value) {
      this.showPositionStock = value
      this.$store.commit("showPositionStock",value)
    },
    selectBatchStock(value) {
      this.showBatchStock = value
      this.$store.commit("showBatchStock",value)
    },
    selectStopItem(value) {
      this.showExpiredItems = value
      this.$store.commit("showExpiredItems",value)
    },
    selectAvailItem(value) {
      this.showAvailItems = value
      this.$store.commit("showAvailItems",value)
    },
    selectShowStockAmount(value) {
      this.viewStockAmount = value
      this.$store.commit('showStockAmount', this.showStockAmount)
    },

    handleQuery() {
      this.$store.commit("queryItemSortType",this.sortType)
      this.$store.commit("stockQtyType",this.qtyType)
      this.newQuery()
      this.showPopup = false
    },
    // 打印调用
    selectBluetoothPrint() {
      this.pageSize = 5000 // 预设一个足够大的数，实际代表全部数量
      this.newQuery(() => {
        // print
        var stockInfo = this.getStockPrintInfo()
        var that = this
        Printing.printStockInfo(stockInfo, res => {
          if (res.result == "OK") {
            Toast.success('打印成功')
            this.pageSize = 200
            that.newQuery()
          } else {
            Toast.fail(res.msg)
            this.pageSize = 200
          }
        })
      })
    },
    // 获取打印库存查询所需要信息
    getStockPrintInfo() {
      return {
        itemName: this.itemName,
        className: this.className,
        branchName: this.branchName,
        time: this.getCurrentTime(),
        listData: this.listData,
        listDataJson: this.listDataJson,
        total: this.total,
        showStockAmount: this.showStockAmount,
        costsOrwholesaleFlag: this.costsOrwholesaleFlag,
        total_cost: this.total_cost,
        wholesale_total: this.wholesale_total,
        contract_total: this.contract_total
      }
    },
    getCurrentTime() {
      var now = new Date();
      var year = now.getFullYear() //得到年份
      var month = now.getMonth()//得到月份
      var date = now.getDate()//得到日期
      var day = now.getDay()//得到周几
      var hour = now.getHours()//得到小时
      var minu = now.getMinutes()//得到分钟
      var sec = now.getSeconds()//得到秒
      month = month + 1
      if (month < 10) month = "0" + month
      if (date < 10) date = "0" + date
      if (hour < 10) hour = "0" + hour
      if (minu < 10) minu = "0" + minu
      if (sec < 10) sec = "0" + sec
      return year + "-" + month + "-" + date + " " + hour + ":" + minu + ":" + sec
    },
    clearSelect() {
      this.classID = ""
      this.className = ""
      this.branchID = ""
      this.branchName = ""
      this.showZeroStock = false
      this.showPositionStock = false
      this.showBatchStock = false
      this.showExpiredItems = false
      this.showAvailItems = true
      this.showAmount = true
      this.showPopup = false
      this.newQuery()
    },
    handleBranch() {
      if (this.$store.state.stockBranchInfo.branchID === "") {
        // 无缓存
        handleStateStockBranchInfo(this)
      } else {
        // 有缓存，判断缓存数据是否存在branchList
        var hasBranch = this.branchList.filter(item => item['branch_id'] === this.$store.state.stockBranchInfo.branchID && item['branch_name'] === this.$store.state.stockBranchInfo.branchName).length
        if (Number(hasBranch) > 0) {
          this.branchID = this.$store.state.stockBranchInfo.branchID
          this.branchName = this.$store.state.stockBranchInfo.branchName
        } else {
          // 若干因素导致缓存中的数据与实际可用数据不符合
          handleStateStockBranchInfo(this)
        }
      }

      function handleStateStockBranchInfo(that) {
        if (that.branchList.length >= 1) {
          that.branchID = that.branchList[0].branch_id
          that.branchName = that.branchList[0].branch_name
        } else {
          that.branchID = ""
          that.branchName = ""
        }
        that.$store.state.stockBranchInfo.branchID = that.branchID
        that.$store.state.stockBranchInfo.branchName = that.branchName
      }
    },
    getAmountFlag() {
      this.costsOrwholesaleFlag = this.$store.state.costsOrwholesaleFlag
      this.showStockAmount = this.$store.state.showStockAmount
    },
    handleListDataToJson(listData, classTotal) {
      if (Object.keys(this.listDataJson).length == 0) {
        // 空的情况需要根据class_total进行数据对象生成
        classTotal.forEach(classItem => {
          this.listDataJson[classItem.order_index + classItem.class_name + classItem.item_class] = {
            className: classItem.class_name,
            item_class: classItem.item_class,
            itemRows: [],
            class_costs: classItem.class_costs,
            total_stock: classItem.b_total_stock + '大' + (classItem.m_total_stock != "0" ? classItem.m_total_stock + "中:" : '') + classItem.s_total_stock + "小"
          }
        })
      } else {
        for (var key in this.listDataJson) {
          // 清空数据，重新push，防止加载数据不全重复push
          this.listDataJson[key].itemRows = []
        }
      }
      for (let i = 0, len = listData.length; i < len; i++) {
        listData[i].orderAndName = listData[i].order_index + listData[i].item_name
        for (var key in this.listDataJson) {
          if (this.listDataJson[key].item_class === listData[i].item_class) {
            this.listDataJson[key].itemRows.push(listData[i])
            break
          }
        }
      }
    },
    enterStockDetail(itemRow){
      if(this.branchId===''){
        Toast.fail({
          message:'请选择仓库',
          duration:500
        })
        return 
      }
      let itemId = itemRow.item_id
      let itemName = itemRow.item_name
      // let newBranchId = itemRow.branch_id?itemRow.branch_id:""
      this.$router.push({
        path:'/StockIndex',
        query:{
          itemId,
          itemName,
          branchId:this.showPositionStock?itemRow.branch_id:this.branchID,
          branchName:this.showPositionStock?itemRow.branch_name:this.branchName,
          branchPosition:this.showPositionStock?itemRow.branch_position:this.branchPosition,
          branchPositionName:this.showPositionStock?itemRow.branch_position_name:this.branchPositionName,
          batchId:this.showBatchStock?itemRow.batch_id:'',
          produceDate:this.showBatchStock?itemRow.produce_date:'',
          batchNo:this.showBatchStock?itemRow.batch_no:''
        }
      })
    }
  }
}
</script>
<style lang="less" scoped>
@flex_ac_jc: {
  display: flex;
  align-items: center;
  justify-content: center;
};

@flex_acent_jbw: {
  display: flex;
  align-items: center;
  justify-content: space-between;
};
@flex_wrap: {
  display: flex;
  flex-wrap: wrap;
};
@flex-left: {
  text-align: left;
};
@flex-right: {
  text-align: right;
};
@flex_a_flex: {
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
};
@search_style: {
  border-bottom: 0.5px solid #ddd;
  margin-right:10px;

  
  height:36px;
};
/deep/ .van-field__control {
  text-align: center;
}

/deep/ .van-cell {
  font-size: 15px;
  padding: 5px;
  justify-content: space-between;
}

/deep/ .van-divider {
  font-size: 15px;
}
/deep/ .van-checkbox__label {
  color: #aaa;
}
/deep/ .van-search__content {
  background-color: #f8f8f8;
}
/deep/ .van-search {
  background-color: #f8f8f8;
}
/deep/ .search_name {
  background-color: #f8f8f8;
}
/deep/ .van_list {
  overflow-y: hidden;
}
.report_no_box {
  width: 100%;
  height: calc(100vh - 140px - 50px - 50px);
  @flex_a_flex();
  background: #f2f2f2;
  color: #999999;

  .whole_box_no_icon {
    font-size: 50px;
  }

  p {
    font-size: 14px;
  }
}

.pages {
  .content {
    display: flex;
    flex-direction: column;
    align-items: center;
    .popQueryContent {
      display: flex;
      flex-direction: column;
      align-items: center;
      height: 140px;
    }
    .query_info_wrapper {
      box-sizing: border-box;
      width: 100%;
      height: 140px;
      display: flex;
      position: absolute;
      flex-direction: column;
      background-color: #f8f8f8;
      /deep/ .van-icon {
        font-size: 20px !important;
      }
      .query_info_1 {
        height: 40px;
        .query_from {
          background-color: #f8f8f8;
          display: flex;
          justify-content: flex-end;
        }
        .van-search {
          width: 50%;
          box-sizing: border-box;
          padding: 5px 3px;
          @search_style();
        }
        .search_name {
          margin-right: 10px;
        }
      }
      .query_info_2 {
        height: 40px;
        .van-form{
          display: flex;
          .van-search {
          width: 50%;
          box-sizing: border-box;

          padding: 5px 3px;
          @search_style();
        }
        }
       
      }
      .query_info_title {
        display: flex;
        height: 40px;
        justify-content: space-between;
        align-items: center;
        padding: 0 5px;
        //border-bottom: 1px solid #ccc;
      }
    }
    .list_wrapper {
      .list_layout {
        width: 95%;
        height: 150px;
      }
      //box-sizing: border-box;
      //padding: 0 10px;
      width: 95%;
      height: calc(100vh - 50px -70px);
      overflow-y: auto;
      .van_list {
        .receive_ul {
          div {
            font-size: 15px;
          }
        }
        .good_info_wrapper {
          margin-bottom: 10px;
          .good_class_wrapper {
            display: flex;
            justify-content: space-between;
            min-height: 30px;
            //background-color: #f7f8fa;
            line-height: 30px;
            font-weight: bold;

            .good_class_title {
              flex: 4;
              text-align: left;
            }

            .good_class_cs {
              display: flex;
              flex: 5;
              justify-content: space-between;

              .good_class_stock {
                text-align: right;
                flex: 1;
              }
            }
          }

          .good_info_content {
            display: flex;
            flex-direction: column;
            border-bottom: 1px solid #eee;
            padding: 3px 0;

            .good_content_name {
              display: flex;
              min-height: 25px;
              padding-top: 4px;
            }

            .good_content_bottom {
              display: flex;
              justify-content: space-between;

              .good_content_layout {
                flex: 1;
              }

              .good_content_costs {
                flex: 1;
              }

              .good_content_qty {
                flex: 1;
                text-align: right;
                padding-right: 5px;
              }
            }
          }
        }
      }
    }
    .stock_footer {
      width: 100%;
      height: 70px;
      background: #f8f8f8;
      font-size: 15px;
      border-top: 1px solid #f2f2f2;
      display: flex;

      .stock_footer_left {
        flex: 1;
        height: 100%;
        justify-content: center;
        align-items: center;
        line-height: 45px;
        align-content: center;
      }

      .stock_footer_right {
        flex: 5;
        display: flex;
        flex-direction: column;
        justify-content: center;
        text-align: right;
        margin-right: 10px;

        .stock_footer_right_top {
          padding-bottom: 2px;
        }

        .stock_footer_right_bottom {
          justify-content: center;
          align-content: center;
        }
      }
    }
  }
}
.btns_wrapper {
  box-sizing: border-box;
  width: 100%;
  padding: 20px 50px;
  display: flex;
  flex-direction: column;
  .btns_top {
    .query_btn {
      width: 100%;
      height: 45px;
      border-radius: 12px;
      background-color: #fde3e4;
      display: flex;
      justify-content: center;
      align-items: center;
      font-size: 18px;
    }
  }
  .btns_bottom {
    display: flex;
    margin-top: 20px;
    justify-content: space-between;
    .btn_other {
      width: 45%;
      height: 45px;
      border-radius: 12px;
      background-color: #eee;
      display: flex;
      justify-content: center;
      align-items: center;
      font-size: 18px;
    }
  }
}
.van_popup {
  overflow: hidden;

  h5 {
    height: 46px;
    @flex_ac_jc();
    font-size: 16px;
    font-weight: normal;
    color: #000000;
    border-bottom: 2px solid #f2f2f2;

    .van-icon {
      width: 20px;
      height: 20px;
      font-size: 20px;
      position: fixed;
      top: 0;
      right: 15px;
      transform: translateY(66%);
      color: gray;
    }
  }

  .alloc_list {
    box-sizing: border-box;
    padding: 0 10px;
    height: calc(100% - 60px - 48px) !important;
    overflow-x: hidden;
    overflow-y: auto;

    .alloc_list_box {
      font-size: 14px;
      margin: 15px 5px;

      h4 {
        height: 20px;
        text-align: left;
        color: rgb(25, 137, 250);
      }

      .van-cell {
        margin: 10px 0;
      }
    }

    .other {
      /deep/ .van-cell__value--alone {
        text-align: center;
      }
    }
  }
}

.clearSelect {
  height: 45px;
  vertical-align: top;
  border-top: 1px solid #f2f2f2;

  button {
    height: 85%;
    vertical-align: top;
    margin: 10px 20px 0;
  }
}

// .branch_list_wrapper {
//   height: 100%;
//   box-sizing: border-box;
//   display: flex;
//   flex-direction: column;
//   padding: 5px 10px 10px;
//   .branch_list_btns {
//     width: 100%;
//     height: 45px;
//     display: flex;
//     justify-content: space-between;
//     align-items: center;
//     .btns_left {
//       border-radius: 10px;
//       display: flex;
//       align-items: center;
//       justify-content: flex-start;
//       width: 150px;
//       height: 26px;

//       //border: 1px solid #FDE3E4;
//     }
//     .btns_right {
//       border-radius: 10px;
//       display: flex;
//       align-items: center;
//       justify-content: center;
//       width: 65px;
//       height: 26px;
//       background: #fde3e4;
//       border: 1px solid #fde3e4;
//     }
//   }
//   .branch_info_content{
//     display:flex;
//     width:100%;
//     .branch_list_content {
//       display:flex;
//       flex-direction:column;
//       width: 40%;
//       // height: 320px;
//       overflow-y: auto;
//       .branch_list_content_item {
//         display: flex;
//         align-items: center;
//       }
//       .branch_list_content_cur_item {
//         background-color:#eee;
//         .van-cell{
//           background-color:#eee;
//         }
//       }
//     } 
//     .branch_position_list_content{
//       display:flex;
//       flex-direction:column;
//       width: 60%;
//       padding:0 8px;
//       overflow-y: auto;
//       background-color:#eee;
//       .branch_position_list_content_item {
//         display: flex;
//         align-items: center;
//         background-color:#eee;
//         .van-cell{
//           background-color:#eee;
//         }
//         .branch_position_list_content_item_checkbox {
//           width: 24px;
//           height: 24px;
//         }
//       }
//     }
//   }
// }
.branch_list_wrapper {
    height: 100%;
    box-sizing: border-box;
    display: flex;
    flex-direction: column;
    padding: 5px 10px 10px;
    .branch_list_btns {
      width: 100%;
      height: 45px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      .btns_left {
        border-radius: 10px;
        display: flex;
        align-items: center;
        justify-content: flex-start;
        width: 150px;
        height: 26px;
  
        //border: 1px solid #FDE3E4;
      }
      .btns_right {
        border-radius: 10px;
        display: flex;
        align-items: center;
        justify-content: center;
        width: 65px;
        height: 26px;
        background: #fde3e4;
        border: 1px solid #fde3e4;
      }
    }
    .branch_list_content {
      width: 100%;
      height: 320px;
      overflow-y: auto;
      .branch_list_content_item {
        display: flex;
        align-items: center;
        height: 40px;
        .branch_list_content_item_checkbox {
          width: 22px;
          height: 22px;
        }
      }
    }
  }

.choose_wrapper{
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  margin-left: 10px;
  margin-right: 10px;
}
</style>
