<template>
  <div style="overflow-y:auto;height: 100%;">
    <div class="wrapper-page" v-if="hasReject">
      <ul class="sales_ul">
        <li class="item_title">拒收</li>
        <li v-for="(item,index) in sumSheet" v-if="item.back_type=='reject'" :key="index+'_reject'" ref="'orders_li'+item.item_name">
          <van-swipe-cell style="width:100%">
            <div class="sheet_wrapper" :style="{background:'#fff'}">
              <div class="sup_info">
                <div class="sup_name">
                  {{ item.item_name }}
                </div>
                <div v-if="moveStock">
                  <div v-if="!item.isAdd || item.isAdd=='false'" style="color: #1989fa;" @click="addRow(item)">添加</div>
                  <div style="color: #1989fa;" v-else @click="deleteRow(item)">删除</div>
                </div>
              </div>
              <div class="sheet_info">
                <div class="sheet_info_left">
                  <div class="sheet_happentime">
                    {{ item.unit_relation1 }}
                  </div>
                  <div class="sheet_happentime"  v-if="vanStock[item.item_id+fromBranch+item.branch_position+item.batch_id]&&vanStock[item.item_id+fromBranch+item.branch_position+item.batch_id].stock_qty_unit">
                    车辆库存:{{ vanStock[item.item_id+fromBranch+item.branch_position+item.batch_id].stock_qty_unit }}
                  </div>
                  <div class="sheet_happentime" style="margin-top:5px"  v-if="item.produce_date">
                    生产日期:{{ item.produce_date }}
                  </div>
                  <div class="sheet_happentime" style="margin-top:5px"  v-if="item.batch_no">
                    批次:{{ item.batch_no }}
                  </div>
                </div>
                <div class="quantity" style="margin-top:20px">
                  <div class="backQty">
                    <div>回库:</div>
                    <div class="sheet_info_right" @click.stop="onItemRowClick(item,index,'reject')">{{ item.move_qty_conv }}</div>
                  </div>
                  <div class="backQty">
                    <div> 留车:</div>
                    <div class="sheet_info_right">{{ item.sale_qty_conv }}</div>
                  </div>
                  <div style="color: #ccc;">拒收:{{ item.need_move_qty_conv }}</div>
                </div>
              </div>
              <div class="sheet_add" style="color: #ccc;"   v-if="item.amount">
                拒收金额:￥{{item.amount  }}
              </div>
              <div v-if="moveStock" class="seller_senders_info">
                <div class="public_query_titleSrc_item" @click="handldBackBranch(item)">          
                  <svg width="22px" height="22px" stroke-width="1.3" class="black">
                    <use :xlink:href="'#icon-assignBranch'"></use>
                  </svg>
                  <input type="text" v-model="item.back_branch_name" placeholder="回库仓" readonly />
                </div>
                <div class="public_query_titleSrc_item" v-if="item.back_branch &&branchPositionList[item.back_branch]&&branchPositionList[item.back_branch].length>0" @click="handldBackBranchPosition(item)">          
                  <svg width="22px" height="22px" stroke-width="1.3" class="black">
                    <use :xlink:href="'#icon-branchPosition'"></use>
                  </svg>
                  <input type="text" v-model="item.back_branch_position_name" placeholder="库位" readonly />
                </div>
              </div>
            </div>
          </van-swipe-cell>
        </li>
      </ul>
    </div>
    <div class="wrapper-page" v-if="hasReturn">
      <ul class="sales_ul">
        <li class="item_title">退货</li>
        <li v-for="(item,index) in sumSheet " v-if="item.back_type=='return'" :key="index+'_return'" ref="'orders_li'+item.item_name">
          <van-swipe-cell style="width:100%">
            <div class="sheet_wrapper" :style="{background:'#fff'}">
              <div class="sup_info">
                <div class="sup_name">
                  {{ item.item_name.replace('(退)','') }}
                </div>
                <div v-if="moveStock">
                  <div v-if="!item.isAdd || item.isAdd=='false'" style="color: #1989fa;" @click="addRow(item)">添加</div>
                  <div style="color: #1989fa;" v-else @click="deleteRow(item)">删除</div>
                </div>
              </div>

              <div class="sheet_info">
                <div class="sheet_info_left">
                  <div class="sheet_happentime">
                    {{ item.unit_relation1 }}
                  </div>
                  <div class="sheet_happentime"   v-if="vanStock[item.item_id+fromBranch+item.branch_position+item.batch_id]&&vanStock[item.item_id+fromBranch+item.branch_position+item.batch_id].stock_qty_unit">
                    车辆库存:{{ vanStock[item.item_id+fromBranch+item.branch_position+item.batch_id].stock_qty_unit }}
                  </div>
                  <div class="sheet_happentime" style="margin-top:5px"  v-if="item.produce_date">
                    生产日期:{{ item.produce_date }}
                  </div>
                  <div class="sheet_happentime" style="margin-top:5px"  v-if="item.batch_no">
                    批次:{{ item.batch_no }}
                  </div>
                </div>
                <div class="quantity">
                  <div class="backQty">
                    <div>回库:</div>
                    <div class="sheet_info_right" @click.stop="onItemRowClick(item,index,'back')">{{ item.move_qty_conv }}</div>
                  </div>
                  <div class="backQty">
                    <div> 留车:</div>
                    <div class="sheet_info_right">{{ item.sale_qty_conv }}</div>
                  </div>
                  <div style="color: #ccc;">退货:{{item.need_move_qty_conv}}</div>

                </div>
              </div>
              <div class="sheet_add" style="color: #ccc;"   v-if="item.amount">
                退货金额:￥{{item.amount  }}
              </div>
              <div v-if="moveStock" class="seller_senders_info">
                <div class="public_query_titleSrc_item" @click="handldBackBranch(item)">          
                  <svg width="22px" height="22px" stroke-width="1.3" class="black">
                    <use :xlink:href="'#icon-assignBranch'"></use>
                  </svg>
                  <input type="text" v-model="item.back_branch_name" placeholder="回库仓" readonly />
                </div>
                <div class="public_query_titleSrc_item" v-if="item.back_branch &&branchPositionList[item.back_branch]&&branchPositionList[item.back_branch].length>0" @click="handldBackBranchPosition(item)">          
                  <svg width="22px" height="22px" stroke-width="1.3" class="black">
                    <use :xlink:href="'#icon-branchPosition'"></use>
                  </svg>
                  <input type="text" v-model="item.back_branch_position_name" placeholder="库位" readonly />
                </div>
              </div>
            </div>
          </van-swipe-cell>
        </li>
      </ul>
    </div>
    <div style="height:200px;color:#ccc;">到底了</div>
    <div class="wrapper">
      <div class="content">
        总计:
        <span class="record">{{ totalQTY }}</span>
      </div>
      <van-button square type="default" @click="saveBackBranchSon" v-if="canMake&&!approve_time" class="button_assign" :disabled="isSaveing">保存</van-button>
      <van-button square type="default" @click="approveBackBranchSon" v-if="canApprove&&!approve_time" class="button_assign" :disabled="isApproveing">审核</van-button>
      <van-button square type="default" @click="deleteBackBranch" v-if="canDelete && !approve_time && back_branch_sheet_no" class="button_assign" :disabled="isApproveing||isSaveing">删除</van-button>
      <van-button square type="default" @click="cancelBackBranch" v-if="canRed&&approve_time&&!red_flag" class="button_assign" :disabled="!isApproveing">红冲</van-button>
      <van-button square type="default" @click="printBackBranhSon" class="button_assign" :disabled="isPrinting">打印</van-button>
    </div>
    <van-popup v-model="popupEditSheetRowPannel" position="bottom" :style="{ height: '75%' }">
      <div class="class_add_goods_m">

        <div class="class_add_goods">
          <EditBackBranchItem :sheet="sumSheet" :editingRow="editingItem" @onRowEditDone="onRowEditDone" @onRowEditExit="onRowEditExit" />
        </div>
      </div>
    </van-popup>
    <van-popup v-model="popToBranchList" position="bottom" :duration="0.4">
      <van-picker :columns="toBranchList" show-toolbar title="仓库选择" value-key="branch_name" @cancel="popToBranchList = false" @confirm="onBackBranchSelected" />
    </van-popup>
    <van-popup v-model="popBranchPosition" position="bottom" :duration="0.4">
      <van-picker :columns="lstBranchPosition" show-toolbar title="库位选择" value-key="branch_position_name" @cancel="popBranchPosition = false" @confirm="onBranchPositionSelected" />
    </van-popup>
    <van-popup v-model="popAddNewRow" position="bottom" :duration="0.4" :style="{height:'40%'}">
      <div class="sheet_wrapper" :style="{background:'#fff'}">
        <div class="sup_info">
          <div class="sup_name" style="margin-top:30px;font-size: 20px;">
            待留车商品
          </div>
        </div>
        <div class="sheet_info" style="margin-top:10px">
          <div class="sheet_info_left">
            <div class="sheet_no_tag">
              <div class="sheet_no">{{ selectedRow.item_name }}</div>
            </div>
            <div class="sheet_happentime" v-if="selectedRow.unit_relation1">
              单位关系:{{ selectedRow.unit_relation1 }}
            </div>
            <div class="sheet_happentime" style="margin-top:5px"  v-if="selectedRow.produce_date">
              生产日期:{{ selectedRow.produce_date }}
            </div>
            <div class="sheet_happentime" style="margin-top:5px"  v-if="selectedRow.batch_no">
              批次:{{ selectedRow.batch_no }}
            </div>
            <div class="backQty">
              <div>小单位回库数:</div>
              <div class="sheet_info_right">{{ selectedRow.move_qty+selectedRow.unit_no }}</div>
            </div>
          </div>
        </div>
        <div class="sheet_add" >
          <div>
            <span style="font-size: 20px;">留车操作:</span>
          </div>
        </div>
        <div class="seller_senders_info">
          
          <div>
            <van-field class="custom-field" label="拆出回库数:" v-model="splitItemQty" type="number" placeholder="请输入拆出小单位数">
              <template #right-icon>
                <span>{{selectedRow.unit_no}}</span> 
              </template>
            </van-field>
          </div>
        </div>
        <div style="margin-top:10px;display:flex;flex-direction: row;align-content: center;justify-content: space-evenly;">
          <van-button square type="default" @click="cancelAdd" style="background:#ffccc;padding-right: 15px;height: 35px;border-radius: 11px;margin-right: 5px;"  >取消</van-button>
          <van-button square type="default" @click="saveAddRow" style="background:#ffccc;padding-right: 15px;height: 35px;border-radius: 11px;margin-right: 5px;"  >确定</van-button>
        </div>

      </div>
      
    </van-popup>
  </div>

</template>
<script>

import { SwipeCell, Cell,Picker, CellGroup, Button, Toast, PullRefresh, Tag, Form, Icon, List, Popup, Field, Calendar, Dialog, Checkbox, CheckboxGroup } from "vant";
import EditBackBranchItem from "./BackBranchRowEdit";
export default {
  name: "AssignVanItem",
  data() {
    return {
      popupEditSheetRowPannel: false,
      editingItem: {},
      EditSheetRowIndex: "",
      rowType: '',
      totalQTY: '',
      popToBranchList:false,
      selectedRow:{},
      popBranchPosition:false,
      lstBranchPosition:[],
      popAddNewRow:false,
      splitItemQty:'',
    }
  },
  mounted() {
    setTimeout(() => {
      this.updateTotalQuantity()
    }, 300);

  },
  components: {
    "van-form": Form,
    "van-icon": Icon,
    "van-list": List,
    "van-popup": Popup,
    "van-tag": Tag,
    "van-pull-refresh": PullRefresh,
    "van-swipe-cell": SwipeCell,
    "van-cell": Cell,
    "van-cell-group": CellGroup,
    "van-button": Button,
    "van-field": Field,
    "van-calendar": Calendar,
    "van-checkbox": Checkbox,
    "van-checkbox-group": CheckboxGroup,
    EditBackBranchItem: EditBackBranchItem,
    "van-picker": Picker,
  },
  props: {
    sumSheet: Array,
    back_branch_sheet_no:String,
    approve_time: String,
    red_flag: String,
    canMake: Boolean,
    canApprove: Boolean,
    canDelete: Boolean,
    canRed: Boolean,
    isSaveing: Boolean,
    isApproveing: Boolean,
    isPrinting: Boolean,
    vanStock:Object,
    hasReject:Boolean,
    hasReturn:Boolean,
    itemsUnits:Object,
    fromBranch:String,
    toBranchList:Array,
    branchPositionList:Object,
    hasSplitRows:Boolean,
    moveStock:Boolean,
  },
  activated() {

  },
  computed: {

  },
  methods: {
    saveAddRow(){
      if(this.splitItemQty){
        if(this.selectedRow.move_qty>this.splitItemQty){
          var index = this.sumSheet.indexOf(this.selectedRow)
          //this.sumSheet = JSON.parse(JSON.stringify(this.sumSheet))
          var newRow =JSON.parse(JSON.stringify(this.selectedRow))
          newRow.move_qty = this.splitItemQty
          newRow.move_qty_conv = this.getItemUnitConvSon(newRow,'back')
          newRow.isAdd = true
          newRow.isSplited = false
          this.sumSheet.forEach(ss=>{
            if(ss.item_id == newRow.item_id &&ss.back_type == newRow.back_type &&ss.batch_id == newRow.batch_id&&ss.isSplited ){
              ss.move_qty = ss.move_qty-this.splitItemQty
              ss.move_qty_conv = this.getItemUnitConvSon(ss,'back')
            }
          })
          this.sumSheet.splice(index+1,0,newRow)
          //this.sumSheet = JSON.parse(JSON.stringify(this.sumSheet)) 
          this.popAddNewRow = false
          this.hasSplitRows = true
          this.splitItemQty = ""
        }else{
          return Toast.fail('拆出数量大于等于本行回库数')
        }
      }else{
        return Toast.fail('请输入数量')
      }
      
    },
    cancelAdd(){
      this.popAddNewRow = false
    },
    addRow(item){
      if(this.approve_time)return
      this.popAddNewRow = true
      this.selectedRow = item
      item.isSplited =true
    },
    deleteRow(row){
      if(this.approve_time)return
      var index = this.sumSheet.indexOf(row)
      var removeRow = JSON.parse(JSON.stringify(row)) 
      this.sumSheet.forEach(ss =>{
        if(ss.back_type == removeRow.back_type && ss.item_id == removeRow.item_id &&ss.batch_id == removeRow.batch_id && ss.isSplited &&ss.branch_position == removeRow.branch_position){
          ss.move_qty += Number(removeRow.move_qty)
          ss.move_qty_conv = this.getItemUnitConvSon(ss,'back')
        }
      })
      this.sumSheet.splice(index,1)
      //this.sumSheet = JSON.parse(JSON.stringify(this.sumSheet)) 
      var tp = false 
      this.sumSheet.forEach(ss =>{
        if(ss.isAdd &&ss.isAdd =="true"){
          tp = true
        }
      })
      this.hasSplitRows = tp
    },
    onBranchPositionSelected(value){
      this.selectedRow.back_branch_position=value.branch_position
      this.selectedRow.back_branch_position_name=value.branch_position_name
      this.popBranchPosition = false
      this.selectedRow={}
      this.lstBranchPosition=[]
    },
    handldBackBranchPosition(item){
      if(this.approve_time)return
      this.popBranchPosition = true
      this.selectedRow = item
      this.lstBranchPosition = this.branchPositionList[item.back_branch]
    },
    onBackBranchSelected(value){
      
      this.selectedRow.back_branch = value.branch_id
      this.selectedRow.back_branch_name = value.branch_name
      this.popToBranchList = false;
      this.selectedRow={}

    },
    handldBackBranch(item){
      if(this.approve_time)return
      this.popToBranchList = true
      this.selectedRow = item
    },
    getItemUnitConvSon(row,type){
      var qty = row.need_move_qty*row.unit_factor
      if (type == 'back') {
          qty = row.move_qty*row.unit_factor
      }else if(type=='sale'){
          qty = row.sale_qty * row.unit_factor
      }
      var flag=qty>=0?1:-1
      qty=Math.abs(qty)
    
      var s=''
      var q =0 
      if(row.b_unit_factor){
        q= parseInt(qty / row.b_unit_factor)
        if (q >= 1) {
            s += q + row.b_unit_no
        }
        qty = qty % row.b_unit_factor
      }
      
      if (Math.abs(qty) < 0.0001) qty = 0

      if (qty>0 && row.m_unit_factor) {
          q = parseInt(qty / row.m_unit_factor)
          if (q >= 1) s += q + row.m_unit_no
          qty = qty % row.m_unit_factor
          if (Math.abs(qty) < 0.0001) qty = 0
      }
      if (qty > 0) s += qty + row.s_unit_no
      if (flag == -1) s = '-' + s
      return s
    },
    saveBackBranchSon() {
      this.$emit('backBranch', 'save')
    },
    approveBackBranchSon() {
      this.$emit('backBranch', 'approve')
    },
    deleteBackBranch() {
      this.$emit('deleteBackBranchSheet')
    },
    cancelBackBranch() {
      this.$emit('cancelBackBranch')
    },
    printBackBranhSon() {
      this.$emit('printBackBranch')
    },
    onItemRowClick(obj, index, type) {
      return
      var readonly = false;
      this.rowType = type
      if (this.approve_time) readonly = true
      this.popupEditSheetRowPannel = true;
      this.EditSheetRowIndex = index;
      let objs = {
        datas: obj,
        readonly: readonly
      }
      this.editingItem = objs;
    },
    onRowEditDone(sheetRow) {
      if (this.rowType === 'reject') {
        this.sumSheet.rejectedRows[this.EditSheetRowIndex] = sheetRow
      } else if (this.rowType === 'back') {
        this.sumSheet.returnedRows[this.EditSheetRowIndex] = sheetRow
      }
      this.updateTotalQuantity()
      this.onRowEditExit()
    },
    updateTotalQuantity() {
      var totalQuantity = '', m_qty = 0, b_qty = 0, s_qty = 0
      this.sumSheet.forEach((row) => {
        var qty = row.move_qty
        var b_unit_factor,m_unit_factor
        var units = this.itemsUnits[row.item_id]
        units.forEach(u=>{
          if(u.unit_type=="b") b_unit_factor = u.unit_factor
          if(u.unit_type=="m") m_unit_factor = u.unit_factor
        })
        var q = 0
        if(b_unit_factor){
          q = parseInt(Number(qty) / Number(b_unit_factor))
          if(q>=1) b_qty += q
          qty = Number(qty) % Number(b_unit_factor)
        }
        if(Math.abs(qty)<0.0001) qty= 0 
        if(qty>0&&m_unit_factor){
          q = parseInt(Number(qty) / Number(m_unit_factor))
          if(q>=1)m_qty +=q
          qty = Number(qty) % Number(m_unit_factor)
        }
        if(qty>0)s_qty += Number(qty)
      });
      if (b_qty && b_qty !== 0) totalQuantity += b_qty + '大'
      if (m_qty && m_qty !== 0) totalQuantity += m_qty + '中'
      if (s_qty && s_qty !== 0) totalQuantity += s_qty + '小'
      this.totalQTY = totalQuantity
    },
    onRowEditExit() {
      this.rowType = ''
      this.EditSheetRowIndex = ''
      this.popupEditSheetRowPannel = false
    }

  },
}
</script>
<style lang="less" scoped>
@flex_w: {
  display: flex;
  flex-wrap: wrap;
};
@flex_a_j: {
  display: flex;
  align-items: center;
  justify-content: center;
};
@flex_a_bw: {
  display: flex;
  align-items: center;
  justify-content: space-between;
};
@flex_a_end: {
  display: flex;
  align-items: center;
  justify-content: flex-end;
};
@flex_a_flex: {
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
};
.wrapper-page {
  border: 1px solid #eee;
  border-radius: 10px;
  margin-top: 20px;
  margin-left: 4px;
  margin-right: 4px;
  font-size: 16px;
}
.custom-field {
  border: 1px solid #ccc; 
  border-radius: 4px; 
}

.item_title {
  flex: 3;
  display: flex;
  align-items: center;
  justify-content: flex-start;
  font-size: 16px;
  line-height: 30px;
  font-weight: bolder;
  text-align: center;
  color: #000;
  justify-content: space-around;
}
.quantity {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 50%;
}
.public_query_titleSrc_item {
  width: 50%;
  height: 100%;
  overflow: hidden;
  position: relative;
  vertical-align: top;
  border: none;
  padding: 10px 0 3px 0;

  div {
    height: 100%;
    width: calc(100% - 40px);
    padding: 0 30px 0 10px;
    border: none;
    font-size: 15px;
    line-height: 35px;
    color: #333333;
    text-align: left;
  }
  input {
    height: 100%;
    width: calc(100% - 60px);
    padding: 0 10px 4px 0px;
    border: none;
    font-size: 15px;
    line-height: 35px;
    color: #333333;
    vertical-align: top;
    border-bottom: 1px solid #eee;
    text-align: right;
  }
  .van-icon {
    position: absolute;
    left: 5px;
    top: 0;
    bottom: -10px;
    width: 30px;
    text-align: center;
    font-size: 22px;
    @flex_a_j();
    color: #aaa;
    background-color: #ffffff;
  }
}
.backQty {
  display: flex;
  align-content: center;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
}
.navi-select-item {
  font-size: 0.65rem;
  color: #1887f7;
  border-bottom: solid 0.025rem #ccc;
  padding-top: 0.5rem;
  padding-bottom: 0.5rem;
}
.public_query {
  background: #ffffff;
  height: 100%;
  .public_query_title {
    padding-top: 5px;
    border-bottom: 1px solid #f2f2f2;
    .public_query_titleSrc {
      padding: 0 10px;
      height: 35px;
      @flex_a_bw();
      margin-top: 5px;
      .public_query_titleSrc_item {
        width: 48%;
        height: 100%;
        border: 1px solid #cccccc;
        border-radius: 4px;
        overflow: hidden;
        position: relative;
        vertical-align: top;
        input {
          height: 100%;
          width: calc(100% - 45px);
          padding: 0 45px 0 5px;
          border: none;
          font-size: 15px;
          line-height: 35px;
          color: #333333;
          vertical-align: top;
        }
        span {
          position: absolute;
          right: 0;
          top: 0;
          bottom: 0;
          width: 45px;
          font-size: 16px;
          color: #000;
          @flex_a_j();
          background: #4c99e7;
        }
        .van-icon {
          position: absolute;
          right: 0;
          top: 0;
          bottom: 0;
          width: 30px;
          font-size: 22px;
          @flex_a_j();
          color: #666666;
        }
      }
    }
    .public_list_title {
      height: 40px;
      @flex_a_bw();
      margin-top: 5px;
      padding: 0 5px;
      div {
        height: 40px;
        line-height: 40px;
        font-size: 15px;
        text-align: center;
        width: calc(25% - 10px);
        padding: 0 5px;
        font-weight: 500;
        color: #333333;
      }
      div:first-child {
        width: calc(20% - 10px);
        text-align: left;
      }
      div:last-child {
        width: calc(25% - 10px);
        text-align: right;
      }
    }
  }
}
.sales_list_boxs {
  height: calc(100% - 88px);
  overflow-x: hidden;
  overflow-y: auto;
  background: #f2f2f2;
  width: 100%;
}
.sales_list_boxs_no {
  height: calc(100% - 54px);
  @flex_a_flex();
  background: #f2f2f2;
  color: #999999;
  .whole_box_no_icon {
    font-size: 50px;
  }
  p {
    font-size: 15px;
  }
}
.sales_ul {
  width: 100%;
  height: auto;
  overflow-y: auto;
  li {
    width: 100%;
    height: auto;
    overflow: hidden;
    border-bottom: 1px solid #f2f2f2;
    display: flex;
    flex-direction: column;
    align-items: center;
    .reject-btn {
      margin-left: 10px;
      display: flex;
      color: #fff;
      height: 100%;
      width: 100px;
      font-size: 18px;
      align-items: center;
      justify-content: center;
      font-size: 12px;
      background: #ee0a24;
    }
  }
}
.wrapper {
  position: fixed;
  left: 0px;
  bottom: 0px;
  width: 100%;
  height: 50px;
  font-size: 0.5em;
  color: #333;
  border-top: 1px solid #f2f2f2;
  box-shadow: 0 -2px 5px #f2f6fc;
  background-color: #fff;
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: space-around;
  .content {
    padding-left: 15px;
  }
  .button_assign {
    padding-right: 15px;
    height: 35px;
    border-radius: 11px;
    margin-right: 5px;
    color: #333;
    background: #ffcccc;
  }
  .record {
    padding: 0 10px;
  }
}
.class_add_goods {
  height: calc(100% - 46px);
  background: #f2f2f2;
  overflow: auto;
}
.class_add_goods_m {
  height: 100%;
}

.sheet_wrapper {
  width: 100%;
  border-radius: 5px;
  display: flex;
  flex-direction: column;
  margin-bottom: 5px;
  padding: 6px;

  background-color: #fff;
  box-sizing: border-box;

  .sup_info {
    border-bottom: 1px solid #f2f2f2;
    margin-bottom: 2px;
    margin: 0 5px;
    padding-bottom: 4px;
    display: flex;
    .sup_name {
      flex: 3;
      display: flex;
      align-items: center;
      justify-content: flex-start;
      text-align: left;
    }
    .order-source-wrapper {
      border: 1px solid #fde3e4;
      padding: 1px 5px;
      background-color: #fde3e4;
      border-radius: 10px;
      margin-left: 10px;
    }
    .sup_contact {
      flex: 2;
      font-size: 16px;
      display: flex;
      flex-direction: column;
      align-items: flex-end;

      .sup_tel {
        margin: 2px 0;
        a {
          color: rgb(12, 89, 190);
        }
      }
    }
  }
  .sheet_info {
    margin: 2px 5px;

    display: flex;
    .sheet_info_left {
      flex: 2;
      display: flex;
      padding: 4px 0;
      flex-direction: column;
      .sheet_no_tag {
        display: flex;
        justify-content: space-between;
        .sheet_no {
          font-size: 17px;
        }
      }
      .sheet_happentime {
        display: flex;
        color: #ccc;
        margin-top:5px
      }
    }
    .sheet_tag {
      flex: 1;
      max-width: 35px;
      display: flex;
      align-items: center;
      justify-content: center;
      span {
        padding: 2px;
      }
    }
    .sheet_info_right {
      flex: 2;
      display: flex;
      font-family: numfont;
      font-size: 22px;
      justify-content: center;
      align-items: center;
    }
    .sheet_checked {
      display: flex;
      justify-content: center;
      align-items: center;
    }
  }
  .sheet_add {
    border-top: 1px solid #ddd;
    padding-top: 2px;
    width: 100%;
    color: #555;
    display: flex;
    padding-left: 5px;
  }
  .seller_senders_info {
    margin: 4px 5px 0;
    display: flex;
    justify-content: space-between;
    margin-right: 20px;
    color: #000;
  }
  .mark-brief {
    margin: 6px 5px 0;
    display: flex;
    color: #999;
  }
}
</style>

