package com.synconset.cordovahttp;

import okhttp3.*;
import java.io.IOException;
import java.util.concurrent.TimeUnit;

/**
 * OkHttp client wrapper for Cordova HTTP plugin
 * Provides a singleton OkHttpClient instance with optimized configuration
 */
public class CordovaOkHttp {
    private static OkHttpClient client;
    private static final Object lock = new Object();
    
    /**
     * Get the singleton OkHttpClient instance
     * @return configured OkHttpClient instance
     */
    public static OkHttpClient getClient() {
        if (client == null) {
            synchronized (lock) {
                if (client == null) {
                    client = new OkHttpClient.Builder()
                        .connectTimeout(30, TimeUnit.SECONDS)
                        .readTimeout(30, TimeUnit.SECONDS)
                        .writeTimeout(30, TimeUnit.SECONDS)
                        .callTimeout(60, TimeUnit.SECONDS)
                        .retryOnConnectionFailure(true)
                        .followRedirects(true)
                        .followSslRedirects(true)
                        .build();
                }
            }
        }
        return client;
    }

    /**
     * Reset the client instance (useful for testing or configuration changes)
     */
    public static void resetClient() {
        synchronized (lock) {
            if (client != null) {
                client.dispatcher().executorService().shutdown();
                client.connectionPool().evictAll();
                client = null;
            }
        }
    }

    /**
     * Create a new OkHttpClient with custom configuration
     * @param connectTimeout connect timeout in seconds
     * @param readTimeout read timeout in seconds
     * @param writeTimeout write timeout in seconds
     * @return configured OkHttpClient instance
     */
    public static OkHttpClient createClient(int connectTimeout, int readTimeout, int writeTimeout) {
        return new OkHttpClient.Builder()
            .connectTimeout(connectTimeout, TimeUnit.SECONDS)
            .readTimeout(readTimeout, TimeUnit.SECONDS)
            .writeTimeout(writeTimeout, TimeUnit.SECONDS)
            .callTimeout(Math.max(connectTimeout + readTimeout + writeTimeout, 60), TimeUnit.SECONDS)
            .retryOnConnectionFailure(true)
            .followRedirects(true)
            .followSslRedirects(true)
            .build();
    }
}
