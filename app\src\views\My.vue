<template>
  <div class="pages" style="overflow-y: auto">
    <!-- <van-nav-bar
        class="nav_bar"
        title="我的"mn         safe-area-inset-top
        style="height:50px;font-size:18px"
    /> -->
    <div class="public_box2_my">
      <div class="public_box2_t">
        <div class="public_title">
          <div class="public_title_t" style="margin: 30px 0 0 0">
            <van-icon name="user-circle-o" class="van_icon" />
          </div>
          <div style="margin: 15px 0">
            <h5>{{ companyName }}</h5>
            <h5>ver:{{ myVersion }}{{showInfo}}
         
            </h5>
          <!--  <div style="color:#aaa;font-size: 12px">到期时间：{{expireTime}}</div>-->
          </div>


          <label>{{ useMyGoBack ? '手动回退' : '' }}</label>


        </div>
      </div>
      <div class="public_box2_b">
        <van-cell class="van_cell" :value="bluetooth_value" is-link @click="$router.push({path: '/PrintersView'})" style="margin-bottom:10px;">
          <template #title>
            <span class="iconfont icon_font">&#xe6a0;</span>
            <span class="custom-title">打印机管理</span>
          </template>
        </van-cell>
        <van-cell is-link @click="$router.push({path: '/SwitchAccount'})">
          <template #title>
            <span class="iconfont icon_font"><van-icon name="exchange" /></span>
            <span class="custom-title">账号切换</span>
          </template>
        </van-cell>
        <van-cell is-link @click="$router.push({path: '/UpdatePassword'})">
          <template #title>
            <span class="iconfont icon_font">&#xe6a1;</span>
            <span class="custom-title">修改密码</span>
          </template>
        </van-cell>
        <van-cell v-if="shareMallCode_l" class="van_cell" is-link @click="handleMinQrClick('r')">
          <template #title>
            <span class="iconfont icon_font"><van-icon name="qr" /></span>
            <span class="custom-title">商城公码(零售版)</span>
          </template>
        </van-cell>
        <van-cell v-if="shareMallCode_p" class="van_cell" is-link @click="handleMinQrClick('w')">
          <template #title>
            <span class="iconfont icon_font"><van-icon name="qr" /></span>
            <span class="custom-title">商城公码(批发版)</span>
          </template>
        </van-cell>
        <!--<div style="font-size: 14px; color: #aaa; text-align: left">
          * 客户专属二维码可通过销售单，选择客户，点击结算（无需下单），通过【邀请】按钮获取二维码。
        </div>带上后，有些手机就不能退出登录了，不支持滚动-->

        <van-cell class="van_cell" is-link style="margin-top:10px" @click="onGoTo('/msgSubscribeCompany')">
          <template #title>
            <span class="iconfont icon_font"><van-icon name="chat-o" /></span>
            <span class="custom-title">员工消息订阅</span>
          </template>
        </van-cell>

        <van-cell class="van_cell" style="margin-top:10px" is-link @click="$router.push({path: '/AboutUs'})">
          <template #title>
            <span class="iconfont icon_font">&#xe69c;</span>
            <span class="custom-title">关于我们</span>
          </template>
        </van-cell>
        <van-cell class="van_cell" is-link @click="$router.push({path: '/AICustomer'})">
          <template #title>
            <span class="iconfont icon_font"><van-icon name="service-o" /></span>
            <span class="custom-title">智能客服</span>
          </template>
        </van-cell>
        <van-cell class="van_cell" is-link @click="updateApp">
          <template #title>
            <span class="iconfont icon_font"><van-icon name="upgrade" /></span>
            <span class="custom-title">版本更新</span>
          </template>
        </van-cell>

        <van-cell class="van_cell" is-link @click="$router.push({path: '/FontSizeSetting'})">
          <template #title>
            <span class="iconfont icon_font">&#xe69c;</span>
            <span class="custom-title">字体大小</span>
          </template>
        </van-cell>
        
        <van-cell class="van_cell" is-link @click="loginOut" style="margin-top:10px">
          <template #title>
            <span class="iconfont icon_font">&#xe69d;</span>
            <span class="custom-title">退出登录</span>
          </template>
        </van-cell>

        <div v-if="this.$store.state.releaseType=='test'" style="margin-top: 20px">
          <van-cell title="" is-link @click="showPop" icon="smile-o">
            <template #title>
              <span class="custom-title">营匠实验室</span>
            </template>
          </van-cell>
        </div>
        <div style="height: 50px"></div>
      </div>
      <div class="phone_type">{{ agent }}</div>
    </div>
    <van-popup lazy-render v-model="testPopShow" position="bottom" :style="{ height: '60%' }">
      <div v-if="testPopShow" class="test-wrapper">
        <div class="test-input-wrapper">
          <input v-model="testInput" v-inputfocus style="margin-top: 20px" placeholder="ios/Android自动对焦，回车换行" v-on:keyup.enter="focusNext">
          <input ref="testInput2" placeholder="数字键盘" v-model="testInputNumber" style="margin-top: 20px" type="number" pattern="number">
          <input ref="testInput3" placeholder="打印前缀" v-model="testPrintHref" style="margin-top: 20px" />
        </div>

        <div class="test-long-wrapper" v-longpress="handeleLongpress">
          <van-popover v-model="showPopover" :actions="actions" @select="onSelect">
            <template #reference>
              长按测试
              <van-icon name="refund-o" />
            </template>
          </van-popover>

        </div>
      </div>

      <div class="test-mixin" @click="handleMixin">
        测试全局混入权限
      </div>
      <div @click="handlePrintESCTest" style="border: double;">
        测试ESC打印
      </div>
    </van-popup>
    <van-dialog v-model="showQr" get-container="body" :overlay="false" :show-confirm-button="false">
      <div class="content-code-wrapper">
        <div class="content-code-title">
          <div class="code-title-first">扫码绑定{{mallSettingInfo.mini_app_private ? '' : '营匠'}}小程序</div>
          <div class="code-title-second">获取更多便捷服务服务...</div>
        </div>
        <div class="content-code-qr-code">
          <img :src="miniQrUrl">
        </div>
        <div class="content-code-btn">
          <van-button type="default" @click="showQr = !showQr">关闭</van-button>
        </div>

      </div>
    </van-dialog>
  </div>
</template>

<script>
import { Cell, Icon, Popup, Popover, Toast, Dialog, Button } from 'vant'
import globalVars from "../static/global-vars";
import { CreateMiniQrCodeBase64, QueryMallSettingInfo, WeXinMiniCreateMiniQRCode } from "../api/api";
export default {
  data() {
    return {
      amount: 0,
      companyName: '',     
      bluetooth_value: '', // '未设置',
      agent: navigator.userAgent,
      device: window.device ? window.device.manufactor : '',
      showQr: false,
      miniQrUrl: '',
      testPopShow: false,
      testPopDOMShow: true,
      expireTime:"",
      testInput: '',
      testInputNumber: '',
      testPrintHref: '29,118,48,0,136,3,124,1',
      showPopover: false,
      showInfo:'',
      actions: [
        { text: '价格方案', price: 1 },
        { text: '上次售价', price: 2 },
        { text: '零售价', price: 3 },
        { text: '批发价', price: 4 }],
      mallSettingInfo: {
        company_id: '',
        mall_type: '',
        default_branch_id: '',
        branch_name: '',
        enable_branch: '',
        branch_map: [],
        qr_code_enable_expired: '',
        qr_code_alive_time: '',
        mini_app_id: '',
        // mini_app_secret: '',
        mini_app_private: ''
      },
      findMatchingDept: {
        nanoid: '',
        dept_id: '',
        branch_id: '',
        dept_name: '',
        dept_path: '',
        branch_name: '',
      }, // 专业版匹配的部门仓库信息
    }
  },
  computed: {
    shareMallCode_l(){
      return hasRight("delicacy.shareMallCode_l.value",true);
    },
    shareMallCode_p(){
      return hasRight("delicacy.shareMallCode_p.value",true);
    },
    isBoss(){
      return window.isBoss()
    },
    useMyGoBack() {
      return window.g_bUseMyGoBack
    },
    myVersion() {
      return globalVars.app_version;
    },    
    qrCodeParams() {
      return {
        company_id: '',
        supcust_id: '',
        mobile: '',
        contact_nick_name: ''
      }
    },

  },
  async mounted() {

  },
  components: {
    "van-cell": Cell,
    "van-button": Button,
    "van-icon": Icon,
    "van-popup": Popup,
    "van-popover": Popover,
    [Dialog.Component.name]: Dialog.Component,
  },
  activated() {
    this.companyName = this.$store.state.account.companyName
    this.expireTime = this.$store.state.operInfo.expire_time||''
    this.showInfo=window.position_way
    // this.bluetooth_value = this.$store.state.printerType == 'bluetooth' ? this.$store.state.printerID ? '已设置蓝牙打印机' : '未设置' : this.$store.state.printerType == 'cloud' ? this.$store.state.cloudPrinterSetted ? '已设置云打印机' : '未设置' : '未设置'
  },
  methods: {
    onGoTo(url){
      debugger
      window.goUrl(this, url)
    },
    loginOut() {
      let account = this.$store.state.account
      let accountList = this.$store.state.accountList
      for (let i = 0, length = accountList.length; i < length; i++) {
        if (accountList[i].mobile === account.mobile && accountList[i].companyId === account.companyId) {
          accountList.splice(i, 1)
          break
        }
      }
      this.$store.commit("accountList", accountList)
      this.clearCreatePersistedState({fullLogout: true})
      window.g_operInfoRefreshed = false
      this.$bus.$emit('activeNavbar', { activeNavbar: 0 })
      this.$router.push({ path: '/Login' })
    },
    clearCreatePersistedState(params) {
      this.$store.commit('clearCreatePersistedState', params || {})
    },
    updateApp() {
      //window.fetchCode(this.$store.state.releaseType)


      this.$toast('正在检测新版本')

      getOperRights(this.$store.state.operKey, function (res) {
        window.g_debugInfo += 'B'
        setTimeout(() => {
          Toast('已经是最新版本')
        }, 3000)

        if (res.result !== 'OK') {

          //setTimeout(function(){
          // that.net_error = true
          //  },1000)
          return
        }

        window.g_operInfoRefreshed = true

   

         
        // console.log(window.g_operInfo)
        // that.startNewPage(that.layout)
        // that.refreshViewByOperRights()
        // that.net_error = false
        //store.commit("operRights", res.operRights)
        //  next(); //继续往后走

      })
    },
    showPop() {
      this.testPopShow = true
    },
    closePop() {
      this.testPopShow = false
      this.testPopDOMShow = false
      this.$nextTick(() => {
        this.testPopDOMShow = true
      })
    },
    focusNext() {
      this.$refs.testInput2.focus();
    },
    handeleLongpress() {
      console.log("长按")
      this.showPopover = true
    },
    onSelect(action) {
      Toast(`${action.text},价格是${action.price}`);
    },
    handleMixin() {
      console.log(this.delicacySheetStatusForPrint)
    },
    handlePrintESCTest() {
      /*
      const hrefs = this.testPrintHref.split(',')
      const pre = []
      hrefs.forEach(h => {
        if (h) {
          pre.push((Number(h)))
        }
      })
      var img = new Image()
      img.crossOrigin = 'Anonymous'
      img.onload = e => {
        var canvas = document.createElement("canvas")
        canvas.width = img.width
        console.log("img.width", img.width)
        canvas.height = img.height
        var ctx = canvas.getContext("2d");
        ctx.drawImage(img, 0, 0, img.width, img.height)
        var imageDataCopy = ctx.createImageData(img.width, img.height)
        var imageData = ctx.getImageData(0, 0, img.width, img.height)
        for (var i = 0; i < imageData.data.length - 4; i = i + 4) {
          var average = (imageData.data[i] + imageData.data[i + 1] + imageData.data[i + 2]) / 3
          imageDataCopy.data[i] = average
          imageDataCopy.data[i + 1] = average
          imageDataCopy.data[i + 2] = average
          imageDataCopy.data[i + 3] = imageData.data[i + 3]
        }
        ctx.putImageData(imageDataCopy, 0, 0)
        var h = img.height + 1;
        var w = (img.width * 1 + 7) / 8 >> 0
        var xl = w % 256
        var xh = w / 256 >> 0
        var yl = h % 256
        var yh = h / 256 >> 0
        var _size = (xl + xh * 256) * (yl + yh * 256)

        const printArray = new Uint8Array(pre.length + _size)
        let index = 0
        pre.forEach(p => {
          printArray[index++] = p
        })
      }
      img.src = 'https://yingjiang.obs.cn-east-3.myhuaweicloud.com/common-resources/test_print_image.png'
      */
    },
    // region 获取商城配置信息，处理商城参数
    handleMinQrClick(qrScene) {
      QueryMallSettingInfo({
        operKey: this.$store.state.operKey
      }).then(res => {
        if (res.code === 0) {

          this.mallSettingInfo = res.result
          this.handelSettingMiniConfigInfo(qrScene)
        } else {
          Toast.fail(res.message)
        }
      }).catch(err => {
        console.log(err)
      })
    },
    handelSettingMiniConfigInfo(qrScene) {
      this.mallSettingInfo.qr_code_enable_expired = this.mallSettingInfo.qr_code_enable_expired === 'True'
      this.mallSettingInfo.mini_app_private = this.mallSettingInfo.mini_app_private === 'True'
      // 在进行校验
      let yingjiangMiniAppId = globalVars.wechatConf.miniAppId
      if (this.mallSettingInfo.mini_app_id !== '' && this.mallSettingInfo.mini_app_id !== yingjiangMiniAppId) {
        this.mallSettingInfo.mini_app_private = true
      } else {
        this.mallSettingInfo.mini_app_private = false
      }
      // if (this.mallSettingInfo.mall_type === 'p') {
      //   // 专业版需要进行匹配
      //   let operDeptPath = this.$store.state.operInfo.oper_dept_path
      //   const findDept = findMatchingDept(operDeptPath, this.mallSettingInfo.branch_map);
      //   if (findDept) {
      //     this.findMatchingDept = findDept
      //   }
      // }
      this.handleCreateMiniQrCodeBase64(qrScene)
      // function findMatchingDept(operDeptPath, branchMap) {
      //   let matchDept = null;
      //   let potentialDepts = [];
      //   for (let dept of branchMap) {
      //     if (operDeptPath === dept.dept_path) {
      //       // 找到了完全匹配的部门，直接返回
      //       return dept;
      //     } else if (operDeptPath.startsWith(dept.dept_path)) {
      //       // 当前部门是 oper_dept_path 的父部门，记录下来
      //       potentialDepts.push(dept);
      //     }
      //   }
      //   if (potentialDepts.length > 0) {
      //     // 如果有潜在的符合条件的部门，找到最深层级的部门并返回
      //     let maxDepth = 0;
      //     for (let dept of potentialDepts) {
      //       let depth = dept.dept_path.split('/').filter(p => p !== '').length;
      //       if (depth > maxDepth) {
      //         maxDepth = depth;
      //         matchDept = dept;
      //       }
      //     }
      //   }
      //   return matchDept;
      // }
    },
    handleCreateMiniQrCodeBase64(qrScene) {
      let errMsg = this.handleCheckMallSettingInfo()
      if (errMsg) {
        Toast.fail(errMsg)
        return
      }
      this.showQr = true
      CreateMiniQrCodeBase64({
        qrCodeExpiresTime: 0,
        operKey: this.$store.state.operKey,
        supcustId: -1,
        qrScene: qrScene,
        otherInfo: {},
        miniAppPrivate: this.mallSettingInfo.mini_app_private,
        miniAppId: this.mallSettingInfo.mini_app_id
      }).then(res => {
        if (res.code === 0) {
          this.miniQrUrl = "data:image/png;base64," + res.result
        } else {
          Toast.fail('创建二维码失败，请重试')
        }
      }).catch(err => {
        Toast.fail(err)
      })
    },
    handleCheckMallSettingInfo() {
      let errMsg = ''
      if (this.mallSettingInfo.mall_type === '') {  // 未开通小程序
        errMsg = '当前未开通小程序'
      } else {
        if (this.mallSettingInfo.default_branch_id === '') {
          errMsg = '未设置公司仓库'
        }
      }
      return errMsg
    },
    // endregion
  }
}
</script>

<style lang="less" scoped>
.pages {
  background-color: #ededed;
}
// .public_box2{
//   height:calc(100% - 40px)
// }
/deep/ .van-cell__title {
  text-align: left;
  vertical-align: middle;
  display: flex;
  justify-content: flex-start;
  .custom-title{
    min-width: 120px;
  }
}

/deep/ .van-nav-bar__title {
  font-size: 18px;
}

/deep/ .van-nav-bar__content {
  height: 50px;
  //background-color: rgba(245, 108, 108, 0.8);
  background-color: #c61549;
}

.custom-title {
  font-size: 16px;
  margin-right: 4px;
  vertical-align: middle;
  margin-left: 10px;
}

.icon_font {
  font-size: 22px;
  //color: rgba(245, 108, 108, 0.8);
  color: #c61549;
  vertical-align: middle;
  margin-right: 8px;
}

.public_box2_t {
  //height: 160px;
  margin-bottom: 10px;
  display: flex;
  align-items: center;
  background: #ffffff;

  .public_title {
    width: 100%;
    height: 50%;

    .public_title_t {
      height: auto;
      display: flex;
      justify-content: center;

      .van_icon {
        width: 60px;
        height: 60px;
        border-radius: 50%;
        font-size: 60px;
        //color: rgba(245, 108, 108, 0.8);
        color: #c61549;
      }
    }

    h5 {
      font-size: 17px;
      //margin-top: 30px;
      color: #000;
    }
  }
}

// .public_box2_b {
  //height: calc(100% - 220px - 50px);
// }

.van_cell {
  height: 50px;
  display: flex;
  align-items: center;
}

.phone_type {
  font-size: 8px;
  color: gray;
  height: 50px;
  display: flex;
  position: fixed;
  bottom: 80px;
  align-items: center;
  justify-content: flex-start;
  display: none;
}

.test-wrapper {
  display: flex;
  flex-direction: column;
  box-sizing: border-box;
  padding: 20px;

  .test-input-wrapper {
    display: flex;
    flex-direction: column;
  }

  .test-long-wrapper {
    display: flex;
    margin-top: 20px;
  }
}

.content-code-wrapper {
  width: 100%;
  min-height: 400px;
  background-color: #eee;
  border-right: 1px solid #eee;
  display: flex;
  box-shadow: 3px 3px 17px #333333;
  flex-direction: column;
  box-sizing: border-box;

  .content-code-title {
    width: 100%;
    height: 60px;
    box-sizing: border-box;
    padding: 5px 10px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    border-bottom: 1px solid #eeeeee;
  }

  .content-code-qr-code {
    height: 300px;
    background-color: #fff;
    border-bottom: 1px solid #eee;
    box-sizing: border-box;
    padding: 10px;

    img {
      width: 100%;
      height: 100%;
    }
  }

  .content-code-btn {
    display: flex;
    height: 60px;
    background-color: #fff;
    justify-content: center;
    padding-top: 10px;

    button {
      width: 80%;
      background-color: #fde3e4;
      border-radius: 10px;
    }
  }
}
</style>
