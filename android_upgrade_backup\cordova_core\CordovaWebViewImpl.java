/*
       Licensed to the Apache Software Foundation (ASF) under one
       or more contributor license agreements.  See the NOTICE file
       distributed with this work for additional information
       regarding copyright ownership.  The ASF licenses this file
       to you under the Apache License, Version 2.0 (the
       "License"); you may not use this file except in compliance
       with the License.  You may obtain a copy of the License at

         http://www.apache.org/licenses/LICENSE-2.0

       Unless required by applicable law or agreed to in writing,
       software distributed under the License is distributed on an
       "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
       KIND, either express or implied.  See the License for the
       specific language governing permissions and limitations
       under the License.
*/
package org.apache.cordova;

import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.lang.reflect.Constructor;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

import android.annotation.SuppressLint;
import android.content.Context;
import android.content.Intent;
import android.content.res.AssetFileDescriptor;
import android.net.Uri;
import android.os.Build;
import android.os.Handler;
import android.os.Looper;
import android.util.Log;
import android.view.Gravity;
import android.view.KeyEvent;
import android.view.View;
import android.view.ViewGroup;
import android.webkit.WebChromeClient;
import android.webkit.WebViewClient;
import android.widget.FrameLayout;

import org.apache.cordova.engine.SystemWebViewEngine;
import org.json.JSONException;
import org.json.JSONObject;
import org.json.JSONArray;
import org.apache.cordova.CordovaWebViewErrorManager;
import org.apache.cordova.WebViewLoadOptimizer;

import java.io.IOException;
import java.util.Arrays;
import java.util.ArrayList;
import java.util.List;

import okhttp3.Call;
import okhttp3.Callback;
import okhttp3.MediaType;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.RequestBody;
import okhttp3.Response;

/**
 * Main class for interacting with a Cordova webview. Manages plugins, events, and a CordovaWebViewEngine.
 * Class uses two-phase initialization. You must call init() before calling any other methods.
 */
public class CordovaWebViewImpl implements CordovaWebView {

    public static final String TAG = "CordovaWebViewImpl";

    private PluginManager pluginManager;

    protected final CordovaWebViewEngine engine;
    private CordovaInterface cordova;

    // Flag to track that a loadUrl timeout occurred
    private int loadSessionId = 0;

    // Current LoadSession for tracking load progress
    private LoadSession currentLoadSession = null;

    private CordovaResourceApi resourceApi;
    private CordovaPreferences preferences;
    private CoreAndroid appPlugin;
    private NativeToJsMessageQueue nativeToJsMessageQueue;
    private EngineClient engineClient = new EngineClient();
    private boolean hasPausedEver;

    // The URL passed to loadUrl(), not necessarily the URL of the current page.
    String loadedUrl;

    /** custom view created by the browser (a video player for example) */
    private View mCustomView;
    private WebChromeClient.CustomViewCallback mCustomViewCallback;

    private Set<Integer> boundKeyCodes = new HashSet<Integer>();

    public static CordovaWebViewEngine createEngine(Context context, CordovaPreferences preferences) {
        String className = preferences.getString("webview", SystemWebViewEngine.class.getCanonicalName());
        try {
            Class<?> webViewClass = Class.forName(className);
            Constructor<?> constructor = webViewClass.getConstructor(Context.class, CordovaPreferences.class);
            return (CordovaWebViewEngine) constructor.newInstance(context, preferences);
        } catch (Exception e) {
            throw new RuntimeException("Failed to create webview. ", e);
        }
    }

    public CordovaWebViewImpl(CordovaWebViewEngine cordovaWebViewEngine) {
        this.engine = cordovaWebViewEngine;
    }

    // Convenience method for when creating programmatically (not from Config.xml).
    public void init(CordovaInterface cordova) {
        init(cordova, new ArrayList<PluginEntry>(), new CordovaPreferences());
    }

    @SuppressLint("Assert")
    @Override
    public void init(CordovaInterface cordova, List<PluginEntry> pluginEntries, CordovaPreferences preferences) {
        if (this.cordova != null) {
            throw new IllegalStateException();
        }
        this.cordova = cordova;
        this.preferences = preferences;
        pluginManager = new PluginManager(this, this.cordova, pluginEntries);
        resourceApi = new CordovaResourceApi(engine.getView().getContext(), pluginManager);
        nativeToJsMessageQueue = new NativeToJsMessageQueue();
        nativeToJsMessageQueue.addBridgeMode(new NativeToJsMessageQueue.NoOpBridgeMode());
        nativeToJsMessageQueue.addBridgeMode(new NativeToJsMessageQueue.LoadUrlBridgeMode(engine, cordova));

        if (preferences.getBoolean("DisallowOverscroll", false)) {
            engine.getView().setOverScrollMode(View.OVER_SCROLL_NEVER);
        }
        engine.init(this, cordova, engineClient, resourceApi, pluginManager, nativeToJsMessageQueue);
        // This isn't enforced by the compiler, so assert here.
        assert engine.getView() instanceof CordovaWebViewEngine.EngineView;

        pluginManager.addService(CoreAndroid.PLUGIN_NAME, "org.apache.cordova.CoreAndroid");
        pluginManager.init();

        // 初始化WebView加载优化器，进行预热
        try {
            WebViewLoadOptimizer.getInstance().prewarmWebView(engine.getView().getContext());
            LOG.d(TAG, "WebView优化器初始化完成");
        } catch (Exception e) {
            LOG.w(TAG, "WebView优化器初始化失败", e);
        }
    }

    @Override
    public boolean isInitialized() {
        return cordova != null;
    }

    @Override
    public void loadUrlIntoView(final String url, boolean recreatePlugins) {
        LOG.d(TAG, ">>> loadUrl(" + url + ")");

        // 检查是否有正在进行的加载会话
        if (currentLoadSession != null) {
            String currentUrl = currentLoadSession.getUrl();
            long currentDuration = System.currentTimeMillis() - currentLoadSession.getLoadStartTime();

            // 如果是相同URL，忽略重复请求
            if (currentUrl.equals(url)) {
                LOG.d(TAG, "忽略重复的加载请求: " + url);
                CordovaWebViewErrorManager.getInstance().addCustomEvent("重复请求",
                    "相同URL重复请求", extractVersionFromUrl(url), 0);
                return;
            }

            // 判断URL优先级，热更新文件优先于APK内置文件
            boolean shouldReplaceCurrentLoad = shouldReplaceCurrentLoad(currentUrl, url);

            if (shouldReplaceCurrentLoad) {
                String reason = analyzeReplaceReason(currentUrl, url, currentDuration);
                LOG.w(TAG, "版本切换: " + reason);
                CordovaWebViewErrorManager.getInstance().setNeedReport(true);
                CordovaWebViewErrorManager.getInstance().addCustomEvent("版本切换",
                    reason, url, currentDuration);

                // 取消当前加载
                stopLoading();
                currentLoadSession = null;
            } else {
                LOG.d(TAG, "当前版本优先级更高，忽略新请求");
                CordovaWebViewErrorManager.getInstance().addCustomEvent("忽略低优先级",
                    "忽略低优先级请求", extractVersionFromUrl(url), 0);
                return;
            }
        }

        // 记录加载开始时间，用于性能分析
        long loadStartTime = System.currentTimeMillis();
        String versionInfo = extractVersionFromUrl(url);
        CordovaWebViewErrorManager.getInstance().addCustomEvent("加载请求",
            String.format("加载请求 - 版本[%s] 重建插件:%s", versionInfo, recreatePlugins), url, 0);

        if (url.equals("about:blank") || url.startsWith("javascript:")) {
            engine.loadUrl(url, false);
            return;
        }

        // 如果是本地文件，异步检查文件是否存在（不阻塞主线程）
        if (url.startsWith("file://")) {
            checkFileExistsAsync(url, recreatePlugins);
        } else {
            // 非本地文件直接加载
            continueLoadingUrlWithRetry(url, recreatePlugins, 0);
        }
    }
    

    
    /**
     * 异步检查文件是否存在，不阻塞主线程
     * 使用Handler延迟检查，允许APK解压继续进行
     */
    private void checkFileExistsAsync(final String url, final boolean recreatePlugins) {
        final Handler handler = new Handler(Looper.getMainLooper());
        
        // 创建统一的WebViewLoadTimeoutListener，文件检查和后续加载共用
        final int currentLoadSessionId = loadSessionId;

        // 使用动态超时时间，特别是热更新文件
        final int timeoutValue;
        if (url.contains("cordova-hot-code-push-plugin")) {
            // 热更新文件使用更长的超时时间，根据设备内存调整
            long availableMemory = Runtime.getRuntime().maxMemory() / 1024 / 1024; // MB
            if (availableMemory < 512) {
                timeoutValue = 90000; // 低内存设备90秒
            } else if (availableMemory < 1024) {
                timeoutValue = 75000; // 中等内存设备75秒
            } else {
                timeoutValue = 60000; // 高内存设备60秒
            }
            LOG.d(TAG, "文件检查阶段使用热更新专用超时时间: " + timeoutValue + "ms (内存: " + availableMemory + "MB)");
        } else {
            // 普通文件使用标准超时时间
            timeoutValue = url.startsWith("file://") ? LOAD_TIMEOUT_VALUE_LOCAL : LOAD_TIMEOUT_VALUE;
            LOG.d(TAG, "文件检查阶段使用标准超时时间: " + timeoutValue + "ms");
        }
        final LoadSession loadSession = new LoadSession(url);
        // 更新当前LoadSession
        this.currentLoadSession = loadSession;
        final WebViewLoadTimeoutListener unifiedListener = new WebViewLoadTimeoutListener(url, recreatePlugins, currentLoadSessionId, timeoutValue, 0, loadSession);
        
        final Runnable checkIndex = new Runnable() {
            int elapsed = 0;
            
            @Override
            public void run() {
                try {
                    // 检查文件是否就绪，热更新文件使用更宽松的检查逻辑
                    boolean fileReady = false;
                    if (url.contains("cordova-hot-code-push-plugin")) {
                        // 热更新文件使用简单的存在性检查，不检查内容
                        try {
                            String filePath = url.replace("file://", "");
                            File file = new File(filePath);
                            fileReady = file.exists() && file.length() > 0;
                            LOG.d(TAG, "热更新文件检查: " + filePath + " 存在=" + file.exists() + " 大小=" + file.length());
                        } catch (Exception e) {
                            LOG.w(TAG, "热更新文件检查异常", e);
                            fileReady = false;
                        }
                    } else {
                        // 普通文件使用标准检查
                        fileReady = isIndexHtmlReady(url);
                    }

                    if (fileReady) {
                        CordovaWebViewErrorManager.getInstance().addCustomEvent("文件就绪",
                            "文件检查完成", url, elapsed);
                        LOG.d(TAG, "文件检查完成，开始加载: " + url + " (检查耗时: " + elapsed + "ms)");

                        // index.html已就绪，立即开始加载，不再等待
                        LOG.d(TAG, "文件已就绪，立即开始加载: " + url);

                        // 尝试预热WebView缓存
                        try {
                            cordova.getActivity().runOnUiThread(new Runnable() {
                                @Override
                                public void run() {
                                    // 预设置WebView参数以优化加载
                                    if (engine.getView() instanceof android.webkit.WebView) {
                                        android.webkit.WebView webView = (android.webkit.WebView) engine.getView();
                                        webView.getSettings().setCacheMode(android.webkit.WebSettings.LOAD_DEFAULT);
                                        webView.getSettings().setDomStorageEnabled(true);
                                        webView.getSettings().setDatabaseEnabled(true);
                                    }
                                }
                            });
                        } catch (Exception e) {
                            LOG.w(TAG, "预设置WebView参数失败", e);
                        }

                        // 立即开始加载，不延迟
                        performLoadWithListener(url, recreatePlugins, unifiedListener);
                    } else if (elapsed < FILE_CHECK_MAX_DELAY) {
                        elapsed += FILE_CHECK_INTERVAL;
                        CordovaWebViewErrorManager.getInstance().setNeedReport(true);
                        
                        CordovaWebViewErrorManager.getInstance().addCustomEvent("文件等待",
                            "等待文件解压中... (已等待" + elapsed + "ms)");
                        LOG.d(TAG, "等待文件解压中... (已等待" + elapsed + "ms): " + url);
                        handler.postDelayed(this, FILE_CHECK_INTERVAL);
                    } else {
                        // 设置需要上报的标记
                        CordovaWebViewErrorManager.getInstance().setNeedReport(true);
                        CordovaWebViewErrorManager.getInstance().addCustomEvent("文件超时",
                            "文件检查超时，强制开始加载 (总等待时间: " + elapsed + "ms)");
                        LOG.w(TAG, "文件检查超时，强制开始加载: " + url + " (总等待时间: " + elapsed + "ms)");
                        // 文件检查超时，记录TIMEOUT事件后加载
                        performLoadWithListener(url, recreatePlugins, unifiedListener);
                    }
                } catch (Exception e) {
                    // 设置需要上报的标记
                    CordovaWebViewErrorManager.getInstance().setNeedReport(true);
                    CordovaWebViewErrorManager.getInstance().addCustomEvent("文件错误", 
                        "检查文件异常: " + e.getMessage());
                    LOG.e(TAG, "检查文件时异常: " + url, e);
                    handleFileNotFoundError(url, e);
                }
            }
        };
        
        checkIndex.run();
    }
    
    // 异步文件检查参数
    private static final int FILE_CHECK_MAX_DELAY = 1000; // 最大等待1秒，热更新文件快速进入加载阶段
    private static final int FILE_CHECK_INTERVAL = 200;  // 每200ms检查一次，加快检查频率
    
    // 单次超时时间（毫秒）
    private static final int LOAD_TIMEOUT_VALUE = 20000; // 网络文件超时时间增加到20秒
    private static final int LOAD_TIMEOUT_VALUE_LOCAL = 15000; // 本地文件超时时间增加到15秒，给设备更多解压和加载时间
    // 加载超时最大重试次数
    private static final int LOAD_TIMEOUT_MAX_RETRY_COUNT = 2; // 适度重试2次，平衡稳定性和等待时间
    // 加载超时重试延迟（毫秒）
    private static final long LOAD_TIMEOUT_RETRY_DELAY_MS = 2000; // 重试延迟2秒，给系统恢复时间
    
    /**
     * 增加指定URL的重试计数
     */
    private int incrementRetryCount(String url) {
        synchronized (networkErrorRetryMap) {
            Integer count = networkErrorRetryMap.get(url);
            if (count == null) {
                count = 0;
            }
            count++;
            networkErrorRetryMap.put(url, count);
            return count;
        }
    }
    
    /**
     * 处理文件不存在的错误
     * 文件检查时已等待解压完成，此处直接处理最终失败情况
     */
    private void handleFileNotFoundError(final String url, Exception e) {
            // 检查是否需要重试文件访问错误
            int retryCount = incrementRetryCount(url);
            
            if (retryCount <= LOAD_TIMEOUT_MAX_RETRY_COUNT) {
                LOG.d(TAG, "本地文件访问错误，计划重试 (" + url + ") - 第 " + retryCount + " 次重试");
                
                // 本地文件错误需要延迟重试，给系统文件访问恢复时间
                retryHandler.postDelayed(new Runnable() {
                    @Override
                    public void run() {
                        LOG.d(TAG, "执行本地文件重试加载: " + url);
                        continueLoadingUrlWithRetry(url, false, retryCount);
                    }
                }, LOAD_TIMEOUT_RETRY_DELAY_MS);
                
                return;
            }
            
            // 达到最大重试次数，处理最终错误
            stopLoading();
            
            // 设置需要上报的标记
            CordovaWebViewErrorManager.getInstance().setNeedReport(true);
            
            String detailedDescription = "文件不存在或无法访问: " + url;
            if (e != null) {
                detailedDescription += " (" + e.getMessage() + ")";
            }
            
            LOG.e(TAG, "文件加载失败: " + url);
            
            // 创建错误JSON对象并发送错误消息
            JSONObject data = new JSONObject();
            try {
                data.put("errorCode", WebViewClient.ERROR_FILE_NOT_FOUND);
                data.put("description", detailedDescription);
                data.put("url", url);
                data.put("isLocalFile", true);
                data.put("retryCount", retryCount);
            } catch (JSONException jsonEx) {
                LOG.e(TAG, "创建错误JSON对象失败: " + jsonEx.getMessage());
            }
            pluginManager.postMessage("onReceivedError", data);
        }
    

    
    /**
     * 带重试机制的加载URL流程
     * @param url 要加载的URL
     * @param recreatePlugins 是否重新创建插件
     * @param retryCount 当前重试次数
     */
    private void continueLoadingUrlWithRetry(String url, boolean recreatePlugins, int retryCount) {
        LoadSession loadSession = new LoadSession(url);
        // 更新当前LoadSession
        this.currentLoadSession = loadSession;
        continueLoadingUrlWithRetry(url, recreatePlugins, retryCount, loadSession);
    }

    private void continueLoadingUrlWithRetry(String url, boolean recreatePlugins, int retryCount, LoadSession loadSession) {
        recreatePlugins = recreatePlugins || (loadedUrl == null);

        if (recreatePlugins) {
            // Don't re-initialize on first load.
            if (loadedUrl != null) {
                appPlugin = null;
                pluginManager.init();
            }
            loadedUrl = url;
        }

        // Create a timeout timer for loadUrl
        final int currentLoadSessionId = loadSessionId;

        // 使用优化器动态计算超时时间，热更新文件使用更长的超时时间
        final int timeoutValue;
        if (url.contains("cordova-hot-code-push-plugin")) {
            // 热更新文件使用更长的超时时间，根据设备内存调整
            long availableMemory = Runtime.getRuntime().maxMemory() / 1024 / 1024; // MB
            if (availableMemory < 512) {
                timeoutValue = 90000; // 低内存设备90秒
            } else if (availableMemory < 1024) {
                timeoutValue = 75000; // 中等内存设备75秒
            } else {
                timeoutValue = 60000; // 高内存设备60秒
            }
            LOG.d(TAG, "使用热更新专用超时时间: " + timeoutValue + "ms (内存: " + availableMemory + "MB)");
        } else {
            timeoutValue = WebViewLoadOptimizer.getInstance()
                .getRecommendedTimeout(url, engine.getView().getContext());
            LOG.d(TAG, "使用通用超时时间: " + timeoutValue + "ms");
        }

        // 创建加载超时监听器（带重试机制）
        WebViewLoadTimeoutListener loadListener = new WebViewLoadTimeoutListener(url, recreatePlugins, currentLoadSessionId, timeoutValue, retryCount, loadSession);

        performLoadWithListener(url, recreatePlugins, loadListener);
    }
    
    /**
     * 使用指定的监听器执行加载
     * @param url 要加载的URL
     * @param recreatePlugins 是否重新创建插件
     * @param loadListener 已创建的WebViewLoadTimeoutListener实例
     */
    private void performLoadWithListener(String url, boolean recreatePlugins, WebViewLoadTimeoutListener loadListener) {
        final boolean _recreatePlugins = recreatePlugins;
        cordova.getActivity().runOnUiThread(new Runnable() {
            public void run() {
                if (loadListener.loadUrlTimeoutValue > 0) {
                    cordova.getThreadPool().execute(loadListener.timeoutCheck);
                }
                engine.loadUrl(url, _recreatePlugins);
            }
        });
    }
    
    /**
     * 会话加载记录器，仅保留时间戳和URL信息
     * 所有事件记录都通过全局ErrorManager处理
     */
    private class LoadSession {
        private final String url;
        private final long loadStartTime;
        
        public LoadSession(String url) {
            this.url = url;
            this.loadStartTime = System.currentTimeMillis();
        }
        
        public long getLoadStartTime() {
            return loadStartTime;
        }
        
        public String getUrl() {
            return url;
        }
    }
    
    /**
     * WebView加载超时监听器，专注于监听加载超时和重试处理
     */
    private class WebViewLoadTimeoutListener {
        private final String url;
        private final boolean recreatePlugins;
        private final int currentLoadSessionId;
        private final int loadUrlTimeoutValue;
        private final int retryCount;
        private final LoadSession loadSession;
        private boolean loadCompleted = false;
        
        public WebViewLoadTimeoutListener(String url, boolean recreatePlugins, int currentLoadSessionId, int loadUrlTimeoutValue, int retryCount, LoadSession loadSession) {
            this.url = url;
            this.recreatePlugins = recreatePlugins;
            this.currentLoadSessionId = currentLoadSessionId;
            this.loadUrlTimeoutValue = loadUrlTimeoutValue;
            this.retryCount = retryCount;
            this.loadSession = loadSession;
            
            long duration = System.currentTimeMillis() - loadSession.getLoadStartTime();
            
            // 区分文件检查阶段和实际加载阶段
            if (url.startsWith("file://") && retryCount == 0) {
                CordovaWebViewErrorManager.getInstance().addCustomEvent("文件检查开始", 
                    "开始文件检查阶段: " + url + ", 超时时间: " + loadUrlTimeoutValue + "ms");
            } else {
                CordovaWebViewErrorManager.getInstance().addCustomEvent("开始加载", 
                    "开始实际加载: " + url + ", 超时时间: " + loadUrlTimeoutValue + "ms, 重试次数: " + retryCount);
            }
        }
        
        public final Runnable timeoutCheck = new Runnable() {
            public void run() {
                try {
                    synchronized (this) {
                        wait(loadUrlTimeoutValue);
                    }
                } catch (InterruptedException e) {
                    e.printStackTrace();
                }

                // 如果加载未完成且会话ID未变化，触发超时重试或最终错误处理
                if (!loadCompleted && loadSessionId == currentLoadSessionId) {
                    // 设置需要上报的标记
                    CordovaWebViewErrorManager.getInstance().setNeedReport(true);
                    CordovaWebViewErrorManager.getInstance().addCustomEvent("超时", 
                        "加载超时 (" + loadUrlTimeoutValue + "ms)");
                    handleLoadTimeout();
                }
            }
        };
        
        private void handleLoadTimeout() {
            if (loadCompleted) {
                LOG.d(TAG, "加载已完成，忽略超时处理");
                return;
            }

            loadCompleted = true;
            
            int nextRetry = retryCount + 1;
            String timeoutReason = analyzeTimeoutReason(url, loadUrlTimeoutValue);
            
            if (nextRetry <= LOAD_TIMEOUT_MAX_RETRY_COUNT) {
                LOG.i(TAG, String.format("加载超时重试 %d/%d: %s", 
                    nextRetry, LOAD_TIMEOUT_MAX_RETRY_COUNT, timeoutReason));
                
                CordovaWebViewErrorManager.getInstance().addRetryEvent(url, nextRetry);
                
                // 超时错误无需延迟，立即重试
                continueLoadingUrlWithRetry(url, recreatePlugins, nextRetry, loadSession);
                
            } else {
                String finalError = String.format("加载最终失败: %s (重试%d次后仍然超时)", 
                    timeoutReason, LOAD_TIMEOUT_MAX_RETRY_COUNT);
                LOG.e(TAG, finalError);
                
                CordovaWebViewErrorManager.getInstance().setNeedReport(true);
                CordovaWebViewErrorManager.getInstance().addCustomEvent("最终错误", 
                    finalError, url, loadUrlTimeoutValue);
                    
                handleFinalLoadError(WebViewClient.ERROR_TIMEOUT, finalError, url);
            }
        }
        
        private void handleFinalLoadError(int errorCode, String description, String failingUrl) {
            stopLoading();
            
            // 设置需要上报的标记
            CordovaWebViewErrorManager.getInstance().setNeedReport(true);
            CordovaWebViewErrorManager.getInstance().addCustomEvent("错误", 
                "最终错误: " + description + " (错误码: " + errorCode + ")");
            
            LOG.e(TAG, "CordovaWebView: 加载失败! URL: " + failingUrl + ", 错误码: " + errorCode + ", 描述: " + description);
            
            // 统一上报完整过程信息
            reportCompleteLoadProcess(false, errorCode, description, failingUrl, loadSession);
        }
        





        private void reportLoadErrorToServer(int errorCode, String description, String failingUrl, String systemInfo) {
            try {
                // 统一使用reportCompleteLoadProcess，创建默认LoadSession
                LoadSession defaultSession = createDefaultLoadSession(failingUrl);
                reportCompleteLoadProcess(false, errorCode, description, failingUrl, defaultSession, 0);

            } catch (Exception e) {
                LOG.e(TAG, "上报加载错误失败: " + e.getMessage());
            }
        }
        

    }
    
    /**
     * 构建系统信息字符串
     */
    protected String buildSystemInfo() {
        return String.format("安卓 %s, 设备: %s %s, 可用内存: %dMB, SDK版本: %d, 应用版本: %s",
                android.os.Build.VERSION.RELEASE,
                android.os.Build.MANUFACTURER,
                android.os.Build.MODEL,
                Runtime.getRuntime().maxMemory() / 1024 / 1024,
                android.os.Build.VERSION.SDK_INT,
                getAppVersion());
    }
    
    /**
     * 获取应用版本号
     */
    protected String getAppVersion() {
        try {
            Context context = getContext();
            if (context != null) {
                return context.getPackageManager()
                        .getPackageInfo(context.getPackageName(), 0)
                        .versionName;
            }
        } catch (Exception e) {
            LOG.w(TAG, "获取应用版本号失败", e);
        }
        return "unknown";
    }
    


    /**
     * 上传完整加载报告到服务器
     */
    private void uploadCompleteReport(String completeReport) {
        try {
            OkHttpClient client = new OkHttpClient();
            
            RequestBody body = RequestBody.create(
                MediaType.parse("application/json; charset=utf-8"), 
                completeReport
            );
            
            Request request = new Request.Builder()
                .url("https://www.yingjiang168.com/AppApi/Login/AndroidDumpReport")
                .post(body)
                .build();
            
            client.newCall(request).enqueue(new Callback() {
                @Override
                public void onFailure(Call call, IOException e) {
                    LOG.e(TAG, "加载报告上传失败: " + e.getMessage());
                }

                @Override
                public void onResponse(Call call, Response response) throws IOException {
                    if (response.isSuccessful()) {
                        LOG.i(TAG, "加载报告上传成功");
                    } else {
                        LOG.e(TAG, "加载报告上传失败，状态码: " + response.code());
                    }
                    response.close();
                }
            });
        } catch (Exception e) {
            LOG.e(TAG, "上传加载报告异常", e);
        }
    }



    @Override
    public void loadUrl(String url) {
        loadUrlIntoView(url, true);
    }

    @Override
    public void showWebPage(String url, boolean openExternal, boolean clearHistory, Map<String, Object> params) {
        LOG.d(TAG, "showWebPage(%s, %b, %b, HashMap)", url, openExternal, clearHistory);

        // If clearing history
        if (clearHistory) {
            engine.clearHistory();
        }

        // If loading into our webview
        if (!openExternal) {
            // Make sure url is in whitelist
            if (pluginManager.shouldAllowNavigation(url)) {
                // TODO: What about params?
                // Load new URL
                loadUrlIntoView(url, true);
                return;
            } else {
                LOG.w(TAG, "showWebPage: Refusing to load URL into webview since it is not in the <allow-navigation> whitelist. URL=" + url);
                return;
            }
        }
        if (!pluginManager.shouldOpenExternalUrl(url)) {
            LOG.w(TAG, "showWebPage: Refusing to send intent for URL since it is not in the <allow-intent> whitelist. URL=" + url);
            return;
        }
        try {
            Intent intent = new Intent(Intent.ACTION_VIEW);
            // To send an intent without CATEGORY_BROWSER, a custom plugin should be used.
            intent.addCategory(Intent.CATEGORY_BROWSABLE);
            Uri uri = Uri.parse(url);
            // Omitting the MIME type for file: URLs causes "No Activity found to handle Intent".
            // Adding the MIME type to http: URLs causes them to not be handled by the downloader.
            if ("file".equals(uri.getScheme())) {
                intent.setDataAndType(uri, resourceApi.getMimeType(uri));
            } else {
                intent.setData(uri);
            }
            cordova.getActivity().startActivity(intent);
        } catch (android.content.ActivityNotFoundException e) {
            LOG.e(TAG, "Error loading url " + url, e);
        }
    }

    private static class WrapperView extends FrameLayout {

        private final CordovaWebViewEngine engine;

        public WrapperView(Context context, CordovaWebViewEngine engine) {
            super(context);
            this.engine = engine;
        }

        @Override
        public boolean dispatchKeyEvent(KeyEvent event) {
            return engine.getView().dispatchKeyEvent(event);
        }
    }

    @Override
    @Deprecated
    public void showCustomView(View view, WebChromeClient.CustomViewCallback callback) {
        // This code is adapted from the original Android Browser code, licensed under the Apache License, Version 2.0
        LOG.d(TAG, "showing Custom View");
        // if a view already exists then immediately terminate the new one
        if (mCustomView != null) {
            callback.onCustomViewHidden();
            return;
        }

        WrapperView wrapperView = new WrapperView(getContext(), engine);
        wrapperView.addView(view);

        // Store the view and its callback for later (to kill it properly)
        mCustomView = wrapperView;
        mCustomViewCallback = callback;

        // Add the custom view to its container.
        ViewGroup parent = (ViewGroup) engine.getView().getParent();
        parent.addView(wrapperView, new FrameLayout.LayoutParams(
                ViewGroup.LayoutParams.MATCH_PARENT,
                ViewGroup.LayoutParams.MATCH_PARENT,
                Gravity.CENTER));

        // Hide the content view.
        engine.getView().setVisibility(View.GONE);

        // Finally show the custom view container.
        parent.setVisibility(View.VISIBLE);
        parent.bringToFront();
    }

    @Override
    @Deprecated
    public void hideCustomView() {
        // This code is adapted from the original Android Browser code, licensed under the Apache License, Version 2.0
        if (mCustomView == null) return;
        LOG.d(TAG, "Hiding Custom View");

        // Hide the custom view.
        mCustomView.setVisibility(View.GONE);

        // Remove the custom view from its container.
        ViewGroup parent = (ViewGroup) engine.getView().getParent();
        parent.removeView(mCustomView);
        mCustomView = null;
        mCustomViewCallback.onCustomViewHidden();

        // Show the content view.
        engine.getView().setVisibility(View.VISIBLE);
    }

    @Override
    @Deprecated
    public boolean isCustomViewShowing() {
        return mCustomView != null;
    }

    @Override
    @Deprecated
    public void sendJavascript(String statement) {
        nativeToJsMessageQueue.addJavaScript(statement);
    }

    @Override
    public void sendPluginResult(PluginResult cr, String callbackId) {
        nativeToJsMessageQueue.addPluginResult(cr, callbackId);
    }

    @Override
    public PluginManager getPluginManager() {
        return pluginManager;
    }
    @Override
    public CordovaPreferences getPreferences() {
        return preferences;
    }
    @Override
    public ICordovaCookieManager getCookieManager() {
        return engine.getCookieManager();
    }
    @Override
    public CordovaResourceApi getResourceApi() {
        return resourceApi;
    }
    @Override
    public CordovaWebViewEngine getEngine() {
        return engine;
    }
    @Override
    public View getView() {
        return engine.getView();
    }
    @Override
    public Context getContext() {
        return engine.getView().getContext();
    }

    private void sendJavascriptEvent(String event) {
        if (appPlugin == null) {
            appPlugin = (CoreAndroid)pluginManager.getPlugin(CoreAndroid.PLUGIN_NAME);
        }

        if (appPlugin == null) {
            LOG.w(TAG, "Unable to fire event without existing plugin");
            return;
        }
        appPlugin.fireJavascriptEvent(event);
    }

    @Override
    public void setButtonPlumbedToJs(int keyCode, boolean override) {
        switch (keyCode) {
            case KeyEvent.KEYCODE_VOLUME_DOWN:
            case KeyEvent.KEYCODE_VOLUME_UP:
            case KeyEvent.KEYCODE_BACK:
            case KeyEvent.KEYCODE_MENU:
                // TODO: Why are search and menu buttons handled separately?
                if (override) {
                    boundKeyCodes.add(keyCode);
                } else {
                    boundKeyCodes.remove(keyCode);
                }
                return;
            default:
                throw new IllegalArgumentException("Unsupported keycode: " + keyCode);
        }
    }

    @Override
    public boolean isButtonPlumbedToJs(int keyCode) {
        return boundKeyCodes.contains(keyCode);
    }

    @Override
    public Object postMessage(String id, Object data) {
        return pluginManager.postMessage(id, data);
    }

    // Engine method proxies:
    @Override
    public String getUrl() {
        return engine.getUrl();
    }

    @Override
    public void stopLoading() {
        // Clear timeout flag
        loadSessionId++;
    }

    @Override
    public boolean canGoBack() {
        return engine.canGoBack();
    }

    @Override
    public void clearCache() {
        engine.clearCache();
    }

    @Override
    @Deprecated
    public void clearCache(boolean b) {
        engine.clearCache();
    }

    @Override
    public void clearHistory() {
        engine.clearHistory();
    }

    @Override
    public boolean backHistory() {
        return engine.goBack();
    }

    /////// LifeCycle methods ///////
    @Override
    public void onNewIntent(Intent intent) {
        if (this.pluginManager != null) {
            this.pluginManager.onNewIntent(intent);
        }
    }
    @Override
    public void handlePause(boolean keepRunning) {
        if (!isInitialized()) {
            return;
        }
        hasPausedEver = true;
        pluginManager.onPause(keepRunning);
        sendJavascriptEvent("pause");

        // If app doesn't want to run in background
        if (!keepRunning) {
            // Pause JavaScript timers. This affects all webviews within the app!
            engine.setPaused(true);
        }
    }
    @Override
    public void handleResume(boolean keepRunning) {
        if (!isInitialized()) {
            return;
        }

        // Resume JavaScript timers. This affects all webviews within the app!
        engine.setPaused(false);
        this.pluginManager.onResume(keepRunning);

        // In order to match the behavior of the other platforms, we only send onResume after an
        // onPause has occurred. The resume event might still be sent if the Activity was killed
        // while waiting for the result of an external Activity once the result is obtained
        if (hasPausedEver) {
            sendJavascriptEvent("resume");
        }
    }
    @Override
    public void handleStart() {
        if (!isInitialized()) {
            return;
        }
        pluginManager.onStart();
    }
    @Override
    public void handleStop() {
        if (!isInitialized()) {
            return;
        }
        pluginManager.onStop();
    }
    @Override
    public void handleDestroy() {
        if (!isInitialized()) {
            return;
        }
        // Cancel pending timeout timer.
        loadSessionId++;

        // Forward to plugins
        this.pluginManager.onDestroy();

        // TODO: about:blank is a bit special (and the default URL for new frames)
        // We should use a blank data: url instead so it's more obvious
        this.loadUrl("about:blank");

        // TODO: Should not destroy webview until after about:blank is done loading.
        engine.destroy();
        hideCustomView();
    }

    // 网络错误重试的最大次数
    private static final int NETWORK_ERROR_MAX_RETRY_COUNT = 2; // 网络错误重试次数也保持适度
    // 网络错误重试间隔（毫秒）
    private static final long NETWORK_ERROR_RETRY_INTERVAL_MS = 1500; // 网络错误重试延迟适度增加
    // 用于存储网络错误的重试计数的映射
    private final Map<String, Integer> networkErrorRetryMap = new HashMap<String, Integer>();
    // 用于处理延迟重试的Handler
    private final Handler retryHandler = new Handler(Looper.getMainLooper());

    private boolean assetExists(String assetPath) {
        try {
            return cordova.getActivity().getAssets().open(assetPath) != null;
        } catch (IOException e) {
            return false;
        }
    }
    
    private boolean isAssetReady(String assetPath) {
        try {
            AssetFileDescriptor afd = cordova.getActivity().getAssets().openFd(assetPath);
            if (afd == null) return false;
            
            // 检查文件大小是否大于0（确保内容完整）
            long length = afd.getLength();
            afd.close();
            
            return length > 0;
        } catch (IOException e) {
            return false;
        }
    }

    private boolean isIndexHtmlReady(String url) {
        try {
            Uri uri = Uri.parse(url);

            int uriType = CordovaResourceApi.getUriType(uri);
            switch (uriType) {
                case CordovaResourceApi.URI_TYPE_FILE:
                    File file = new File(uri.getPath());
                    if (!file.exists() || !file.isFile()) {
                        return false;
                    }

                    // 检查文件大小 - HTML文件应该至少有一些内容
                    long fileSize = file.length();
                    if (fileSize < 100) { // HTML文件至少应该有100字节
                        LOG.d(TAG, "文件太小，可能不完整: " + fileSize + " bytes");
                        return false;
                    }

                    // 尝试读取文件开头，确保文件可读且包含HTML内容
                    try {
                        java.io.FileInputStream fis = new java.io.FileInputStream(file);
                        byte[] buffer = new byte[50];
                        int bytesRead = fis.read(buffer);
                        fis.close();

                        if (bytesRead > 0) {
                            String content = new String(buffer, 0, bytesRead).toLowerCase();
                            // 检查是否包含HTML标记
                            boolean hasHtml = content.contains("<!doctype") || content.contains("<html") || content.contains("<head");
                            if (hasHtml) {
                                LOG.d(TAG, "文件内容验证通过: " + url + " (大小: " + fileSize + " bytes)");
                                return true;
                            } else {
                                LOG.d(TAG, "文件不包含HTML内容: " + content.substring(0, Math.min(30, content.length())));
                                return false;
                            }
                        }
                    } catch (IOException e) {
                        LOG.d(TAG, "读取文件内容失败: " + e.getMessage());
                        return false;
                    }

                    return fileSize > 0;

                case CordovaResourceApi.URI_TYPE_CONTENT:
                case CordovaResourceApi.URI_TYPE_RESOURCE:
                    try {
                        AssetFileDescriptor assetFd = resourceApi.openForRead(uri, true).assetFd;
                        if (assetFd != null) {
                            boolean ready = assetFd.getLength() > 100; // 至少100字节
                            assetFd.close();
                            return ready;
                        }
                    } catch (IOException e) {
                        return false;
                    }
                    break;

                case CordovaResourceApi.URI_TYPE_ASSET:
                    try {
                        InputStream inputStream = resourceApi.openForRead(uri, true).inputStream;
                        if (inputStream != null) {
                            boolean ready = inputStream.available() > 100; // 至少100字节
                            inputStream.close();
                            return ready;
                        }
                    } catch (IOException e) {
                        return false;
                    }
                    break;
            }
            return false;
        } catch (Exception e) {
            LOG.w(TAG, "检查文件就绪状态时出错: " + e.getMessage());
            return false;
        }
    }

    /**
     * 获取当前LoadSession，如果没有则创建默认的
     */
    private LoadSession getCurrentLoadSession(String url) {
        if (currentLoadSession != null && currentLoadSession.getUrl().equals(url)) {
            return currentLoadSession;
        }
        // 如果没有匹配的LoadSession，创建默认的
        return createDefaultLoadSession(url);
    }

    /**
     * 创建默认的LoadSession（用于没有现有LoadSession的场景）
     * 注意：默认LoadSession的开始时间是创建时间，可能不准确反映实际加载开始时间
     */
    private LoadSession createDefaultLoadSession(String url) {
        LoadSession session = new LoadSession(url);
        LOG.d(TAG, "创建默认LoadSession用于上报: " + url);
        return session;
    }

    /**
     * 分析版本替换的原因，提供更有意义的错误信息
     */
    private String analyzeReplaceReason(String currentUrl, String newUrl, long currentDuration) {
        String currentVersion = extractVersionFromUrl(currentUrl);
        String newVersion = extractVersionFromUrl(newUrl);
        
        // 判断是否为版本回退
        boolean isDowngrade = isVersionDowngrade(currentVersion, newVersion);
        
        // 判断是否为超时情况
        boolean isTimeout = currentDuration > 30000; // 30秒超时
        
        StringBuilder reason = new StringBuilder();
        
        if (isDowngrade) {
            reason.append("版本回退: ");
            if (isTimeout) {
                reason.append(String.format("[%s]加载失败(%.1fs)回退至[%s]", 
                    currentVersion, currentDuration/1000.0, newVersion));
            } else {
                reason.append(String.format("从[%s]回退至[%s]", currentVersion, newVersion));
            }
        } else {
            reason.append("版本切换: ");
            reason.append(String.format("从[%s]切换至[%s]", currentVersion, newVersion));
            if (isTimeout) {
                reason.append(String.format("(前版本超时%.1fs)", currentDuration/1000.0));
            }
        }
        
        return reason.toString();
    }
    
    /**
     * 简单的版本比较，判断是否为版本回退
     */
    private boolean isVersionDowngrade(String currentVersion, String newVersion) {
        try {
            // 如果都是日期格式的版本号，比较日期
            if (currentVersion.matches("\\d{4}\\.\\d{2}\\.\\d{2}-\\d{2}\\.\\d{2}\\.\\d{2}") &&
                newVersion.matches("\\d{4}\\.\\d{2}\\.\\d{2}-\\d{2}\\.\\d{2}\\.\\d{2}")) {
                return newVersion.compareTo(currentVersion) < 0;
            }
            
            // 热更新版本 vs APK内置版本
            if (currentVersion.matches("\\d{4}\\.\\d{2}\\.\\d{2}-\\d{2}\\.\\d{2}\\.\\d{2}") && 
                newVersion.equals("APK内置")) {
                return true; // 从热更新回退到APK内置
            }
            
        } catch (Exception e) {
            LOG.w(TAG, "版本比较失败", e);
        }
        
        return false;
    }

    /**
     * 从 URL 中提取版本号，方便日志阅读
     */
    private String extractVersionFromUrl(String url) {
        if (url == null) return "unknown";
        
        // 对于热更新文件，提取版本号
        if (url.contains("cordova-hot-code-push-plugin")) {
            try {
                // 提取类似 2025.09.08-03.20.28 的版本号
                String[] parts = url.split("/");
                for (String part : parts) {
                    if (part.matches("\\d{4}\\.\\d{2}\\.\\d{2}-\\d{2}\\.\\d{2}\\.\\d{2}")) {
                        return part;
                    }
                }
            } catch (Exception e) {
                // 如果提取失败，返回类型
            }
            return "HotUpdate";
        } else if (url.contains("android_asset")) {
            return "APK内置";
        } else {
            return "网络文件";
        }
    }

    /**
     * 分析超时原因，提供更有针对性的错误信息
     */
    private String analyzeTimeoutReason(String url, int timeoutValue) {
        StringBuilder reason = new StringBuilder();
        
        String version = extractVersionFromUrl(url);
        reason.append(version).append("加载超时(").append(timeoutValue/1000).append("s)");
        
        // 根据URL类型分析可能原因
        if (url.contains("cordova-hot-code-push-plugin")) {
            reason.append(" - 可能原因:热更新文件下载慢或包含在线JS资源");
        } else if (url.contains("android_asset")) {
            reason.append(" - 可能原因:APK文件损坏或设备性能低");
        } else {
            reason.append(" - 可能原因:网络连接问题或服务器响应慢");
        }
        
        return reason.toString();
    }

    /**
     * 判断是否应该用新的URL替换当前正在加载的URL
     * 优先级规则：
     * 1. 热更新文件 > APK内置文件
     * 2. 相同类型文件，后来的请求优先级更高
     */
    private boolean shouldReplaceCurrentLoad(String currentUrl, String newUrl) {
        // 判断是否为热更新文件（包含cordova-hot-code-push-plugin路径）
        boolean currentIsHotUpdate = currentUrl.contains("cordova-hot-code-push-plugin");
        boolean newIsHotUpdate = newUrl.contains("cordova-hot-code-push-plugin");

        // 如果新请求是热更新文件，当前不是，则替换
        if (newIsHotUpdate && !currentIsHotUpdate) {
            LOG.d(TAG, "热更新文件优先级高于APK内置文件");
            return true;
        }

        // 如果当前是热更新文件，新请求不是，则不替换
        if (currentIsHotUpdate && !newIsHotUpdate) {
            LOG.d(TAG, "保持热更新文件，忽略APK内置文件请求");
            return false;
        }

        // 相同类型的文件，后来的请求优先级更高（可能是重试或更新的请求）
        LOG.d(TAG, "相同类型文件，新请求优先级更高");
        return true;
    }

    /**
     * 统一上报完整加载过程信息
     */
    private void reportCompleteLoadProcess(boolean success, int errorCode, String description, String failingUrl, LoadSession loadSession) {
        reportCompleteLoadProcess(success, errorCode, description, failingUrl, loadSession, 0);
    }

    /**
     * 统一上报完整加载过程信息（带重试次数）
     */
    private void reportCompleteLoadProcess(boolean success, int errorCode, String description, String failingUrl, LoadSession loadSession, int retryCount) {
        try {
            // 只在需要上报时才上报
            if (!CordovaWebViewErrorManager.getInstance().isNeedReport()) {
                return;
            }

            String systemInfo = buildSystemInfo();

            // 构建完整的加载报告
            String completeReport = buildCompleteLoadReport(success, errorCode, description, failingUrl, systemInfo, loadSession, retryCount);

            // 上传到服务器
            uploadCompleteReport(completeReport);

        } catch (Exception e) {
            LOG.e(TAG, "上报完整加载过程失败", e);
        }
    }

    /**
     * 构建完整的加载报告
     */
    private String buildCompleteLoadReport(boolean success, int errorCode, String description, String failingUrl, String systemInfo, LoadSession loadSession) {
        return buildCompleteLoadReport(success, errorCode, description, failingUrl, systemInfo, loadSession, 0);
    }

    /**
     * 构建简洁的加载报告（带重试次数）
     */
    private String buildCompleteLoadReport(boolean success, int errorCode, String description, String failingUrl, String systemInfo, LoadSession loadSession, int retryCount) {
        long duration = loadSession != null ?
            (System.currentTimeMillis() - loadSession.getLoadStartTime()) : 0;

        // 获取简化的错误历史
        String simplifiedErrorHistory = getSimplifiedErrorHistory();

        try {
            JSONObject report = new JSONObject();
            report.put("success", success);
            report.put("errorCode", errorCode);
            report.put("description", description);
            
            // 简化URL显示
            String shortUrl = failingUrl != null ? extractVersionFromUrl(failingUrl) : "unknown";
            report.put("version", shortUrl);
            report.put("url", failingUrl != null ? failingUrl : "unknown");
            
            report.put("duration", duration);
            report.put("systemInfo", systemInfo);
            report.put("errorHistory", simplifiedErrorHistory);
            report.put("timestamp", System.currentTimeMillis());
            report.put("appVersion", getAppVersion());
            report.put("platform", "安卓");
            report.put("retryCount", retryCount);
            report.put("hasLoadSession", loadSession != null);

            return report.toString();
        } catch (JSONException e) {
            // JSON构建失败时返回简化的文本格式
            String shortUrl = failingUrl != null ? extractVersionFromUrl(failingUrl) : "unknown";
            return String.format(
                "WebView加载报告\n" +
                "结果: %s\n" +
                "错误码: %d\n" +
                "描述: %s\n" +
                "版本: %s\n" +
                "耗时: %.1f毫秒\n" +
                "系统: %s\n" +
                "重试: %d次\n" +
                "错误历史: %s",
                success ? "成功" : "失败",
                errorCode,
                description,
                shortUrl,
                duration,
                systemInfo,
                retryCount,
                simplifiedErrorHistory.isEmpty() ? "无" : simplifiedErrorHistory
            );
        }
    }
    
    /**
     * 获取简化的错误历史，只包含关键信息
     */
    private String getSimplifiedErrorHistory() {
        List<CordovaWebViewErrorManager.WebViewError> errors = 
            CordovaWebViewErrorManager.getInstance().getErrorHistory();
        
        if (errors.isEmpty()) {
            return "";
        }
        
        StringBuilder simplified = new StringBuilder();
        for (CordovaWebViewErrorManager.WebViewError error : errors) {
            // 只显示关键错误
            String desc = error.description;
            if (desc.contains("版本回退") || desc.contains("加载超时") || 
                desc.contains("最终失败") || desc.contains("版本切换")) {
                if (simplified.length() > 0) {
                    simplified.append("; ");
                }
                simplified.append(desc);
            }
        }
        
        return simplified.toString();
    }

    protected class EngineClient implements CordovaWebViewEngine.Client {
        @Override
        public void clearLoadTimeoutTimer() {
            loadSessionId++;
        }

        @Override
        public void onPageStarted(String newUrl) {
            LOG.d(TAG, "onPageDidNavigate(" + newUrl + ")");
            boundKeyCodes.clear();
            pluginManager.onReset();
            pluginManager.postMessage("onPageStarted", newUrl);
        }

        @Override
        public void onReceivedError(int errorCode, String description, String failingUrl) {
            clearLoadTimeoutTimer();
            
            // 记录到全局错误管理器
            long loadDuration = System.currentTimeMillis();
            CordovaWebViewErrorManager.getInstance().addLoadError(errorCode, description, failingUrl);
            
            // 增强错误日志，记录详细错误信息
            LOG.e(TAG, "WebView加载错误: 错误码=" + errorCode + ", 描述=" + description + ", URL=" + failingUrl);
            
            // 检查是否需要重试
            if (shouldRetryError(errorCode, failingUrl)) {
                // 增加重试计数
                int retryCount = incrementRetryCount(failingUrl);
                
                // 如果未达到最大重试次数，则进行重试
                if (retryCount <= NETWORK_ERROR_MAX_RETRY_COUNT) {
                    LOG.d(TAG, "计划重试加载URL: " + failingUrl + " (第 " + retryCount + " 次重试)");
                    
                    // 网络错误需要短暂延迟重试，避免系统资源冲突
                    retryHandler.postDelayed(new Runnable() {
                        @Override
                        public void run() {
                            LOG.d(TAG, "执行重试加载URL: " + failingUrl);
                            CordovaWebViewImpl.this.loadUrlIntoView(failingUrl, false);
                        }
                    }, NETWORK_ERROR_RETRY_INTERVAL_MS);
                    
                    // 构建重试中的错误消息给前端
                    JSONObject retryData = new JSONObject();
                    try {
                        retryData.put("errorCode", errorCode);
                        retryData.put("description", "正在重试加载... (第 " + retryCount + " 次尝试)");
                        retryData.put("originalDescription", description);
                        retryData.put("url", failingUrl);
                        retryData.put("isLocalFile", failingUrl != null && failingUrl.startsWith("file://"));
                        retryData.put("timestamp", System.currentTimeMillis());
                        retryData.put("isRetrying", true);
                        retryData.put("retryCount", retryCount);
                    } catch (JSONException e) {
                        LOG.e(TAG, "创建重试错误JSON对象失败: " + e.getMessage(), e);
                    }
                    pluginManager.postMessage("onReceivedRetry", retryData);
                    return; // 不立即上报，继续重试流程
                } else {
                    // 达到最大重试次数，清除计数
                    networkErrorRetryMap.remove(failingUrl);
                }
            }
            
            // 重试用尽或无需重试，统一使用reportCompleteLoadProcess上报
            LoadSession defaultSession = createDefaultLoadSession(failingUrl);
            int finalRetryCount = networkErrorRetryMap.getOrDefault(failingUrl, 0);
            reportCompleteLoadProcess(false, errorCode, description, failingUrl, defaultSession, finalRetryCount);

            // 清理当前LoadSession（加载失败）
            if (currentLoadSession != null && currentLoadSession.getUrl().equals(failingUrl)) {
                currentLoadSession = null;
            }
        }
        
        /**
         * 判断是否应该对特定错误进行重试
         */
        private boolean shouldRetryError(int errorCode, String failingUrl) {
            // 排除about:blank等特殊URL
            if (failingUrl == null || "about:blank".equals(failingUrl)) {
                return false;
            }
            
            // 对所有网络相关错误和本地文件错误进行重试
            switch (errorCode) {
                case WebViewClient.ERROR_CONNECT:
                case WebViewClient.ERROR_IO:
                case WebViewClient.ERROR_HOST_LOOKUP:
                case WebViewClient.ERROR_TIMEOUT:
                case WebViewClient.ERROR_FILE_NOT_FOUND: // 文件未找到也重试
                case WebViewClient.ERROR_UNKNOWN: // 对于未知错误，也尝试重试
                    return true;
                default:
                    // 其他错误也允许重试（包括本地文件错误）
                    return true;
            }
        }
        
        

        @Override
        public void onPageFinishedLoading(String url) {
            LOG.d(TAG, "onPageFinished(" + url + ")");

            clearLoadTimeoutTimer();

            // 计算加载时间并进行性能分析
            LoadSession currentSession = getCurrentLoadSession(url);
            if (currentSession != null) {
                long loadDuration = System.currentTimeMillis() - currentSession.getLoadStartTime();

                // 使用优化器分析加载性能
                WebViewLoadOptimizer.getInstance().analyzeLoadPerformance(url, loadDuration, true);

                // 热更新文件额外分析
                if (url.contains("cordova-hot-code-push-plugin")) {
                    LOG.d(TAG, "热更新文件加载性能分析: 耗时=" + loadDuration + "ms");
                    if (loadDuration > 30000) {
                        LOG.w(TAG, "热更新文件加载过慢，建议检查文件大小和设备性能");
                    }
                }

                LOG.i(TAG, "页面加载完成: " + url + " (耗时: " + loadDuration + "ms)");
            }

            // 检查是否有中间错误需要上报（页面最终加载成功，但过程中有错误）
            CordovaWebViewErrorManager errorManager = CordovaWebViewErrorManager.getInstance();
            if (errorManager.isNeedReport()) {
                if (currentSession != null) {
                    // 页面最终成功加载，但过程中有错误，上报这些中间错误
                    LOG.d(TAG, "页面加载成功，但过程中有错误，进行上报: " + url);
                    reportCompleteLoadProcess(true, 0, "页面加载成功，但过程中有错误", url, currentSession, 0);
                }
                // 重置上报标记并清理错误历史
                errorManager.setNeedReport(false);
                errorManager.clearErrorHistory();
            }

            // 清理当前LoadSession（页面加载完成）
            if (currentLoadSession != null && currentLoadSession.getUrl().equals(url)) {
                currentLoadSession = null;
            }

            // Broadcast message that page has loaded
            pluginManager.postMessage("onPageFinished", url);

            // Make app visible after 2 sec in case there was a JS error and Cordova JS never initialized correctly
            if (engine.getView().getVisibility() != View.VISIBLE) {
                Thread t = new Thread(new Runnable() {
                    public void run() {
                        try {
                            Thread.sleep(2000);
                            cordova.getActivity().runOnUiThread(new Runnable() {
                                public void run() {
                                    pluginManager.postMessage("spinner", "stop");
                                }
                            });
                        } catch (InterruptedException e) {
                        }
                    }
                });
                t.start();
            }

            // Shutdown if blank loaded
            if (url.equals("about:blank")) {
                pluginManager.postMessage("exit", null);
            }
        }

        @Override
        public Boolean onDispatchKeyEvent(KeyEvent event) {
            int keyCode = event.getKeyCode();
            boolean isBackButton = keyCode == KeyEvent.KEYCODE_BACK;
            if (event.getAction() == KeyEvent.ACTION_DOWN) {
                if (isBackButton && mCustomView != null) {
                    return true;
                } else if (boundKeyCodes.contains(keyCode)) {
                    return true;
                } else if (isBackButton) {
                    return engine.canGoBack();
                }
            } else if (event.getAction() == KeyEvent.ACTION_UP) {
                if (isBackButton && mCustomView != null) {
                    hideCustomView();
                    return true;
                } else if (boundKeyCodes.contains(keyCode)) {
                    String eventName = null;
                    switch (keyCode) {
                        case KeyEvent.KEYCODE_VOLUME_DOWN:
                            eventName = "volumedownbutton";
                            break;
                        case KeyEvent.KEYCODE_VOLUME_UP:
                            eventName = "volumeupbutton";
                            break;
                        case KeyEvent.KEYCODE_SEARCH:
                            eventName = "searchbutton";
                            break;
                        case KeyEvent.KEYCODE_MENU:
                            eventName = "menubutton";
                            break;
                        case KeyEvent.KEYCODE_BACK:
                            eventName = "backbutton";
                            break;
                    }
                    if (eventName != null) {
                        sendJavascriptEvent(eventName);
                        return true;
                    }
                } else if (isBackButton) {
                    return engine.goBack();
                }
            }
            return null;
        }

        @Override
        public boolean onNavigationAttempt(String url) {
            // Give plugins the chance to handle the url
            if (pluginManager.onOverrideUrlLoading(url)) {
                return true;
            } else if (pluginManager.shouldAllowNavigation(url)) {
                return false;
            } else if (pluginManager.shouldOpenExternalUrl(url)) {
                showWebPage(url, true, false, null);
                return true;
            }
            LOG.w(TAG, "Blocked (possibly sub-frame) navigation to non-allowed URL: " + url);
            return true;
        }
    }
}
